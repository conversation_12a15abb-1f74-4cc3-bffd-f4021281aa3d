# ===== 认证服务开发环境配置 =====
# 开发环境专用配置，禁用限流和安全限制

# 环境配置
NODE_ENV=development

# 限流配置 - 开发环境禁用
RATE_LIMIT_ENABLED=false
RATE_LIMIT_GLOBAL_ENABLED=false
RATE_LIMIT_LOGIN_ENABLED=false
RATE_LIMIT_REGISTER_ENABLED=false
RATE_LIMIT_PASSWORD_RESET_ENABLED=false

# 全局限流配置（即使启用也设置为极高值）
RATE_LIMIT_GLOBAL_TTL=1
RATE_LIMIT_GLOBAL_LIMIT=999999
RATE_LIMIT_SKIP_SUCCESSFUL=true
RATE_LIMIT_SKIP_FAILED=true

# 登录限流配置（开发环境宽松）
RATE_LIMIT_LOGIN_TTL=1
RATE_LIMIT_LOGIN_LIMIT=999999
RATE_LIMIT_LOGIN_BLOCK_DURATION=1

# 注册限流配置（开发环境宽松）
RATE_LIMIT_REGISTER_TTL=1
RATE_LIMIT_REGISTER_LIMIT=999999

# 密码重置限流配置（开发环境宽松）
RATE_LIMIT_PASSWORD_RESET_TTL=1
RATE_LIMIT_PASSWORD_RESET_LIMIT=999999

# 账户保护配置（开发环境宽松）
ACCOUNT_PROTECTION_LOGIN_ENABLED=false
ACCOUNT_PROTECTION_MAX_ATTEMPTS=999999
ACCOUNT_PROTECTION_LOCKOUT_DURATION=1
ACCOUNT_PROTECTION_PROGRESSIVE_LOCKOUT=false
ACCOUNT_PROTECTION_CAPTCHA_THRESHOLD=999999
ACCOUNT_PROTECTION_IP_BASED_LOCKOUT=false
ACCOUNT_PROTECTION_DEVICE_BASED_LOCKOUT=false

# IP白名单配置（开发环境禁用）
ENABLE_IP_WHITELIST=false
ALLOWED_IPS=
TRUSTED_PROXIES=

# 服务间认证配置（开发环境简化）
ENABLE_SERVICE_AUTH=false
SERVICE_SECRET=dev-secret-key
SERVICE_TOKEN_EXPIRY=86400

# 监控和日志配置
SECURITY_LOG_LEVEL=debug
LOG_SUCCESSFUL_ACCESS=true
LOG_FAILED_ACCESS=true
ENABLE_SECURITY_METRICS=true

# JWT配置（开发环境宽松）
JWT_SECRET=development-secret-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_BLACKLIST_ENABLED=false

# 密码策略配置（开发环境简化）
PASSWORD_SALT_ROUNDS=10
PASSWORD_MIN_LENGTH=6
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=false
PASSWORD_REQUIRE_LOWERCASE=false
PASSWORD_REQUIRE_NUMBERS=false
PASSWORD_REQUIRE_SPECIAL_CHARS=false
PASSWORD_HISTORY_COUNT=0
PASSWORD_MAX_AGE=999999

# 会话配置（开发环境宽松）
SESSION_MAX_CONCURRENT=10
SESSION_TIMEOUT=86400
SESSION_ABSOLUTE_TIMEOUT=604800
SESSION_RENEWAL_THRESHOLD=3600

# Redis配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=1

# 数据库配置
MONGODB_URI=mongodb://***************:27017/football_manager_auth_dev

# 开发环境特殊配置
DEBUG_MODE=true
VERBOSE_LOGGING=true
DISABLE_CORS=false
ALLOW_ALL_ORIGINS=true
