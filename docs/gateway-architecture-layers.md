# 网关服务分层架构设计文档

## 🎯 **概述**

本文档详细描述了网关服务的分层架构设计原理、实现方式和使用指南。网关服务采用了清晰的四层架构模式，确保了代码的可维护性、可扩展性和可测试性。

## 🏗️ **架构概览**

```
┌─────────────────────────────────────────┐
│              应用层 (App Layer)           │  ← 系统级服务和运维功能
├─────────────────────────────────────────┤
│             业务层 (Domain Layer)        │  ← 网关业务逻辑和用例实现
├─────────────────────────────────────────┤
│             核心层 (Core Layer)          │  ← 网关核心能力和领域规则
├─────────────────────────────────────────┤
│            基础设施层 (Infrastructure)    │  ← 技术基础设施和外部集成
├─────────────────────────────────────────┤
│             通用层 (Common Layer)        │  ← 共享组件和工具函数
└─────────────────────────────────────────┘
```

### **依赖方向规则**
```
应用层 ──→ 业务层 ──→ 核心层 ──→ 基础设施层
   ↑         ↑         ↑         ↑
   │         │         │         └── 只提供技术能力
   │         │         └── 提供网关核心能力
   │         └── 实现网关业务用例
   └── 提供应用服务

✅ 上层可以依赖下层
❌ 下层不能依赖上层
❌ 同层之间避免直接依赖
✅ 通过接口实现依赖倒置
```

## 📁 **目录结构**

```
apps/gateway/src/
├── app/                       # 应用层
│   ├── health/                # 健康检查服务
│   │   ├── health.controller.ts
│   │   ├── health.module.ts
│   │   ├── health.service.ts
│   │   └── indicators/
│   └── metrics/               # 指标监控服务
│       ├── metrics.controller.ts
│       └── metrics.module.ts
├── domain/                    # 业务层
│   ├── user/                  # 用户认证业务
│   │   ├── user.controller.ts
│   │   ├── user.module.ts
│   │   ├── user.service.ts
│   │   ├── dto/
│   │   ├── guards/
│   │   └── strategies/
│   ├── proxy/                 # HTTP代理业务
│   │   ├── proxy.controller.ts
│   │   ├── proxy.module.ts
│   │   └── proxy.service.ts
│   ├── websocket/             # WebSocket业务
│   │   ├── websocket.gateway.ts
│   │   ├── websocket.module.ts
│   │   ├── session.service.ts
│   │   └── guards/
│   └── graphql/               # GraphQL业务
│       └── graphql.gateway.ts
├── core/                      # 核心层
│   ├── auth/                  # 认证核心
│   │   ├── jwt-auth.service.ts
│   │   └── interfaces/
│   ├── cache/                 # 缓存核心
│   │   └── gateway-cache.service.ts
│   ├── circuit-breaker/       # 熔断器核心
│   │   └── circuit-breaker.service.ts
│   ├── load-balancer/         # 负载均衡核心
│   │   └── load-balancer.service.ts
│   ├── rate-limit/            # 限流核心
│   │   └── rate-limit.service.ts
│   ├── router/                # 路由核心
│   │   ├── route-matcher.service.ts
│   │   ├── route-manager.service.ts
│   │   └── interfaces/
│   └── shared/                # 共享核心服务
│       └── core.module.ts
├── infrastructure/            # 基础设施层
│   ├── microservices/         # 微服务客户端
│   │   ├── microservices.module.ts
│   │   └── microservices.config.ts
│   ├── jwt/                   # JWT配置
│   │   └── jwt-shared.module.ts
│   ├── guards/                # 守卫组件
│   │   ├── guards.module.ts
│   │   ├── auth.guard.ts
│   │   └── rate-limit.guard.ts
│   ├── interceptors/          # 拦截器组件
│   │   ├── interceptors.module.ts
│   │   ├── proxy.interceptor.ts
│   │   ├── request-logging.interceptor.ts
│   │   └── response-transform.interceptor.ts
│   ├── filters/               # 过滤器组件
│   │   ├── filters.module.ts
│   │   └── gateway-exception.filter.ts
│   ├── middleware/            # 中间件
│   │   ├── auth.middleware.ts
│   │   ├── logging.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   └── security.middleware.ts
│   ├── config/                # 配置管理
│   │   └── config-manager.service.ts
│   ├── discovery/             # 服务发现
│   │   └── service-discovery.service.ts
│   ├── logging/               # 日志服务
│   │   └── logging.service.ts
│   ├── monitoring/            # 监控服务
│   │   └── metrics.service.ts
│   └── tracing/               # 链路追踪
│       └── tracing.service.ts
├── common/                    # 通用层
│   ├── constants/             # 常量定义
│   ├── interfaces/            # 接口定义
│   ├── dto/                   # 数据传输对象
│   ├── decorators/            # 装饰器
│   └── utils/                 # 工具函数
├── config/                    # 配置层
│   ├── gateway.config.ts
│   └── services.config.ts
├── app.module.ts              # 根模块
├── main.ts                    # 应用入口
└── architecture.spec.ts       # 架构测试
```

## 🔍 **各层详细说明**

### **1. 应用层 (App Layer)**

#### **职责范围**
- 系统级服务和运维功能
- 健康检查和监控
- 系统管理接口
- 跨业务模块协调

#### **设计原则**
```typescript
✅ 面向运维人员和系统管理
✅ 提供系统级别的功能
✅ 不包含业务逻辑
✅ 可以依赖业务层获取状态信息
✅ 独立于具体业务场景
```

#### **典型组件**
- **HealthModule**: 健康检查服务，监控各个业务模块状态
- **MetricsModule**: 指标收集和监控，提供系统性能数据

#### **示例代码**
```typescript
// app/health/health.service.ts
@Injectable()
export class HealthService {
  constructor(
    private readonly proxyService: ProxyService,    // 依赖业务层
    private readonly websocketService: WebSocketService,
    private readonly authService: AuthService,
  ) {}

  async checkHealth(): Promise<HealthStatus> {
    // 检查各业务模块健康状态
    const proxyHealth = await this.proxyService.getHealth();
    const wsHealth = await this.websocketService.getHealth();
    const authHealth = await this.authService.getHealth();
    
    return {
      status: 'ok',
      modules: { proxy: proxyHealth, websocket: wsHealth, auth: authHealth }
    };
  }
}
```

### **2. 业务层 (Domain Layer)**

#### **职责范围**
- 网关业务逻辑实现
- 具体业务用例编排
- 业务流程控制
- 外部接口适配

#### **设计原则**
```typescript
✅ 实现具体的网关业务场景
✅ 编排核心层服务完成业务流程
✅ 面向业务需求设计
✅ 包含业务规则和验证
✅ 可以依赖核心层和基础设施层
```

#### **典型组件**
- **UserModule**: 用户认证和授权业务
- **ProxyModule**: HTTP请求代理转发业务
- **WebSocketModule**: WebSocket连接管理业务
- **GraphQLModule**: GraphQL网关业务

#### **示例代码**
```typescript
// domain/proxy/proxy.service.ts
@Injectable()
export class ProxyService {
  constructor(
    private readonly routeMatcher: RouteMatcherService,      // 依赖核心层
    private readonly loadBalancer: LoadBalancerService,     // 依赖核心层
    private readonly circuitBreaker: CircuitBreakerService, // 依赖核心层
    private readonly authService: AuthService,              // 依赖核心层
  ) {}

  async handleRequest(request: Request): Promise<Response> {
    // 1. 认证检查
    const user = await this.authService.validateRequest(request);
    
    // 2. 路由匹配
    const route = await this.routeMatcher.match(request.path);
    
    // 3. 负载均衡选择目标服务
    const target = await this.loadBalancer.selectTarget(route.service);
    
    // 4. 熔断器保护
    return this.circuitBreaker.execute(() => 
      this.forwardRequest(request, target)
    );
  }
}
```

### **3. 核心层 (Core Layer)**

#### **职责范围**
- 网关核心能力实现
- 领域专家知识封装
- 核心算法和策略
- 技术无关的业务规则

#### **设计原则**
```typescript
✅ 包含网关领域的核心规则和逻辑
✅ 技术实现无关，专注于业务能力
✅ 高内聚低耦合的服务设计
✅ 被业务层依赖和编排
✅ 不依赖具体的技术实现
```

#### **典型组件**
- **AuthService**: 认证核心逻辑，JWT验证、权限检查
- **RouteMatcherService**: 路由匹配算法和规则
- **LoadBalancerService**: 负载均衡策略实现
- **CircuitBreakerService**: 熔断器模式实现
- **RateLimitService**: 限流算法和策略
- **GatewayCacheService**: 网关缓存策略

#### **示例代码**
```typescript
// core/load-balancer/load-balancer.service.ts
@Injectable()
export class LoadBalancerService {
  private strategies = new Map<string, LoadBalanceStrategy>();

  constructor() {
    this.strategies.set('round-robin', new RoundRobinStrategy());
    this.strategies.set('weighted', new WeightedStrategy());
    this.strategies.set('least-connections', new LeastConnectionsStrategy());
  }

  async selectTarget(
    serviceName: string,
    targets: ServiceTarget[],
    strategy: string = 'round-robin'
  ): Promise<ServiceTarget> {
    const loadBalancer = this.strategies.get(strategy);
    if (!loadBalancer) {
      throw new Error(`Unknown load balance strategy: ${strategy}`);
    }

    return loadBalancer.select(targets);
  }
}
```

### **4. 基础设施层 (Infrastructure Layer)**

#### **职责范围**
- 技术基础设施提供
- 外部系统集成
- 技术组件封装
- 配置和环境管理

#### **设计原则**
```typescript
✅ 提供技术能力，不包含业务逻辑
✅ 封装外部依赖和技术细节
✅ 可以被任何上层模块使用
✅ 技术实现细节的抽象
✅ 配置驱动的组件设计
```

#### **典型组件**
- **MicroservicesModule**: 微服务客户端统一管理
- **JwtSharedModule**: JWT配置统一管理
- **GuardsModule**: 守卫组件集合
- **InterceptorsModule**: 拦截器组件集合
- **FiltersModule**: 过滤器组件集合
- **MiddlewareModule**: 中间件组件集合

#### **示例代码**
```typescript
// infrastructure/microservices/microservices.module.ts
@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'AUTH_SERVICE',
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get('REDIS_HOST', 'localhost'),
            port: configService.get('REDIS_PORT', 6379),
            password: configService.get('REDIS_PASSWORD'),
            retryAttempts: 5,
            retryDelay: 3000,
          },
        }),
      },
      // 其他微服务配置...
    ]),
  ],
  exports: [ClientsModule],
})
export class MicroservicesModule {}
```

### **5. 通用层 (Common Layer)**

#### **职责范围**
- 共享组件和工具
- 常量和接口定义
- 通用装饰器和工具函数
- 跨层共享的数据结构

#### **设计原则**
```typescript
✅ 不依赖任何其他层
✅ 提供通用的工具和组件
✅ 可以被任何层使用
✅ 无状态的纯函数设计
✅ 高度可重用的组件
```

#### **典型组件**
- **Constants**: 系统常量定义
- **Interfaces**: 通用接口定义
- **DTOs**: 数据传输对象
- **Decorators**: 自定义装饰器
- **Utils**: 工具函数集合

## 🔧 **模块设计模式**

### **1. 共享模块模式**

用于解决重复注册问题，统一管理共享资源：

```typescript
// 问题：多个模块重复注册相同的服务
// ❌ 错误方式
@Module({
  imports: [
    ClientsModule.registerAsync([...]), // 在多个模块中重复
    JwtModule.registerAsync([...]),     // 在多个模块中重复
  ],
})
export class SomeModule {}

// ✅ 正确方式：创建共享模块
@Module({
  imports: [MicroservicesModule, JwtSharedModule],
})
export class SomeModule {}
```

### **2. 核心模块模式**

将相关的核心服务组织在一起：

```typescript
// core/shared/core.module.ts
@Module({
  providers: [
    RouteMatcherService,
    LoadBalancerService,
    CircuitBreakerService,
    RateLimitService,
    GatewayCacheService,
    AuthService,
  ],
  exports: [
    RouteMatcherService,
    LoadBalancerService,
    CircuitBreakerService,
    RateLimitService,
    GatewayCacheService,
    AuthService,
  ],
})
export class CoreModule {}
```

### **3. 业务模块模式**

业务模块专注于业务逻辑，依赖核心模块：

```typescript
// domain/proxy/proxy.module.ts
@Module({
  imports: [
    ConfigModule,
    CoreModule,             // 依赖核心模块
    MicroservicesModule,    // 依赖基础设施模块
    JwtSharedModule,        // 依赖基础设施模块
  ],
  controllers: [ProxyController],
  providers: [ProxyService],
  exports: [ProxyService],
})
export class ProxyModule {}
```

## 📋 **架构规则和约束**

### **1. 依赖规则**

```typescript
// ✅ 允许的依赖方向
应用层 → 业务层
业务层 → 核心层
核心层 → 基础设施层
任何层 → 通用层

// ❌ 禁止的依赖方向
业务层 → 应用层
核心层 → 业务层
基础设施层 → 核心层
基础设施层 → 业务层
通用层 → 任何其他层
```

### **2. 模块组织规则**

```typescript
// ✅ 正确的模块组织
- 每个模块有明确的职责边界
- 模块内部高内聚
- 模块之间低耦合
- 通过接口定义模块边界

// ❌ 错误的模块组织
- 模块职责不清晰
- 循环依赖
- 直接依赖具体实现
- 跨层直接调用
```

### **3. 服务设计规则**

```typescript
// ✅ 正确的服务设计
- 单一职责原则
- 依赖注入
- 接口隔离
- 开闭原则

// ❌ 错误的服务设计
- 上帝类（God Class）
- 紧耦合
- 硬编码依赖
- 违反SOLID原则
```

## 🧪 **架构验证**

### **1. 自动化架构测试**

项目包含自动化的架构测试，验证分层规则：

```bash
# 运行架构测试
npx jest --config apps/gateway/jest.architecture.config.js
```

### **2. 架构健康度检查**

定期检查架构健康度指标：

```typescript
// 架构健康度指标
- 模块耦合度: 低
- 代码重复率: <5%
- 依赖关系复杂度: 低
- 模块职责清晰度: 高
- 测试覆盖率: >80%
```

## 📈 **架构演进策略**

### **1. 渐进式重构**

```typescript
// 重构原则
✅ 只改变代码组织方式，不改变功能实现
✅ 保留所有现有的 module、service 及功能代码
✅ 只做复制、移动、编辑操作
✅ 不重新生成任何已存在的代码
✅ 分阶段渐进式重构，每阶段验证
```

### **2. 向后兼容**

```typescript
// 兼容性保证
✅ API 接口完全兼容
✅ 配置格式向后兼容
✅ 数据结构保持一致
✅ 行为语义不变
```

### **3. 持续改进**

```typescript
// 改进机制
- 架构评审：每月评审架构健康度
- 依赖检查：CI/CD 中集成重复注册检查
- 性能监控：持续监控内存使用和启动时间
- 文档更新：及时更新架构文档和开发指南
- 知识分享：定期分享架构设计经验
```

## 🎯 **总结**

网关服务的分层架构设计遵循了清晰的职责分离原则，通过四层架构模式实现了：

1. **清晰的职责边界**: 每一层都有明确的职责和边界
2. **良好的可维护性**: 模块化设计便于维护和扩展
3. **高度的可测试性**: 分层设计支持单元测试和集成测试
4. **优秀的可扩展性**: 新功能可以在合适的层次添加
5. **强大的可重用性**: 核心组件可以在不同场景中重用

这种架构设计为网关服务的长期发展奠定了坚实的基础，支持团队的高效协作和系统的持续演进。
