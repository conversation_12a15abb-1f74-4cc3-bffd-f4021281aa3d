# Payload上下文使用示例 v2.0

## 📋 概述

本文档展示如何在Controller中使用优化后的payload上下文提取功能，包括新的 `injectedContext` 结构和相关工具函数。

## 🎯 核心改进

- ✅ 统一的 `injectedContext` 结构
- ✅ 类型安全的上下文提取工具函数
- ✅ 简化的二次微服务调用
- ✅ 向后兼容的数据读取

## 🔄 对比：优化前后

### **优化前（不推荐）**
```typescript
// ❌ 旧的写法：类型不安全，容易出错
@MessagePattern('character.getInfo')
async getCharacterInfo(@Payload() payload: any) {
  // 不安全的数据访问
  const userId = payload.userId;
  const characterId = payload.serverContext?.characterId || payload.characterId;
  const serverId = payload.serverContext?.serverId || payload.serverId;
  
  // 二次调用时容易丢失上下文
  const goldInfo = await this.microserviceClient.call('character', 'character.getCurrency', {
    characterId: characterId,
    // ❌ 上下文信息丢失！
  });
}
```

### **优化后（推荐）**
```typescript
// ✅ 新的写法：类型安全，上下文完整
import { 
  getTrustedUserId, 
  getTrustedCharacterId, 
  getTrustedServerId,
  prepareForMicroserviceCall,
  logContextInfo,
  createSuccessResponse 
} from '@common/utils';

@MessagePattern('character.getInfo')
async getCharacterInfo(@Payload() payload: { 
  characterId?: string; 
  injectedContext?: InjectedContext 
}) {
  // ✅ 类型安全的数据提取
  logContextInfo(this.logger, payload, 'getCharacterInfo');
  
  const trustedUserId = getTrustedUserId(payload);
  const trustedCharacterId = getTrustedCharacterId(payload);
  const trustedServerId = getTrustedServerId(payload);
  
  // ✅ 二次调用时自动传递上下文
  const goldInfo = await this.microserviceClient.call(
    'character', 
    'character.getCurrency',
    prepareForMicroserviceCall({ characterId: trustedCharacterId }, payload)
  );
  
  return createSuccessResponse(goldInfo.data, '获取成功');
}
```

## 🎯 实际使用示例

### 1. 简单查询API

```typescript
@PublicMessagePattern('character.getInfo')
async getCharacterInfo(@Payload() payload: { 
  characterId?: string; 
  injectedContext?: InjectedContext 
}) {
  // 🎯 记录上下文信息
  logContextInfo(this.logger, payload, 'getCharacterInfo');

  // 🎯 获取可信的数据
  const trustedCharacterId = getTrustedCharacterId(payload);

  // 🎯 验证权限
  if (!validateOperationPermission(payload, trustedCharacterId)) {
    throw new ForbiddenException('只能查看自己的角色信息');
  }

  const character = await this.characterService.getCharacterInfo(trustedCharacterId);
  return createSuccessResponse(character, '获取角色信息成功');
}
```

### 2. 复杂业务逻辑（二次调用）

```typescript
@PublicMessagePattern('hero.breakthrough')
async breakthroughHero(@Payload() payload: { 
  heroId: string; 
  injectedContext?: InjectedContext 
}) {
  logContextInfo(this.logger, payload, 'breakthroughHero');

  const trustedCharacterId = getTrustedCharacterId(payload);

  // 🎯 检查金币（二次调用，自动传递上下文）
  const goldInfo = await this.microserviceClient.call(
    'character',
    'character.getInfo',
    prepareForMicroserviceCall({ characterId: trustedCharacterId }, payload)
  );

  if (goldInfo.data.gold < 1000) {
    throw new BadRequestException('金币不足');
  }

  // 🎯 扣除金币（二次调用，自动传递上下文）
  await this.microserviceClient.call(
    'character',
    'character.currency.subtract',
    prepareForMicroserviceCall({
      characterId: trustedCharacterId,
      currencyDto: { currencyType: 'gold', amount: 1000, reason: 'hero_breakthrough' }
    }, payload)
  );

  // 执行突破逻辑
  const result = await this.heroService.breakthrough(payload.heroId);
  return createSuccessResponse(result, '英雄突破成功');
}
```

### 3. 内部API

```typescript
@InternalMessagePattern('character.currency.add')
async addCurrency(@Payload() payload: { 
  characterId: string;
  currencyDto: CurrencyOperationDto;
  injectedContext?: InjectedContext 
}) {
  // 🎯 内部API也记录上下文（用于审计）
  logContextInfo(this.logger, payload, 'addCurrency');

  const trustedCharacterId = getTrustedCharacterId(payload) || payload.characterId;
  const requestId = getRequestId(payload);

  this.logger.log(`💰 增加货币: ${trustedCharacterId}, 类型: ${payload.currencyDto.currencyType}, 数量: ${payload.currencyDto.amount}, 请求ID: ${requestId}`);

  const result = await this.characterService.addCurrency(trustedCharacterId, payload.currencyDto);
  return createSuccessResponse(result, '货币增加成功');
}
```

## 🔧 工具函数说明

### 基础提取函数
- `getTrustedUserId(payload)` - 获取可信的用户ID
- `getTrustedCharacterId(payload)` - 获取可信的角色ID  
- `getTrustedServerId(payload)` - 获取可信的区服ID
- `getRequestId(payload)` - 获取请求ID（链路追踪）

### 业务辅助函数
- `logContextInfo(logger, payload, apiName)` - 记录上下文信息
- `validateOperationPermission(payload, targetCharacterId)` - 基础权限验证
- `prepareForMicroserviceCall(businessData, sourcePayload)` - 准备二次调用数据

### 响应格式化函数
- `createSuccessResponse(data, message)` - 创建成功响应
- `createErrorResponse(message, code)` - 创建错误响应

## 📝 最佳实践

1. **始终使用工具函数**：不要直接访问 `payload.injectedContext`
2. **记录上下文信息**：使用 `logContextInfo` 记录重要操作
3. **验证操作权限**：使用 `validateOperationPermission` 进行基础权限检查
4. **二次调用传递上下文**：使用 `prepareForMicroserviceCall` 确保上下文传递
5. **使用标准响应格式**：使用 `createSuccessResponse` 和 `createErrorResponse`

## 🚀 迁移指南

1. **运行批量迁移脚本**：
   ```bash
   npm run migrate:payload:dry-run  # 预览
   npm run migrate:payload          # 执行
   ```

2. **手动优化复杂类型**：脚本无法处理的复杂类型需要手动添加 `injectedContext?: InjectedContext`

3. **更新业务逻辑**：将直接访问 `payload.userId` 等改为使用工具函数

4. **测试验证**：确保所有API功能正常，上下文传递正确
