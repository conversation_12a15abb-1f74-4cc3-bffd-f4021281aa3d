# 本机测试指南

## 概述

本指南介绍如何在本机环境下进行足球经理游戏服务器的测试，无需Docker，直接连接内网数据库服务。

## 测试架构

```
本机环境 (Windows)
├── Auth Service (端口: 3002)
├── Gateway Service (端口: 3001)
└── 连接到内网服务器
    ├── Redis (***************:6379)
    └── MongoDB (***************:27017)
```

## 环境要求

### 必需软件
- Node.js 16+ 
- npm 或 yarn
- Git

### 网络要求
- 能够访问内网服务器 ***************
- Redis端口 6379 可访问
- MongoDB端口 27017 可访问

## 配置文件

### 主配置文件 (.env)
```bash
# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456

# MongoDB 配置
MONGODB_URI=*****************************************************************

# 服务端口配置
AUTH_PORT=3001
GATEWAY_PORT=3000

# JWT 配置
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=24h

# 日志配置
LOG_LEVEL=debug
NODE_ENV=development
```

### 测试环境配置 (.env.test)
```bash
# 继承主配置，但使用测试数据库
MONGODB_URI=**************************************************************
NODE_ENV=test
LOG_LEVEL=info
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 构建项目
```bash
npm run build
```

### 3. 启动服务

#### 方法A: 分别启动（推荐用于开发）
```bash
# 终端1: 启动认证服务
npm run start:auth

# 终端2: 启动网关服务  
npm run start:gateway
```

#### 方法B: 同时启动
```bash
npm run start:all
```

### 4. 验证服务
```bash
# 检查认证服务
curl http://localhost:3002/health

# 检查网关服务
curl http://localhost:3001/health
```

## 功能特性清单

### Auth服务功能特性
- ✅ **用户认证**: 注册、登录、JWT令牌管理、令牌刷新
- ✅ **多因子认证(MFA)**: TOTP、备用码、QR码生成
- ✅ **密码管理**: 强度验证、哈希存储、历史记录、重置功能
- ✅ **会话管理**: 多设备支持、设备指纹、受信任设备
- ✅ **权限角色**: RBAC模型、角色继承、细粒度权限
- ✅ **安全防护**: 登录限制、暴力破解防护、审计日志
- ✅ **用户管理**: 信息查询更新、状态管理、游戏档案

### Gateway服务功能特性
- ✅ **路由管理**: 动态配置、智能匹配、API版本控制
- ✅ **认证集成**: JWT验证、多令牌来源、权限控制
- ✅ **限流控制**: 多维度限流、多种算法、动态自适应
- ✅ **负载均衡**: 多种策略、健康检查、故障转移
- ✅ **熔断器**: 故障检测、自动熔断、恢复机制
- ✅ **缓存系统**: 多级缓存、多种策略、失效更新
- ✅ **WebSocket网关**: 实时通信、房间管理、消息路由
- ✅ **HTTP代理**: 微服务转发、请求转换、连接池
- ✅ **监控健康**: 状态监控、性能指标、错误统计

## 测试类型

### 1. 单元测试
```bash
# 运行所有单元测试
npm test

# 运行特定服务的测试
npm run test:auth
npm run test:gateway

# 运行测试并生成覆盖率报告
npm run test:coverage
```

### 2. 集成测试
```bash
# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

### 3. 功能测试
```bash
# 运行完整功能测试
npm run test:local

# 运行API功能测试
node scripts/api-test.js

# 运行WebSocket测试
node scripts/websocket-test.js

# 运行安全测试
node scripts/security-test.js
```

### 4. 性能测试
```bash
# 运行负载测试
node scripts/load-test.js

# 运行压力测试
node scripts/stress-test.js

# 运行并发测试
node scripts/concurrent-test.js
```

## 测试工具

### 内置测试脚本
- `scripts/local-test.js` - 本机测试主脚本
- `scripts/health-check.js` - 服务健康检查
- `scripts/api-test.js` - API功能测试
- `scripts/websocket-test.js` - WebSocket功能测试
- `scripts/security-test.js` - 安全功能测试

### 专项测试命令
```bash
# API功能测试
npm run test:api

# WebSocket功能测试
npm run test:websocket

# 安全功能测试
npm run test:security

# 综合功能测试
npm run test:comprehensive
```

### 外部工具
- **Postman**: API测试集合位于 `test/postman/`
- **Artillery**: 负载测试配置位于 `test/load/`
- **Jest**: 单元测试和集成测试

## 常见问题

### 连接问题
1. **无法连接Redis**: 检查网络连接和防火墙设置
2. **MongoDB认证失败**: 验证用户名密码和数据库权限
3. **端口冲突**: 确保3001和3002端口未被占用

### 性能问题
1. **启动缓慢**: 检查网络延迟和数据库连接
2. **测试超时**: 调整测试超时配置

### 数据问题
1. **测试数据污染**: 使用独立的测试数据库
2. **数据不一致**: 在测试前清理数据

## 最佳实践

### 开发流程
1. 修改代码
2. 运行单元测试
3. 启动本机服务
4. 运行集成测试
5. 提交代码

### 测试策略
1. **快速反馈**: 优先运行单元测试
2. **完整验证**: 定期运行集成测试
3. **性能监控**: 定期运行负载测试

### 数据管理
1. **隔离测试数据**: 使用专门的测试数据库
2. **清理策略**: 测试后自动清理数据
3. **备份恢复**: 重要测试前备份数据

## 故障排除

### 日志查看
```bash
# 查看服务日志
tail -f logs/auth.log
tail -f logs/gateway.log

# 查看测试日志
cat test-results/test.log
```

### 调试模式
```bash
# 启动调试模式
npm run start:auth:debug
npm run start:gateway:debug
```

### 重置环境
```bash
# 清理并重新安装
npm run clean
npm install
npm run build
```

## 性能基准

### 预期响应时间
- 健康检查: < 50ms
- 用户认证: < 200ms
- API调用: < 500ms

### 并发能力
- 认证服务: 100 req/s
- 网关服务: 200 req/s

## 详细测试用例

### Auth服务测试用例

#### 用户认证测试
- ✅ 用户注册（用户名/邮箱验证）
- ✅ 用户登录（用户名/邮箱 + 密码）
- ✅ JWT令牌验证和刷新
- ✅ 用户登出和令牌撤销
- ✅ 密码强度验证
- ✅ 重复注册检测

#### MFA功能测试
- ✅ TOTP密钥生成和QR码
- ✅ MFA启用/禁用
- ✅ 备用码生成和验证
- ✅ MFA登录流程

#### 安全防护测试
- ✅ 暴力破解防护和账户锁定
- ✅ 输入验证和SQL注入防护
- ✅ 会话安全管理
- ✅ 权限访问控制

### Gateway服务测试用例

#### 路由管理测试
- ✅ 精确路径匹配
- ✅ 参数路径匹配
- ✅ 通配符路径匹配
- ✅ 路由不存在处理
- ✅ HTTP方法验证

#### 认证集成测试
- ✅ 有效令牌验证
- ✅ 无效令牌拒绝
- ✅ 过期令牌处理
- ✅ 权限验证

#### WebSocket功能测试
- ✅ WebSocket连接认证
- ✅ 房间加入和离开
- ✅ 消息转发
- ✅ 连接限流
- ✅ 心跳检测

#### 限流和缓存测试
- ✅ 请求限流机制
- ✅ 限流恢复
- ✅ 用户独立限流
- ✅ 缓存性能提升

## 测试报告

### 测试结果查看
```bash
# 查看详细测试报告
npm run test:api -- --verbose

# 保存测试报告
npm run test:comprehensive -- --save

# 查看健康检查报告
npm run health -- --json --save
```

### 测试数据管理
- 测试用户自动创建和清理
- 独立的测试数据库
- 测试后数据自动清理

## 下一步

1. 阅读 [API文档](./API_DOCUMENTATION.md)
2. 查看 [架构设计](./DESIGN.md)
3. 了解 [部署指南](./DEPLOYMENT.md)
4. 查看 [综合测试计划](./comprehensive-testing-plan.md)
