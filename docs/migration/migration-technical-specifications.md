# 足球经理游戏迁移技术规范

## 1. 微服务架构设计

### 1.1 微服务划分

```
apps/
├── gateway/                # 现有 - 网关服务 (端口: 3000)
├── auth/                   # 现有 - 认证服务 (端口: 3001)
├── character/              # 新增 - 角色服务 (端口: 3002)
│   ├── src/
│   │   ├── character/      # 角色模块
│   │   ├── formation/      # 阵容模块
│   │   ├── inventory/      # 背包模块
│   │   └── item/           # 物品模块
│   └── docs/
├── game/                   # 现有 - 游戏服务 (端口: 3003)
├── club/                   # 现有 - 俱乐部服务 (端口: 3004)
├── match/                  # 现有 - 比赛服务 (端口: 3005)
├── card/                   # 现有 - 卡片服务 (端口: 3006)
├── hero/                   # 新增 - 球员服务 (端口: 3007)
│   ├── src/
│   │   ├── hero/           # 球员模块
│   │   ├── skill/          # 技能模块
│   │   └── training/       # 训练模块 (暂时不需要复杂的训练模块)
│   └── docs/
├── notification/           # 现有 - 通知服务 (端口: 3008)
├── economy/                # 新增 - 经济服务 (端口: 3009)
│   ├── src/
│   │   ├── shop/           # 商店模块
│   │   ├── payment/        # 支付模块
│   │   ├── currency/       # 货币模块
│   │   └── trade/          # 交易模块
│   └── docs/
├── social/                 # 新增 - 社交服务 (端口: 3010)
│   ├── src/
│   │   ├── friend/         # 好友模块
│   │   ├── guild/          # 公会模块
│   │   ├── mail/           # 邮件模块
│   │   └── chat/           # 聊天模块
│   └── docs/
└── activity/               # 新增 - 活动服务 (端口: 3011)
    ├── src/
    │   ├── task/           # 任务模块
    │   ├── event/          # 活动模块
    │   ├── sign/           # 签到模块
    │   └── guide/          # 引导模块
    └── docs/
```

### 1.2 共享库设计

```
libs/
├── common/                 # 现有 - 通用库
├── shared/                 # 现有 - 共享库
├── game-config/            # 新增 - 游戏配置库
│   ├── src/
│   │   ├── skill/          # 技能配置
│   │   │   ├── skill-definition.schema.ts
│   │   │   ├── skill-definition.service.ts
│   │   │   └── skill-definition.repository.ts
│   │   ├── hero/           # 球员配置
│   │   │   ├── hero-definition.schema.ts
│   │   │   └── hero-definition.service.ts
│   │   ├── item/           # 物品配置
│   │   │   ├── item-definition.schema.ts
│   │   │   └── item-definition.service.ts
│   │   ├── formation/      # 阵型配置
│   │   │   ├── formation-definition.schema.ts
│   │   │   └── formation-definition.service.ts
│   │   └── index.ts        # 统一导出
├── game-constants/         # 新增 - 游戏常量库
│   ├── src/
│   │   ├── enums/          # 枚举定义
│   │   ├── codes/          # 错误码定义
│   │   └── constants/      # 常量定义
├── game-utils/             # 新增 - 游戏工具库
│   ├── src/
│   │   ├── calculators/    # 计算工具
│   │   ├── validators/     # 验证工具
│   │   └── formatters/     # 格式化工具
└── game-types/             # 新增 - 游戏类型库
    ├── src/
    │   ├── dtos/           # 数据传输对象
    │   ├── entities/       # 实体接口
    │   └── responses/          # 响应类型
```

### 1.3 配置管理架构

#### 配置数据分类
- **静态配置**: 游戏策划配置的数据表，存储在共享配置库中
- **动态实例**: 玩家游戏过程中产生的数据，存储在各微服务中

#### 命名规范
```typescript
// 静态配置（libs/game-config中）
skill-definition.schema.ts      # 技能定义/配置表
hero-definition.schema.ts       # 球员定义/配置表
item-definition.schema.ts       # 物品定义/配置表
formation-definition.schema.ts  # 阵型定义/配置表

// 动态实例（各微服务中）
skill.schema.ts                 # 球员技能实例
hero.schema.ts                  # 球员实例
item.schema.ts                  # 物品实例（在背包中）
formation.schema.ts             # 阵容实例
```

#### 配置库使用方式
```typescript
// 在微服务中使用配置库
import { SkillDefinitionService } from '@app/game-config';

@Injectable()
export class SkillService {
  constructor(
    private skillDefinitionService: SkillDefinitionService
  ) {}

  async getSkillConfig(skillId: number) {
    return this.skillDefinitionService.getSkillDefinition(skillId);
  }
}
```

#### 配置管理优势
- **高性能**: 本地访问，无网络延迟
- **类型安全**: TypeScript编译时检查
- **强一致性**: 所有服务使用相同数据结构
- **易于维护**: 配置集中管理，统一更新

## 2. 数据模型设计

### 2.1 核心实体Schema

#### 角色实体 (Character)
```typescript
// apps/character/src/character/schemas/character.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ collection: 'characters', timestamps: true })
export class Character extends Document {
  @Prop({ required: true, unique: true })
  characterId: string;

  @Prop({ required: true })
  userId: string;            // 所属用户ID

  @Prop({ required: true })
  serverId: string;          // 所在区服ID

  @Prop({ required: true })
  name: string;

  @Prop({ default: 1 })
  level: number;

  @Prop({ default: 10000 })
  cash: number;              // 欧元

  @Prop({ default: 0 })
  gold: number;              // 金币

  @Prop({ default: 100 })
  energy: number;            // 体力

  @Prop({ default: 0 })
  vip: number;               // VIP等级

  @Prop({ default: 0 })
  fame: number;              // 声望

  @Prop({ default: 0 })
  trophy: number;            // 奖杯

  @Prop({ type: Object })
  gameProgress: {
    createRoleStep: number;   // 创角步骤
    isNewer: boolean;         // 是否新手
    activeDay: number;        // 活跃天数
  };

  @Prop({ type: Object })
  loginInfo: {
    loginTime: Date;          // 登录时间
    leaveTime: Date;          // 离线时间
    ip: string;               // IP地址
    frontendId: string;       // 前端ID
  };

  @Prop({ type: Object })
  energyInfo: {
    buyEnergyCount: number;   // 购买体力次数
    buyTime: Date;            // 购买时间
    freeTime: Date;           // 免费领取时间
  };

  @Prop({ type: [Number], default: [] })
  clubFaceIcon: number[];    // 俱乐部头像

  @Prop({ type: [Number], default: [] })
  countryFaceIcon: number[]; // 国家队头像

  @Prop({ type: Object })
  beliefInfo: {
    beliefId: number;         // 信仰ID
  };
}

export const CharacterSchema = SchemaFactory.createForClass(Character);

// 创建索引
CharacterSchema.index({ characterId: 1 }, { unique: true });
CharacterSchema.index({ userId: 1 });
CharacterSchema.index({ serverId: 1 });
CharacterSchema.index({ level: 1 });
```

#### 球员实体 (Hero)
```typescript
// apps/hero/src/hero/schemas/hero.schema.ts
@Schema({ collection: 'heroes', timestamps: true })
export class Hero extends Document {
  @Prop({ required: true })
  characterId: string;       // 所属角色ID

  @Prop({ required: true, unique: true })
  heroId: string;

  @Prop({ required: true })
  resId: number;             // 配置表ID

  @Prop({ default: 1 })
  level: number;

  @Prop({ default: 1 })
  star: number;              // 星级

  @Prop({ type: Object })
  attributes: {
    speed: number;           // 速度
    jumping: number;         // 弹跳
    strength: number;        // 力量
    stamina: number;         // 耐力
    finishing: number;       // 射门
    dribbling: number;       // 盘带
    passing: number;         // 传球
    heading: number;         // 头球
    standingTackle: number;  // 抢断
    slidingTackle: number;   // 铲球
    longPassing: number;     // 长传
    longShots: number;       // 远射
    penalties: number;       // 点球
    cornerKick: number;      // 角球
    freeKick: number;        // 任意球
    explosiveForce: number;  // 爆发力
    attack: number;          // 出击
    volleys: number;         // 制空
    save: number;            // 扑救
    resistanceDamage: number; // 抗伤性
  };

  @Prop({ type: [Number], default: [] })
  skills: number[];          // 技能列表

  @Prop({ type: Object })
  training: {
    trainLevel: number;      // 训练等级
    trainExp: number;        // 训练经验
    trainAttributes: object; // 训练属性加成
  };

  @Prop({ type: Object })
  starInfo: {
    starLevel: number;       // 升星等级
    starExp: number;         // 升星经验
    starAttributes: object;  // 升星属性加成
  };

  @Prop({ type: Object })
  skillInfo: {
    activeSkills: number[];  // 激活技能
    skillLevels: object;     // 技能等级
  };

  @Prop({ default: false })
  isLocked: boolean;         // 是否锁定

  @Prop({ default: Date.now })
  acquiredTime: Date;        // 获得时间
}

export const HeroSchema = SchemaFactory.createForClass(Hero);

// 创建索引
HeroSchema.index({ characterId: 1 });
HeroSchema.index({ heroId: 1 }, { unique: true });
HeroSchema.index({ resId: 1 });
```

#### 阵容实体 (Formation)
```typescript
// apps/character/src/formation/schemas/formation.schema.ts
@Schema({ collection: 'formations', timestamps: true })
export class Formation extends Document {
  @Prop({ required: true })
  characterId: string;       // 所属角色ID

  @Prop({ required: true, unique: true })
  formationId: string;

  @Prop({ required: true })
  name: string;              // 阵容名称

  @Prop({ required: true })
  formationType: number;     // 阵型类型 (4-4-2, 4-3-3, etc.)

  @Prop({ type: Object })
  positions: {
    GK: string;              // 门将 (heroId)
    DC: string[];            // 中后卫
    DL: string;              // 左后卫
    DR: string;              // 右后卫
    DM: string[];            // 后腰
    MC: string[];            // 中场
    ML: string;              // 左中场
    MR: string;              // 右中场
    AM: string[];            // 前腰
    ST: string[];            // 前锋
    WL: string;              // 左边锋
    WR: string;              // 右边锋
  };

  @Prop({ type: Object })
  tactics: {
    attackTactic: number;    // 进攻战术
    defenseTactic: number;   // 防守战术
    formation: number;       // 阵型ID
  };

  @Prop({ type: Object })
  coaches: {
    headCoach: string;       // 主教练
    assistantCoaches: string[]; // 助理教练
  };

  @Prop({ type: Object })
  attributes: {
    totalRating: number;     // 总评分
    attackRating: number;    // 进攻评分
    defenseRating: number;   // 防守评分
    midFieldRating: number;  // 中场评分
  };

  @Prop({ default: false })
  isDefault: boolean;        // 是否默认阵容

  @Prop({ default: false })
  isLeagueFormation: boolean; // 是否联赛阵容

  @Prop({ default: false })
  isWarOfFaithFormation: boolean; // 是否信仰之战阵容
}

export const FormationSchema = SchemaFactory.createForClass(Formation);

// 创建索引
FormationSchema.index({ characterId: 1 });
FormationSchema.index({ formationId: 1 }, { unique: true });
```

### 2.2 数据库配置

#### MongoDB连接配置
```typescript
// libs/database/src/mongodb.config.ts
export const mongodbConfig = {
  'character': {
    uri: process.env.CHARACTER_MONGODB_URI,
    dbName: 'character_db',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
  },
  'hero': {
    uri: process.env.HERO_MONGODB_URI,
    dbName: 'hero_db',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
    },
  },
  'economy': {
    uri: process.env.ECONOMY_MONGODB_URI,
    dbName: 'economy_db',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
    },
  },
  'social': {
    uri: process.env.SOCIAL_MONGODB_URI,
    dbName: 'social_db',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
    },
  },
  'activity': {
    uri: process.env.ACTIVITY_MONGODB_URI,
    dbName: 'activity_db',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
    },
  },
};
```

## 3. 服务接口设计

### 3.1 角色服务接口

```typescript
// apps/character/src/character/interfaces/character.service.interface.ts
export interface ICharacterService {
  // 角色基础操作
  login(loginDto: LoginDto): Promise<LoginResult>;
  logout(characterId: string): Promise<void>;
  getCharacterInfo(characterId: string): Promise<CharacterInfo>;
  updateCharacterInfo(characterId: string, updateDto: UpdateCharacterDto): Promise<Character>;

  // 资源管理
  addCurrency(characterId: string, currencyType: CurrencyType, amount: number): Promise<void>;
  deductCurrency(characterId: string, currencyType: CurrencyType, amount: number): Promise<boolean>;
  checkCurrency(characterId: string, currencyType: CurrencyType, amount: number): Promise<boolean>;

  // 能量管理
  addEnergy(characterId: string, amount: number): Promise<void>;
  deductEnergy(characterId: string, amount: number): Promise<boolean>;
  buyEnergy(characterId: string): Promise<BuyEnergyResult>;

  // 等级经验
  addExp(characterId: string, exp: number): Promise<LevelUpResult>;
  levelUp(characterId: string): Promise<void>;

  // 角色状态
  setOnline(characterId: string, frontendId: string, sessionId: string): Promise<void>;
  setOffline(characterId: string): Promise<void>;
  isOnline(characterId: string): Promise<boolean>;
}
```

### 3.2 球员服务接口

```typescript
// apps/hero/src/hero/interfaces/hero.service.interface.ts
export interface IHeroService {
  // 球员基础操作
  addHero(characterId: string, resId: number, level?: number, star?: number): Promise<Hero>;
  removeHero(heroId: string): Promise<void>;
  getHero(heroId: string): Promise<Hero>;
  getHeroesByCharacter(characterId: string): Promise<Hero[]>;

  // 球员培养
  trainHero(heroId: string, trainDto: TrainHeroDto): Promise<TrainResult>;
  starUpHero(heroId: string): Promise<StarUpResult>;
  upgradeHeroSkill(heroId: string, skillId: number): Promise<SkillUpgradeResult>;

  // 属性计算
  calculateAttributes(heroId: string): Promise<HeroAttributes>;
  calculateRating(heroId: string): Promise<number>;

  // 球员状态
  lockHero(heroId: string): Promise<void>;
  unlockHero(heroId: string): Promise<void>;
  isHeroLocked(heroId: string): Promise<boolean>;
}
```

### 3.3 阵容服务接口

```typescript
// apps/character/src/formation/interfaces/formation.service.interface.ts
export interface IFormationService {
  // 阵容基础操作
  createFormation(characterId: string, createDto: CreateFormationDto): Promise<Formation>;
  updateFormation(formationId: string, updateDto: UpdateFormationDto): Promise<Formation>;
  deleteFormation(formationId: string): Promise<void>;
  getFormation(formationId: string): Promise<Formation>;
  getFormationsByCharacter(characterId: string): Promise<Formation[]>;

  // 球员位置管理
  setHeroPosition(formationId: string, position: string, heroId: string): Promise<void>;
  removeHeroFromPosition(formationId: string, position: string): Promise<void>;
  swapHeroPositions(formationId: string, pos1: string, pos2: string): Promise<void>;

  // 战术管理
  setTactics(formationId: string, tacticsDto: SetTacticsDto): Promise<void>;
  setCoaches(formationId: string, coachesDto: SetCoachesDto): Promise<void>;

  // 阵容计算
  calculateFormationRating(formationId: string): Promise<FormationRating>;
  validateFormation(formationId: string): Promise<ValidationResult>;

  // 阵容状态
  setDefaultFormation(characterId: string, formationId: string): Promise<void>;
  setLeagueFormation(characterId: string, formationId: string): Promise<void>;
}
```

## 4. 微服务通信协议

### 4.1 HTTP API规范

#### 请求格式
```typescript
// 统一请求格式
interface ApiRequest<T = any> {
  data: T;
  timestamp: number;
  requestId: string;
  serverId?: string;
}

// 统一响应格式
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
  requestId: string;
}
```

#### API路由规范
```
# 角色相关API
GET    /api/character/characters/:characterId
POST   /api/character/users/:userId/characters
PUT    /api/character/characters/:characterId
DELETE /api/character/characters/:characterId
POST   /api/character/characters/:characterId/login

# 球员相关API
GET    /api/hero/characters/:characterId/heroes
POST   /api/hero/characters/:characterId/heroes
PUT    /api/hero/heroes/:heroId
DELETE /api/hero/heroes/:heroId
POST   /api/hero/heroes/:heroId/train
POST   /api/hero/heroes/:heroId/star-up

# 阵容相关API
GET    /api/character/characters/:characterId/formations
POST   /api/character/characters/:characterId/formations
PUT    /api/character/formations/:formationId
DELETE /api/character/formations/:formationId
POST   /api/character/formations/:formationId/positions
```

### 4.2 事件系统设计

#### 领域事件定义
```typescript
// libs/game-types/src/events/player.events.ts
export class PlayerCreatedEvent {
  constructor(
    public readonly playerId: string,
    public readonly openId: string,
    public readonly timestamp: Date = new Date(),
  ) {}
}

export class PlayerLevelUpEvent {
  constructor(
    public readonly playerId: string,
    public readonly oldLevel: number,
    public readonly newLevel: number,
    public readonly timestamp: Date = new Date(),
  ) {}
}

export class HeroAcquiredEvent {
  constructor(
    public readonly playerId: string,
    public readonly heroId: string,
    public readonly resId: number,
    public readonly timestamp: Date = new Date(),
  ) {}
}
```

#### 事件发布订阅
```typescript
// libs/common/src/events/event.service.ts
@Injectable()
export class EventService {
  constructor(
    private redisService: RedisService,
    private eventEmitter: EventEmitter2,
  ) {}

  async publishEvent(event: any): Promise<void> {
    // 本地事件发布
    this.eventEmitter.emit(event.constructor.name, event);
    
    // 跨服务事件发布 (通过Redis)
    await this.redisService.publish('game.events', JSON.stringify(event));
  }

  subscribeToEvent<T>(eventType: string, handler: (event: T) => void): void {
    this.eventEmitter.on(eventType, handler);
  }

  async subscribeToRemoteEvents(): Promise<void> {
    await this.redisService.subscribe('game.events', (message) => {
      const event = JSON.parse(message);
      this.eventEmitter.emit(event.type, event);
    });
  }
}
```

## 5. 缓存策略

### 5.1 Redis缓存设计

#### 缓存键命名规范
```typescript
// libs/redis/src/cache-keys.ts
export const CacheKeys = {
  // 角色缓存
  CHARACTER_INFO: (characterId: string) => `character:info:${characterId}`,
  CHARACTER_ONLINE: (characterId: string) => `character:online:${characterId}`,
  USER_CHARACTERS: (userId: string) => `user:characters:${userId}`,

  // 球员缓存
  HERO_INFO: (heroId: string) => `hero:info:${heroId}`,
  CHARACTER_HEROES: (characterId: string) => `character:heroes:${characterId}`,

  // 阵容缓存
  FORMATION_INFO: (formationId: string) => `formation:info:${formationId}`,
  CHARACTER_FORMATIONS: (characterId: string) => `character:formations:${characterId}`,

  // 配置缓存
  FOOTBALLER_CONFIG: (resId: number) => `config:footballer:${resId}`,
  SYSTEM_CONFIG: (key: string) => `config:system:${key}`,

  // 排行榜缓存
  RANKING_LEVEL: 'ranking:level',
  RANKING_POWER: 'ranking:power',
} as const;
```

#### 缓存服务实现
```typescript
// libs/redis/src/services/game-cache.service.ts
@Injectable()
export class GameCacheService {
  constructor(private redisService: RedisService) {}

  // 角色信息缓存
  async cacheCharacterInfo(characterId: string, characterInfo: any, ttl: number = 3600): Promise<void> {
    const key = CacheKeys.CHARACTER_INFO(characterId);
    await this.redisService.setex(key, ttl, JSON.stringify(characterInfo));
  }

  async getCharacterInfo(characterId: string): Promise<any | null> {
    const key = CacheKeys.CHARACTER_INFO(characterId);
    const cached = await this.redisService.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  // 球员信息缓存
  async cacheHeroInfo(heroId: string, heroInfo: any, ttl: number = 1800): Promise<void> {
    const key = CacheKeys.HERO_INFO(heroId);
    await this.redisService.setex(key, ttl, JSON.stringify(heroInfo));
  }

  async getHeroInfo(heroId: string): Promise<any | null> {
    const key = CacheKeys.HERO_INFO(heroId);
    const cached = await this.redisService.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  // 批量缓存操作
  async cacheCharacterHeroes(characterId: string, heroes: any[], ttl: number = 1800): Promise<void> {
    const key = CacheKeys.CHARACTER_HEROES(characterId);
    await this.redisService.setex(key, ttl, JSON.stringify(heroes));
  }

  async getCharacterHeroes(characterId: string): Promise<any[] | null> {
    const key = CacheKeys.CHARACTER_HEROES(characterId);
    const cached = await this.redisService.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  // 缓存失效
  async invalidateCharacterCache(characterId: string): Promise<void> {
    const keys = [
      CacheKeys.CHARACTER_INFO(characterId),
      CacheKeys.CHARACTER_HEROES(characterId),
      CacheKeys.CHARACTER_FORMATIONS(characterId),
    ];
    await this.redisService.del(...keys);
  }
}
```

## 6. 错误处理和日志

### 6.1 错误码定义

```typescript
// libs/game-constants/src/codes/error-codes.ts
export enum ErrorCode {
  // 通用错误 (1000-1999)
  SUCCESS = 1000,
  UNKNOWN_ERROR = 1001,
  INVALID_PARAMETER = 1002,
  PERMISSION_DENIED = 1003,
  
  // 角色相关错误 (2000-2999)
  CHARACTER_NOT_FOUND = 2001,
  CHARACTER_ALREADY_EXISTS = 2002,
  CHARACTER_OFFLINE = 2003,
  INSUFFICIENT_CURRENCY = 2004,
  INSUFFICIENT_ENERGY = 2005,
  
  // 球员相关错误 (3000-3999)
  HERO_NOT_FOUND = 3001,
  HERO_ALREADY_EXISTS = 3002,
  HERO_LOCKED = 3003,
  HERO_MAX_LEVEL = 3004,
  HERO_MAX_STAR = 3005,
  
  // 阵容相关错误 (4000-4999)
  FORMATION_NOT_FOUND = 4001,
  FORMATION_INVALID = 4002,
  POSITION_OCCUPIED = 4003,
  HERO_NOT_IN_FORMATION = 4004,
}

export const ErrorMessages = {
  [ErrorCode.SUCCESS]: '成功',
  [ErrorCode.UNKNOWN_ERROR]: '未知错误',
  [ErrorCode.INVALID_PARAMETER]: '参数错误',
  [ErrorCode.PERMISSION_DENIED]: '权限不足',
  
  [ErrorCode.CHARACTER_NOT_FOUND]: '角色不存在',
  [ErrorCode.CHARACTER_ALREADY_EXISTS]: '角色已存在',
  [ErrorCode.CHARACTER_OFFLINE]: '角色离线',
  [ErrorCode.INSUFFICIENT_CURRENCY]: '货币不足',
  [ErrorCode.INSUFFICIENT_ENERGY]: '体力不足',
  
  [ErrorCode.HERO_NOT_FOUND]: '球员不存在',
  [ErrorCode.HERO_ALREADY_EXISTS]: '球员已存在',
  [ErrorCode.HERO_LOCKED]: '球员已锁定',
  [ErrorCode.HERO_MAX_LEVEL]: '球员已达最大等级',
  [ErrorCode.HERO_MAX_STAR]: '球员已达最大星级',
  
  [ErrorCode.FORMATION_NOT_FOUND]: '阵容不存在',
  [ErrorCode.FORMATION_INVALID]: '阵容无效',
  [ErrorCode.POSITION_OCCUPIED]: '位置已被占用',
  [ErrorCode.HERO_NOT_IN_FORMATION]: '球员不在阵容中',
} as const;
```

### 6.2 异常处理

```typescript
// libs/common/src/exceptions/game.exception.ts
export class GameException extends Error {
  constructor(
    public readonly code: ErrorCode,
    public readonly message: string = ErrorMessages[code],
    public readonly details?: any,
  ) {
    super(message);
    this.name = 'GameException';
  }
}

// 全局异常过滤器
@Catch(GameException)
export class GameExceptionFilter implements ExceptionFilter {
  catch(exception: GameException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    
    response.status(200).json({
      code: exception.code,
      message: exception.message,
      data: null,
      timestamp: new Date().toISOString(),
      details: exception.details,
    });
  }
}
```

---

**注意**: 本技术规范基于NestJS最佳实践制定，在实施过程中应根据具体需求进行调整和完善。
