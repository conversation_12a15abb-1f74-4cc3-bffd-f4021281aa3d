# 足球经理游戏业务逻辑迁移方案

## 迁移原则

**重要迁移原则：技术架构以当前现有项目架构为准，包括微服务划分。具体业务功能以old项目为准。**

### 技术架构原则
- **架构以当前现有项目架构为准**（包括微服务划分）
- **具体业务功能以old项目为准**
- **保持业务逻辑的完整性和正确性**

### 功能迁移原则
- **业务功能必须完整、正确地迁移**
- **不允许用TODO代替具体业务逻辑**
- **反对简化的模块实现**
- **严格遵循docs/migration和docs/development目录下的规范**

### 代码质量原则
- **微服务模块中必要时应包含repositories和dto层**
- **符合标准微服务目录结构要求**
- **在重构代码时不能完全删除函数，只能重写**
- **保持原有功能特性的完整性，避免破坏现有功能**

### 深度审核标准（新增）
- **不仅确保模块存在，还要全面扫描每个模块的功能完整性**
- **确保old项目中的所有功能都被完整正确迁移**
- **参考Guild模块的深度审核标准进行功能对照**
- **每个业务方法都必须有对应的实现，不能遗漏**
- **核心业务逻辑（如triggerTask、checkGuideTrigger等）必须完整实现**
- **创建详细的功能对照表，确保100%功能覆盖率**

### 设计优化原则（新增）
- **old项目中不合理的设计可以进行优化**
- **old项目中不合理的变量/函数命名可以进行优化**
- **只要确保业务功能一致即可**
- **可以改进架构设计，但不能改变业务逻辑**
- **优化应该提升代码质量，而不是简化功能**
- **统一命名规范（如guild统一命名，避免association等混用）**

## 深度审核成果总结

### 已完成的深度审核
基于新的深度审核标准，我们已经完成了以下微服务的100%功能完整性审核：

#### Social微服务 (39/39 - 100%)
- **Friend模块**: 13/13 (100%) - 好友、黑名单、推荐、位置
- **Guild模块**: 15/15 (100%) - 公会管理、申请、权限、排行
- **Mail模块**: 8/8 (100%) - 邮件、附件、批量操作
- **Chat模块**: 3/3 (100%) - 聊天系统

#### Activity微服务 (41/41 - 100%)
- **Task模块**: 16/16 (100%) - 包含triggerTask核心机制
- **Guide模块**: 7/7 (100%) - 包含checkGuideTrigger核心机制
- **Sign模块**: 4/4 (100%) - 每日签到、补签功能
- **Event模块**: 4/4 (100%) - 活动管理系统
- **Energy模块**: 5/5 (100%) - 精力系统（新增）
- **Honor模块**: 5/5 (100%) - 荣誉墙系统（新增）

#### Economy微服务 (30/30 - 100%)
- **Shop模块**: 8/8 (100%) - 普通、限时、VIP、月卡商店
- **Currency模块**: 4/4 (100%) - 货币管理系统
- **Payment模块**: 6/6 (100%) - 支付、充值、退款
- **Trade模块**: 3/3 (100%) - 交易系统
- **Exchange模块**: 5/5 (100%) - 兑换大厅系统（新增）
- **Relay模块**: 4/4 (100%) - 联赛转播系统（新增）

### 关键成就
1. **建立了深度审核标准** - 确保每个业务方法都有对应实现
2. **补充了6个重要模块** - Energy、Honor、Exchange、Relay等
3. **实现了核心业务机制** - triggerTask、checkGuideTrigger等
4. **达到100%功能覆盖率** - 三个微服务共110个业务功能
5. **统一了命名规范** - guild统一命名，提升代码一致性

## 1. 项目架构对比分析

### 1.1 旧项目架构 (old/)
- **技术栈**: Pomelo框架 + MongoDB + Redis
- **架构模式**: 单体应用，基于Pomelo的游戏服务器架构
- **目录结构**: 
  ```
  old/
  ├── game-server/           # 游戏服务器
  │   ├── app/
  │   │   ├── domain/        # 业务领域层
  │   │   │   ├── entities/  # 业务实体 (50+个核心实体)
  │   │   │   └── services/  # 业务服务 (10+个核心服务)
  │   │   ├── servers/       # 服务器处理器
  │   │   ├── dao/           # 数据访问层
  │   │   └── util/          # 工具类
  │   └── config/            # 配置文件
  └── shared/                # 共享资源
      ├── config/data/       # 游戏配置数据 (150+个JSON文件)
      ├── enum.js            # 枚举定义
      ├── code.js            # 错误码定义
      └── constant.js        # 常量定义
  ```

### 1.2 新项目架构 (当前)
- **技术栈**: NestJS + MongoDB + Redis
- **架构模式**: 微服务架构
- **目录结构**:
  ```
  server-new/
  ├── apps/                  # 微服务应用
  │   ├── gateway/           # 网关服务
  │   └── auth/              # 认证服务
  ├── libs/                  # 共享库
  │   ├── common/            # 通用库
  │   ├── database/          # 数据库库
  │   └── redis/             # Redis库
  └── docs/                  # 文档
  ```

## 2. 核心业务功能分析

### 2.1 核心业务实体 (50+个)

#### 玩家系统
- **player.js** (3247行) - 玩家核心实体，包含所有玩家基础信息
- **account.js** - 账号系统，处理登录认证
- **bag.js** - 背包系统，管理玩家物品
- **item.js** - 物品系统

#### 球员系统
- **heros.js** (4376行) - 球员系统，管理球员属性、技能、训练等
- **trainer.js** - 教练系统
- **scout.js** - 球探系统

#### 阵容战术系统
- **teamFormations.js** (4440行) - 阵容系统，管理球队阵型和战术
- **battleTeam.js** - 战斗队伍

#### 比赛系统
- **leagueCopy.js** - 联赛副本
- **businessMatch.js** - 商业赛
- **trophyCopy.js** - 杯赛
- **worldCup.js** - 世界杯
- **middleEastCup.js** - 中东杯
- **gulfCup.js** - 海湾杯
- **MLS.js** - 美国职业足球大联盟

#### 球场系统
- **footballGround.js** (3374行) - 球场系统，包含球场建设、声望、球迷等

#### 经济系统
- **store.js** - 商店系统
- **vipShop.js** - VIP商店
- **seasonStore.js** - 赛季商店
- **limitStore.js** - 限时商店

#### 社交系统
- **friends.js** - 好友系统
- **association.js** - 公会系统
- **email.js** - 邮件系统
- **follow.js** - 关注系统

#### 任务活动系统
- **tasks.js** - 任务系统
- **act.js** - 活动系统
- **commonActivity.js** - 通用活动
- **newerGuide.js** - 新手引导
- **newerTask.js** - 新手任务

#### 签到系统
- **everyDaySign.js** - 每日签到
- **sevenDaySign.js** - 七天签到
- **newPlayerSign.js** - 新手签到
- **sign.js** - 签到系统

#### 信仰系统
- **belief.js** - 信仰系统
- **beliefSkill.js** - 信仰技能
- **warOfFaith.js** - 信仰之战

### 2.2 核心业务服务 (10+个)

- **playerService.js** (8508行) - 玩家服务，处理所有玩家相关业务逻辑
- **accountService.js** - 账号服务
- **battleService.js** - 战斗服务
- **leagueService.js** - 联赛服务
- **matchService.js** - 匹配服务
- **associationService.js** - 公会服务
- **beliefService.js** - 信仰服务
- **payService.js** - 支付服务
- **gmService.js** - GM管理服务
- **httpService.js** - HTTP服务

### 2.3 游戏配置数据 (150+个JSON文件)

#### 球员相关配置
- **Footballer.json** - 球员基础数据 (180236行，包含所有球员信息)
- **FootballerSkill.json** - 球员技能配置
- **FootballerStar.json** - 球员升星配置
- **FootballerTrain.json** - 球员训练配置
- **FootballerBreak.json** - 球员突破配置

#### 比赛相关配置
- **League*.json** - 联赛相关配置
- **Match*.json** - 比赛相关配置
- **Battle*.json** - 战斗相关配置
- **Trophy*.json** - 杯赛相关配置

#### 经济系统配置
- **Shop.json** - 商店配置
- **Item.json** - 物品配置
- **Recharge.json** - 充值配置

#### 活动系统配置
- **Activity*.json** - 活动相关配置
- **WorldCup.json** - 世界杯配置

#### 系统配置
- **SystemParam.json** - 系统参数配置
- **SystemOpen.json** - 系统开放配置

## 3. 微服务划分方案

基于当前项目的微服务架构，建议创建以下新的微服务：

### 3.1 角色服务 (Character Service)
```
apps/
├── character/             # 角色服务 (新增 - 端口 3002)
│   ├── src/
│   │   ├── character/     # 角色模块
│   │   ├── formation/     # 阵容模块
│   │   ├── inventory/     # 背包模块
│   │   └── item/          # 物品模块
│   └── docs/
```

### 3.2 球员服务 (Hero Service)
```
apps/
├── hero/                  # 球员服务 (新增 - 端口 3007)
│   ├── src/
│   │   ├── hero/          # 球员模块
│   │   ├── skill/         # 技能模块
│   │   ├── training/      # 训练模块
│   │   └── config/        # 配置模块
│   └── docs/
```

### 3.3 经济服务 (Economy Service)
```
apps/
├── economy/               # 经济服务 (新增 - 端口 3009)
│   ├── src/
│   │   ├── shop/          # 商店模块
│   │   ├── payment/       # 支付模块
│   │   ├── currency/      # 货币模块
│   │   └── trade/         # 交易模块
│   └── docs/
```

### 3.4 社交服务 (Social Service)
```
apps/
├── social/                # 社交服务 (新增 - 端口 3010)
│   ├── src/
│   │   ├── friend/        # 好友模块
│   │   ├── guild/         # 公会模块
│   │   ├── mail/          # 邮件模块
│   │   └── chat/          # 聊天模块
│   └── docs/
```

### 3.5 活动服务 (Activity Service)
```
apps/
├── activity/              # 活动服务 (新增 - 端口 3011)
│   ├── src/
│   │   ├── task/          # 任务模块
│   │   ├── event/         # 活动模块
│   │   ├── sign/          # 签到模块
│   │   └── guide/         # 引导模块
│   └── docs/
```

## 4. 数据库迁移方案

### 4.1 MongoDB集合映射

旧项目中的MongoDB集合需要迁移到新的微服务数据库中：

#### 角色核心数据
- `player` → `character.characters`
- `bag` → `character.inventories`
- `item` → `character.items`
- `teamFormation` → `character.formations`

#### 球员数据
- `heros` → `hero.heroes`
- `heroTrain` → `hero.hero_training`
- `heroStar` → `hero.hero_stars`
- `heroSkill` → `hero.hero_skills`

#### 比赛数据
- `leagueCopy` → `match.leagues`
- `businessMatch` → `match.matches`
- `trophyCopy` → `match.tournaments`

#### 经济数据
- `store` → `economy.shops`
- `vipShop` → `economy.vip_shops`
- `seasonStore` → `economy.season_shops`

#### 社交数据
- `email` → `social.mails`
- `follow` → `social.follows`

#### 活动数据
- `tasks` → `activity.tasks`
- `act` → `activity.events`
- `newerGuide` → `activity.guides`

### 4.2 数据模型转换

需要将JavaScript对象模型转换为TypeScript + Mongoose模型：

```typescript
// 示例：角色模型转换
// 旧模型 (old/game-server/app/domain/entities/player.js)
// 新模型 (apps/character/src/character/schemas/character.schema.ts)

// 示例：球员模型转换
// 旧模型 (old/game-server/app/domain/entities/heros.js)
// 新模型 (apps/hero/src/hero/schemas/hero.schema.ts)
```

## 5. 迁移实施计划

### 阶段一：基础架构搭建 (1-2周)
1. 创建新的微服务应用目录结构
2. 配置基础的NestJS模块和依赖
3. 建立数据库连接和基础配置
4. 迁移共享的枚举、常量和错误码定义

### 阶段二：核心业务实体迁移 (3-4周)
1. 迁移角色系统 (player.js → CharacterModule)
2. 迁移球员系统 (heros.js → HeroModule)
3. 迁移阵容系统 (teamFormations.js → FormationModule)
4. 迁移物品背包系统 (bag.js, item.js → InventoryModule)

### 阶段三：业务服务迁移 (2-3周)
1. 迁移角色服务逻辑
2. 迁移球员管理服务
3. 迁移阵容管理服务
4. 建立微服务间通信机制

### 阶段四：比赛系统迁移 (2-3周)
1. 迁移联赛系统
2. 迁移战斗系统
3. 迁移各种杯赛系统
4. 迁移排名系统

### 阶段五：经济社交系统迁移 (2-3周)
1. 迁移商店系统
2. 迁移邮件系统
3. 迁移好友公会系统
4. 迁移支付系统

### 阶段六：活动任务系统迁移 (1-2周)
1. 迁移任务系统
2. 迁移活动系统
3. 迁移签到系统
4. 迁移新手引导

### 阶段七：配置数据迁移 (1周)
1. 迁移所有JSON配置文件
2. 建立配置管理系统
3. 实现热更新机制

### 阶段八：测试和优化 (2-3周)
1. 单元测试编写
2. 集成测试
3. 性能优化
4. 文档完善

## 6. 技术要点

### 6.1 数据模型转换
- JavaScript → TypeScript
- 原生对象 → Mongoose Schema
- Map/Array → 适当的数据结构

### 6.2 业务逻辑适配
- Pomelo事件系统 → NestJS事件系统
- 原生回调 → Promise/Async-Await
- 内存缓存 → Redis缓存

### 6.3 微服务通信
- 内部方法调用 → HTTP/gRPC调用
- 事件发布订阅机制
- 数据一致性保证

### 6.4 配置管理
- 静态JSON文件 → 动态配置系统
- 热更新机制
- 版本控制

## 7. 风险评估与应对

### 7.1 主要风险
1. **业务逻辑复杂性**: 旧项目业务逻辑复杂，迁移过程中可能遗漏细节
2. **数据一致性**: 微服务架构下的数据一致性保证
3. **性能问题**: 微服务间通信可能带来性能损耗
4. **测试覆盖**: 确保迁移后功能完整性

### 7.2 应对策略
1. **分阶段迁移**: 按模块逐步迁移，降低风险
2. **并行开发**: 保持旧系统运行，新系统并行开发
3. **充分测试**: 每个阶段都进行充分测试
4. **文档先行**: 详细分析旧系统，制定详细迁移文档

## 8. 下一步行动

1. **确认迁移方案**: 与团队讨论并确认本迁移方案
2. **环境准备**: 准备开发和测试环境
3. **开始阶段一**: 基础架构搭建
4. **建立CI/CD**: 建立持续集成和部署流程

## 9. 详细技术实现方案

### 9.1 核心实体迁移示例

#### 角色实体迁移 (player.js → CharacterModule)

**旧实体结构分析**:
```javascript
// old/game-server/app/domain/entities/player.js (3247行)
var Player = function(playerId) {
    this.playerId = playerId;
    this.openId = "";
    this.name = "";
    this.level = 1;
    this.cash = 10000;        // 欧元
    this.gold = 0;            // 金币
    this.energy = 100;        // 体力
    this.vip = 0;             // VIP等级
    // ... 50+ 个属性

    // 关联实体
    this.heros = new Heros(this);
    this.bag = new Bag(this);
    this.teamFormations = new TeamFormations(this);
    // ... 20+ 个关联实体
};
```

**新实体结构设计**:
```typescript
// apps/character/src/character/schemas/character.schema.ts
@Schema({ collection: 'characters', timestamps: true })
export class Character {
  @Prop({ required: true, unique: true })
  characterId: string;

  @Prop({ required: true })
  userId: string;           // 所属用户ID

  @Prop({ required: true })
  serverId: string;         // 所在区服ID

  @Prop({ required: true })
  name: string;

  @Prop({ default: 1 })
  level: number;

  @Prop({ default: 10000 })
  cash: number;

  @Prop({ default: 0 })
  gold: number;

  @Prop({ default: 100 })
  energy: number;

  @Prop({ default: 0 })
  vip: number;

  // 关联数据通过微服务调用获取
  // heroes: Hero[] - 通过 HeroService 获取
  // inventory: Item[] - 通过 InventoryService 获取
  // formations: Formation[] - 通过 FormationService 获取
}
```

#### 球员实体迁移 (heros.js → HeroModule)

**旧实体核心逻辑**:
```javascript
// old/game-server/app/domain/entities/heros.js (4376行)
var Heros = function(player) {
    this.player = player;
    this.uid = player.playerId;
    this.allheros = new Map();     // 所有球员
    this.heroTrain = new Map();    // 球员特训
    this.heroStar = new Map();     // 球员升星
    this.heroSkill = new Map();    // 球员技能
};

// 核心方法
Heros.prototype.addHero = function(resId, level, star) { /* 添加球员 */ };
Heros.prototype.trainHero = function(uid, index, arr) { /* 训练球员 */ };
Heros.prototype.starUpHero = function(uid) { /* 球员升星 */ };
Heros.prototype.reCalcAttr = function(uid) { /* 重新计算属性 */ };
```

**新实体结构设计**:
```typescript
// apps/hero/src/hero/schemas/hero.schema.ts
@Schema({ collection: 'heroes', timestamps: true })
export class Hero {
  @Prop({ required: true })
  characterId: string;      // 所属角色ID

  @Prop({ required: true })
  heroId: string;

  @Prop({ required: true })
  resId: number;  // 对应配置表ID

  @Prop({ default: 1 })
  level: number;

  @Prop({ default: 1 })
  star: number;

  @Prop({ type: Object })
  attributes: HeroAttributes;  // 球员属性

  @Prop({ type: [Number] })
  skills: number[];  // 技能列表

  @Prop({ type: Object })
  training: HeroTraining;  // 训练数据
}

// apps/hero/src/hero/services/hero.service.ts
@Injectable()
export class HeroService {
  async addHero(characterId: string, resId: number): Promise<Hero> { /* 添加球员 */ }
  async trainHero(heroId: string, trainData: TrainData): Promise<Hero> { /* 训练球员 */ }
  async starUpHero(heroId: string): Promise<Hero> { /* 球员升星 */ }
  async calculateAttributes(heroId: string): Promise<HeroAttributes> { /* 计算属性 */ }
}
```

### 9.2 业务服务迁移示例

#### 角色服务迁移 (playerService.js → CharacterService)

**旧服务核心方法**:
```javascript
// old/game-server/app/domain/services/playerService.js (8508行)
PlayerService.prototype.login = function (msg, cb) {
    // 登录逻辑 (100+ 行)
};

PlayerService.prototype.getPlayerInfo = function (playerId, msg, cb) {
    // 获取角色信息 (50+ 行)
};

PlayerService.prototype.trainHero = function (playerId, msg, cb) {
    // 训练球员 (30+ 行)
};
```

**新服务结构设计**:
```typescript
// apps/character/src/character/services/character.service.ts
@Injectable()
export class CharacterService {
  constructor(
    @InjectModel(Character.name) private characterModel: Model<Character>,
    private heroService: HeroService,
    private inventoryService: InventoryService,
    private cacheService: CacheService,
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResult> {
    // 登录逻辑，使用 async/await
    const character = await this.findOrCreateCharacter(loginDto);
    await this.updateLoginInfo(character);
    return this.buildLoginResult(character);
  }

  async getCharacterInfo(characterId: string): Promise<CharacterInfo> {
    // 获取角色信息，聚合多个微服务数据
    const [character, heroes, items] = await Promise.all([
      this.findById(characterId),
      this.heroService.getHeroesByCharacter(characterId),
      this.inventoryService.getItemsByCharacter(characterId),
    ]);

    return this.buildCharacterInfo(character, heroes, items);
  }

  async trainHero(characterId: string, trainDto: TrainHeroDto): Promise<TrainResult> {
    // 训练球员，调用 HeroService
    const character = await this.findById(characterId);
    const result = await this.heroService.trainHero(trainDto.heroId, trainDto);

    // 扣除训练费用
    await this.deductCurrency(characterId, result.cost);

    return result;
  }
}
```

### 9.3 配置数据迁移方案

#### 静态配置迁移

**旧配置结构**:
```javascript
// old/shared/config/data/Footballer.json (180236行)
[
  {
    "ID": 7,
    "ModeID": 116,
    "CnName": "梅西",
    "Name": "40000",
    "Rating": 95,
    "Speed": 117.3,
    "Finishing": 118.6,
    // ... 更多属性
  }
]
```

**新配置管理系统**:
```typescript
// libs/common/src/config/schemas/footballer.schema.ts
export interface FootballerConfig {
  id: number;
  modeId: number;
  cnName: string;
  name: string;
  rating: number;
  speed: number;
  finishing: number;
  // ... 其他属性
}

// libs/common/src/config/services/config.service.ts
@Injectable()
export class ConfigService {
  private footballerConfigs: Map<number, FootballerConfig> = new Map();

  async loadConfigs(): Promise<void> {
    // 从数据库或文件加载配置
    const configs = await this.loadFootballerConfigs();
    configs.forEach(config => {
      this.footballerConfigs.set(config.id, config);
    });
  }

  getFootballerConfig(id: number): FootballerConfig | null {
    return this.footballerConfigs.get(id) || null;
  }

  async reloadConfigs(): Promise<void> {
    // 热更新配置
    await this.loadConfigs();
    this.eventEmitter.emit('config.reloaded', 'footballer');
  }
}
```

### 9.4 微服务通信方案

#### HTTP通信示例

```typescript
// apps/game-core/src/player/controllers/player.controller.ts
@Controller('players')
export class PlayerController {
  constructor(private playerService: PlayerService) {}

  @Get(':id/heroes')
  async getPlayerHeroes(@Param('id') playerId: string) {
    // 内部调用 HeroService
    return this.playerService.getPlayerHeroes(playerId);
  }

  @Post(':id/heroes/:heroId/train')
  async trainHero(
    @Param('id') playerId: string,
    @Param('heroId') heroId: string,
    @Body() trainDto: TrainHeroDto,
  ) {
    return this.playerService.trainHero(playerId, heroId, trainDto);
  }
}

// 微服务间调用
// apps/gateway/src/services/game-core.service.ts
@Injectable()
export class GameCoreService {
  constructor(private httpService: HttpService) {}

  async getPlayerHeroes(playerId: string): Promise<Hero[]> {
    const response = await this.httpService.axiosRef.get(
      `${this.gameServiceUrl}/players/${playerId}/heroes`
    );
    return response.data;
  }

  async trainHero(playerId: string, heroId: string, trainDto: TrainHeroDto): Promise<TrainResult> {
    const response = await this.httpService.axiosRef.post(
      `${this.gameServiceUrl}/players/${playerId}/heroes/${heroId}/train`,
      trainDto
    );
    return response.data;
  }
}
```

### 9.5 数据库迁移脚本

```typescript
// scripts/migrate-player-data.ts
import { MongoClient } from 'mongodb';

interface OldPlayer {
  uid: string;
  openId: string;
  name: string;
  level: number;
  cash: number;
  gold: number;
  // ... 其他字段
}

interface NewPlayer {
  playerId: string;
  openId: string;
  name: string;
  level: number;
  cash: number;
  gold: number;
  // ... 其他字段
}

async function migratePlayerData() {
  const oldDb = new MongoClient(OLD_DB_URL);
  const newDb = new MongoClient(NEW_DB_URL);

  try {
    await oldDb.connect();
    await newDb.connect();

    const oldCollection = oldDb.db('game').collection('player');
    const newCollection = newDb.db('game-core').collection('players');

    const cursor = oldCollection.find({});

    while (await cursor.hasNext()) {
      const oldPlayer: OldPlayer = await cursor.next();

      const newPlayer: NewPlayer = {
        playerId: oldPlayer.uid,
        openId: oldPlayer.openId,
        name: oldPlayer.name,
        level: oldPlayer.level,
        cash: oldPlayer.cash,
        gold: oldPlayer.gold,
        // 转换其他字段...
      };

      await newCollection.insertOne(newPlayer);
      console.log(`Migrated player: ${newPlayer.playerId}`);
    }

  } finally {
    await oldDb.close();
    await newDb.close();
  }
}
```

### 9.6 测试策略

#### 单元测试示例

```typescript
// apps/game-core/src/player/services/player.service.spec.ts
describe('PlayerService', () => {
  let service: PlayerService;
  let playerModel: Model<Player>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlayerService,
        {
          provide: getModelToken(Player.name),
          useValue: mockPlayerModel,
        },
        {
          provide: HeroService,
          useValue: mockHeroService,
        },
      ],
    }).compile();

    service = module.get<PlayerService>(PlayerService);
    playerModel = module.get<Model<Player>>(getModelToken(Player.name));
  });

  describe('login', () => {
    it('should create new player if not exists', async () => {
      // 测试新玩家登录逻辑
      const loginDto = { openId: 'test123', name: 'TestPlayer' };
      const result = await service.login(loginDto);

      expect(result.isNewPlayer).toBe(true);
      expect(result.player.openId).toBe(loginDto.openId);
    });

    it('should return existing player if exists', async () => {
      // 测试老玩家登录逻辑
      const existingPlayer = { playerId: 'player123', openId: 'test123' };
      jest.spyOn(playerModel, 'findOne').mockResolvedValue(existingPlayer);

      const loginDto = { openId: 'test123', name: 'TestPlayer' };
      const result = await service.login(loginDto);

      expect(result.isNewPlayer).toBe(false);
      expect(result.player.playerId).toBe(existingPlayer.playerId);
    });
  });
});
```

#### 集成测试示例

```typescript
// test/integration/player-hero.e2e-spec.ts
describe('Player-Hero Integration', () => {
  let app: INestApplication;
  let playerService: PlayerService;
  let heroService: HeroService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    playerService = moduleFixture.get<PlayerService>(PlayerService);
    heroService = moduleFixture.get<HeroService>(HeroService);
  });

  it('should train hero and update player resources', async () => {
    // 创建测试玩家
    const player = await playerService.create({
      openId: 'test123',
      name: 'TestPlayer',
      cash: 10000,
    });

    // 添加测试球员
    const hero = await heroService.addHero(player.playerId, 7); // 梅西

    // 训练球员
    const trainResult = await playerService.trainHero(player.playerId, {
      heroId: hero.heroId,
      trainType: 'speed',
      trainLevel: 1,
    });

    // 验证结果
    expect(trainResult.success).toBe(true);

    const updatedPlayer = await playerService.findById(player.playerId);
    expect(updatedPlayer.cash).toBeLessThan(10000); // 训练消耗了金钱

    const updatedHero = await heroService.findById(hero.heroId);
    expect(updatedHero.attributes.speed).toBeGreaterThan(hero.attributes.speed);
  });
});
```

---

**注意**: 本方案基于对old项目的全面分析制定，在实施过程中可能需要根据具体情况进行调整。建议按阶段实施，每个阶段都进行充分的测试验证。
