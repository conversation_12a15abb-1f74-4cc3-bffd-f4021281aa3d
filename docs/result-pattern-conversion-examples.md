# Result模式转换实战示例

## 📋 转换清单

### Repository层转换清单
- [ ] 修改所有方法返回类型为 `Promise<Result<T>>`
- [ ] 添加 `RepositoryResultWrapper` import
- [ ] 使用对应的包装方法包装原有逻辑
- [ ] 移除 try-catch 块（由包装器处理）

### Service层转换清单
- [ ] 修改所有方法返回类型为 `Promise<Result<T>>`
- [ ] 添加 `ServiceResultHandler` import
- [ ] 检查每个Repository调用的结果
- [ ] 替换所有 throw 语句为 `ResultUtils.error()`
- [ ] 替换所有 return 语句为 `ResultUtils.ok()`

### Controller层转换清单
- [ ] 方法返回类型已通过脚本添加
- [ ] 使用 `MicroserviceResponseUtils.fromResult()` 转换结果

## 🔧 Repository层转换示例

### 示例1：简单查询方法
```typescript
// ❌ 原始代码
async findById(id: string): Promise<CharacterDocument | null> {
  try {
    return await this.characterModel.findById(id).exec();
  } catch (error) {
    throw new Error('查询失败');
  }
}

// ✅ 转换后
async findById(id: string): Promise<Result<CharacterDocument | null>> {
  return await RepositoryResultWrapper.wrapNullable(async () => {
    return await this.characterModel.findById(id).exec();
  });
}
```

### 示例2：数组查询方法
```typescript
// ❌ 原始代码
async findByUserId(userId: string): Promise<CharacterDocument[]> {
  return await this.characterModel.find({ userId }).exec();
}

// ✅ 转换后
async findByUserId(userId: string): Promise<Result<CharacterDocument[]>> {
  return await RepositoryResultWrapper.wrapArray(async () => {
    return await this.characterModel.find({ userId }).exec();
  });
}
```

### 示例3：创建方法
```typescript
// ❌ 原始代码
async create(dto: CreateCharacterDto): Promise<CharacterDocument> {
  try {
    const character = new this.characterModel(dto);
    return await character.save();
  } catch (error) {
    throw new Error('创建失败');
  }
}

// ✅ 转换后
async create(dto: CreateCharacterDto): Promise<Result<CharacterDocument>> {
  return await RepositoryResultWrapper.wrap(async () => {
    const character = new this.characterModel(dto);
    return await character.save();
  });
}
```

### 示例4：存在性检查
```typescript
// ❌ 原始代码
async existsByName(name: string): Promise<boolean> {
  const count = await this.characterModel.countDocuments({ name }).exec();
  return count > 0;
}

// ✅ 转换后
async existsByName(name: string): Promise<Result<boolean>> {
  return await RepositoryResultWrapper.wrapBoolean(async () => {
    const count = await this.characterModel.countDocuments({ name }).exec();
    return count > 0;
  });
}
```

## 🏢 Service层转换示例

### 示例1：简单业务方法
```typescript
// ❌ 原始代码
async getCharacterInfo(id: string): Promise<CharacterInfoDto> {
  const character = await this.characterRepository.findById(id);
  if (!character) {
    throw new NotFoundException('角色不存在');
  }
  
  return {
    id: character.id,
    name: character.name,
    level: character.level
  };
}

// ✅ 转换后
async getCharacterInfo(id: string): Promise<Result<CharacterInfoDto>> {
  const characterResult = await this.characterRepository.findById(id);
  const errorResult = ServiceResultHandler.propagateError<CharacterInfoDto>(characterResult);
  if (errorResult) return errorResult;
  
  const character = characterResult.data;
  if (!character) {
    return ResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
  }
  
  return ResultUtils.ok({
    id: character.id,
    name: character.name,
    level: character.level
  });
}
```

### 示例2：复杂业务方法
```typescript
// ❌ 原始代码
async createCharacter(dto: CreateCharacterDto): Promise<CharacterDocument> {
  // 检查角色名是否已存在
  const nameExists = await this.characterRepository.existsByName(dto.name);
  if (nameExists) {
    throw new BadRequestException({
      code: ErrorCode.CHARACTER_NAME_TAKEN,
      message: '角色名已存在'
    });
  }
  
  // 检查用户是否已有角色
  const existingCharacters = await this.characterRepository.findByUserId(dto.userId);
  if (existingCharacters.length >= 3) {
    throw new BadRequestException({
      code: ErrorCode.CHARACTER_LIMIT_EXCEEDED,
      message: '角色数量已达上限'
    });
  }
  
  // 创建角色
  const character = await this.characterRepository.create(dto);
  return character;
}

// ✅ 转换后
async createCharacter(dto: CreateCharacterDto): Promise<Result<CharacterDocument>> {
  // 检查角色名是否已存在
  const nameExistsResult = await this.characterRepository.existsByName(dto.name);
  const nameError = ServiceResultHandler.propagateError<CharacterDocument>(nameExistsResult);
  if (nameError) return nameError;
  
  if (nameExistsResult.data) {
    return ResultUtils.error('角色名已存在', 'CHARACTER_NAME_TAKEN');
  }
  
  // 检查用户是否已有角色
  const existingResult = await this.characterRepository.findByUserId(dto.userId);
  const existingError = ServiceResultHandler.propagateError<CharacterDocument>(existingResult);
  if (existingError) return existingError;
  
  if (existingResult.data.length >= 3) {
    return ResultUtils.error('角色数量已达上限', 'CHARACTER_LIMIT_EXCEEDED');
  }
  
  // 创建角色
  const createResult = await this.characterRepository.create(dto);
  return createResult; // 直接返回Repository的Result
}
```

### 示例3：更新方法
```typescript
// ❌ 原始代码
async updateCharacter(id: string, dto: UpdateCharacterDto): Promise<CharacterDocument> {
  const character = await this.characterRepository.findById(id);
  if (!character) {
    throw new NotFoundException('角色不存在');
  }
  
  // 如果要修改名称，检查新名称是否已存在
  if (dto.name && dto.name !== character.name) {
    const nameExists = await this.characterRepository.existsByName(dto.name);
    if (nameExists) {
      throw new BadRequestException('角色名已存在');
    }
    character.name = dto.name;
  }
  
  if (dto.level) {
    character.level = dto.level;
  }
  
  return await this.characterRepository.update(character);
}

// ✅ 转换后
async updateCharacter(id: string, dto: UpdateCharacterDto): Promise<Result<CharacterDocument>> {
  const characterResult = await this.characterRepository.findById(id);
  const characterError = ServiceResultHandler.propagateError<CharacterDocument>(characterResult);
  if (characterError) return characterError;
  
  const character = characterResult.data;
  if (!character) {
    return ResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
  }
  
  // 如果要修改名称，检查新名称是否已存在
  if (dto.name && dto.name !== character.name) {
    const nameExistsResult = await this.characterRepository.existsByName(dto.name);
    const nameError = ServiceResultHandler.propagateError<CharacterDocument>(nameExistsResult);
    if (nameError) return nameError;
    
    if (nameExistsResult.data) {
      return ResultUtils.error('角色名已存在', 'CHARACTER_NAME_TAKEN');
    }
    character.name = dto.name;
  }
  
  if (dto.level) {
    character.level = dto.level;
  }
  
  const updateResult = await this.characterRepository.update(character);
  return updateResult; // 直接返回Repository的Result
}
```

## 🎯 Controller层转换示例

```typescript
// ✅ Controller层转换（返回类型已通过脚本添加）
@MessagePattern('character.create')
async createCharacter(@Payload() payload: { 
  createDto: CreateCharacterDto; 
  injectedContext?: InjectedContext 
}): Promise<MicroserviceResponse<CharacterDocument>> {
  const result = await this.characterService.createCharacter(payload.createDto);
  return MicroserviceResponseUtils.fromResult(result);
}

@MessagePattern('character.getInfo')
async getCharacterInfo(@Payload() payload: { 
  id: string; 
  injectedContext?: InjectedContext 
}): Promise<MicroserviceResponse<CharacterInfoDto>> {
  const result = await this.characterService.getCharacterInfo(payload.id);
  return MicroserviceResponseUtils.fromResult(result);
}
```

## 🔍 常见转换模式

### 模式1：Repository调用 + 错误检查
```typescript
// 标准模式
const result = await this.repository.someMethod();
const errorResult = ServiceResultHandler.propagateError<ReturnType>(result);
if (errorResult) return errorResult;

const data = result.data;
// 使用 data...
```

### 模式2：条件检查 + 业务错误
```typescript
// 业务验证模式
if (someCondition) {
  return ResultUtils.error('业务错误消息', 'ERROR_CODE');
}
```

### 模式3：成功返回
```typescript
// 返回数据
return ResultUtils.ok(data);

// 返回空成功
return ResultUtils.empty();

// 直接返回Repository结果
return repositoryResult;
```

## ⚠️ 注意事项

1. **错误传递**：使用 `ServiceResultHandler.propagateError()` 而不是丢弃错误信息
2. **类型安全**：确保泛型类型正确，避免类型转换错误
3. **错误代码**：使用有意义的错误代码，便于前端处理
4. **测试**：每转换一个方法就测试编译，及时发现问题
5. **渐进转换**：先转换Repository层，再转换Service层，最后转换Controller层
