# 微服务安全架构设计指南（待实施）

## 📋 概述

本文档是基于足球经理游戏项目的微服务安全架构设计指南，涵盖分区分服架构、会话管理、通信安全等核心设计原则和最佳实践。

## 🎯 核心设计原则

### 1. 职责分离原则

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端层       │    │   网关层         │    │   微服务层       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 用户交互       │    │ • 认证验证       │    │ • 业务逻辑       │
│ • 数据展示       │    │ • 会话管理       │    │ • 数据处理       │
│ • 请求发起       │    │ • 路由转发       │    │ • 资源验证       │
│ • 会话维护       │    │ • 安全注入       │    │ • 权限检查       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 零信任安全模型

**核心理念**：永远不信任，始终验证

- **网关层**：验证用户身份和会话有效性
- **微服务层**：验证资源归属和操作权限
- **数据层**：验证数据完整性和访问权限

## 🏗️ 分区分服架构设计

### 架构概览

```typescript
interface GameArchitecture {
  // 分区：不同的游戏世界
  regions: {
    [regionId: string]: {
      name: string;
      servers: Server[];
      database: DatabaseCluster;
      redis: RedisCluster;
    }
  };
  
  // 分服：同一区域内的不同服务器
  servers: {
    [serverId: string]: {
      regionId: string;
      capacity: number;
      status: 'active' | 'maintenance' | 'full';
      microservices: MicroserviceInstance[];
    }
  };
}
```

### 会话管理架构

#### 1. 游戏会话生命周期

```typescript
// 会话创建流程
interface GameSessionLifecycle {
  // 1. 账号登录
  accountLogin: {
    input: { username: string; password: string; };
    output: { accountToken: string; availableCharacters: Character[]; };
  };
  
  // 2. 区服选择
  serverSelection: {
    input: { accountToken: string; serverId: string; };
    output: { serverToken: string; characters: Character[]; };
  };
  
  // 3. 角色登录
  characterLogin: {
    input: { serverToken: string; characterId: string; };
    output: { gameSession: GameSession; };
  };
}

// 游戏会话结构
interface GameSession {
  sessionId: string;        // 会话唯一标识
  userId: string;          // 账号ID
  characterId: string;     // 当前角色ID
  serverId: string;        // 当前服务器ID
  regionId: string;        // 当前区域ID
  loginTime: number;       // 登录时间
  lastActiveTime: number;  // 最后活跃时间
  permissions: string[];   // 权限列表
  metadata: {
    deviceId?: string;
    ipAddress: string;
    userAgent: string;
  };
}
```

#### 2. 会话存储策略

```typescript
// Redis存储结构
const SESSION_KEYS = {
  // 主会话数据
  session: (sessionId: string) => `game:session:${sessionId}`,
  
  // 用户活跃会话列表
  userSessions: (userId: string) => `game:user:${userId}:sessions`,
  
  // 服务器会话统计
  serverSessions: (serverId: string) => `game:server:${serverId}:sessions`,
  
  // 会话索引（用于快速查找）
  sessionIndex: (characterId: string) => `game:character:${characterId}:session`
};

// 会话管理服务
@Injectable()
export class GameSessionService {
  /**
   * 创建游戏会话
   */
  async createGameSession(
    userId: string,
    characterId: string,
    serverId: string
  ): Promise<GameSession> {
    // 1. 验证角色归属
    await this.validateCharacterOwnership(userId, characterId);
    
    // 2. 检查服务器状态
    await this.validateServerAvailability(serverId);
    
    // 3. 清理旧会话
    await this.cleanupOldSessions(characterId);
    
    // 4. 创建新会话
    const session: GameSession = {
      sessionId: this.generateSessionId(),
      userId,
      characterId,
      serverId,
      regionId: await this.getServerRegion(serverId),
      loginTime: Date.now(),
      lastActiveTime: Date.now(),
      permissions: await this.getUserPermissions(userId),
      metadata: {
        ipAddress: this.getClientIP(),
        userAgent: this.getUserAgent()
      }
    };
    
    // 5. 存储会话
    await this.storeSession(session);
    
    return session;
  }
  
  /**
   * 角色切换
   */
  async switchCharacter(
    sessionId: string,
    newCharacterId: string
  ): Promise<boolean> {
    const session = await this.getSession(sessionId);
    if (!session) return false;
    
    // 验证新角色归属
    await this.validateCharacterOwnership(session.userId, newCharacterId);
    
    // 更新会话
    session.characterId = newCharacterId;
    session.lastActiveTime = Date.now();
    
    await this.updateSession(session);
    return true;
  }
}
```

## 🔐 通信安全架构

### 1. 客户端到网关的请求流程

```typescript
// 客户端请求格式
interface ClientRequest {
  sessionId: string;      // 会话标识（必须）
  action: string;         // 操作标识（如：skill.learn）
  data: any;             // 业务数据（纯净的DTO）
  requestId?: string;     // 请求追踪ID
  timestamp?: number;     // 请求时间戳
}

// 网关处理流程
@Injectable()
export class GatewaySecurityService {
  async processRequest(request: ClientRequest): Promise<any> {
    // 1. 会话验证
    const session = await this.validateSession(request.sessionId);
    
    // 2. 权限检查
    await this.checkPermission(session, request.action);
    
    // 3. 限流检查
    await this.checkRateLimit(session.userId, request.action);
    
    // 4. 构造安全载荷
    const securePayload: MicroservicePayload<any> = {
      data: request.data,
      _session: {
        characterId: session.characterId,
        serverId: session.serverId,
        userId: session.userId,
        sessionId: session.sessionId
      },
      _metadata: {
        requestId: request.requestId || this.generateRequestId(),
        timestamp: Date.now(),
        source: 'gateway',
        clientIP: this.getClientIP()
      }
    };
    
    // 5. 调用微服务
    const [serviceName, actionName] = request.action.split('.');
    return await this.microserviceClient.call(serviceName, actionName, securePayload);
  }
}
```

### 2. 微服务间调用的Session传递

#### 核心问题分析

当微服务A需要调用微服务B时，必须传递当前用户的session上下文，确保：
1. **身份连续性**：下游服务知道是哪个用户发起的操作
2. **权限传递**：下游服务能够进行正确的权限验证
3. **审计追踪**：完整的操作链路追踪

#### Session传递策略

```typescript
// 微服务间调用的Session传递接口
interface MicroserviceCallContext {
  // 原始会话信息（必须传递）
  session: {
    characterId: string;
    serverId: string;
    userId: string;
    sessionId: string;
  };

  // 调用链信息（用于追踪）
  trace: {
    requestId: string;
    parentService: string;
    callDepth: number;
    timestamp: number;
  };

  // 权限上下文（可选，用于权限传递）
  permissions?: {
    roles: string[];
    permissions: string[];
    scope: string;
  };
}

// 微服务调用封装
@Injectable()
export class SecureMicroserviceClient {
  constructor(
    private readonly microserviceClient: MicroserviceClientService,
    private readonly logger: Logger
  ) {}

  /**
   * 安全的微服务调用 - 自动传递session上下文
   */
  async callWithSession<T = any>(
    serviceName: string,
    action: string,
    data: any,
    session: GameSession,
    options?: {
      timeout?: number;
      retries?: number;
      skipPermissionCheck?: boolean;
    }
  ): Promise<T> {
    // 构造调用上下文
    const callContext: MicroserviceCallContext = {
      session: {
        characterId: session.characterId,
        serverId: session.serverId,
        userId: session.userId,
        sessionId: session.sessionId
      },
      trace: {
        requestId: this.generateRequestId(),
        parentService: this.getCurrentServiceName(),
        callDepth: this.getCallDepth() + 1,
        timestamp: Date.now()
      }
    };

    // 构造安全载荷
    const securePayload = {
      data,
      _session: callContext.session,
      _trace: callContext.trace,
      _metadata: {
        source: 'microservice',
        caller: this.getCurrentServiceName()
      }
    };

    // 记录调用日志
    this.logger.debug(`微服务调用: ${serviceName}.${action}`, {
      requestId: callContext.trace.requestId,
      characterId: session.characterId,
      callDepth: callContext.trace.callDepth
    });

    try {
      const result = await this.microserviceClient.call(serviceName, action, securePayload);

      // 记录成功日志
      this.logger.debug(`微服务调用成功: ${serviceName}.${action}`, {
        requestId: callContext.trace.requestId,
        duration: Date.now() - callContext.trace.timestamp
      });

      return result;
    } catch (error) {
      // 记录错误日志
      this.logger.error(`微服务调用失败: ${serviceName}.${action}`, {
        requestId: callContext.trace.requestId,
        error: error.message,
        characterId: session.characterId
      });

      throw error;
    }
  }

  /**
   * 批量微服务调用 - 并行执行多个调用
   */
  async batchCallWithSession<T = any>(
    calls: Array<{
      serviceName: string;
      action: string;
      data: any;
    }>,
    session: GameSession
  ): Promise<T[]> {
    const promises = calls.map(call =>
      this.callWithSession(call.serviceName, call.action, call.data, session)
    );

    return Promise.all(promises);
  }
}
```

#### 实际使用示例

```typescript
// Character服务调用Hero服务的示例
@Injectable()
export class CharacterService {
  constructor(
    private readonly secureMicroserviceClient: SecureMicroserviceClient
  ) {}

  /**
   * 创建初始球员 - 需要调用Hero服务
   */
  async createInitialHeroes(session: GameSession, qualified: number): Promise<any[]> {
    // 获取初始球员配置
    const config = await this.getInitialHeroConfig(qualified);

    // 批量创建球员 - 自动传递session
    const heroCreationCalls = config.heroes.map(heroConfig => ({
      serviceName: MICROSERVICE_NAMES.HERO_SERVICE,
      action: 'hero.createHero',
      data: {
        resId: heroConfig.resId,
        obtainSource: 'initial_creation'
        // 注意：不需要手动传递characterId和serverId
        // 这些信息会通过session自动传递
      }
    }));

    const heroes = await this.secureMicroserviceClient.batchCallWithSession(
      heroCreationCalls,
      session
    );

    return heroes;
  }

  /**
   * 创建初始阵容 - 需要调用Formation服务
   */
  async createInitialFormation(session: GameSession, heroes: any[]): Promise<any> {
    // 1. 创建阵容
    const formation = await this.secureMicroserviceClient.callWithSession(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.createFormation',
      {
        resId: 442, // 4-4-2阵型
        type: 1     // 普通阵容
      },
      session
    );

    // 2. 添加球员到阵容
    const addHeroCalls = heroes.slice(0, 11).map((hero, index) => ({
      serviceName: MICROSERVICE_NAMES.CHARACTER_SERVICE,
      action: 'formation.addHeroToPosition',
      data: {
        formationId: formation.data.uid,
        position: this.getPositionByIndex(index),
        heroId: hero.uid,
        index: 0
      }
    }));

    await this.secureMicroserviceClient.batchCallWithSession(addHeroCalls, session);

    return formation;
  }
}
```

#### Hero服务接收微服务调用

```typescript
// Hero服务接收来自其他微服务的调用
@Controller()
export class HeroController extends BaseGameController {
  @MessagePattern('hero.createHero')
  async createHero(@Payload() payload: MicroservicePayload<CreateHeroDto>) {
    // 1. 提取session信息（无论是来自网关还是其他微服务）
    const session = this.getSession(payload);

    // 2. 检查调用来源
    const isFromGateway = payload._metadata?.source === 'gateway';
    const isFromMicroservice = payload._metadata?.source === 'microservice';

    // 3. 根据来源进行不同的验证
    if (isFromGateway) {
      // 来自网关：需要完整的权限验证
      await this.checkCreateHeroPermission(session);
    } else if (isFromMicroservice) {
      // 来自微服务：信任调用，但仍需验证session有效性
      await this.validateSessionIntegrity(session);

      // 记录微服务调用链
      this.logMicroserviceCall(payload._trace, 'hero.createHero');
    }

    // 4. 执行业务逻辑
    const hero = await this.heroService.createHero({
      ...payload.data,
      characterId: session.characterId,
      serverId: session.serverId
    });

    return {
      code: 0,
      message: '球员创建成功',
      data: hero
    };
  }

  /**
   * 验证session完整性
   */
  private async validateSessionIntegrity(session: GameSession): Promise<void> {
    // 验证session格式
    if (!session.characterId || !session.serverId || !session.userId) {
      throw new BadRequestException('Session信息不完整');
    }

    // 可选：验证session是否仍然有效（轻量级检查）
    const isValid = await this.sessionService.quickValidateSession(session.sessionId);
    if (!isValid) {
      throw new UnauthorizedException('Session已失效');
    }
  }

  /**
   * 记录微服务调用链
   */
  private logMicroserviceCall(trace: any, action: string): void {
    this.logger.log({
      type: 'microservice_call',
      action,
      requestId: trace?.requestId,
      parentService: trace?.parentService,
      callDepth: trace?.callDepth,
      timestamp: Date.now()
    });
  }
}
```

#### Session传递的安全考虑

```typescript
// 微服务间调用的安全策略
@Injectable()
export class MicroserviceSecurityService {
  /**
   * 验证微服务调用的合法性
   */
  async validateMicroserviceCall(payload: MicroservicePayload): Promise<boolean> {
    // 1. 验证调用来源
    const caller = payload._metadata?.caller;
    if (!this.isTrustedService(caller)) {
      throw new ForbiddenException(`不信任的调用来源: ${caller}`);
    }

    // 2. 验证session完整性
    if (!this.isValidSessionStructure(payload._session)) {
      throw new BadRequestException('Session结构无效');
    }

    // 3. 验证调用深度（防止无限递归）
    const callDepth = payload._trace?.callDepth || 0;
    if (callDepth > this.maxCallDepth) {
      throw new BadRequestException(`调用深度超限: ${callDepth}`);
    }

    // 4. 验证请求时效性（防止重放攻击）
    const timestamp = payload._trace?.timestamp || 0;
    if (Date.now() - timestamp > this.maxRequestAge) {
      throw new BadRequestException('请求已过期');
    }

    return true;
  }

  private isTrustedService(serviceName: string): boolean {
    const trustedServices = [
      'character-service',
      'hero-service',
      'activity-service',
      'economy-service',
      'social-service'
    ];
    return trustedServices.includes(serviceName);
  }
}
```

#### 调用链追踪和监控

```typescript
// 微服务调用链追踪
@Injectable()
export class CallTraceService {
  /**
   * 记录调用链
   */
  recordCall(trace: {
    requestId: string;
    parentService: string;
    targetService: string;
    action: string;
    callDepth: number;
    characterId: string;
    timestamp: number;
  }): void {
    // 记录到分布式追踪系统
    this.tracingService.recordSpan({
      traceId: trace.requestId,
      spanId: this.generateSpanId(),
      parentSpanId: this.getParentSpanId(trace.parentService),
      operationName: `${trace.targetService}.${trace.action}`,
      tags: {
        'service.name': trace.targetService,
        'user.character_id': trace.characterId,
        'call.depth': trace.callDepth
      },
      startTime: trace.timestamp
    });

    // 记录到业务日志
    this.logger.info('微服务调用', {
      requestId: trace.requestId,
      callChain: `${trace.parentService} -> ${trace.targetService}`,
      action: trace.action,
      characterId: trace.characterId,
      callDepth: trace.callDepth
    });
  }

  /**
   * 分析调用模式
   */
  async analyzeCallPatterns(characterId: string, timeRange: number): Promise<any> {
    // 分析用户的调用模式，检测异常行为
    const calls = await this.getCallHistory(characterId, timeRange);

    return {
      totalCalls: calls.length,
      uniqueServices: new Set(calls.map(c => c.targetService)).size,
      averageCallDepth: calls.reduce((sum, c) => sum + c.callDepth, 0) / calls.length,
      suspiciousPatterns: this.detectSuspiciousPatterns(calls)
    };
  }
}
```

### 4. 网关到微服务的请求流程

```typescript
// 标准的微服务载荷接口
interface MicroservicePayload<T = any> {
  data: T;                    // 业务数据（DTO）
  _session: {                 // 会话信息（由网关注入）
    characterId: string;
    serverId: string;
    userId: string;
    sessionId: string;
  };
  _metadata?: {               // 元数据（可选）
    requestId: string;
    timestamp: number;
    source: string;
    clientIP?: string;
  };
}

// 微服务控制器基类
export abstract class BaseGameController {
  /**
   * 提取会话信息
   */
  protected getSession(payload: MicroservicePayload): GameSession {
    if (!payload._session) {
      throw new UnauthorizedException('缺少会话信息');
    }
    return payload._session;
  }
  
  /**
   * 验证资源归属
   */
  protected async validateResourceOwnership(
    characterId: string,
    resourceId: string,
    resourceType: 'hero' | 'item' | 'formation'
  ): Promise<void> {
    const isOwner = await this.checkResourceOwnership(characterId, resourceId, resourceType);
    if (!isOwner) {
      throw new ForbiddenException(`资源不属于当前角色: ${resourceType}:${resourceId}`);
    }
  }
  
  /**
   * 记录操作日志
   */
  protected logOperation(
    session: GameSession,
    operation: string,
    details?: any
  ): void {
    this.logger.log({
      operation,
      characterId: session.characterId,
      serverId: session.serverId,
      userId: session.userId,
      timestamp: Date.now(),
      details
    });
  }
}

// 具体业务控制器实现
@Controller()
export class SkillController extends BaseGameController {
  @MessagePattern('skill.learn')
  async learnSkill(@Payload() payload: MicroservicePayload<LearnSkillDto>) {
    // 1. 提取会话信息
    const session = this.getSession(payload);
    
    // 2. 提取业务数据
    const learnDto = payload.data;
    
    // 3. 验证资源归属
    await this.validateResourceOwnership(session.characterId, learnDto.heroId, 'hero');
    
    // 4. 记录操作
    this.logOperation(session, 'skill.learn', { heroId: learnDto.heroId, configId: learnDto.configId });
    
    // 5. 执行业务逻辑
    const result = await this.skillService.learnSkill({
      ...learnDto,
      characterId: session.characterId,
      serverId: session.serverId
    });
    
    return {
      code: 0,
      message: '技能学习成功',
      data: result
    };
  }
}
```

## 🔄 微服务调用最佳实践

### 1. Session传递的设计模式

#### 模式1：透明传递模式（推荐）
```typescript
// 在BaseGameService中封装session传递逻辑
export abstract class BaseGameService {
  constructor(
    protected readonly secureMicroserviceClient: SecureMicroserviceClient
  ) {}

  // 当前请求的session上下文（通过依赖注入或线程本地存储）
  protected currentSession: GameSession;

  /**
   * 设置当前session上下文
   */
  setCurrentSession(session: GameSession): void {
    this.currentSession = session;
  }

  /**
   * 透明的微服务调用 - 自动使用当前session
   */
  protected async callService<T>(
    serviceName: string,
    action: string,
    data: any
  ): Promise<T> {
    if (!this.currentSession) {
      throw new Error('当前session未设置，无法进行微服务调用');
    }

    return this.secureMicroserviceClient.callWithSession(
      serviceName,
      action,
      data,
      this.currentSession
    );
  }
}

// 业务服务继承基类
@Injectable()
export class CharacterService extends BaseGameService {
  async createCharacter(session: GameSession, createDto: CreateCharacterDto) {
    // 设置当前session
    this.setCurrentSession(session);

    // 透明调用其他服务，无需手动传递session
    const heroes = await this.callService(
      MICROSERVICE_NAMES.HERO_SERVICE,
      'hero.batchCreate',
      { heroConfigs: this.getInitialHeroConfigs() }
    );

    const formation = await this.callService(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.createInitial',
      { heroes: heroes.data }
    );

    return { heroes, formation };
  }
}
```

#### 模式2：显式传递模式
```typescript
// 对于需要明确控制session传递的场景
@Injectable()
export class AdvancedCharacterService {
  /**
   * 跨角色操作 - 需要显式指定不同的session
   */
  async transferItemBetweenCharacters(
    fromSession: GameSession,
    toSession: GameSession,
    itemId: string
  ) {
    // 从源角色移除道具
    await this.secureMicroserviceClient.callWithSession(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'inventory.removeItem',
      { itemId },
      fromSession  // 使用源角色的session
    );

    // 给目标角色添加道具
    await this.secureMicroserviceClient.callWithSession(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'inventory.addItem',
      { itemId },
      toSession   // 使用目标角色的session
    );
  }
}
```

### 2. 错误处理和重试机制

```typescript
// 微服务调用的错误处理
@Injectable()
export class RobustMicroserviceClient extends SecureMicroserviceClient {
  /**
   * 带重试的微服务调用
   */
  async callWithRetry<T>(
    serviceName: string,
    action: string,
    data: any,
    session: GameSession,
    options: {
      maxRetries?: number;
      retryDelay?: number;
      retryCondition?: (error: any) => boolean;
    } = {}
  ): Promise<T> {
    const { maxRetries = 3, retryDelay = 1000, retryCondition } = options;

    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.callWithSession(serviceName, action, data, session);
      } catch (error) {
        lastError = error;

        // 检查是否应该重试
        if (attempt === maxRetries || !this.shouldRetry(error, retryCondition)) {
          break;
        }

        // 等待后重试
        await this.delay(retryDelay * Math.pow(2, attempt)); // 指数退避

        this.logger.warn(`微服务调用重试: ${serviceName}.${action}, 尝试 ${attempt + 1}/${maxRetries}`, {
          error: error.message,
          characterId: session.characterId
        });
      }
    }

    throw lastError;
  }

  private shouldRetry(error: any, customCondition?: (error: any) => boolean): boolean {
    if (customCondition) {
      return customCondition(error);
    }

    // 默认重试条件：网络错误、超时、服务不可用
    const retryableErrors = [
      'ECONNREFUSED',
      'ETIMEDOUT',
      'SERVICE_UNAVAILABLE',
      'INTERNAL_SERVER_ERROR'
    ];

    return retryableErrors.some(errorType =>
      error.message?.includes(errorType) || error.code === errorType
    );
  }
}
```

### 3. 性能优化策略

```typescript
// 微服务调用的性能优化
@Injectable()
export class OptimizedMicroserviceClient extends SecureMicroserviceClient {
  private callCache = new Map<string, { data: any; timestamp: number }>();

  /**
   * 带缓存的微服务调用
   */
  async callWithCache<T>(
    serviceName: string,
    action: string,
    data: any,
    session: GameSession,
    cacheOptions: {
      ttl?: number;
      key?: string;
      skipCache?: boolean;
    } = {}
  ): Promise<T> {
    const { ttl = 60000, skipCache = false } = cacheOptions;

    // 生成缓存键
    const cacheKey = cacheOptions.key || this.generateCacheKey(
      serviceName, action, data, session.characterId
    );

    // 检查缓存
    if (!skipCache) {
      const cached = this.callCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < ttl) {
        this.logger.debug(`微服务调用缓存命中: ${serviceName}.${action}`);
        return cached.data;
      }
    }

    // 调用微服务
    const result = await this.callWithSession(serviceName, action, data, session);

    // 缓存结果
    this.callCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    return result;
  }

  /**
   * 批量调用优化 - 合并相同服务的调用
   */
  async optimizedBatchCall<T>(
    calls: Array<{
      serviceName: string;
      action: string;
      data: any;
    }>,
    session: GameSession
  ): Promise<T[]> {
    // 按服务分组
    const groupedCalls = this.groupCallsByService(calls);

    // 并行调用不同服务，串行调用同一服务
    const results = await Promise.all(
      Object.entries(groupedCalls).map(async ([serviceName, serviceCalls]) => {
        // 对于同一服务的调用，可以考虑批量接口
        if (serviceCalls.length > 1 && this.supportsBatchCall(serviceName)) {
          return this.callBatchInterface(serviceName, serviceCalls, session);
        } else {
          // 串行调用避免对同一服务造成压力
          const serviceResults = [];
          for (const call of serviceCalls) {
            const result = await this.callWithSession(
              serviceName, call.action, call.data, session
            );
            serviceResults.push(result);
          }
          return serviceResults;
        }
      })
    );

    return results.flat();
  }
}
```

## 🛡️ 安全最佳实践

### 1. 防御性编程原则

```typescript
// 多层验证策略
class SecurityValidation {
  // 网关层：基础安全验证
  async gatewayValidation(request: ClientRequest): Promise<void> {
    // 会话有效性
    // 基础权限检查
    // 限流检查
    // 输入格式验证
  }
  
  // 微服务层：业务安全验证
  async microserviceValidation(payload: MicroservicePayload): Promise<void> {
    // 资源归属验证
    // 业务权限检查
    // 数据完整性验证
    // 操作合法性检查
  }
  
  // 数据层：数据安全验证
  async dataValidation(operation: string, data: any): Promise<void> {
    // 数据格式验证
    // 业务规则验证
    // 约束条件检查
    // 审计日志记录
  }
}
```

### 2. 错误处理和日志记录

```typescript
// 统一错误处理
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToRpc();
    const payload = ctx.getData();
    
    // 记录安全相关错误
    if (exception instanceof UnauthorizedException || 
        exception instanceof ForbiddenException) {
      this.securityLogger.warn({
        error: exception.message,
        session: payload._session,
        timestamp: Date.now(),
        severity: 'HIGH'
      });
    }
    
    // 返回安全的错误信息
    return {
      code: this.getErrorCode(exception),
      message: this.getSafeErrorMessage(exception),
      timestamp: Date.now()
    };
  }
}
```

## 📊 性能优化策略

### 1. 会话缓存优化

```typescript
// 多级缓存策略
class SessionCacheStrategy {
  // L1: 内存缓存（最热数据）
  private memoryCache = new Map<string, GameSession>();
  
  // L2: Redis缓存（分布式共享）
  private redisCache: RedisService;
  
  // L3: 数据库（持久化存储）
  private database: DatabaseService;
  
  async getSession(sessionId: string): Promise<GameSession | null> {
    // 1. 检查内存缓存
    let session = this.memoryCache.get(sessionId);
    if (session) return session;
    
    // 2. 检查Redis缓存
    session = await this.redisCache.get(`session:${sessionId}`);
    if (session) {
      this.memoryCache.set(sessionId, session);
      return session;
    }
    
    // 3. 从数据库加载
    session = await this.database.findSession(sessionId);
    if (session) {
      await this.redisCache.setex(`session:${sessionId}`, 3600, session);
      this.memoryCache.set(sessionId, session);
    }
    
    return session;
  }
}
```

### 2. 微服务调用优化

```typescript
// 批量操作优化
class BatchOperationOptimizer {
  // 批量验证资源归属
  async batchValidateOwnership(
    characterId: string,
    resources: Array<{ id: string; type: string }>
  ): Promise<boolean[]> {
    // 按类型分组
    const grouped = this.groupByType(resources);
    
    // 并行验证
    const results = await Promise.all(
      Object.entries(grouped).map(([type, ids]) =>
        this.validateOwnershipByType(characterId, type, ids)
      )
    );
    
    return this.flattenResults(results);
  }
}
```

## 🔄 实施指南

### 阶段1：基础设施准备（2-3天）

1. **扩展Auth服务**
   - 实现GameSessionService
   - 添加角色切换功能
   - 完善会话管理

2. **增强Gateway功能**
   - 添加会话注入中间件
   - 实现安全载荷构造
   - 完善错误处理

### 阶段2：微服务改造（3-5天）

1. **创建基础设施**
   - BaseGameController基类
   - MicroservicePayload接口
   - 安全验证装饰器

2. **重构业务控制器**
   - 使用新的载荷模式
   - 添加资源验证
   - 完善日志记录

### 阶段3：客户端适配（2-3天）

1. **调整请求格式**
   - 移除显式身份参数
   - 使用会话ID认证
   - 更新错误处理

2. **测试和验证**
   - 安全性测试
   - 性能测试
   - 兼容性测试

## 📋 检查清单

### 安全检查清单

- [ ] 所有微服务接口都使用MicroservicePayload
- [ ] 所有资源操作都验证归属关系
- [ ] 所有敏感操作都记录审计日志
- [ ] 所有错误信息都经过安全过滤
- [ ] 所有会话都有过期机制
- [ ] 所有权限都经过双重验证

### 微服务调用安全检查清单

- [ ] 所有微服务间调用都使用SecureMicroserviceClient
- [ ] 所有调用都正确传递session上下文
- [ ] 调用来源都经过验证（isTrustedService）
- [ ] 调用深度有限制（防止无限递归）
- [ ] 请求时效性有验证（防止重放攻击）
- [ ] Session结构完整性有验证
- [ ] 调用链都有完整的追踪记录
- [ ] 异常调用模式有监控告警

### 性能检查清单

- [ ] 会话信息使用多级缓存
- [ ] 批量操作使用并行处理
- [ ] 数据库查询使用索引优化
- [ ] 网络调用使用连接池
- [ ] 日志记录使用异步处理
- [ ] 监控指标完整覆盖

### 微服务调用性能检查清单

- [ ] 同一服务的批量调用使用批量接口
- [ ] 频繁调用使用缓存机制
- [ ] 调用失败有重试机制（指数退避）
- [ ] 调用超时有合理设置
- [ ] 调用并发有限流控制
- [ ] 调用性能有监控指标
- [ ] 慢调用有告警机制
- [ ] 调用链深度有性能分析

### 开发和调试检查清单

- [ ] 开发环境有详细的调用日志
- [ ] 调用链可视化工具可用
- [ ] 性能分析报告可生成
- [ ] 错误调试信息完整
- [ ] 调用模拟工具可用
- [ ] 单元测试覆盖微服务调用场景
- [ ] 集成测试覆盖调用链场景
- [ ] 压力测试验证调用性能

## 🔧 技术实现细节

### 1. 会话装饰器实现

```typescript
// libs/common/src/decorators/session.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const SessionContext = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToRpc().getData();
    if (!request._session) {
      throw new UnauthorizedException('缺少会话信息');
    }
    return request._session;
  },
);

// 使用示例
@MessagePattern('skill.learn')
async learnSkill(
  @Payload('data') learnDto: LearnSkillDto,
  @SessionContext() session: GameSession
) {
  // session 自动注入，learnDto 保持纯净
}
```

### 2. 网关中间件实现

```typescript
// apps/gateway/src/middleware/session-injection.middleware.ts
@Injectable()
export class SessionInjectionMiddleware implements NestInterceptor {
  constructor(
    private readonly sessionService: GameSessionService,
    private readonly logger: Logger
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToWs().getData();

    try {
      // 从请求中提取会话ID
      const sessionId = this.extractSessionId(request);

      if (sessionId) {
        // 获取会话信息
        const session = await this.sessionService.getSession(sessionId);

        if (session) {
          // 注入会话信息到请求载荷
          request._session = {
            characterId: session.characterId,
            serverId: session.serverId,
            userId: session.userId,
            sessionId: session.sessionId
          };

          // 更新最后活跃时间
          await this.sessionService.updateLastActivity(sessionId);
        }
      }
    } catch (error) {
      this.logger.error('会话注入失败', error);
      // 不阻断请求，让后续验证处理
    }

    return next.handle();
  }

  private extractSessionId(request: any): string | null {
    return request.sessionId ||
           request.headers?.['x-session-id'] ||
           request.metadata?.sessionId ||
           null;
  }
}
```

### 3. 资源验证服务

```typescript
// libs/common/src/services/resource-validation.service.ts
@Injectable()
export class ResourceValidationService {
  constructor(
    private readonly microserviceClient: MicroserviceClientService,
    private readonly cacheService: CacheService
  ) {}

  async validateResourceOwnership(
    characterId: string,
    resourceId: string,
    resourceType: 'hero' | 'item' | 'formation' | 'tactic'
  ): Promise<boolean> {
    // 缓存键
    const cacheKey = `resource:${resourceType}:${resourceId}:owner`;

    // 检查缓存
    let ownerId = await this.cacheService.get(cacheKey);

    if (!ownerId) {
      // 调用对应微服务验证
      ownerId = await this.getResourceOwner(resourceType, resourceId);

      if (ownerId) {
        // 缓存结果（5分钟）
        await this.cacheService.setex(cacheKey, 300, ownerId);
      }
    }

    return ownerId === characterId;
  }

  private async getResourceOwner(resourceType: string, resourceId: string): Promise<string | null> {
    const serviceMap = {
      hero: MICROSERVICE_NAMES.HERO_SERVICE,
      item: MICROSERVICE_NAMES.CHARACTER_SERVICE,
      formation: MICROSERVICE_NAMES.CHARACTER_SERVICE,
      tactic: MICROSERVICE_NAMES.CHARACTER_SERVICE
    };

    const actionMap = {
      hero: 'hero.getOwner',
      item: 'inventory.getItemOwner',
      formation: 'formation.getOwner',
      tactic: 'tactic.getOwner'
    };

    try {
      const result = await this.microserviceClient.call(
        serviceMap[resourceType],
        actionMap[resourceType],
        { resourceId }
      );

      return result?.data?.characterId || null;
    } catch (error) {
      return null;
    }
  }
}
```

## 🔍 微服务调用监控和调试

### 1. 调用链监控

```typescript
// 微服务调用链监控
@Injectable()
export class MicroserviceCallMonitor {
  private readonly metrics = {
    // 调用统计
    callsTotal: new Counter('microservice_calls_total', '微服务调用总数', ['source', 'target', 'action']),
    callDuration: new Histogram('microservice_call_duration_seconds', '微服务调用耗时', ['source', 'target', 'action']),
    callErrors: new Counter('microservice_call_errors_total', '微服务调用错误数', ['source', 'target', 'action', 'error_type']),

    // Session相关
    sessionPropagation: new Counter('session_propagation_total', 'Session传递次数', ['source', 'target']),
    sessionErrors: new Counter('session_propagation_errors_total', 'Session传递错误数', ['error_type']),

    // 调用深度
    callDepth: new Histogram('microservice_call_depth', '微服务调用深度', ['source']),
    maxCallDepth: new Gauge('microservice_max_call_depth', '最大调用深度')
  };

  /**
   * 记录微服务调用指标
   */
  recordCall(
    source: string,
    target: string,
    action: string,
    duration: number,
    success: boolean,
    callDepth: number,
    error?: any
  ): void {
    // 基础调用指标
    this.metrics.callsTotal.inc({ source, target, action });
    this.metrics.callDuration.observe({ source, target, action }, duration / 1000);
    this.metrics.callDepth.observe({ source }, callDepth);

    // 更新最大调用深度
    this.metrics.maxCallDepth.set(Math.max(this.metrics.maxCallDepth.get(), callDepth));

    // 错误指标
    if (!success && error) {
      const errorType = this.categorizeError(error);
      this.metrics.callErrors.inc({ source, target, action, error_type: errorType });
    }

    // Session传递指标
    this.metrics.sessionPropagation.inc({ source, target });
  }

  /**
   * 记录Session传递错误
   */
  recordSessionError(errorType: string, details: any): void {
    this.metrics.sessionErrors.inc({ error_type: errorType });

    this.logger.error('Session传递错误', {
      errorType,
      details,
      timestamp: Date.now()
    });
  }

  private categorizeError(error: any): string {
    if (error.code === 'ECONNREFUSED') return 'connection_refused';
    if (error.code === 'ETIMEDOUT') return 'timeout';
    if (error.message?.includes('Unauthorized')) return 'unauthorized';
    if (error.message?.includes('Forbidden')) return 'forbidden';
    if (error.message?.includes('Session')) return 'session_error';
    return 'unknown';
  }
}
```

### 2. 调用链可视化

```typescript
// 调用链可视化数据收集
@Injectable()
export class CallChainVisualizer {
  private callChains = new Map<string, CallChainNode[]>();

  interface CallChainNode {
    requestId: string;
    service: string;
    action: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    parentId?: string;
    children: CallChainNode[];
    session: {
      characterId: string;
      userId: string;
    };
    metadata: {
      success: boolean;
      error?: string;
      callDepth: number;
    };
  }

  /**
   * 开始记录调用链节点
   */
  startCall(
    requestId: string,
    service: string,
    action: string,
    session: GameSession,
    parentId?: string,
    callDepth: number = 0
  ): string {
    const nodeId = this.generateNodeId();

    const node: CallChainNode = {
      requestId,
      service,
      action,
      startTime: Date.now(),
      parentId,
      children: [],
      session: {
        characterId: session.characterId,
        userId: session.userId
      },
      metadata: {
        success: false,
        callDepth
      }
    };

    // 存储节点
    if (!this.callChains.has(requestId)) {
      this.callChains.set(requestId, []);
    }
    this.callChains.get(requestId)!.push(node);

    // 建立父子关系
    if (parentId) {
      const parentNode = this.findNode(requestId, parentId);
      if (parentNode) {
        parentNode.children.push(node);
      }
    }

    return nodeId;
  }

  /**
   * 结束调用链节点记录
   */
  endCall(requestId: string, nodeId: string, success: boolean, error?: any): void {
    const node = this.findNode(requestId, nodeId);
    if (node) {
      node.endTime = Date.now();
      node.duration = node.endTime - node.startTime;
      node.metadata.success = success;
      if (error) {
        node.metadata.error = error.message;
      }
    }
  }

  /**
   * 获取调用链可视化数据
   */
  getCallChainVisualization(requestId: string): any {
    const nodes = this.callChains.get(requestId) || [];
    const rootNodes = nodes.filter(node => !node.parentId);

    return {
      requestId,
      totalNodes: nodes.length,
      maxDepth: Math.max(...nodes.map(n => n.metadata.callDepth)),
      totalDuration: Math.max(...nodes.map(n => n.endTime || Date.now())) -
                    Math.min(...nodes.map(n => n.startTime)),
      tree: this.buildTree(rootNodes),
      timeline: this.buildTimeline(nodes)
    };
  }

  private buildTree(nodes: CallChainNode[]): any[] {
    return nodes.map(node => ({
      id: node.requestId,
      service: node.service,
      action: node.action,
      duration: node.duration,
      success: node.metadata.success,
      error: node.metadata.error,
      callDepth: node.metadata.callDepth,
      children: this.buildTree(node.children)
    }));
  }

  private buildTimeline(nodes: CallChainNode[]): any[] {
    return nodes
      .sort((a, b) => a.startTime - b.startTime)
      .map(node => ({
        service: node.service,
        action: node.action,
        startTime: node.startTime,
        endTime: node.endTime,
        duration: node.duration,
        success: node.metadata.success,
        callDepth: node.metadata.callDepth
      }));
  }
}
```

### 3. 调试工具

```typescript
// 微服务调用调试工具
@Injectable()
export class MicroserviceDebugger {
  private debugMode = process.env.NODE_ENV === 'development';
  private callLogs = new Map<string, any[]>();

  /**
   * 调试模式下记录详细调用信息
   */
  logCall(
    requestId: string,
    phase: 'start' | 'end' | 'error',
    details: any
  ): void {
    if (!this.debugMode) return;

    if (!this.callLogs.has(requestId)) {
      this.callLogs.set(requestId, []);
    }

    this.callLogs.get(requestId)!.push({
      phase,
      timestamp: Date.now(),
      details
    });

    // 实时输出调试信息
    this.logger.debug(`[${phase.toUpperCase()}] ${details.service}.${details.action}`, {
      requestId,
      characterId: details.session?.characterId,
      callDepth: details.callDepth,
      ...details
    });
  }

  /**
   * 生成调用报告
   */
  generateCallReport(requestId: string): any {
    const logs = this.callLogs.get(requestId) || [];

    const report = {
      requestId,
      totalCalls: logs.filter(l => l.phase === 'start').length,
      successfulCalls: logs.filter(l => l.phase === 'end').length,
      failedCalls: logs.filter(l => l.phase === 'error').length,
      services: [...new Set(logs.map(l => l.details.service))],
      timeline: logs.map(log => ({
        phase: log.phase,
        timestamp: log.timestamp,
        service: log.details.service,
        action: log.details.action,
        duration: log.details.duration
      })),
      sessionInfo: logs[0]?.details?.session,
      performance: this.analyzePerformance(logs)
    };

    return report;
  }

  private analyzePerformance(logs: any[]): any {
    const durations = logs
      .filter(l => l.phase === 'end' && l.details.duration)
      .map(l => l.details.duration);

    if (durations.length === 0) return null;

    return {
      totalDuration: durations.reduce((sum, d) => sum + d, 0),
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations),
      slowCalls: logs
        .filter(l => l.details.duration > 1000) // 超过1秒的调用
        .map(l => ({
          service: l.details.service,
          action: l.details.action,
          duration: l.details.duration
        }))
    };
  }
}
```

## 📈 监控和告警

### 1. 安全监控指标

```typescript
// libs/common/src/monitoring/security-metrics.service.ts
@Injectable()
export class SecurityMetricsService {
  private readonly metrics = {
    // 认证相关
    authAttempts: new Counter('auth_attempts_total', '认证尝试次数'),
    authFailures: new Counter('auth_failures_total', '认证失败次数'),
    sessionCreated: new Counter('sessions_created_total', '会话创建次数'),
    sessionExpired: new Counter('sessions_expired_total', '会话过期次数'),

    // 权限相关
    permissionChecks: new Counter('permission_checks_total', '权限检查次数'),
    permissionDenied: new Counter('permission_denied_total', '权限拒绝次数'),
    resourceViolations: new Counter('resource_violations_total', '资源访问违规次数'),

    // 性能相关
    sessionLookupTime: new Histogram('session_lookup_duration_seconds', '会话查找耗时'),
    validationTime: new Histogram('validation_duration_seconds', '验证耗时')
  };

  recordAuthAttempt(success: boolean, method: string): void {
    this.metrics.authAttempts.inc({ method });
    if (!success) {
      this.metrics.authFailures.inc({ method });
    }
  }

  recordPermissionCheck(granted: boolean, resource: string, action: string): void {
    this.metrics.permissionChecks.inc({ resource, action });
    if (!granted) {
      this.metrics.permissionDenied.inc({ resource, action });
    }
  }

  recordResourceViolation(characterId: string, resourceType: string, resourceId: string): void {
    this.metrics.resourceViolations.inc({ resourceType });

    // 记录详细日志用于安全分析
    this.logger.warn('资源访问违规', {
      characterId,
      resourceType,
      resourceId,
      timestamp: Date.now(),
      severity: 'HIGH'
    });
  }
}
```

### 2. 告警规则配置

```yaml
# monitoring/alerts/security-alerts.yml
groups:
  - name: security.rules
    rules:
      # 认证失败率过高
      - alert: HighAuthFailureRate
        expr: rate(auth_failures_total[5m]) / rate(auth_attempts_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "认证失败率过高"
          description: "过去5分钟认证失败率超过10%"

      # 权限拒绝率异常
      - alert: HighPermissionDenialRate
        expr: rate(permission_denied_total[5m]) > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "权限拒绝率异常"
          description: "过去5分钟权限拒绝次数超过10次/分钟"

      # 资源访问违规
      - alert: ResourceViolationDetected
        expr: increase(resource_violations_total[1m]) > 0
        for: 0s
        labels:
          severity: critical
        annotations:
          summary: "检测到资源访问违规"
          description: "发现用户尝试访问不属于自己的资源"
```

## 🧪 测试策略

### 1. 安全测试用例

```typescript
// tests/security/session-security.spec.ts
describe('会话安全测试', () => {
  it('应该拒绝无效的会话ID', async () => {
    const invalidSessionId = 'invalid-session-id';

    await expect(
      gatewayService.processRequest({
        sessionId: invalidSessionId,
        action: 'hero.getList',
        data: {}
      })
    ).rejects.toThrow(UnauthorizedException);
  });

  it('应该拒绝过期的会话', async () => {
    const expiredSession = await createExpiredSession();

    await expect(
      gatewayService.processRequest({
        sessionId: expiredSession.sessionId,
        action: 'hero.getList',
        data: {}
      })
    ).rejects.toThrow(UnauthorizedException);
  });

  it('应该拒绝跨角色资源访问', async () => {
    const session1 = await createSession('user1', 'char1');
    const session2 = await createSession('user2', 'char2');

    // 创建属于char2的英雄
    const hero = await createHero('char2');

    // char1尝试访问char2的英雄
    await expect(
      skillController.learnSkill({
        data: { heroId: hero.id, configId: 1001 },
        _session: session1
      })
    ).rejects.toThrow(ForbiddenException);
  });
});
```

### 2. 性能测试用例

```typescript
// tests/performance/session-performance.spec.ts
describe('会话性能测试', () => {
  it('会话查找应该在100ms内完成', async () => {
    const sessionId = await createSession();

    const startTime = Date.now();
    await sessionService.getSession(sessionId);
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(100);
  });

  it('并发会话验证应该保持性能', async () => {
    const sessions = await createManySessions(1000);

    const startTime = Date.now();
    await Promise.all(
      sessions.map(session =>
        gatewayService.validateSession(session.sessionId)
      )
    );
    const endTime = Date.now();

    // 1000个并发验证应该在1秒内完成
    expect(endTime - startTime).toBeLessThan(1000);
  });
});
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-27
**维护者**: 架构团队
**审核状态**: 待审核
