# Auth服务部署和配置指南

## 📋 概述

本文档详细描述了Auth服务的部署流程、配置管理、环境要求和运维指南。

## 🏗️ 环境要求

### 系统要求
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **MongoDB**: >= 5.0
- **Redis**: >= 6.0
- **操作系统**: Linux/macOS/Windows

### 硬件要求
- **CPU**: 最少2核，推荐4核
- **内存**: 最少4GB，推荐8GB
- **存储**: 最少20GB可用空间
- **网络**: 稳定的网络连接

## 🔧 环境配置

### 环境变量配置
创建 `.env` 文件并配置以下环境变量：

```bash
# 应用基础配置
NODE_ENV=production
PORT=3001
APP_NAME=football-manager-auth

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/football-manager
MONGODB_AUTH_DB=football-manager-auth

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# 基础JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=football-manager-auth
JWT_AUDIENCE=football-manager-app

# 角色级Token配置
CHARACTER_JWT_SECRET=your-character-jwt-secret-change-in-production-min-32-chars
CHARACTER_JWT_EXPIRES_IN=4h
CHARACTER_JWT_ALGORITHM=HS256
CHARACTER_JWT_ISSUER=football-manager-game
CHARACTER_JWT_AUDIENCE=football-manager-app

# 多服务器配置
MULTI_SERVER_ENABLED=true
DEFAULT_SERVER_ID=server_001
MAX_SERVERS=10
CHARACTER_SESSION_TIMEOUT=14400

# 区服配置
SERVER_001_ID=server_001
SERVER_001_NAME=新手村
SERVER_001_STATUS=active
SERVER_001_MAX_PLAYERS=10000

SERVER_002_ID=server_002
SERVER_002_NAME=勇者大陆
SERVER_002_STATUS=active
SERVER_002_MAX_PLAYERS=10000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/auth-service.log

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_TIMEOUT=5000
```

### 配置验证
使用以下命令验证配置：

```bash
# 验证环境变量
npm run config:validate

# 测试数据库连接
npm run db:test

# 测试Redis连接
npm run redis:test
```

## 🚀 部署流程

### 1. 源码部署

#### 克隆代码
```bash
git clone https://github.com/your-org/football-manager.git
cd football-manager
```

#### 安装依赖
```bash
npm install
```

#### 构建应用
```bash
npm run build:auth
```

#### 启动服务
```bash
# 开发环境
npm run start:auth:dev

# 生产环境
npm run start:auth:prod
```

### 2. Docker部署

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源码
COPY . .

# 构建应用
RUN npm run build:auth

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/auth/health || exit 1

# 启动应用
CMD ["npm", "run", "start:auth:prod"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  auth-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/football-manager
      - REDIS_HOST=redis
    depends_on:
      - mongo
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/var/log
    networks:
      - football-manager

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongo_data:/data/db
    networks:
      - football-manager

  redis:
    image: redis:6.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass your-redis-password
    volumes:
      - redis_data:/data
    networks:
      - football-manager

volumes:
  mongo_data:
  redis_data:

networks:
  football-manager:
    driver: bridge
```

#### 启动Docker服务
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f auth-service

# 停止服务
docker-compose down
```

### 3. Kubernetes部署

#### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
data:
  NODE_ENV: "production"
  PORT: "3001"
  MONGODB_URI: "mongodb://mongo-service:27017/football-manager"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  MULTI_SERVER_ENABLED: "true"
  DEFAULT_SERVER_ID: "server_001"
```

#### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: auth-secrets
type: Opaque
data:
  JWT_SECRET: <base64-encoded-secret>
  CHARACTER_JWT_SECRET: <base64-encoded-secret>
  REDIS_PASSWORD: <base64-encoded-password>
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: football-manager/auth-service:latest
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: auth-config
        - secretRef:
            name: auth-secrets
        livenessProbe:
          httpGet:
            path: /api/auth/health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/auth/health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

#### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3001
  type: ClusterIP
```

## 🔒 安全配置

### SSL/TLS配置
```bash
# 生成SSL证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365

# 配置HTTPS
export HTTPS_ENABLED=true
export SSL_CERT_PATH=/path/to/cert.pem
export SSL_KEY_PATH=/path/to/key.pem
```

### 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 3001/tcp  # Auth服务端口
sudo ufw allow 27017/tcp # MongoDB端口
sudo ufw allow 6379/tcp  # Redis端口

# 限制访问源
sudo ufw allow from 10.0.0.0/8 to any port 27017
sudo ufw allow from 10.0.0.0/8 to any port 6379
```

### JWT密钥管理
```bash
# 生成强密钥
openssl rand -base64 32

# 密钥轮换脚本
#!/bin/bash
NEW_SECRET=$(openssl rand -base64 32)
kubectl patch secret auth-secrets -p='{"data":{"JWT_SECRET":"'$(echo -n $NEW_SECRET | base64)'"}}'
kubectl rollout restart deployment/auth-service
```

## 📊 监控和日志

### 健康检查端点
- **基础健康检查**: `GET /api/auth/health`
- **详细健康检查**: `GET /api/auth/health/detailed`
- **就绪检查**: `GET /api/auth/ready`

### 监控指标
```javascript
// Prometheus指标示例
const promClient = require('prom-client');

// 自定义指标
const authRequestsTotal = new promClient.Counter({
  name: 'auth_requests_total',
  help: 'Total number of authentication requests',
  labelNames: ['method', 'status']
});

const tokenGenerationDuration = new promClient.Histogram({
  name: 'token_generation_duration_seconds',
  help: 'Duration of token generation',
  buckets: [0.1, 0.5, 1, 2, 5]
});
```

### 日志配置
```javascript
// Winston日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

## 🔄 备份和恢复

### 数据库备份
```bash
# MongoDB备份
mongodump --uri="mongodb://localhost:27017/football-manager" --out=/backup/$(date +%Y%m%d)

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR
mongodump --uri="$MONGODB_URI" --out=$BACKUP_DIR
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### Redis备份
```bash
# Redis备份
redis-cli --rdb /backup/dump-$(date +%Y%m%d).rdb

# 自动备份脚本
#!/bin/bash
BACKUP_FILE="/backup/redis-$(date +%Y%m%d).rdb"
redis-cli --rdb $BACKUP_FILE
gzip $BACKUP_FILE
```

### 恢复流程
```bash
# MongoDB恢复
mongorestore --uri="mongodb://localhost:27017/football-manager" /backup/20240115

# Redis恢复
redis-cli --pipe < /backup/redis-20240115.rdb
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
netstat -tulpn | grep 3001

# 检查环境变量
env | grep JWT

# 检查日志
tail -f /var/log/auth-service.log
```

#### 2. 数据库连接失败
```bash
# 测试MongoDB连接
mongo $MONGODB_URI

# 检查MongoDB状态
systemctl status mongod

# 查看MongoDB日志
tail -f /var/log/mongodb/mongod.log
```

#### 3. Redis连接失败
```bash
# 测试Redis连接
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping

# 检查Redis状态
systemctl status redis

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
```

### 性能调优

#### 数据库优化
```javascript
// MongoDB索引优化
db.user_histories.createIndex({ userId: 1 }, { unique: true });
db.character_sessions.createIndex({ userId: 1, serverId: 1 });
db.character_sessions.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
```

#### Redis优化
```bash
# Redis配置优化
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

#### 应用优化
```javascript
// 连接池配置
const mongoOptions = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
};

const redisOptions = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
};
```

## 📈 扩容指南

### 水平扩容
```bash
# Docker Compose扩容
docker-compose up -d --scale auth-service=3

# Kubernetes扩容
kubectl scale deployment auth-service --replicas=5
```

### 负载均衡
```nginx
# Nginx配置
upstream auth_backend {
    server auth-service-1:3001;
    server auth-service-2:3001;
    server auth-service-3:3001;
}

server {
    listen 80;
    location /api/auth/ {
        proxy_pass http://auth_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔐 安全最佳实践

1. **定期更新依赖**: 使用 `npm audit` 检查安全漏洞
2. **密钥轮换**: 定期轮换JWT密钥
3. **访问控制**: 限制数据库和Redis的网络访问
4. **日志审计**: 记录所有认证相关的操作
5. **监控告警**: 设置异常登录和失败率告警
6. **备份验证**: 定期验证备份的完整性
