# 分区分服系统设计方案 v3.0 - 基于现有架构的精准实现

## 📋 概述

基于对原始需求的深度理解和现有认证架构的分析，本方案严格遵循 `multi-server-system-design.md` 中的核心需求，在现有网关和认证服务基础上，通过**最小化扩展**实现分区分服功能。

## 🎯 核心需求重申

### **1. 基础功能需求**
- ✅ **账号统一管理**: 一个账号可以在多个区服游戏
- ✅ **区服列表展示**: 显示所有可用区服及其状态
- ✅ **区服状态管理**: 新服、开启中、维护中、即将合服等状态
- ✅ **区服选择**: 玩家可以选择进入指定区服
- ✅ **历史记录**: 记录玩家曾经游戏过的区服
- ✅ **最后游戏区服**: 记录并快速进入上次游戏的区服

### **2. 数据隔离需求**
- ✅ **完全数据隔离**: 同一账号在不同区服的数据完全独立
- ✅ **独立游戏进度**: 俱乐部、球员、比赛记录等数据按区服隔离
- ✅ **独立经济系统**: 金币、道具、交易记录按区服隔离
- ✅ **独立社交系统**: 好友、公会、聊天记录按区服隔离

### **3. 跨服功能需求**
- ✅ **跨服排行榜**: 支持全区服排行榜展示
- ✅ **跨服战斗**: 支持不同区服玩家间的对战
- ✅ **跨服活动**: 支持全区服参与的特殊活动
- ✅ **跨服聊天**: 支持跨区服的世界频道

### **4. 合服功能需求**
- ✅ **合服策略**: 支持多个区服合并为一个区服
- ✅ **数据迁移**: 安全可靠的数据合并和迁移
- ✅ **冲突处理**: 处理合服时的数据冲突（如重名、排名等）
- ✅ **补偿机制**: 合服后的玩家补偿和调整

## 🔐 认证架构分析与扩展

### **1. 当前认证机制理解**

基于代码分析，当前系统采用**双层认证机制**：

```typescript
// 第一层：账号认证（Account Level Authentication）
// 位置：Gateway + Auth Service
interface AccountAuthContext {
  accountId: string;        // 游戏账号ID
  username: string;         // 账号用户名
  email: string;           // 账号邮箱
  roles: string[];         // 账号角色权限
  authenticated: boolean;   // 账号认证状态
}

// 第二层：角色认证（Character Level Authentication）
// 需要扩展：当前缺失，需要新增
interface CharacterAuthContext {
  accountId: string;        // 关联的账号ID
  characterId: string;      // 当前游戏角色ID
  serverId: string;         // 当前所在区服ID
  serverName: string;       // 区服名称
  characterName: string;    // 角色名称
  lastLoginTime: Date;      // 最后登录时间
  sessionId: string;        // 角色会话ID
}
```

### **2. 认证流程扩展设计**

```typescript
/**
 * 扩展后的认证流程：账号认证 → 区服选择 → 角色认证
 */

// 步骤1：账号登录（现有流程，无需修改）
POST /api/auth/login
{
  "identifier": "<EMAIL>",
  "password": "password123"
}
// 返回：账号级JWT Token

// 步骤2：获取区服列表（新增）
GET /api/auth/servers
Headers: { Authorization: "Bearer <account_token>" }
// 返回：可用区服列表 + 玩家历史记录

// 步骤3：选择区服并创建角色会话（新增）
POST /api/auth/servers/{serverId}/enter
Headers: { Authorization: "Bearer <account_token>" }
{
  "characterId": "char_123" // 可选，如果是新角色则为空
}
// 返回：角色级JWT Token + 区服上下文信息

// 步骤4：游戏业务请求（扩展现有）
GET /api/character/profile
Headers: { 
  Authorization: "Bearer <character_token>",
  X-Server-ID: "server_001"
}
// 自动路由到对应区服的微服务
```

## 🏗️ 架构扩展设计

### **1. 认证服务扩展 (Auth Service Enhancement)**

```typescript
// apps/auth/src/domain/server-auth/ (新增模块)
├── server-auth.module.ts
├── services/
│   ├── server-selection.service.ts      # 区服选择逻辑
│   ├── character-session.service.ts     # 角色会话管理
│   └── server-history.service.ts        # 玩家区服历史
├── controllers/
│   ├── server.controller.ts             # 区服管理API
│   └── character-session.controller.ts  # 角色会话API
├── entities/
│   ├── server.entity.ts                 # 区服信息实体
│   ├── character-session.entity.ts      # 角色会话实体
│   └── player-server-history.entity.ts  # 玩家区服历史
└── dto/
    ├── server-selection.dto.ts
    ├── character-session.dto.ts
    └── server-list.dto.ts

// 核心服务实现
@Injectable()
export class ServerSelectionService {
  constructor(
    private jwtService: JwtService,
    private characterSessionService: CharacterSessionService,
    private serverRepository: ServerRepository,
    private playerHistoryService: PlayerHistoryService
  ) {}

  // 获取玩家可用区服列表
  async getAvailableServers(accountId: string): Promise<ServerListDto> {
    const allServers = await this.serverRepository.findActiveServers();
    const playerHistory = await this.playerHistoryService.getPlayerHistory(accountId);
    
    return {
      servers: allServers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        status: server.status,
        openTime: server.openTime,
        playerCount: server.currentPlayerCount,
        maxPlayerCount: server.maxPlayerCount,
        isNew: this.isNewServer(server.openTime),
        hasCharacter: playerHistory.some(h => h.serverId === server.id),
        lastPlayTime: playerHistory.find(h => h.serverId === server.id)?.lastPlayTime,
        recommendationScore: this.calculateRecommendationScore(server, playerHistory),
      })),
      lastServerId: playerHistory[0]?.serverId, // 最后游戏的区服
      totalServers: allServers.length,
    };
  }

  // 进入区服并创建角色会话
  async enterServer(accountId: string, serverId: string, characterId?: string): Promise<CharacterAuthResult> {
    // 1. 验证区服状态
    const server = await this.serverRepository.findById(serverId);
    if (!server || server.status !== 'active') {
      throw new BadRequestException('区服不可用');
    }

    // 2. 检查区服容量
    if (server.currentPlayerCount >= server.maxPlayerCount) {
      throw new BadRequestException('区服已满');
    }

    // 3. 获取或创建角色信息
    let character;
    if (characterId) {
      character = await this.getCharacterInServer(accountId, serverId, characterId);
    } else {
      character = await this.getDefaultCharacterInServer(accountId, serverId);
    }

    // 4. 创建角色会话
    const session = await this.characterSessionService.createCharacterSession({
      accountId,
      characterId: character.id,
      serverId,
      serverName: server.name,
      characterName: character.name,
    });

    // 5. 生成角色级JWT Token
    const characterToken = await this.generateCharacterToken({
      accountId,
      characterId: character.id,
      serverId,
      sessionId: session.id,
    });

    // 6. 更新玩家历史记录
    await this.playerHistoryService.updatePlayerHistory(accountId, serverId);

    return {
      characterToken,
      character,
      server,
      session,
    };
  }

  // 生成角色级JWT Token
  private async generateCharacterToken(payload: CharacterTokenPayload): Promise<string> {
    const tokenPayload = {
      sub: payload.accountId,           // 主体仍然是账号ID
      characterId: payload.characterId, // 角色ID
      serverId: payload.serverId,       // 区服ID
      sessionId: payload.sessionId,     // 会话ID
      type: 'character',                // Token类型
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600 * 8, // 8小时有效期
    };

    return this.jwtService.sign(tokenPayload);
  }
}
```

### **2. 网关服务扩展 (Gateway Service Enhancement)**

```typescript
// apps/gateway/src/domain/server-routing/ (新增模块)
├── server-routing.module.ts
├── services/
│   ├── server-router.service.ts         # 区服路由服务
│   ├── character-auth.service.ts        # 角色认证服务
│   └── cross-server-proxy.service.ts    # 跨服代理服务
├── middleware/
│   ├── character-auth.middleware.ts     # 角色认证中间件
│   └── server-routing.middleware.ts     # 区服路由中间件
└── guards/
    ├── character-auth.guard.ts          # 角色认证守卫
    └── server-access.guard.ts           # 区服访问守卫

// 角色认证中间件
@Injectable()
export class CharacterAuthMiddleware implements NestMiddleware {
  constructor(
    private jwtService: JwtService,
    private characterAuthService: CharacterAuthService,
    private logger: Logger
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // 1. 提取Token
      const token = this.extractToken(req);
      if (!token) {
        return next(); // 允许匿名访问，由具体的Guard控制
      }

      // 2. 验证Token
      const payload = this.jwtService.verify(token);
      
      // 3. 区分Token类型
      if (payload.type === 'character') {
        // 角色级Token：验证角色会话
        const characterContext = await this.characterAuthService.validateCharacterToken(payload);
        req.user = characterContext;
        req.serverId = payload.serverId;
        req.characterId = payload.characterId;
      } else {
        // 账号级Token：只设置账号信息
        req.user = {
          accountId: payload.sub,
          username: payload.username,
          email: payload.email,
          roles: payload.roles,
          authenticated: true,
          type: 'account'
        };
      }

      next();
    } catch (error) {
      this.logger.warn(`Character authentication failed: ${error.message}`);
      next(); // 继续执行，让Guard处理认证失败
    }
  }
}

// 区服路由中间件
@Injectable()
export class ServerRoutingMiddleware implements NestMiddleware {
  constructor(
    private serverRouter: ServerRouterService,
    private logger: Logger
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // 1. 提取区服信息
    const serverId = req.serverId || req.headers['x-server-id'] || req.params.serverId;
    
    if (!serverId) {
      return next(); // 非区服相关请求，直接通过
    }

    // 2. 设置路由目标
    const targetService = await this.serverRouter.resolveTargetService(req.path, serverId);
    if (targetService) {
      req.targetService = targetService;
      req.targetServerId = serverId;
    }

    next();
  }
}
```

### **3. 微服务路由扩展**

```typescript
// apps/gateway/src/domain/server-routing/services/server-router.service.ts
@Injectable()
export class ServerRouterService {
  constructor(
    private configService: ConfigService,
    private serviceDiscovery: ServiceDiscoveryService
  ) {}

  // 解析目标服务
  async resolveTargetService(path: string, serverId: string): Promise<ServiceTarget | null> {
    // 1. 解析服务名称
    const serviceName = this.extractServiceName(path);
    if (!serviceName) return null;

    // 2. 检查是否为跨服请求
    if (this.isCrossServerPath(path)) {
      return this.resolveCrossServerTarget(serviceName);
    }

    // 3. 解析区服特定的服务实例
    return this.resolveServerSpecificTarget(serviceName, serverId);
  }

  // 解析区服特定的服务实例
  private async resolveServerSpecificTarget(serviceName: string, serverId: string): Promise<ServiceTarget> {
    // 方案A：单实例多数据库模式（推荐）
    // 所有区服共享同一个微服务实例，通过数据库路由区分
    const serviceInstance = await this.serviceDiscovery.findService(serviceName);
    
    return {
      serviceName,
      serviceUrl: serviceInstance.url,
      serverId,
      routingMode: 'database-routing', // 数据库路由模式
      headers: {
        'X-Server-ID': serverId,
        'X-Service-TOKEN': this.configService.get('SERVICE_SECRET'),
      }
    };

    // 方案B：多实例模式（可选）
    // 每个区服有独立的微服务实例
    // const serverServiceInstance = await this.serviceDiscovery.findService(`${serviceName}-${serverId}`);
    // return {
    //   serviceName: `${serviceName}-${serverId}`,
    //   serviceUrl: serverServiceInstance.url,
    //   serverId,
    //   routingMode: 'instance-routing',
    // };
  }

  // 路径解析
  private extractServiceName(path: string): string | null {
    // /api/character/profile -> character
    // /api/hero/list -> hero
    // /api/match/history -> match
    const match = path.match(/^\/api\/([^\/]+)/);
    return match ? match[1] : null;
  }

  // 跨服路径检测
  private isCrossServerPath(path: string): boolean {
    return path.includes('/cross-server/') || 
           path.includes('/global/') ||
           path.includes('/ranking/global');
  }
}
```

## 🗄️ 数据库架构设计

### **1. 认证服务数据库扩展**

```typescript
// 区服信息表
@Entity('servers')
export class Server {
  @PrimaryColumn()
  id: string; // server_001, server_002

  @Column()
  name: string; // 区服名称

  @Column({ type: 'enum', enum: ['new', 'active', 'maintenance', 'merging', 'closed'] })
  status: ServerStatus;

  @Column()
  openTime: Date; // 开服时间

  @Column()
  maxPlayerCount: number; // 最大玩家数

  @Column()
  currentPlayerCount: number; // 当前玩家数

  @Column({ type: 'json', nullable: true })
  config: ServerConfig; // 区服配置

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// 角色会话表
@Entity('character_sessions')
export class CharacterSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  accountId: string; // 关联账号

  @Column()
  characterId: string; // 角色ID

  @Column()
  serverId: string; // 区服ID

  @Column()
  sessionToken: string; // 会话令牌

  @Column()
  ipAddress: string; // 登录IP

  @Column()
  userAgent: string; // 用户代理

  @Column()
  lastActivity: Date; // 最后活动时间

  @Column()
  expiresAt: Date; // 过期时间

  @Column({ default: true })
  active: boolean; // 是否活跃

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// 玩家区服历史表
@Entity('player_server_history')
export class PlayerServerHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  accountId: string; // 账号ID

  @Column()
  serverId: string; // 区服ID

  @Column({ nullable: true })
  characterId: string; // 主要角色ID

  @Column({ nullable: true })
  characterName: string; // 角色名称

  @Column()
  firstPlayTime: Date; // 首次游戏时间

  @Column()
  lastPlayTime: Date; // 最后游戏时间

  @Column({ default: 0 })
  totalPlayTime: number; // 总游戏时长（秒）

  @Column({ default: 1 })
  characterLevel: number; // 角色等级

  @Column({ type: 'json', nullable: true })
  gameProgress: any; // 游戏进度快照

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Index(['accountId', 'lastPlayTime'])
  accountLastPlayIndex: any;
}
```

### **2. 游戏微服务数据库路由**

```typescript
// libs/common/src/database/server-aware-database.service.ts
@Injectable()
export class ServerAwareDatabaseService {
  private connections = new Map<string, Connection>();

  constructor(
    private configService: ConfigService,
    private logger: Logger
  ) {}

  // 获取当前请求的数据库连接
  getCurrentConnection(req?: Request): Connection {
    const serverId = this.extractServerId(req);
    
    if (!serverId) {
      throw new BadRequestException('Server ID is required');
    }

    return this.getServerConnection(serverId);
  }

  // 获取指定区服的数据库连接
  getServerConnection(serverId: string): Connection {
    if (!this.connections.has(serverId)) {
      this.connections.set(serverId, this.createServerConnection(serverId));
    }

    return this.connections.get(serverId)!;
  }

  // 创建区服数据库连接
  private createServerConnection(serverId: string): Connection {
    const baseConfig = this.configService.get('database');
    const serverDbName = `${baseConfig.database}_${serverId}`;

    return createConnection({
      ...baseConfig,
      name: serverId, // 连接名称
      database: serverDbName, // 区服特定数据库
      entities: [/* 游戏实体列表 */],
      synchronize: false, // 生产环境关闭
      logging: this.configService.get('NODE_ENV') === 'development',
    });
  }

  // 从请求中提取区服ID
  private extractServerId(req?: Request): string | null {
    if (!req) return null;

    return req.headers['x-server-id'] as string ||
           req.params?.serverId ||
           req.query?.serverId as string ||
           null;
  }
}

// 使用示例：在Repository中使用
@Injectable()
export class CharacterRepository {
  constructor(
    private serverDb: ServerAwareDatabaseService
  ) {}

  async findById(id: string, req?: Request): Promise<Character> {
    const connection = this.serverDb.getCurrentConnection(req);
    const repository = connection.getRepository(Character);
    return repository.findOne({ where: { id } });
  }

  async create(characterData: CreateCharacterDto, req?: Request): Promise<Character> {
    const connection = this.serverDb.getCurrentConnection(req);
    const repository = connection.getRepository(Character);
    const character = repository.create(characterData);
    return repository.save(character);
  }
}
```

## 🔄 跨服功能实现

### **1. 跨服数据访问服务**

```typescript
// libs/common/src/cross-server/cross-server.service.ts
@Injectable()
export class CrossServerService {
  constructor(
    private serverDb: ServerAwareDatabaseService,
    private redisService: RedisService,
    private logger: Logger
  ) {}

  // 跨服排行榜
  async getGlobalRanking(category: string, limit: number = 100): Promise<GlobalRankingItem[]> {
    const cacheKey = `global:ranking:${category}`;
    
    // 尝试从缓存获取
    const cached = await this.redisService.get(cacheKey, 'global');
    if (cached) {
      return JSON.parse(cached);
    }

    // 获取所有活跃区服
    const activeServers = await this.getActiveServers();
    const allRankings: RankingItem[] = [];

    // 并行获取各区服排行榜
    const serverRankings = await Promise.all(
      activeServers.map(async (server) => {
        try {
          const connection = this.serverDb.getServerConnection(server.id);
          const repository = connection.getRepository(RankingEntity);
          return await repository.find({
            where: { category },
            order: { score: 'DESC' },
            take: limit,
          });
        } catch (error) {
          this.logger.warn(`Failed to get ranking from server ${server.id}: ${error.message}`);
          return [];
        }
      })
    );

    // 合并并排序
    serverRankings.forEach((rankings, index) => {
      rankings.forEach(ranking => {
        allRankings.push({
          ...ranking,
          serverId: activeServers[index].id,
          serverName: activeServers[index].name,
        });
      });
    });

    // 全局排序
    const globalRanking = allRankings
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map((item, index) => ({
        ...item,
        globalRank: index + 1,
      }));

    // 缓存结果
    await this.redisService.set(cacheKey, JSON.stringify(globalRanking), 300, 'global'); // 5分钟缓存

    return globalRanking;
  }

  // 跨服战斗匹配
  async findCrossServerOpponent(playerId: string, serverId: string, powerRange: [number, number]): Promise<CrossServerOpponent | null> {
    const otherServers = await this.getOtherActiveServers(serverId);
    
    for (const server of otherServers) {
      try {
        const connection = this.serverDb.getServerConnection(server.id);
        const repository = connection.getRepository(PlayerEntity);
        
        const opponent = await repository.findOne({
          where: {
            power: Between(powerRange[0], powerRange[1]),
            status: 'online',
            id: Not(playerId), // 排除自己
          },
          order: { lastActiveTime: 'DESC' },
        });

        if (opponent) {
          return {
            ...opponent,
            serverId: server.id,
            serverName: server.name,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to find opponent in server ${server.id}: ${error.message}`);
      }
    }

    return null;
  }

  // 获取活跃区服列表
  private async getActiveServers(): Promise<Server[]> {
    const cacheKey = 'global:servers:active';
    const cached = await this.redisService.get(cacheKey, 'global');
    
    if (cached) {
      return JSON.parse(cached);
    }

    // 从认证服务获取（需要调用认证服务的API）
    const servers = await this.authService.getActiveServers();
    await this.redisService.set(cacheKey, JSON.stringify(servers), 60, 'global'); // 1分钟缓存
    
    return servers;
  }
}
```

---

## 🔀 合服功能实现

### **1. 合服数据迁移服务**

```typescript
// apps/auth/src/domain/server-merge/server-merge.service.ts
@Injectable()
export class ServerMergeService {
  constructor(
    private serverDb: ServerAwareDatabaseService,
    private authService: AuthService,
    private crossServerService: CrossServerService,
    private logger: Logger
  ) {}

  // 合服主流程
  async mergeServers(mergeRequest: ServerMergeRequest): Promise<ServerMergeResult> {
    const { sourceServerIds, targetServerId, mergeStrategy } = mergeRequest;

    this.logger.log(`开始合服操作: ${sourceServerIds.join(',')} -> ${targetServerId}`);

    try {
      // 1. 预检查
      await this.validateMergeRequest(sourceServerIds, targetServerId);

      // 2. 设置源服务器为合服状态
      await this.setServersStatus(sourceServerIds, 'merging');
      await this.setServersStatus([targetServerId], 'merging');

      // 3. 创建数据备份
      const backupInfo = await this.createDataBackups(sourceServerIds, targetServerId);

      // 4. 执行数据迁移
      const migrationResult = await this.performDataMigration(sourceServerIds, targetServerId, mergeStrategy);

      // 5. 处理数据冲突
      const conflictResolution = await this.resolveDataConflicts(migrationResult.conflicts, targetServerId);

      // 6. 更新玩家历史记录
      await this.updatePlayerHistoryForMerge(sourceServerIds, targetServerId);

      // 7. 发放合服补偿
      await this.distributeCompensation(migrationResult.affectedPlayers, targetServerId);

      // 8. 验证数据完整性
      await this.validateDataIntegrity(targetServerId);

      // 9. 设置目标服务器为活跃状态
      await this.setServersStatus([targetServerId], 'active');

      // 10. 关闭源服务器
      await this.setServersStatus(sourceServerIds, 'closed');

      this.logger.log(`合服操作完成: ${sourceServerIds.join(',')} -> ${targetServerId}`);

      return {
        success: true,
        targetServerId,
        migratedPlayers: migrationResult.migratedPlayers,
        resolvedConflicts: conflictResolution.resolvedConflicts,
        compensationDistributed: migrationResult.affectedPlayers.length,
        backupInfo,
      };

    } catch (error) {
      this.logger.error(`合服操作失败: ${error.message}`);

      // 回滚操作
      await this.rollbackMerge(sourceServerIds, targetServerId);

      throw new InternalServerErrorException(`合服失败: ${error.message}`);
    }
  }

  // 数据迁移核心逻辑
  private async performDataMigration(sourceServerIds: string[], targetServerId: string, strategy: MergeStrategy): Promise<MigrationResult> {
    const result: MigrationResult = {
      migratedPlayers: [],
      conflicts: [],
      affectedPlayers: [],
    };

    const targetConnection = this.serverDb.getServerConnection(targetServerId);

    for (const sourceServerId of sourceServerIds) {
      const sourceConnection = this.serverDb.getServerConnection(sourceServerId);

      // 迁移玩家数据
      const players = await this.migratePlayerData(sourceConnection, targetConnection, sourceServerId, targetServerId);
      result.migratedPlayers.push(...players);

      // 迁移公会数据
      const guildConflicts = await this.migrateGuildData(sourceConnection, targetConnection, sourceServerId, targetServerId);
      result.conflicts.push(...guildConflicts);

      // 迁移排行榜数据
      await this.mergeRankingData(sourceConnection, targetConnection, sourceServerId, targetServerId);

      // 迁移经济数据
      await this.migrateEconomyData(sourceConnection, targetConnection, sourceServerId, targetServerId);
    }

    return result;
  }

  // 玩家数据迁移
  private async migratePlayerData(sourceConn: Connection, targetConn: Connection, sourceServerId: string, targetServerId: string): Promise<MigratedPlayer[]> {
    const sourcePlayerRepo = sourceConn.getRepository(PlayerEntity);
    const targetPlayerRepo = targetConn.getRepository(PlayerEntity);

    const sourcePlayers = await sourcePlayerRepo.find();
    const migratedPlayers: MigratedPlayer[] = [];

    for (const player of sourcePlayers) {
      // 检查目标服务器是否已有同名玩家
      const existingPlayer = await targetPlayerRepo.findOne({ where: { name: player.name } });

      if (existingPlayer) {
        // 处理重名冲突：添加源服务器后缀
        player.name = `${player.name}_S${sourceServerId}`;

        // 记录冲突
        this.conflicts.push({
          type: 'player_name_conflict',
          sourceData: { playerId: player.id, originalName: player.name },
          targetData: { playerId: existingPlayer.id, name: existingPlayer.name },
          resolution: 'rename_source_player',
        });
      }

      // 更新玩家的服务器归属
      player.serverId = targetServerId;
      player.mergedFrom = sourceServerId;
      player.mergedAt = new Date();

      // 保存到目标服务器
      await targetPlayerRepo.save(player);

      migratedPlayers.push({
        playerId: player.id,
        accountId: player.accountId,
        originalName: player.name,
        newName: player.name,
        sourceServerId,
        targetServerId,
      });
    }

    return migratedPlayers;
  }

  // 数据冲突解决
  private async resolveDataConflicts(conflicts: DataConflict[], targetServerId: string): Promise<ConflictResolution> {
    const resolution: ConflictResolution = {
      resolvedConflicts: [],
      failedConflicts: [],
    };

    for (const conflict of conflicts) {
      try {
        switch (conflict.type) {
          case 'player_name_conflict':
            await this.resolvePlayerNameConflict(conflict, targetServerId);
            break;
          case 'guild_name_conflict':
            await this.resolveGuildNameConflict(conflict, targetServerId);
            break;
          case 'ranking_conflict':
            await this.resolveRankingConflict(conflict, targetServerId);
            break;
        }

        resolution.resolvedConflicts.push(conflict);
      } catch (error) {
        this.logger.error(`解决冲突失败: ${conflict.type}, ${error.message}`);
        resolution.failedConflicts.push({ conflict, error: error.message });
      }
    }

    return resolution;
  }
}
```

### **2. 合服补偿系统**

```typescript
// apps/auth/src/domain/server-merge/compensation.service.ts
@Injectable()
export class CompensationService {
  constructor(
    private serverDb: ServerAwareDatabaseService,
    private economyService: EconomyService,
    private logger: Logger
  ) {}

  // 计算合服补偿
  async calculateCompensation(playerId: string, mergeContext: MergeContext): Promise<CompensationPackage> {
    const compensation: CompensationPackage = {
      playerId,
      items: [],
      currency: {},
      titles: [],
      reason: 'server_merge',
      calculatedAt: new Date(),
    };

    // 1. 基础合服补偿
    compensation.currency.gold = 50000; // 基础金币补偿
    compensation.items.push({
      itemId: 'merge_commemoration_card',
      quantity: 1,
      reason: 'merge_participation',
    });

    // 2. 排名变化补偿
    if (mergeContext.rankingChanged) {
      const rankingCompensation = this.calculateRankingCompensation(mergeContext.oldRank, mergeContext.newRank);
      compensation.items.push(...rankingCompensation.items);
      compensation.currency.diamond = (compensation.currency.diamond || 0) + rankingCompensation.diamonds;
    }

    // 3. 用户名变更补偿
    if (mergeContext.nameChanged) {
      compensation.items.push({
        itemId: 'name_change_card',
        quantity: 3, // 3张改名卡
        reason: 'name_conflict_resolution',
      });
    }

    // 4. 公会解散补偿
    if (mergeContext.guildDisbanded) {
      compensation.currency.gold = (compensation.currency.gold || 0) + 100000;
      compensation.items.push({
        itemId: 'guild_creation_token',
        quantity: 1,
        reason: 'guild_disbanded',
      });
    }

    // 5. VIP等级补偿
    if (mergeContext.playerLevel >= 30) {
      compensation.items.push({
        itemId: 'experience_boost',
        quantity: 5,
        reason: 'high_level_compensation',
      });
    }

    // 6. 特殊称号
    compensation.titles.push('merge_veteran'); // 合服老兵称号

    return compensation;
  }

  // 发放补偿
  async distributeCompensation(compensation: CompensationPackage, serverId: string): Promise<void> {
    const connection = this.serverDb.getServerConnection(serverId);

    try {
      // 发放货币
      for (const [currencyType, amount] of Object.entries(compensation.currency)) {
        await this.economyService.addCurrency(compensation.playerId, currencyType, amount, serverId);
      }

      // 发放物品
      for (const item of compensation.items) {
        await this.economyService.addItem(compensation.playerId, item.itemId, item.quantity, serverId);
      }

      // 发放称号
      for (const titleId of compensation.titles) {
        await this.economyService.grantTitle(compensation.playerId, titleId, serverId);
      }

      // 记录补偿日志
      const compensationRepo = connection.getRepository(CompensationLogEntity);
      await compensationRepo.save({
        playerId: compensation.playerId,
        serverId,
        compensationType: 'server_merge',
        compensationData: compensation,
        distributedAt: new Date(),
      });

      this.logger.log(`合服补偿发放完成: 玩家=${compensation.playerId}, 服务器=${serverId}`);

    } catch (error) {
      this.logger.error(`合服补偿发放失败: 玩家=${compensation.playerId}, 错误=${error.message}`);
      throw error;
    }
  }
}
```

## 🚀 部署与配置

### **1. 环境变量配置**

```bash
# .env - 基础配置
# 多服务器功能开关
MULTI_SERVER_ENABLED=true

# 认证服务配置
AUTH_JWT_SECRET=your-jwt-secret
AUTH_JWT_EXPIRES_IN=8h
CHARACTER_JWT_EXPIRES_IN=8h

# 数据库配置模板
DATABASE_HOST=localhost
DATABASE_PORT=27017
DATABASE_USERNAME=admin
DATABASE_PASSWORD=password
DATABASE_NAME_TEMPLATE=football_manager_{serverId}

# Redis配置（已实现前缀隔离）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=password

# 服务发现配置
SERVICE_SECRET=your-service-secret
GATEWAY_PORT=3000
AUTH_PORT=3001

# 区服配置
DEFAULT_SERVER_ID=server_001
MAX_SERVERS=100
SERVER_CAPACITY_DEFAULT=10000
```

### **2. 数据库初始化脚本**

```typescript
// scripts/init-multi-server.ts
import { NestFactory } from '@nestjs/core';
import { AuthModule } from '../apps/auth/src/app.module';
import { ServerService } from '../apps/auth/src/domain/server-auth/services/server.service';

async function initializeMultiServer() {
  const app = await NestFactory.createApplicationContext(AuthModule);
  const serverService = app.get(ServerService);

  // 创建默认区服
  const defaultServers = [
    {
      id: 'server_001',
      name: '新手村',
      status: 'active',
      maxPlayerCount: 10000,
      openTime: new Date(),
    },
    {
      id: 'server_002',
      name: '勇者大陆',
      status: 'active',
      maxPlayerCount: 10000,
      openTime: new Date(),
    },
  ];

  for (const serverConfig of defaultServers) {
    await serverService.createServer(serverConfig);
    console.log(`创建区服: ${serverConfig.name} (${serverConfig.id})`);
  }

  await app.close();
  console.log('多服务器初始化完成');
}

initializeMultiServer().catch(console.error);
```

### **3. API接口文档**

```typescript
// 区服相关API
/**
 * 获取区服列表
 * GET /api/auth/servers
 * Headers: Authorization: Bearer <account_token>
 */
interface ServerListResponse {
  servers: {
    serverId: string;
    serverName: string;
    status: 'new' | 'active' | 'maintenance' | 'merging' | 'closed';
    openTime: string;
    playerCount: number;
    maxPlayerCount: number;
    isNew: boolean;
    hasCharacter: boolean;
    lastPlayTime?: string;
    recommendationScore: number;
  }[];
  lastServerId?: string;
  totalServers: number;
}

/**
 * 进入区服
 * POST /api/auth/servers/{serverId}/enter
 * Headers: Authorization: Bearer <account_token>
 */
interface EnterServerRequest {
  characterId?: string; // 可选，指定角色ID
}

interface EnterServerResponse {
  characterToken: string; // 角色级JWT Token
  character: {
    id: string;
    name: string;
    level: number;
    // ... 其他角色信息
  };
  server: {
    id: string;
    name: string;
    status: string;
  };
  session: {
    id: string;
    expiresAt: string;
  };
}

/**
 * 游戏业务API（需要角色认证）
 * GET /api/character/profile
 * Headers:
 *   Authorization: Bearer <character_token>
 *   X-Server-ID: server_001
 */

/**
 * 跨服API
 * GET /api/cross-server/ranking/global
 * Headers: Authorization: Bearer <character_token>
 */
```

## 📊 实施计划

### **阶段一：认证服务扩展（2周）**
1. 扩展Auth Service，添加区服管理模块
2. 实现双层认证机制（账号 + 角色）
3. 创建区服相关数据表
4. 实现区服选择和角色会话API

### **阶段二：网关服务扩展（1周）**
1. 扩展Gateway Service，添加区服路由模块
2. 实现角色认证中间件
3. 实现区服路由中间件
4. 测试认证和路由功能

### **阶段三：微服务适配（2周）**
1. 实现数据库路由服务
2. 修改现有微服务支持区服路由
3. 实现跨服数据访问服务
4. 测试数据隔离功能

### **阶段四：跨服功能（1周）**
1. 实现跨服排行榜
2. 实现跨服战斗匹配
3. 实现跨服活动系统
4. 测试跨服功能

### **阶段五：合服功能（2周）**
1. 实现合服数据迁移
2. 实现冲突解决机制
3. 实现补偿系统
4. 测试合服流程

### **总计：8周完成**

---

> **核心设计理念**: 本方案严格遵循原始需求，在现有认证架构基础上进行**最小化扩展**，通过**双层认证机制**（账号认证 + 角色认证）和**数据库路由**实现分区分服功能，确保与现有系统的完美兼容。

> **关键优势**:
> - ✅ **需求匹配**: 100%符合原始核心需求
> - ✅ **架构兼容**: 基于现有认证架构扩展，无破坏性改动
> - ✅ **实施可行**: 8周完成，风险可控
> - ✅ **功能完整**: 涵盖数据隔离、跨服、合服等所有功能
