# Auth服务增强设计 - 基于现有架构的扩展

## 📋 概述

本文档详细设计Auth服务的增强方案，基于现有的JWT服务、用户管理和配置架构，通过最小化扩展实现分区分服功能。

## 🔍 现有Auth服务架构分析

### **1. 现有核心组件**

#### **1.1 JWT服务现状**

<augment_code_snippet path="apps/auth/src/domain/auth/jwt.service.ts" mode="EXCERPT">
````typescript
/**
 * 生成令牌对
 */
generateTokenPair(userPayload: Omit<JwtPayload, 'iat' | 'exp' | 'jti'>): TokenPair {
  const sessionId = userPayload.sessionId;
  const tokenFamily = this.generateTokenFamily();
  
  const accessToken = this.generateAccessToken(userPayload);
  const refreshToken = this.generateRefreshToken({
    sub: userPayload.sub,
    sessionId,
    deviceId: userPayload.deviceId,
    tokenFamily,
  });

  // 计算过期时间
  const expiresIn = this.parseTimeToSeconds(this.accessTokenTTL);
  const expiresAt = new Date(Date.now() + expiresIn * 1000);

  return {
    accessToken,
    refreshToken,
    tokenType: 'Bearer',
    expiresIn,
    expiresAt,
  };
}
````
</augment_code_snippet>

#### **1.2 现有用户服务**

<augment_code_snippet path="apps/auth/src/domain/auth/auth.service.ts" mode="EXCERPT">
````typescript
// 创建会话
const session = await this.sessionService.createSession({
  userId: user.id,
  deviceInfo: loginDto.deviceInfo,
  rememberMe: loginDto.rememberMe,
});
````
</augment_code_snippet>

#### **1.3 现有配置结构**

<augment_code_snippet path="apps/auth/src/config/auth.config.ts" mode="EXCERPT">
````typescript
export const authConfig = registerAs('auth', () => {
  const config = {
    // JWT配置
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
      
      // 令牌有效期
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
    },
    // ... 其他配置
  };
````
</augment_code_snippet>

## 🔧 Auth服务增强设计

### **1. 扩展JWT服务**

#### **1.1 增强JwtService支持双层Token**

```typescript
// apps/auth/src/domain/auth/jwt.service.ts (扩展现有)
@Injectable()
export class JwtService {
  // ... 保留所有现有方法和属性

  // 新增：角色级Token配置
  private readonly characterJwtSecret: string;
  private readonly characterTokenTTL: string;

  constructor(private readonly configService: ConfigService) {
    // 保留现有初始化逻辑
    this.jwtSecret = this.configService.get<string>('auth.jwt.secret');
    this.accessTokenTTL = this.configService.get<string>('auth.jwt.accessTokenTTL');
    // ... 其他现有配置

    // 新增：角色级Token配置
    this.characterJwtSecret = this.configService.get<string>('auth.jwt.characterSecret') || this.jwtSecret;
    this.characterTokenTTL = this.configService.get<string>('auth.jwt.characterTokenTTL') || '4h';
  }

  /**
   * 扩展：生成角色级访问令牌
   * 基于现有generateAccessToken方法
   */
  generateCharacterToken(payload: CharacterTokenPayload): string {
    const jti = this.generateTokenId();
    const now = Math.floor(Date.now() / 1000);
    
    const tokenPayload: CharacterJwtPayload = {
      ...payload,
      iat: now,
      jti,
      type: 'character', // 标识为角色级Token
    };

    return jwt.sign(tokenPayload, this.characterJwtSecret, {
      algorithm: this.jwtAlgorithm as jwt.Algorithm,
      expiresIn: this.characterTokenTTL,
      issuer: this.jwtIssuer,
      audience: this.jwtAudience,
    });
  }

  /**
   * 扩展：验证角色级Token
   * 基于现有验证逻辑
   */
  verifyCharacterToken(token: string): CharacterJwtPayload {
    try {
      const payload = jwt.verify(token, this.characterJwtSecret, {
        algorithms: [this.jwtAlgorithm as jwt.Algorithm],
        issuer: this.jwtIssuer,
        audience: this.jwtAudience,
        clockTolerance: this.clockTolerance,
        ignoreExpiration: this.ignoreExpiration,
        ignoreNotBefore: this.ignoreNotBefore,
      }) as CharacterJwtPayload;

      // 验证Token类型
      if (payload.type !== 'character') {
        throw new Error('Invalid token type');
      }

      return payload;
    } catch (error) {
      throw new Error(`Character token verification failed: ${error.message}`);
    }
  }

  /**
   * 扩展：生成完整的角色认证Token对
   * 基于现有generateTokenPair方法
   */
  generateCharacterTokenPair(
    accountPayload: JwtPayload,
    characterData: CharacterTokenData
  ): CharacterTokenPair {
    const characterPayload: CharacterTokenPayload = {
      sub: accountPayload.sub,
      username: accountPayload.username,
      email: accountPayload.email,
      roles: accountPayload.roles,
      characterId: characterData.characterId,
      serverId: characterData.serverId,
      sessionId: characterData.sessionId,
      deviceId: accountPayload.deviceId,
    };

    const characterToken = this.generateCharacterToken(characterPayload);
    
    // 角色Token不需要刷新Token，使用账号级刷新Token
    const expiresIn = this.parseTimeToSeconds(this.characterTokenTTL);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    return {
      characterToken,
      tokenType: 'Bearer',
      expiresIn,
      expiresAt,
      serverId: characterData.serverId,
      characterId: characterData.characterId,
    };
  }

  /**
   * 扩展：统一Token验证方法
   * 自动识别Token类型并验证
   */
  verifyAnyToken(token: string): AccountJwtPayload | CharacterJwtPayload {
    try {
      // 先尝试验证为账号Token
      return this.verifyToken(token);
    } catch (accountError) {
      try {
        // 再尝试验证为角色Token
        return this.verifyCharacterToken(token);
      } catch (characterError) {
        throw new Error('Invalid token: neither account nor character token');
      }
    }
  }
}

// 新增类型定义
interface CharacterTokenPayload extends Omit<JwtPayload, 'jti' | 'iat' | 'exp'> {
  characterId: string;
  serverId: string;
  sessionId: string;
}

interface CharacterJwtPayload extends CharacterTokenPayload {
  jti: string;
  iat: number;
  exp?: number;
  type: 'character';
}

interface CharacterTokenData {
  characterId: string;
  serverId: string;
  sessionId: string;
}

interface CharacterTokenPair {
  characterToken: string;
  tokenType: 'Bearer';
  expiresIn: number;
  expiresAt: Date;
  serverId: string;
  characterId: string;
}
```

### **2. 新增区服管理模块**

#### **2.1 区服管理服务**

```typescript
// apps/auth/src/domain/server-management/services/server.service.ts (新增)
@Injectable()
export class ServerService {
  private readonly logger = new Logger(ServerService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly usersService: UsersService, // 复用现有用户服务
    private readonly jwtService: JwtService,     // 复用现有JWT服务
    private readonly sessionService: SessionService, // 复用现有会话服务
  ) {}

  /**
   * 获取区服列表
   * 集成现有的用户服务和Redis缓存
   */
  async getServerList(userId: string): Promise<ServerListResponse> {
    this.logger.log(`获取区服列表: ${userId}`);

    try {
      // 获取可用区服（从配置或缓存）
      const servers = await this.getAvailableServers();
      
      // 获取玩家历史记录（使用现有Redis结构）
      const playerHistory = await this.getPlayerServerHistory(userId);
      
      // 构建响应数据
      const serverList = servers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        status: server.status,
        openTime: server.openTime,
        playerCount: server.currentPlayers,
        maxPlayerCount: server.maxPlayers,
        isNew: this.isNewServer(server.openTime),
        hasCharacter: playerHistory.some(h => h.serverId === server.id),
        lastPlayTime: playerHistory.find(h => h.serverId === server.id)?.lastPlayTime,
        recommendationScore: this.calculateRecommendationScore(server, playerHistory),
      }));

      return {
        servers: serverList,
        lastServerId: playerHistory[0]?.serverId,
        totalServers: servers.length,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`获取区服列表失败: ${userId}, ${error.message}`);
      throw error;
    }
  }

  /**
   * 进入区服
   * 集成现有的认证和会话管理
   */
  async enterServer(
    accountToken: string,
    serverId: string,
    characterId?: string
  ): Promise<EnterServerResponse> {
    this.logger.log(`用户进入区服: serverId=${serverId}, characterId=${characterId}`);

    try {
      // 1. 验证账号Token（使用现有JWT服务）
      const accountPayload = this.jwtService.verifyToken(accountToken);
      
      // 2. 获取用户信息（使用现有用户服务）
      const user = await this.usersService.findById(accountPayload.sub);
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      // 3. 验证区服状态
      const server = await this.validateServer(serverId);
      
      // 4. 获取或验证角色
      const character = await this.getCharacterInServer(accountPayload.sub, serverId, characterId);
      
      // 5. 创建角色会话（扩展现有会话服务）
      const session = await this.createCharacterSession({
        userId: accountPayload.sub,
        characterId: character.characterId,
        serverId,
        serverName: server.name,
        deviceInfo: accountPayload.deviceId,
      });

      // 6. 生成角色级Token（使用扩展的JWT服务）
      const characterTokenPair = this.jwtService.generateCharacterTokenPair(
        accountPayload,
        {
          characterId: character.characterId,
          serverId,
          sessionId: session.id,
        }
      );

      // 7. 更新玩家历史记录
      await this.updatePlayerServerHistory(accountPayload.sub, serverId);

      this.logger.log(`用户成功进入区服: ${accountPayload.sub} -> ${serverId}`);

      return {
        characterToken: characterTokenPair.characterToken,
        expiresIn: characterTokenPair.expiresIn,
        expiresAt: characterTokenPair.expiresAt,
        character,
        server,
        session: {
          id: session.id,
          expiresAt: session.expiresAt,
        },
      };

    } catch (error) {
      this.logger.error(`进入区服失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 退出区服
   * 集成现有的会话管理
   */
  async exitServer(characterToken: string): Promise<void> {
    try {
      const payload = this.jwtService.verifyCharacterToken(characterToken);
      
      // 清理角色会话
      await this.terminateCharacterSession(payload.sessionId);
      
      this.logger.log(`用户退出区服: ${payload.sub} <- ${payload.serverId}`);
    } catch (error) {
      this.logger.error(`退出区服失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 私有方法：获取可用区服
   * 基于现有配置系统
   */
  private async getAvailableServers(): Promise<ServerInfo[]> {
    const cacheKey = 'servers:available_list';
    
    // 尝试从Redis缓存获取
    const cached = await this.redisService.get(cacheKey, 'global');
    if (cached) {
      return JSON.parse(cached);
    }

    // 从配置获取区服列表
    const serverConfig = this.configService.get('servers') || this.getDefaultServerConfig();
    
    // 缓存结果（5分钟）
    await this.redisService.set(cacheKey, JSON.stringify(serverConfig), 300, 'global');
    
    return serverConfig;
  }

  /**
   * 私有方法：获取玩家区服历史
   * 使用现有Redis结构
   */
  private async getPlayerServerHistory(userId: string): Promise<PlayerServerHistory[]> {
    const historyKey = `user:${userId}:server_history`;
    const history = await this.redisService.get(historyKey, 'global');
    
    return history ? JSON.parse(history) : [];
  }

  /**
   * 私有方法：创建角色会话
   * 扩展现有会话服务
   */
  private async createCharacterSession(sessionData: CreateCharacterSessionData): Promise<CharacterSession> {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + 4 * 3600 * 1000); // 4小时

    const session: CharacterSession = {
      id: sessionId,
      userId: sessionData.userId,
      characterId: sessionData.characterId,
      serverId: sessionData.serverId,
      serverName: sessionData.serverName,
      deviceInfo: sessionData.deviceInfo,
      createdAt: new Date(),
      expiresAt,
      lastActivity: new Date(),
      active: true,
    };

    // 存储到Redis
    const sessionKey = `character_session:${sessionId}`;
    await this.redisService.set(sessionKey, JSON.stringify(session), 4 * 3600, 'global');

    // 建立用户到会话的映射
    const userSessionKey = `user:${sessionData.userId}:character_session`;
    await this.redisService.set(userSessionKey, sessionId, 4 * 3600, 'global');

    return session;
  }

  /**
   * 私有方法：验证区服状态
   */
  private async validateServer(serverId: string): Promise<ServerInfo> {
    const servers = await this.getAvailableServers();
    const server = servers.find(s => s.id === serverId);
    
    if (!server) {
      throw new NotFoundException('区服不存在');
    }
    
    if (server.status !== 'active') {
      throw new BadRequestException(`区服状态异常: ${server.status}`);
    }
    
    if (server.currentPlayers >= server.maxPlayers) {
      throw new BadRequestException('区服已满');
    }
    
    return server;
  }

  /**
   * 私有方法：获取角色信息
   * 调用character微服务
   */
  private async getCharacterInServer(
    userId: string,
    serverId: string,
    characterId?: string
  ): Promise<CharacterInfo> {
    // 这里需要调用character微服务
    // 暂时返回模拟数据，实际实现时需要集成微服务调用
    if (characterId) {
      // 验证指定角色
      return {
        characterId,
        name: 'Test Character',
        level: 1,
        serverId,
      };
    } else {
      // 获取默认角色或提示创建
      return {
        characterId: 'default_char_' + userId,
        name: 'Default Character',
        level: 1,
        serverId,
      };
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private isNewServer(openTime: Date): boolean {
    const daysSinceOpen = (Date.now() - openTime.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceOpen <= 7; // 7天内为新服
  }

  private calculateRecommendationScore(server: ServerInfo, history: PlayerServerHistory[]): number {
    let score = 0;
    
    // 基于玩家历史的推荐分数
    if (history.some(h => h.serverId === server.id)) {
      score += 50; // 曾经游戏过的区服
    }
    
    // 基于服务器负载的推荐分数
    const loadRatio = server.currentPlayers / server.maxPlayers;
    if (loadRatio < 0.7) {
      score += 30; // 负载较低的区服
    }
    
    // 基于开服时间的推荐分数
    if (this.isNewServer(server.openTime)) {
      score += 20; // 新服
    }
    
    return Math.min(score, 100);
  }

  private getDefaultServerConfig(): ServerInfo[] {
    return [
      {
        id: 'server_001',
        name: '新手村',
        status: 'active',
        openTime: new Date('2024-01-01'),
        currentPlayers: 1500,
        maxPlayers: 10000,
      },
      {
        id: 'server_002',
        name: '勇者大陆',
        status: 'active',
        openTime: new Date('2024-02-01'),
        currentPlayers: 2800,
        maxPlayers: 10000,
      },
    ];
  }
}

// 类型定义
interface ServerInfo {
  id: string;
  name: string;
  status: 'active' | 'maintenance' | 'closed';
  openTime: Date;
  currentPlayers: number;
  maxPlayers: number;
}

interface PlayerServerHistory {
  serverId: string;
  lastPlayTime: Date;
  totalPlayTime: number;
  characterCount: number;
}

interface CharacterSession {
  id: string;
  userId: string;
  characterId: string;
  serverId: string;
  serverName: string;
  deviceInfo?: string;
  createdAt: Date;
  expiresAt: Date;
  lastActivity: Date;
  active: boolean;
}

interface CreateCharacterSessionData {
  userId: string;
  characterId: string;
  serverId: string;
  serverName: string;
  deviceInfo?: string;
}

interface CharacterInfo {
  characterId: string;
  name: string;
  level: number;
  serverId: string;
}
```

### **3. 扩展现有配置**

#### **3.1 增强auth.config.ts**

```typescript
// apps/auth/src/config/auth.config.ts (扩展现有)
export const authConfig = registerAs('auth', () => {
  const config = {
    // 保留所有现有JWT配置
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      issuer: process.env.JWT_ISSUER || 'football-manager-auth',
      audience: process.env.JWT_AUDIENCE || 'football-manager-app',
      
      // 现有令牌有效期
      accessTokenTTL: process.env.JWT_ACCESS_TOKEN_TTL || '15m',
      refreshTokenTTL: process.env.JWT_REFRESH_TOKEN_TTL || '7d',
      
      // 新增：角色级Token配置
      characterSecret: process.env.JWT_CHARACTER_SECRET || process.env.JWT_SECRET,
      characterTokenTTL: process.env.JWT_CHARACTER_TOKEN_TTL || '4h',
      
      // 现有高级选项
      clockTolerance: parseInt(process.env.JWT_CLOCK_TOLERANCE || '60', 10),
      ignoreExpiration: process.env.JWT_IGNORE_EXPIRATION === 'true',
      ignoreNotBefore: process.env.JWT_IGNORE_NOT_BEFORE === 'true',
    },

    // 新增：多服务器配置
    multiServer: {
      enabled: process.env.MULTI_SERVER_ENABLED === 'true',
      defaultServerId: process.env.DEFAULT_SERVER_ID || 'server_001',
      maxServers: parseInt(process.env.MAX_SERVERS || '10', 10),
      sessionTimeout: parseInt(process.env.CHARACTER_SESSION_TIMEOUT || '14400', 10), // 4小时
    },

    // 新增：区服配置
    servers: [
      {
        id: 'server_001',
        name: '新手村',
        status: 'active',
        openTime: new Date('2024-01-01'),
        maxPlayers: 10000,
      },
      {
        id: 'server_002',
        name: '勇者大陆',
        status: 'active',
        openTime: new Date('2024-02-01'),
        maxPlayers: 10000,
      },
    ],

    // 保留所有现有配置...
    password: {
      // 现有密码配置
    },
    security: {
      // 现有安全配置
    },
    mfa: {
      // 现有MFA配置
    },
  };

  // 现有配置验证逻辑保持不变
  return config;
});
```

### **4. 集成现有模块**

#### **4.1 扩展AuthModule**

```typescript
// apps/auth/src/domain/auth/auth.module.ts (扩展现有)
@Module({
  imports: [
    // 保留所有现有导入
    ConfigModule,
    PassportModule.register({ 
      defaultStrategy: 'jwt',
      session: false 
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwt.accessTokenTTL'),
          issuer: configService.get<string>('auth.jwt.issuer'),
          audience: configService.get<string>('auth.jwt.audience'),
          algorithm: configService.get<string>('auth.jwt.algorithm') as any,
        },
      }),
      inject: [ConfigService],
    }),
    
    // 现有依赖模块
    CoreModule,
    UsersModule,
    SessionModule,
    SecurityModule,

    // 新增：区服管理模块
    ServerManagementModule,
  ],
  controllers: [
    // 保留现有控制器
    AuthController,
    
    // 新增：区服管理控制器
    ServerManagementController,
  ],
  providers: [
    // 保留所有现有提供者
    AuthService,
    JwtService, // 扩展后的JWT服务
    PasswordService,
    MfaService,
    JwtStrategy,
    
    // 新增：区服管理服务
    ServerService,
    CharacterSessionService,
  ],
  exports: [
    // 保留所有现有导出
    AuthService,
    JwtService,
    PasswordService,
    
    // 新增导出
    ServerService,
  ],
})
export class AuthModule {}
```

---

> **增强设计原则**：
> - 🔧 **最小化扩展**：在现有JWT和用户服务基础上扩展
> - 🏗️ **架构复用**：充分利用现有的配置、缓存和会话管理
> - 📊 **向后兼容**：所有现有功能保持不变
> - 🔄 **渐进式集成**：支持逐步启用多服务器功能
> - 🛡️ **安全一致性**：保持现有的安全策略和验证机制


### 玩家历史记录表 (user_history)**
```typescript
interface UserHistory {
  id: string;                      // 记录ID
  userId: string;                // 玩家ID
  lastServerId: string;            // 上次游戏的区服ID
  serverHistory: ServerHistoryItem[]; // 区服历史记录
  createdAt: Date;
  updatedAt: Date;
}

interface ServerHistoryItem {
  serverId: string;                // 区服ID
  serverName: string;              // 区服名称
  firstLoginTime: Date;            // 首次登录时间
  lastLoginTime: Date;             // 最后登录时间
  totalPlayTime: number;           // 总游戏时长
  ...                              // 角色名，等级，战力，货币，头像等基础信息（后续考虑同步机制）
  achievements: string[];          // 成就列表
}
```