# Mongoose ID字段映射开发指南

## 📋 概述

本文档详细说明了在NestJS + Mongoose项目中，什么情况下需要配置 `_id` 到 `id` 的虚拟字段映射，以及相关的最佳实践。

## 🎯 核心问题

在Mongoose中，MongoDB的文档默认使用 `_id` 字段作为主键，但在业务逻辑中我们通常希望使用 `id` 字段。这就需要在特定情况下配置虚拟字段映射。

## 🔍 判断标准

### ✅ **需要配置虚拟字段映射的情况**

#### 1. 实体类中显式声明了 `id` 字段
```typescript
export class User {
  @ApiProperty({ description: '用户ID' })
  id?: string;  // ← 显式声明了id字段
  
  @ApiProperty({ description: '用户名' })
  @Prop({ required: true, unique: true })
  username: string;
}
```

#### 2. 业务逻辑中需要使用 `entity.id` 访问主键
```typescript
// Token生成
const tokens = this.generateTokens({
  sub: user.id,  // ← 需要访问user.id
  username: user.username,
});

// 关联查询
const profile = await this.profileService.findByUserId(user.id);

// 日志记录
this.logger.info(`用户操作: ${user.id}`);
```

#### 3. API响应需要返回 `id` 字段而不是 `_id`
```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",  // ← 前端期望id字段
    "username": "john_doe",
    "email": "<EMAIL>"
  }
}
```

### ❌ **不需要配置虚拟字段映射的情况**

#### 1. 使用自定义业务ID字段
```typescript
export class Hero {
  // 没有声明id字段，使用自定义业务ID
  @Prop({ required: true, unique: true })
  heroId: string;  // 自定义业务ID
  
  @Prop({ required: true })
  characterId: string;  // 关联的角色ID
  
  @Prop({ required: true })
  resId: number;  // 配置表ID
}
```

#### 2. 业务逻辑中使用自定义ID字段
```typescript
// 使用自定义ID进行查询
const hero = await this.heroRepository.findByHeroId(heroData.heroId);

// 使用自定义ID进行关联
const formation = await this.formationService.addHero(hero.heroId);

// 日志记录使用自定义ID
this.logger.info(`球员操作: ${hero.heroId}`);
```

## 🛠️ 配置方法

### 标准配置模板

```typescript
export const UserSchema = SchemaFactory.createForClass(User);

// 配置虚拟字段：将_id映射到id
UserSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 确保虚拟字段包含在JSON序列化中
UserSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});
```

### 配置说明

1. **虚拟字段定义**：`UserSchema.virtual('id').get()` 创建一个虚拟的 `id` 字段
2. **ID转换**：`this._id.toHexString()` 将ObjectId转换为字符串
3. **JSON序列化**：`toJSON` 配置确保虚拟字段包含在API响应中
4. **字段清理**：删除 `_id` 和 `__v` 字段，保持响应数据的整洁

## 📊 项目中的实际应用

### ✅ 已配置虚拟字段的Schema

#### 1. User Schema (Auth服务)
```typescript
// apps/auth/src/modules/user/entities/user.entity.ts
export class User {
  @ApiProperty({ description: '用户ID' })
  id?: string;  // 需要虚拟字段映射
  
  @Prop({ required: true, unique: true })
  username: string;
}
```

**原因**：
- Token生成需要 `user.id`
- 用户管理API需要返回 `id` 字段
- 与前端的数据格式约定

#### 2. UserHistory Schema
```typescript
// user-history/entities/user-history.entity.ts
UserHistorySchema.virtual('id').get(function() {
  return this._id.toHexString();
});
```

**原因**：
- 统一的ID访问方式
- API响应格式一致性

### ❌ 不需要配置的Schema（大多数）

#### 1. Character Schema
```typescript
export class Character {
  @Prop({ required: true, unique: true })
  characterId: string;  // 使用自定义业务ID
  
  @Prop({ required: true })
  userId: string;  // 关联用户ID
}
```

#### 2. Hero Schema
```typescript
export class Hero {
  @Prop({ required: true, unique: true })
  heroId: string;  // 使用自定义业务ID
  
  @Prop({ required: true })
  characterId: string;  // 关联角色ID
}
```

#### 3. 其他游戏业务Schema
- **Item Schema** - 使用 `uid` 作为业务ID
- **Formation Schema** - 使用 `formationId` 作为业务ID
- **Activity Schema** - 使用 `activityRecordId` 作为业务ID
- **所有其他游戏实体** - 都有自己的业务ID字段

## 🎨 设计模式分析

项目采用了两种不同的ID管理模式：

### 1. 通用实体模式（需要虚拟字段）
```typescript
// 适用于：用户、权限、系统管理等通用功能
export class User {
  id?: string;        // 依赖MongoDB的_id，需要虚拟字段映射
  username: string;
  email: string;
}
```

**特点**：
- 使用MongoDB的ObjectId作为主键
- 需要虚拟字段映射
- 适用于系统级实体

### 2. 业务实体模式（不需要虚拟字段）
```typescript
// 适用于：游戏业务实体
export class Hero {
  heroId: string;      // 自定义业务ID，不需要虚拟字段映射
  characterId: string; // 关联的角色ID
  resId: number;       // 配置表ID
}
```

**特点**：
- 使用自定义业务ID字段
- 不依赖MongoDB的_id
- 业务语义更清晰
- 适用于游戏业务实体

## ⚠️ 常见问题与解决方案

### 问题1：Token生成时 `user.id` 为 undefined

**症状**：
```javascript
// Token payload缺少sub字段
{
  "username": "test_user",
  "email": "<EMAIL>",
  // "sub": undefined  ← 缺少用户ID
}
```

**原因**：User实体声明了 `id` 字段，但没有配置虚拟字段映射

**解决方案**：
```typescript
// 添加虚拟字段映射
UserSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

UserSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});
```

### 问题2：API响应包含 `_id` 而不是 `id`

**症状**：
```json
{
  "_id": "507f1f77bcf86cd799439011",  // ← 不符合前端期望
  "username": "john_doe"
}
```

**解决方案**：配置 `toJSON` 转换函数，将 `_id` 转换为 `id`

### 问题3：过度配置虚拟字段

**症状**：为所有Schema都配置了虚拟字段映射

**问题**：
- 增加了不必要的复杂度
- 可能与自定义ID字段冲突
- 性能开销

**解决方案**：
- 只为需要的Schema配置虚拟字段
- 游戏业务实体使用自定义ID字段
- 遵循"最小配置原则"

## 🎯 最佳实践

### 1. 明确ID字段的用途
- **系统实体**：使用MongoDB ObjectId + 虚拟字段映射
- **业务实体**：使用自定义业务ID字段

### 2. 保持一致性
- 同类型的实体使用相同的ID管理模式
- API响应格式保持一致

### 3. 文档化ID字段
```typescript
export class User {
  @ApiProperty({ 
    description: '用户ID（MongoDB ObjectId的字符串形式）',
    example: '507f1f77bcf86cd799439011'
  })
  id?: string;
}

export class Hero {
  @ApiProperty({ 
    description: '球员业务ID（自定义生成）',
    example: 'hero_12345_67890'
  })
  @Prop({ required: true, unique: true })
  heroId: string;
}
```

### 4. 测试ID字段的正确性
```typescript
// 单元测试
it('should have correct id field', () => {
  const user = new User();
  user._id = new ObjectId();
  
  const jsonUser = user.toJSON();
  expect(jsonUser.id).toBeDefined();
  expect(jsonUser._id).toBeUndefined();
});
```

## 📚 相关文档

- [Mongoose Virtual Fields](https://mongoosejs.com/docs/guide.html#virtuals)
- [Mongoose toJSON Transform](https://mongoosejs.com/docs/guide.html#transform)
- [NestJS Mongoose Integration](https://docs.nestjs.com/techniques/mongodb)

## 🔄 更新记录

- **2025-07-30**: 初始版本，基于User实体ID映射问题的分析和解决
- **问题背景**: Token刷新失败，原因是刷新后的Token缺少 `sub` 字段
- **解决方案**: 为User Schema配置虚拟字段映射，确保 `user.id` 正确返回用户ID

---

**总结**：虚拟字段映射不是必需的配置，只在特定场景下使用。正确理解和应用这个概念，可以避免不必要的复杂度，同时确保业务逻辑的正确性。
