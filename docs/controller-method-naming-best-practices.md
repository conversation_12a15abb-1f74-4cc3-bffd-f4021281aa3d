# Controller方法命名最佳实践

## 问题描述

当一个Controller需要同时提供HTTP接口和微服务调用接口，且功能相同但调用方式不同时，会遇到方法同名的问题。

## 解决方案

### 方案1: 方法名后缀区分（推荐）

为HTTP接口和微服务接口使用不同的方法名后缀：

```typescript
@Controller('auth')
export class AuthController {
  
  // HTTP接口
  @Post('verify-token')
  async verifyTokenHttp(@Body() dto: VerifyTokenDto): Promise<ApiResponseDto<any>> {
    const result = await this.authService.verifyToken(dto.token);
    return {
      success: true,
      data: result,
      message: result.valid ? '令牌有效' : '令牌无效',
      timestamp: new Date().toISOString(),
    };
  }

  // 微服务接口
  @MessagePattern('verifyToken')
  async verifyTokenMicroservice(@Payload() data: { token: string }) {
    try {
      return await this.authService.verifyToken(data.token);
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }
}
```

**优点:**
- 方法名清晰表明调用方式
- 代码可读性好
- 维护简单

**缺点:**
- 方法名稍长

### 方案2: 分离Controller（适用于复杂场景）

将HTTP接口和微服务接口分离到不同的Controller：

```typescript
// HTTP接口Controller
@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('verify-token')
  async verifyToken(@Body() dto: VerifyTokenDto): Promise<ApiResponseDto<any>> {
    const result = await this.authService.verifyToken(dto.token);
    return {
      success: true,
      data: result,
      message: result.valid ? '令牌有效' : '令牌无效',
      timestamp: new Date().toISOString(),
    };
  }
}

// 微服务接口Controller
@Controller()
export class AuthMicroserviceController {
  constructor(private readonly authService: AuthService) {}

  @MessagePattern('verifyToken')
  async verifyToken(@Payload() data: { token: string }) {
    try {
      return await this.authService.verifyToken(data.token);
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }
}
```

**优点:**
- 职责分离清晰
- 方法名保持简洁
- 便于独立测试和维护

**缺点:**
- 需要维护两个Controller
- 代码重复度稍高

### 方案3: 使用装饰器组合

创建自定义装饰器来组合HTTP和微服务接口：

```typescript
// 自定义装饰器
function DualEndpoint(httpPath: string, messagePattern: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // 应用HTTP装饰器
    Post(httpPath)(target, propertyKey, descriptor);
    // 应用微服务装饰器
    MessagePattern(messagePattern)(target, propertyKey, descriptor);
  };
}

@Controller('auth')
export class AuthController {
  
  @DualEndpoint('verify-token', 'verifyToken')
  async verifyToken(@Body() dto: any, @Payload() payload: any) {
    // 根据参数类型判断调用来源
    const token = dto?.token || payload?.token;
    const result = await this.authService.verifyToken(token);
    
    // 根据调用来源返回不同格式
    if (dto) {
      // HTTP调用
      return {
        success: true,
        data: result,
        message: result.valid ? '令牌有效' : '令牌无效',
        timestamp: new Date().toISOString(),
      };
    } else {
      // 微服务调用
      return result;
    }
  }
}
```

**优点:**
- 代码复用度高
- 方法名保持简洁

**缺点:**
- 实现复杂
- 调试困难
- 不推荐在生产环境使用

### 方案4: 使用私有方法提取公共逻辑

```typescript
@Controller('auth')
export class AuthController {
  
  // HTTP接口
  @Post('verify-token')
  async verifyTokenHttp(@Body() dto: VerifyTokenDto): Promise<ApiResponseDto<any>> {
    const result = await this._verifyTokenCore(dto.token);
    return {
      success: true,
      data: result,
      message: result.valid ? '令牌有效' : '令牌无效',
      timestamp: new Date().toISOString(),
    };
  }

  // 微服务接口
  @MessagePattern('verifyToken')
  async verifyTokenMicroservice(@Payload() data: { token: string }) {
    try {
      return await this._verifyTokenCore(data.token);
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  // 私有方法，提取公共逻辑
  private async _verifyTokenCore(token: string) {
    return await this.authService.verifyToken(token);
  }
}
```

**优点:**
- 避免代码重复
- 逻辑清晰
- 易于维护

**缺点:**
- 增加了私有方法

## 推荐方案

### 对于简单场景
使用**方案1（方法名后缀区分）**，这是最直接和清晰的解决方案。

### 对于复杂场景
使用**方案2（分离Controller）**，特别是当：
- HTTP接口和微服务接口的业务逻辑差异较大
- 需要不同的权限控制
- 需要不同的错误处理策略

### 对于有大量重复逻辑的场景
使用**方案4（私有方法提取公共逻辑）**，结合方案1或方案2。

## 命名约定

### HTTP接口方法命名
- 使用动词 + 名词的形式：`getUserInfo`, `verifyToken`, `refreshToken`
- 可以添加`Http`后缀以区分：`verifyTokenHttp`, `getUserInfoHttp`

### 微服务接口方法命名
- 使用与MessagePattern相同的名称：`verifyToken`, `getUserInfo`
- 可以添加`Microservice`后缀：`verifyTokenMicroservice`, `getUserInfoMicroservice`

### MessagePattern命名
- 使用camelCase：`verifyToken`, `getUserInfo`, `checkPermission`
- 保持简洁和语义化
- 避免使用动词前缀如`get`, `set`，除非必要

## 实际应用示例

```typescript
@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
  ) {}

  // ==================== HTTP接口 ====================
  
  @Post('verify-token')
  @ApiOperation({ summary: '验证令牌有效性' })
  async verifyTokenHttp(
    @Body() dto: VerifyTokenDto
  ): Promise<ApiResponseDto<any>> {
    const result = await this._verifyTokenCore(dto.token);
    return this._formatHttpResponse(result, '令牌验证');
  }

  @Post('user-info')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取用户信息' })
  async getUserInfoHttp(
    @CurrentUser() user: UserDocument
  ): Promise<ApiResponseDto<any>> {
    const result = await this._getUserInfoCore(user.id);
    return this._formatHttpResponse(result, '获取用户信息');
  }

  // ==================== 微服务接口 ====================
  
  @MessagePattern('verifyToken')
  async verifyTokenMicroservice(@Payload() data: { token: string }) {
    try {
      return await this._verifyTokenCore(data.token);
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  @MessagePattern('getUserInfo')
  async getUserInfoMicroservice(@Payload() data: { userId: string }) {
    try {
      return await this._getUserInfoCore(data.userId);
    } catch (error) {
      return { error: error.message };
    }
  }

  // ==================== 私有方法 ====================
  
  private async _verifyTokenCore(token: string) {
    return await this.authService.verifyToken(token);
  }

  private async _getUserInfoCore(userId: string) {
    return await this.authService.getUserInfo(userId);
  }

  private _formatHttpResponse(data: any, operation: string): ApiResponseDto<any> {
    return {
      success: true,
      data,
      message: `${operation}成功`,
      timestamp: new Date().toISOString(),
    };
  }
}
```

## 总结

选择合适的方案取决于具体的业务需求和团队偏好。推荐优先使用方案1（方法名后缀区分），它简单直接，易于理解和维护。对于复杂场景，可以考虑方案2（分离Controller）来实现更好的职责分离。
