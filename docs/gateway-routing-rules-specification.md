# 网关路由规则规范

## 🎯 **规范概述**

本文档定义了足球经理游戏服务器网关的统一路由规则，解决当前路由混乱问题，建立清晰简洁的路由体系。

### **核心原则**
1. **微服务无前缀**：各微服务内部不添加全局前缀，保持路径简洁
2. **网关统一代理**：网关负责添加和管理路由前缀
3. **明确路由分离**：系统路由与微服务路由完全分离
4. **精确错误处理**：区分"路由不存在"、"服务不可用"和"服务未启动"

## 📋 **路由架构设计**

### **1. 路由分类**

#### **系统路由（网关直接处理）**
```
/health              → 网关健康检查
/docs                → 网关 API 文档
/metrics             → 网关监控指标
/monitoring/*        → 网关监控面板
/user/*              → 网关用户会话管理
```

#### **微服务路由（网关代理）**
```
/api/{service}/*     → 代理到对应微服务
```

### **2. 路径重写规则**
```
请求路径: /api/{service}/{endpoint}
重写为:   /{endpoint}
代理到:   {service_url}/{endpoint}
```

## 🔧 **技术实现规范**

### **1. 常量定义**
```typescript
// apps/gateway/src/common/constants/index.ts
export const ROUTE_CONSTANTS = {
  PROXY_PREFIX: '/api',         // 微服务代理前缀
  HEALTH_PREFIX: '/health',     // 健康检查
  METRICS_PREFIX: '/metrics',   // 监控指标
  DOCS_PATH: '/docs',           // API 文档（不使用 /api 前缀）
  WEBSOCKET_PATH: '/ws',        // WebSocket 路径
  GRAPHQL_PATH: '/graphql',     // GraphQL 路径
} as const;
```

### **2. 微服务映射**
```typescript
// 直接使用微服务名称常量，无需额外映射
import { MICROSERVICE_NAMES } from '@shared/constants';
```

### **3. 路由解析逻辑**
```typescript
class RouteResolver {
  resolveRoute(path: string): RouteResult {
    if (path.startsWith(ROUTE_CONSTANTS.PROXY_PREFIX)) {
      const serviceName = this.extractServiceName(path);

      if (MICROSERVICE_MAPPING[serviceName]) {
        return {
          type: 'microservice',
          service: MICROSERVICE_MAPPING[serviceName],
          originalPath: path,
          rewrittenPath: this.rewritePath(path, serviceName)
        };
      } else {
        return { type: 'not_found', path };
      }
    }

    return { type: 'system', path };
  }

  private rewritePath(path: string, serviceName: string): string {
    // /api/auth/login -> /login
    return path.replace(`${ROUTE_CONSTANTS.PROXY_PREFIX}/${serviceName}`, '');
  }
}

```

## 📊 **路由示例**

### **1. 系统路由示例**
```
GET /health          → 网关健康检查
GET /docs            → 网关 API 文档（Swagger UI）
GET /api-json        → 网关 OpenAPI 规范
GET /metrics         → 网关监控指标
GET /monitoring/*    → 网关监控面板
POST /user/login     → 网关用户登录（会话管理）
POST /user/logout    → 网关用户登出
```

### **2. 微服务路由示例**
```
请求: GET /api/auth/health
解析: service='auth', path='/health'
代理: GET http://auth-service/health

请求: POST /api/auth/login
解析: service='auth', path='/login'
代理: POST http://auth-service/login

请求: GET /api/user/profile
解析: service='user', path='/profile'
代理: GET http://user-service/profile

请求: GET /api/club/123
解析: service='club', path='/123'
代理: GET http://club-service/123
```

### **3. 错误处理示例**
```
请求: GET /api/unknown/test
结果: 404 Not Found - "Unknown service: unknown"

请求: GET /api/user/profile (user服务未启动)
结果: 503 Service Unavailable - "Service user is not available"

请求: GET /api/auth/invalid (auth服务返回404)
结果: 404 Not Found - 透传认证服务的响应
```

## 🚀 **实施计划**

### **第一阶段：修复微服务前缀问题**

#### **1. 移除认证服务的全局前缀**
```typescript
// apps/auth/src/main.ts - 修改前
const globalPrefix = configService.get<string>('app.globalPrefix', 'api');
app.setGlobalPrefix(globalPrefix);

// apps/auth/src/main.ts - 修改后
// 移除全局前缀设置，让网关统一管理
// app.setGlobalPrefix(globalPrefix); // 删除这行
```

#### **2. 修复网关 Swagger 文档路径**
```typescript
// apps/gateway/src/main.ts - 修改前
SwaggerModule.setup('api/docs', app, document);

// apps/gateway/src/main.ts - 修改后
SwaggerModule.setup('docs', app, document);
```

#### **3. 更新路径重写规则**
```typescript
// apps/gateway/src/domain/proxy/proxy.service.ts
private getPathRewriteRules(serviceName: string): Record<string, string> {
  return {
    [`^${ROUTE_CONSTANTS.PROXY_PREFIX}/${serviceName}`]: '',  // 完全移除前缀
  };
}
```

### **第二阶段：完善错误处理**

#### **1. 区分服务状态**
```typescript
class RouteResolver {
  resolveRoute(path: string): RouteResult {
    if (path.startsWith(ROUTE_CONSTANTS.PROXY_PREFIX)) {
      const serviceName = this.extractServiceName(path);

      if (!MICROSERVICE_MAPPING[serviceName]) {
        return {
          type: 'not_found',
          error: `Unknown service: ${serviceName}`,
          statusCode: 404
        };
      }

      const serviceInstances = this.loadBalancer.getHealthyInstances(serviceName);
      if (serviceInstances.length === 0) {
        return {
          type: 'service_unavailable',
          error: `Service ${serviceName} is not available`,
          statusCode: 503
        };
      }

      return {
        type: 'microservice',
        service: serviceName,
        originalPath: path,
        rewrittenPath: this.rewritePath(path, serviceName)
      };
    }

    return { type: 'system', path };
  }
}
```

### **第三阶段：测试验证**

#### **1. 系统路由测试**
```bash
# 健康检查
curl http://localhost:3000/health
# 预期: 200 OK

# API 文档
curl http://localhost:3000/docs
# 预期: 200 OK (Swagger UI)

# 监控指标
curl http://localhost:3000/metrics
# 预期: 200 OK
```

#### **2. 微服务路由测试**
```bash
# 认证服务健康检查
curl http://localhost:3000/api/auth/health
# 预期: 200 OK (如果认证服务启动)
# 预期: 503 Service Unavailable (如果认证服务未启动)

# 用户服务测试
curl http://localhost:3000/api/user/health
# 预期: 200 OK (如果用户服务启动)
# 预期: 503 Service Unavailable (如果用户服务未启动)

# 未知服务测试
curl http://localhost:3000/api/unknown/test
# 预期: 404 Not Found - "Unknown service: unknown"
```

#### **3. 路径重写验证**
```bash
# 验证路径重写是否正确
# 请求: /api/auth/login
# 应该代理到: http://auth-service/login (而不是 /api/login)
```

## ⚠️ **关键变更说明**

### **1. 微服务前缀移除**
- **认证服务**: 移除 `app.setGlobalPrefix('api')`
- **用户服务**: 移除 `app.setGlobalPrefix('api')`
- **其他服务**: 确保没有全局前缀设置

### **2. 网关路径调整**
- **Swagger 文档**: `/api/docs` → `/docs`
- **路径重写**: `/api/{service}/{path}` → `/{path}`

### **3. 错误处理改进**
- **未知服务**: 返回 404 Not Found
- **服务未启动**: 返回 503 Service Unavailable
- **服务内部错误**: 透传原始响应

## 📋 **实施检查清单**

### **✅ 必须完成的修改**

#### **1. 认证服务 (apps/auth/src/main.ts)**
```typescript
// 删除或注释掉这行
// app.setGlobalPrefix(globalPrefix);
```

#### **2. 网关 Swagger 配置 (apps/gateway/src/main.ts)**
```typescript
// 修改前: SwaggerModule.setup('api/docs', app, document);
// 修改后:
SwaggerModule.setup('docs', app, document);
```

#### **3. 路径重写规则 (apps/gateway/src/domain/proxy/proxy.service.ts)**
```typescript
private getPathRewriteRules(serviceName: string): Record<string, string> {
  return {
    [`^${ROUTE_CONSTANTS.PROXY_PREFIX}/${serviceName}`]: '',  // 完全移除前缀
  };
}
```

#### **4. 错误处理逻辑 (apps/gateway/src/core/router/route-resolver.service.ts)**
```typescript
// 区分"未知服务"和"服务不可用"
if (!Object.values(MICROSERVICE_NAMES).includes(serviceName)) {
  return { type: 'not_found', statusCode: 404 };
}

if (serviceInstances.length === 0) {
  return { type: 'service_unavailable', statusCode: 503 };
}
```

## 🎯 **预期效果**

### **修复后的路由行为**
```bash
# 系统路由 - 直接访问网关功能
curl http://localhost:3000/health        # ✅ 200 OK
curl http://localhost:3000/docs          # ✅ 200 OK (Swagger UI)
curl http://localhost:3000/metrics       # ✅ 200 OK

# 微服务路由 - 代理到对应服务
curl http://localhost:3000/api/auth/health   # ✅ 200 OK (如果auth服务启动)
curl http://localhost:3000/api/user/health   # ✅ 503 Service Unavailable (如果user服务未启动)
curl http://localhost:3000/api/unknown/test  # ✅ 404 Not Found (未知服务)

# 路径重写验证
# 请求: /api/auth/login
# 代理到: http://auth-service/login (而不是 /api/login)
```

### **解决的问题**
1. ✅ **消除路径重复**: 微服务不再添加自己的前缀
2. ✅ **明确路由分离**: 系统路由与微服务路由完全分离
3. ✅ **精确错误处理**: 区分未知服务、服务不可用等状态
4. ✅ **简化配置**: 统一使用常量，避免硬编码

---

## 📝 **文档信息**

- **文档版本**: v2.0
- **创建日期**: 2025-01-01
- **最后更新**: 2025-01-01
- **状态**: 待实施
- **优先级**: 高
- **影响范围**: 网关路由系统、所有微服务

### **相关文档**
- [网关架构设计](./gateway-architecture.md)
- [微服务通信规范](./microservice-communication.md)
- [API 接口规范](./api-specification.md)



