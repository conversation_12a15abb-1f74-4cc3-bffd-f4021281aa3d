# 足球经理游戏微服务开发规范

## 📋 概述

本文档基于Character服务的成功实现，总结了足球经理游戏项目的微服务开发规范，作为后续所有微服务开发的强制约束和模板。

## 🔍 功能完整性审核标准

### 深度审核原则
- **不仅确保模块存在，还要全面扫描每个模块的功能完整性**
- **确保old项目中的所有功能都被完整正确迁移**
- **每个业务方法都必须有对应的实现，不能遗漏**
- **核心业务逻辑必须完整实现，不允许用TODO代替**

### 审核流程
1. **模块级审核** - 确认所有必要模块都已创建
2. **功能级审核** - 逐个检查每个模块的业务方法
3. **逻辑级审核** - 验证核心业务逻辑的完整性
4. **对照表创建** - 制作详细的功能对照表
5. **编译验证** - 确保所有代码能正常编译通过

### 功能对照表模板
```markdown
| old项目方法 | 新项目实现 | 状态 | 说明 |
|------------|-----------|------|------|
| triggerTask | triggerTask | ✅ | 任务触发机制（核心功能） |
| getAllTaskList | getTaskList | ✅ | 获取任务列表 |
| getTaskReward | claimTaskReward | ✅ | 领取任务奖励 |
```

### 完成度要求
- **模块完成度**: 100% - 所有模块都必须实现
- **功能完成度**: 100% - 所有业务方法都必须实现
- **编译通过率**: 100% - 所有代码必须能正常编译

## 🏗️ 架构原则

### 1. 微服务通信规范

#### ✅ 优先使用 @MessagePattern，特定场景保留HTTP
```typescript
// ✅ 推荐：大部分业务逻辑使用@MessagePattern
@MessagePattern('service.action')
async handleAction(@Payload() payload: ActionDto) {
  // 业务逻辑
}

// ✅ 必须保留HTTP的场景：
// 1. 认证相关接口（登录、注册、令牌刷新等）
@Post('login')
async login(@Body() loginDto: LoginDto) {
  // 初始认证必须HTTP
}

// 2. 健康检查接口
@Get('health')
async healthCheck() {
  // 监控系统需要HTTP
}

// 3. 文件上传接口
@Post('upload')
async uploadFile(@UploadedFile() file: Express.Multer.File) {
  // 大文件传输适合HTTP
}

// 4. 第三方集成接口（Webhooks、OAuth回调等）
@Post('webhooks/payment')
async paymentCallback(@Body() payload: any) {
  // 第三方系统通常只支持HTTP
}
```

#### 消息模式命名规范
- 格式：`{服务名}.{操作}`
- 示例：`character.create`、`hero.train`、`formation.update`
- 嵌套操作：`character.currency.add`、`hero.skill.upgrade`

### 2. 缓存管理规范

#### ✅ 优先使用缓存装饰器，复杂场景使用手动缓存
```typescript
// ✅ 推荐：简单场景使用缓存装饰器
@MessagePattern('character.getInfo')
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  dataType: 'server',
  serverId: '#{payload.serverId}',
  ttl: 3600
})
async getCharacterInfo(@Payload() payload) {}

// ✅ 推荐：更新操作使用@CachePut
@MessagePattern('character.update')
@CachePut({
  key: 'character:info:#{payload.characterId}',
  dataType: 'server',
  serverId: '#{payload.serverId}',
  ttl: 3600
})
async updateCharacter(@Payload() payload) {}

// ✅ 推荐：删除操作使用@CacheEvict
@MessagePattern('character.delete')
@CacheEvict({
  key: 'character:info:#{payload.characterId}',
  dataType: 'server',
  serverId: '#{payload.serverId}'
})
async deleteCharacter(@Payload() payload) {}
```

#### ✅ 复杂场景使用Service层手动缓存管理
```typescript
// ✅ 适合手动缓存的场景：
class CharacterService {
  // 1. 复杂的缓存逻辑（多层缓存、条件缓存等）
  async getCharacterWithComplexLogic(id: string) {
    // 先检查L1缓存
    const l1Cache = await this.redisService.get(`l1:character:${id}`);
    if (l1Cache) return l1Cache;

    // 再检查L2缓存
    const l2Cache = await this.redisService.get(`l2:character:${id}`);
    if (l2Cache) {
      // 回填L1缓存
      await this.redisService.set(`l1:character:${id}`, l2Cache, 300);
      return l2Cache;
    }

    // 从数据库获取并设置多层缓存
    const data = await this.repository.findById(id);
    await Promise.all([
      this.redisService.set(`l1:character:${id}`, data, 300),
      this.redisService.set(`l2:character:${id}`, data, 3600)
    ]);
    return data;
  }

  // 2. 批量操作的缓存管理
  async batchUpdateCharacters(updates: UpdateData[]) {
    const results = await this.repository.batchUpdate(updates);

    // 批量清除相关缓存
    const cacheKeys = updates.map(u => `character:info:${u.characterId}`);
    await this.redisService.del(...cacheKeys);

    return results;
  }

  // 3. 条件性缓存（根据业务逻辑决定是否缓存）
  async getCharacterData(id: string, includePrivate: boolean) {
    if (includePrivate) {
      // 包含私密信息，不缓存
      return await this.repository.findByIdWithPrivateData(id);
    }

    // 公开信息，可以缓存
    const cacheKey = `character:public:${id}`;
    const cached = await this.redisService.get(cacheKey);
    if (cached) return cached;

    const data = await this.repository.findById(id);
    await this.redisService.set(cacheKey, data, 3600);
    return data;
  }
}
```

#### 📋 缓存方式选择指南
| 场景 | 推荐方式 | 原因 |
|------|----------|------|
| 简单CRUD操作 | 缓存装饰器 | 代码简洁，自动管理 |
| 多层缓存 | 手动管理 | 复杂逻辑，精确控制 |
| 批量操作 | 手动管理 | 需要批量缓存操作 |
| 条件性缓存 | 手动管理 | 根据业务逻辑决定 |
| 缓存预热 | 手动管理 | 主动缓存策略 |
| 简单查询 | 缓存装饰器 | 标准化缓存模式 |

### 3. 数据库访问规范

#### ✅ 必须使用Repository模式
```typescript
// ✅ Repository层：数据库访问
@Injectable()
export class EntityRepository {
  constructor(
    @InjectModel(Entity.name) private entityModel: Model<EntityDocument>,
  ) {}

  async findById(id: string): Promise<EntityDocument | null> {
    return await this.entityModel.findOne({ entityId: id }).exec();
  }
}

// ✅ Service层：纯业务逻辑
@Injectable()
export class EntityService {
  constructor(
    private readonly entityRepository: EntityRepository,
  ) {}

  async getEntity(id: string): Promise<EntityDto> {
    const entity = await this.entityRepository.findById(id);
    return this.toDto(entity);
  }
}
```

## 📁 目录结构规范

### 标准微服务目录结构
```
apps/{service-name}/
├── src/
│   ├── {entity}/
│   │   ├── schemas/
│   │   │   └── {entity}.schema.ts          # 实例数据Schema
│   │   ├── repositories/
│   │   │   └── {entity}.repository.ts      # 数据库访问层
│   │   ├── dto/
│   │   │   └── {entity}.dto.ts             # 数据传输对象
│   │   ├── {entity}.controller.ts          # 实体控制器
│   │   ├── {entity}.service.ts             # 实体服务
│   │   ├── {entity}.controller.spec.ts     # 控制器测试
│   │   └── {entity}.module.ts              # 实体模块
│   ├── {feature}/                          # 其他功能模块
│   │   ├── schemas/
│   │   ├── repositories/
│   │   ├── dto/
│   │   ├── {feature}.controller.ts
│   │   ├── {feature}.service.ts
│   │   └── {feature}.module.ts
│   ├── health/
│   │   ├── health.controller.ts            # 健康检查控制器
│   │   ├── health.service.ts               # 健康检查服务
│   │   └── health.module.ts                # 健康检查模块
│   ├── app.module.ts                       # 应用主模块
│   └── main.ts                             # 启动入口
└── test/                                   # 测试文件

libs/game-config/                           # 共享配置库
├── src/
│   ├── {entity}/
│   │   ├── {entity}-definition.schema.ts   # 配置定义Schema
│   │   ├── {entity}-definition.service.ts  # 配置服务
│   │   └── {entity}-definition.repository.ts # 配置数据访问
│   └── index.ts                            # 统一导出
```

### 模块化组织原则
- **每个业务域创建独立模块**: 避免在app.module.ts中导入所有组件代码
- **功能内聚**: 相关的controller、service、repository放在同一目录
- **清晰的模块边界**: 每个模块有自己的module.ts文件
- **统一的导入导出**: 通过模块接口暴露服务
- **配置数据分离**: 静态配置使用共享库，动态实例数据在各服务中

## 🎯 配置管理规范

### 配置数据分类
- **静态配置**: 游戏策划配置的数据表，如技能定义、物品定义、球员模板等
- **动态实例**: 玩家游戏过程中产生的数据，如角色信息、背包物品、球员实例等

### 命名规范
```typescript
// ✅ 静态配置（共享库中）
skill-definition.schema.ts    # 技能定义/配置表
hero-definition.schema.ts     # 球员定义/配置表
item-definition.schema.ts     # 物品定义/配置表
formation-definition.schema.ts # 阵型定义/配置表

// ✅ 动态实例（各服务中）
skill.schema.ts              # 球员技能实例
hero.schema.ts              # 球员实例
item.schema.ts              # 物品实例（在背包中）
formation.schema.ts         # 阵容实例
```

### 共享配置库架构
```typescript
libs/game-config/
├── src/
│   ├── skill/
│   │   ├── skill-definition.schema.ts
│   │   ├── skill-definition.service.ts
│   │   └── skill-definition.repository.ts
│   ├── hero/
│   │   ├── hero-definition.schema.ts
│   │   └── hero-definition.service.ts
│   ├── item/
│   │   ├── item-definition.schema.ts
│   │   └── item-definition.service.ts
│   └── index.ts                    # 统一导出
```

### 使用方式
```typescript
// 在微服务中使用配置库
import { SkillDefinitionService } from '@app/game-config';

@Injectable()
export class SkillService {
  constructor(
    private skillDefinitionService: SkillDefinitionService
  ) {}

  async getSkillConfig(skillId: number) {
    return this.skillDefinitionService.getSkillDefinition(skillId);
  }
}
```

### 配置管理原则
- **性能优先**: 使用共享库避免网络开销
- **类型安全**: TypeScript编译时检查
- **强一致性**: 所有服务使用相同数据结构
- **易于维护**: 配置集中管理，统一更新

## 🔧 代码模板

### 1. Schema模板
```typescript
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ 
  collection: '{collection_name}', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Entity {
  @Prop({ required: true, unique: true })
  entityId: string;

  @Prop({ required: true, index: true })
  characterId: string;

  @Prop({ required: true, index: true })
  serverId: string;

  // 其他业务字段...
}

export const EntitySchema = SchemaFactory.createForClass(Entity);
export type EntityDocument = Entity & Document;

// 创建索引
EntitySchema.index({ entityId: 1 }, { unique: true });
EntitySchema.index({ characterId: 1 });
EntitySchema.index({ serverId: 1 });
```

### 2. Repository模板
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Entity, EntityDocument } from '../schemas/entity.schema';

@Injectable()
export class EntityRepository {
  private readonly logger = new Logger(EntityRepository.name);

  constructor(
    @InjectModel(Entity.name) private entityModel: Model<EntityDocument>,
  ) {}

  async create(createData: any): Promise<EntityDocument> {
    const entity = new this.entityModel(createData);
    return await entity.save();
  }

  async findById(entityId: string): Promise<EntityDocument | null> {
    return await this.entityModel.findOne({ entityId }).exec();
  }

  async update(
    entityId: string, 
    updateData: UpdateQuery<EntityDocument>
  ): Promise<EntityDocument | null> {
    return await this.entityModel.findOneAndUpdate(
      { entityId },
      updateData,
      { new: true }
    ).exec();
  }

  async delete(entityId: string): Promise<EntityDocument | null> {
    return await this.entityModel.findOneAndDelete({ entityId }).exec();
  }
}
```

### 3. Service模板
```typescript
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { EntityRepository } from './repositories/entity.repository';
import { CreateEntityDto, UpdateEntityDto, EntityInfoDto } from './dto/entity.dto';
import { ErrorCode, ErrorMessages } from '@app/game-constants';

@Injectable()
export class EntityService {
  private readonly logger = new Logger(EntityService.name);

  constructor(
    private readonly entityRepository: EntityRepository,
  ) {}

  async createEntity(createDto: CreateEntityDto): Promise<EntityDocument> {
    try {
      const entityData = {
        entityId: this.generateEntityId(),
        ...createDto,
      };

      const savedEntity = await this.entityRepository.create(entityData);
      this.logger.log(`实体创建成功: ${savedEntity.entityId}`);
      return savedEntity;
    } catch (error) {
      this.logger.error('创建实体失败', error);
      throw error;
    }
  }

  async getEntityInfo(entityId: string): Promise<EntityInfoDto> {
    try {
      const entity = await this.entityRepository.findById(entityId);
      if (!entity) {
        throw new NotFoundException({
          code: ErrorCode.ENTITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ENTITY_NOT_FOUND],
        });
      }

      return this.toEntityInfoDto(entity);
    } catch (error) {
      this.logger.error('获取实体信息失败', error);
      throw error;
    }
  }

  private generateEntityId(): string {
    return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private toEntityInfoDto(entity: EntityDocument): EntityInfoDto {
    return {
      entityId: entity.entityId,
      characterId: entity.characterId,
      serverId: entity.serverId,
      // 其他字段映射...
    };
  }
}
```

### 4. Controller模板
```typescript
import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { EntityService } from './entity.service';
import { CreateEntityDto, UpdateEntityDto } from './dto/entity.dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';

@Controller()
export class EntityController {
  private readonly logger = new Logger(EntityController.name);

  constructor(private readonly entityService: EntityService) {}

  @MessagePattern('entity.create')
  @CacheEvict({
    key: 'character:entities:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createEntity(@Payload() createDto: CreateEntityDto) {
    this.logger.log(`创建实体请求: ${JSON.stringify(createDto)}`);
    const entity = await this.entityService.createEntity(createDto);
    return {
      code: 0,
      message: '实体创建成功',
      data: entity,
    };
  }

  @MessagePattern('entity.getInfo')
  @Cacheable({
    key: 'entity:info:#{payload.entityId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getEntityInfo(@Payload() payload: { entityId: string; serverId?: string }) {
    this.logger.log(`获取实体信息: ${payload.entityId}`);
    const entity = await this.entityService.getEntityInfo(payload.entityId);
    return {
      code: 0,
      message: '获取成功',
      data: entity,
    };
  }
}
```

### 5. Module模板
```typescript
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Entity相关组件
import { EntityController } from './entity.controller';
import { EntityService } from './entity.service';
import { Entity, EntitySchema } from './schemas/entity.schema';
import { EntityRepository } from './repositories/entity.repository';

@Module({
  imports: [
    // 注册Entity Schema
    MongooseModule.forFeature([
      { name: Entity.name, schema: EntitySchema },
    ]),
  ],

  controllers: [EntityController],

  providers: [
    EntityService,
    EntityRepository,
  ],

  exports: [
    EntityService,
    EntityRepository,
  ],
})
export class EntityModule {}
```

### 6. App Module模板
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 功能模块
import { HealthModule } from './health/health.module';
import { EntityModule } from './entity/entity.module';
import { FeatureModule } from './feature/feature.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@libs/redis';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'service-name'),
        ...setupDatabaseEvents('service-name'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'service-name',
      serverId: 'server_001',
    }),

    // 功能模块
    HealthModule,
    EntityModule,
    FeatureModule,
  ],
})
export class AppModule {}
```

## 🔍 缓存策略规范

### 缓存键命名规范
```typescript
// 实体信息缓存
'entity:info:#{entityId}'

// 角色关联数据缓存
'character:entities:#{characterId}'

// 列表数据缓存
'entities:list:#{characterId}:#{page}:#{limit}'

// 统计数据缓存
'server:stats:#{serverId}'
```

### TTL设置规范
```typescript
const CacheTTL = {
  ENTITY_INFO: 3600,        // 1小时 - 实体基础信息
  LIST_DATA: 1800,          // 30分钟 - 列表数据
  STATS_DATA: 300,          // 5分钟 - 统计数据
  CONFIG_DATA: 7200,        // 2小时 - 配置数据
  SESSION_DATA: 86400,      // 24小时 - 会话数据
};
```

## 📝 开发检查清单

### ✅ 必须完成项
- [ ] 根据场景选择@MessagePattern或HTTP接口
- [ ] 优先使用缓存装饰器，复杂场景使用手动缓存
- [ ] 实现Repository模式数据库访问
- [ ] 创建完整的Schema、Repository、Service、Controller
- [ ] 为每个业务域创建独立的Module
- [ ] 在app.module.ts中只导入功能模块，不导入具体组件
- [ ] 添加健康检查端点
- [ ] 配置统一的启动日志
- [ ] 编写完整的DTO和类型定义
- [ ] 实现错误处理和日志记录
- [ ] 通过构建和启动验证

### 🚫 禁止事项
- ❌ 不要在不必要的场景使用HTTP接口（参考必须保留HTTP的场景）
- ❌ 不要在简单场景使用手动缓存（优先装饰器）
- ❌ 不要直接在Service层注入Model，必须通过Repository
- ❌ 不要忽略错误处理和日志记录
- ❌ 不要使用any类型，保持类型安全

### 📋 HTTP接口使用场景
#### ✅ 必须使用HTTP的场景
- 认证相关接口（登录、注册、令牌刷新、登出、忘记密码）
- 系统监控接口（健康检查、就绪检查、存活检查、指标监控）
- 文件上传接口（头像上传、文档上传、文件下载）
- 第三方集成接口（支付回调、OAuth回调、Webhooks）

#### ✅ 建议使用HTTP的场景
- 数据导出接口（大量数据查询、报表导出）
- 管理后台接口（用户管理、系统配置、日志查询）
- 公开API接口（排行榜、比赛日程等无需认证的接口）

#### ✅ 使用@MessagePattern的场景
- 实时游戏业务逻辑（角色管理、球员操作、阵容配置等）
- 需要低延迟的双向通信
- 微服务间内部通信

## 🎯 质量标准

1. **代码质量**: TypeScript编译无错误无警告
2. **架构一致性**: 严格遵循Repository模式和缓存装饰器规范
3. **功能完整性**: 实现完整的CRUD操作和业务逻辑
4. **测试验证**: 构建成功、启动成功、健康检查通过
5. **文档完整**: 完整的注释和类型定义

## 🔧 设计优化原则

### 允许的优化
- **架构改进**: 可以改进old项目中不合理的架构设计
- **命名统一**: 统一命名规范（如guild替代association）
- **代码结构**: 优化代码组织和模块划分
- **性能优化**: 改进数据库查询和缓存策略
- **类型安全**: 增强TypeScript类型定义

### 优化约束
- **业务逻辑不变**: 不能改变核心业务逻辑
- **功能完整性**: 不能简化或删除业务功能
- **接口兼容**: 保持对外接口的兼容性
- **数据一致**: 确保数据结构的一致性

### 优化示例
```typescript
// ❌ old项目中的不合理设计
class Association {
  associationId: string;
  associationName: string;
}

// ✅ 优化后的统一命名
class Guild {
  guildId: string;
  guildName: string;
}
```

---

**本规范基于Character服务的成功实现总结，是所有后续微服务开发的强制约束。**
