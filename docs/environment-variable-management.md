# 环境变量管理指南

## 概述

本文档详细说明了足球经理游戏服务器项目中环境变量的管理策略，包括开发、测试和生产环境的配置方法。

## ⚠️ 重要概念澄清

### NODE_ENV 的值来源

**关键理解**：`process.env.NODE_ENV` 的值**不是**从 `.env` 文件中获取的！

```typescript
ConfigModule.forRoot({
  envFilePath: [
    `.env.${process.env.NODE_ENV || 'development'}`,  // process.env.NODE_ENV 在此时已确定
    '.env'
  ]
})
```

### 执行顺序说明

1. **Node.js 进程启动** → `process.env.NODE_ENV` 已确定（来自命令行或系统环境变量）
2. **ConfigModule 初始化** → 根据 `process.env.NODE_ENV` 决定加载哪个 `.env` 文件
3. **加载环境文件** → 从对应的 `.env.xxx` 文件中读取其他变量

### 实际例子

#### 开发环境启动
```bash
cross-env NODE_ENV=development npm start
```
**执行过程**：
1. `process.env.NODE_ENV` = `"development"`
2. ConfigModule 加载 `.env.development` 文件
3. 从 `.env.development` 中读取 `ENABLE_RATE_LIMIT=false` 等变量

#### 生产环境启动
```bash
cross-env NODE_ENV=production npm start
```
**执行过程**：
1. `process.env.NODE_ENV` = `"production"`
2. ConfigModule 加载 `.env.production` 文件
3. 从 `.env.production` 中读取 `ENABLE_RATE_LIMIT=true` 等变量

### ❌ 常见误区

如果在根目录 `.env` 文件中设置：
```bash
# 根目录 .env
NODE_ENV=production
```

这个值**不会**影响 `process.env.NODE_ENV`，因为：
- `process.env.NODE_ENV` 在 `.env` 文件加载之前就已经确定
- `.env` 文件中的 `NODE_ENV` 只能通过 `configService.get('NODE_ENV')` 获取
- 可能导致 `process.env.NODE_ENV` 和 `configService.get('NODE_ENV')` 不一致

**正确做法**：使用 `cross-env` 或系统环境变量设置 `NODE_ENV`

## 🏗️ 环境变量架构

### 1. 环境变量加载优先级

NestJS ConfigModule 按以下优先级加载环境变量：

```typescript
ConfigModule.forRoot({
  envFilePath: [
    `.env.${process.env.NODE_ENV || 'development'}`,  // 1. 环境特定文件
    '.env'                                            // 2. 根目录通用文件
  ]
})
```

**优先级顺序（从高到低）**：
1. `cross-env NODE_ENV=xxx` 命令行参数（最高优先级）
2. 系统环境变量 `export NODE_ENV=xxx`
3. 根目录 `.env` 文件中的 `NODE_ENV=xxx`
4. 默认值 `'development'`

### 2. 环境文件结构

```
project-root/
├── .env                           # 通用环境变量
├── .env.development              # 开发环境变量
├── .env.production               # 生产环境变量
├── .env.test                     # 测试环境变量
└── apps/
    └── auth/
        ├── .env.security.development    # 开发环境安全配置
        ├── .env.security.production     # 生产环境安全配置
        └── .env.security.test           # 测试环境安全配置
```

## 🔧 环境配置管理

### 开发环境

#### 启动方式
```bash
# 使用 npm scripts（推荐）
npm run start:auth:dev

# 等价于
cross-env NODE_ENV=development nest start auth --watch
```

#### 配置特点
- 限流功能禁用（`ENABLE_RATE_LIMIT=false`）
- 详细日志输出
- 热重载支持
- 宽松的安全策略

### 生产环境

#### 启动方式

**方式一：系统环境变量（推荐）**
```bash
# Linux/macOS
export NODE_ENV=production
npm run start:auth

# Windows PowerShell
$env:NODE_ENV="production"
npm run start:auth
```

**方式二：cross-env（开发测试）**
```bash
npm run start:auth:prod
# 等价于
cross-env NODE_ENV=production nest start auth --watch
```

**方式三：根目录 .env 文件**
```bash
# 在根目录 .env 文件中设置
echo "NODE_ENV=production" > .env
npm run start:auth
```

#### 配置特点
- 限流功能启用（`ENABLE_RATE_LIMIT=true`）
- IP白名单严格控制
- 服务间认证启用
- 最小化日志输出

### 测试环境

```bash
# 单元测试
npm run test:auth

# 集成测试
cross-env NODE_ENV=test npm run test:auth:e2e
```

## 🐳 Docker 部署

### Dockerfile 配置
```dockerfile
# 设置生产环境
ENV NODE_ENV=production

# 启动应用
CMD ["npm", "run", "start:auth"]
```

### Docker Compose 配置
```yaml
version: '3.8'
services:
  auth-service:
    build: .
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - MONGODB_URI=mongodb://mongo:27017/football-manager
    ports:
      - "3001:3001"
```

## 📋 环境变量清单

### 核心环境变量

| 变量名 | 开发环境 | 生产环境 | 描述 |
|--------|----------|----------|------|
| `NODE_ENV` | `development` | `production` | 运行环境标识 |
| `HTTP_PORT` | `3001` | `3001` | HTTP服务端口 |
| `ENABLE_RATE_LIMIT` | `false` | `true` | 是否启用限流 |

### 安全相关变量

| 变量名 | 开发环境 | 生产环境 | 描述 |
|--------|----------|----------|------|
| `ALLOWED_IPS` | `127.0.0.1,::1` | `生产IP列表` | IP白名单 |
| `ENABLE_SERVICE_AUTH` | `false` | `true` | 服务间认证 |
| `JWT_SECRET` | `开发密钥` | `生产密钥` | JWT签名密钥 |

### 数据库连接

| 变量名 | 开发环境 | 生产环境 | 描述 |
|--------|----------|----------|------|
| `MONGODB_URI` | `本地MongoDB` | `生产MongoDB` | 数据库连接 |
| `REDIS_HOST` | `***************` | `生产Redis` | Redis主机 |
| `REDIS_PORT` | `6379` | `6379` | Redis端口 |

## 🔍 环境验证

### 验证环境变量加载
```typescript
// 在应用启动时验证
console.log('当前环境:', process.env.NODE_ENV);
console.log('限流状态:', process.env.ENABLE_RATE_LIMIT);
console.log('HTTP端口:', process.env.HTTP_PORT);
```

### 健康检查端点
```bash
# 检查服务状态和环境信息
curl http://localhost:3001/health/detailed
```

## 🚨 安全注意事项

### 1. 敏感信息保护
- 生产环境密钥不得提交到版本控制
- 使用环境变量或密钥管理服务
- 定期轮换密钥和令牌

### 2. 环境隔离
- 开发和生产环境完全隔离
- 不同环境使用不同的数据库和Redis实例
- 严格控制生产环境访问权限

### 3. 配置验证
- 启动时验证必需的环境变量
- 使用配置模式验证
- 记录配置加载过程

## 🛠️ 故障排除

### 常见问题

#### 1. 环境变量未生效
```bash
# 检查 process.env.NODE_ENV（进程级别）
echo $NODE_ENV

# 在应用中验证两个值是否一致
console.log('process.env.NODE_ENV:', process.env.NODE_ENV);
console.log('configService.get NODE_ENV:', configService.get('NODE_ENV'));

# 查看应用启动日志中的环境信息
```

#### 2. NODE_ENV 值不一致问题
**症状**：`process.env.NODE_ENV` 和 `configService.get('NODE_ENV')` 返回不同值

**原因**：在 `.env` 文件中设置了 `NODE_ENV`，但启动时没有使用 `cross-env`

**解决方案**：
```bash
# ❌ 错误做法
# .env 文件中: NODE_ENV=production
npm start

# ✅ 正确做法
cross-env NODE_ENV=production npm start
```

#### 2. 限流功能异常
```bash
# 确认环境和限流配置
grep -r "ENABLE_RATE_LIMIT" apps/auth/.env*

# 检查守卫是否正确注册
# 查看 ThrottlerBehindProxyGuard 的执行日志
```

#### 3. 跨平台兼容性
```bash
# Windows 环境使用 cross-env
npm install cross-env --save-dev

# 确保所有启动脚本都使用 cross-env
```

## 📚 最佳实践

### 1. 开发阶段
- **必须使用 `cross-env`** 设置 `NODE_ENV`，不要在 `.env` 文件中设置
- 确保 `process.env.NODE_ENV` 和 `configService.get('NODE_ENV')` 一致
- 本地开发时禁用限流和严格安全策略
- 使用详细日志便于调试

### 2. 部署阶段
- 生产环境优先使用系统环境变量
- 容器化部署使用 `ENV` 指令设置环境变量
- 实施配置验证和健康检查

### 3. 维护阶段
- 定期审查和更新环境配置
- 监控环境变量的使用情况
- 建立配置变更的审批流程

## 🔗 相关文档

- [NestJS 配置管理](https://docs.nestjs.com/techniques/configuration)
- [限流功能配置](./rate-limiting-configuration.md)
- [安全配置指南](./security-configuration.md)
- [Docker 部署指南](./docker-deployment.md)

---

**更新日期**: 2025-07-04  
**版本**: v1.0.0  
**维护者**: 开发团队
