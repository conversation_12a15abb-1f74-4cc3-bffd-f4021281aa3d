# 认证服务分层架构设计

## 🎯 **架构概述**

认证服务采用清晰的分层架构设计，确保代码的可维护性、可扩展性和可测试性。每一层都有明确的职责边界和依赖方向。

## 🏗️ **分层结构**

```
apps/auth/src/
├── app/                       # 应用层 - 系统管理和运维功能
│   ├── admin/                 # 管理后台
│   └── health/                # 健康检查
├── domain/                    # 业务层 - 核心业务逻辑
│   ├── auth/                  # 认证业务
│   ├── users/                 # 用户管理
│   ├── roles/                 # 角色管理
│   └── permissions/           # 权限管理
├── core/                      # 核心层 - 领域核心服务
│   ├── security/              # 安全核心
│   ├── session/               # 会话核心
│   └── shared/                # 共享核心服务
├── infrastructure/            # 基础设施层 - 技术基础设施
│   ├── guards/                # 守卫组件
│   ├── interceptors/          # 拦截器组件
│   ├── pipes/                 # 管道组件
│   ├── filters/               # 过滤器组件
│   └── services/              # 基础设施服务
├── common/                    # 通用层 - 跨层共享资源
│   ├── constants/             # 常量定义
│   ├── interfaces/            # 接口定义
│   ├── dto/                   # 数据传输对象
│   ├── decorators/            # 装饰器
│   └── utils/                 # 工具函数
└── config/                    # 配置层
```

## 📋 **各层职责详解**

### **应用层 (Application Layer)**

**目录**: `app/`

**职责**:
- 系统管理功能
- 运维监控功能
- 跨业务模块协调
- 面向系统管理员

**包含模块**:
- `AdminModule`: 管理后台功能
- `HealthModule`: 健康检查和监控

**设计原则**:
- ✅ 可以依赖业务层、核心层、基础设施层
- ❌ 不应该被其他层依赖
- ✅ 面向系统运维需求
- ✅ 提供跨模块的管理功能

**示例代码**:
```typescript
// app/admin/admin.controller.ts
@Controller('admin')
export class AdminController {
  constructor(
    private usersService: UsersService,     // 依赖业务层
    private rolesService: RolesService,     // 依赖业务层
    private securityService: SecurityService // 依赖核心层
  ) {}
}
```

### **业务层 (Domain Layer)**

**目录**: `domain/`

**职责**:
- 实现核心业务逻辑
- 定义业务实体和规则
- 处理业务用例
- 编排核心层服务

**包含模块**:
- `AuthModule`: 认证和授权业务
- `UsersModule`: 用户管理业务
- `RolesModule`: 角色管理业务
- `PermissionsModule`: 权限管理业务

**设计原则**:
- ✅ 可以依赖核心层、基础设施层
- ❌ 不应该依赖应用层
- ✅ 包含业务规则和流程
- ✅ 定义领域实体

**示例代码**:
```typescript
// domain/auth/auth.service.ts
@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,       // 同层依赖
    private passwordService: PasswordService, // 依赖核心层
    private sessionService: SessionService    // 依赖核心层
  ) {}
  
  async login(credentials: LoginDto): Promise<AuthResult> {
    // 业务逻辑实现
  }
}
```

### **核心层 (Core Layer)**

**目录**: `core/`

**职责**:
- 提供领域核心服务
- 实现安全策略
- 管理会话和状态
- 提供基础业务能力

**包含模块**:
- `SecurityModule`: 安全核心服务
- `SessionModule`: 会话管理服务
- `CoreModule`: 共享核心服务

**设计原则**:
- ✅ 可以依赖基础设施层
- ❌ 不应该依赖业务层和应用层
- ✅ 包含领域核心逻辑
- ✅ 相对稳定，变化较少

**示例代码**:
```typescript
// core/shared/core.module.ts
@Module({
  providers: [
    PasswordService,    // 核心业务服务
    EncryptionService,  // 核心安全服务
    CryptoService,      // 依赖基础设施层
  ],
  exports: [
    PasswordService,
    EncryptionService,
    CryptoService,
  ],
})
export class CoreModule {}
```

### **基础设施层 (Infrastructure Layer)**

**目录**: `infrastructure/`

**职责**:
- 提供技术基础设施
- 实现横切关注点
- 提供框架集成
- 处理外部系统集成

**包含模块**:
- `GuardsModule`: 守卫组件
- `InterceptorsModule`: 拦截器组件
- `PipesModule`: 管道组件
- `FiltersModule`: 过滤器组件
- `SharedModule`: 基础设施协调

**设计原则**:
- ❌ 不应该依赖上层模块
- ✅ 可以依赖通用层
- ✅ 提供技术能力
- ✅ 可替换性强

**示例代码**:
```typescript
// infrastructure/guards/guards.module.ts
@Module({
  imports: [
    RolesModule, // 特殊情况：守卫需要业务服务
  ],
  providers: [
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
  exports: [
    JwtAuthGuard,
    RolesGuard,
    PermissionsGuard,
  ],
})
export class GuardsModule {}
```

### **通用层 (Common Layer)**

**目录**: `common/`

**职责**:
- 提供跨层共享资源
- 定义通用接口和类型
- 提供工具函数
- 定义常量和配置

**包含内容**:
- `constants/`: 常量定义
- `interfaces/`: 接口定义
- `dto/`: 数据传输对象
- `decorators/`: 装饰器
- `utils/`: 工具函数

**设计原则**:
- ❌ 不应该依赖其他层
- ✅ 可以被任何层使用
- ✅ 保持简单和纯净
- ✅ 高度可重用

**示例代码**:
```typescript
// common/interfaces/auth.interface.ts
export interface AuthResult {
  user: UserDocument;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// common/constants/auth.constants.ts
export const AUTH_CONSTANTS = {
  JWT_EXPIRATION: '15m',
  REFRESH_TOKEN_EXPIRATION: '7d',
  MAX_LOGIN_ATTEMPTS: 5,
} as const;
```

## 🔄 **依赖方向规则**

### **允许的依赖方向**

```
应用层 ──→ 业务层 ──→ 核心层 ──→ 基础设施层
   ↓         ↓         ↓         ↓
   └─────────└─────────└─────────└──→ 通用层
```

### **依赖规则详解**

1. **向下依赖**: 上层可以依赖下层
2. **通用层依赖**: 任何层都可以依赖通用层
3. **禁止向上依赖**: 下层不能依赖上层
4. **同层依赖**: 同层模块间可以相互依赖，但需要注意循环依赖

### **特殊情况处理**

#### **循环依赖解决**
```typescript
// 使用 forwardRef 解决必要的循环依赖
@Module({
  imports: [
    forwardRef(() => RolesModule),
  ],
  providers: [PermissionsService],
  exports: [PermissionsService],
})
export class PermissionsModule {}
```

#### **跨层服务注入**
```typescript
// 基础设施层需要业务层服务的特殊情况
@Module({
  imports: [
    RolesModule, // GuardsModule 需要 RolesService
  ],
  providers: [PermissionsGuard],
  exports: [PermissionsGuard],
})
export class GuardsModule {}
```

## 🎯 **设计优势**

### **1. 清晰的职责分离**
- 每层都有明确的职责边界
- 便于团队协作和代码维护
- 降低模块间的耦合度

### **2. 良好的可扩展性**
- 新功能可以按层次有序添加
- 支持水平和垂直扩展
- 便于微服务拆分

### **3. 高度可测试性**
- 每层可以独立测试
- 便于模拟和存根
- 支持单元测试和集成测试

### **4. 技术栈无关性**
- 基础设施层可以独立替换
- 核心业务逻辑不依赖具体技术
- 支持渐进式技术升级

## 📝 **开发指南**

### **新功能开发流程**

1. **确定功能归属层次**
2. **设计接口和数据结构** (通用层)
3. **实现核心业务逻辑** (核心层/业务层)
4. **添加基础设施支持** (基础设施层)
5. **提供应用级接口** (应用层)

### **模块创建规范**

1. **选择合适的层次目录**
2. **遵循命名约定**
3. **明确模块依赖关系**
4. **编写架构测试**
5. **更新文档**

### **重构指导原则**

1. **保持依赖方向正确**
2. **避免跨层直接调用**
3. **使用接口抽象依赖**
4. **定期检查循环依赖**
5. **保持层次边界清晰**

这种分层架构确保了认证服务的长期可维护性和扩展性，为团队提供了清晰的开发指导和架构约束。
