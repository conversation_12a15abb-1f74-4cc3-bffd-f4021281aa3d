# Windows 11 测试环境配置和执行指南

## 🎯 **快速开始**

### **前置条件检查**
在开始之前，请确保您的Windows 11系统满足以下条件：
- Windows 11 专业版或企业版
- 至少8GB内存
- 至少20GB可用磁盘空间
- 管理员权限

## 🚀 **方案A: Docker Desktop + WSL2 (推荐)**

### **步骤1: 安装WSL2**

1. **启用WSL功能**
   ```powershell
   # 以管理员身份运行PowerShell
   Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux
   Enable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform
   ```

2. **重启计算机**

3. **安装WSL2内核更新**
   - 下载: https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi
   - 双击安装

4. **设置WSL2为默认**
   ```powershell
   wsl --set-default-version 2
   ```

5. **安装Ubuntu**
   ```powershell
   wsl --install -d Ubuntu-22.04
   ```

### **步骤2: 安装Docker Desktop**

1. **下载并安装**
   - 访问: https://www.docker.com/products/docker-desktop/
   - 下载Windows版本并安装
   - 安装时选择"Use WSL 2 instead of Hyper-V"

2. **配置Docker**
   - 启动Docker Desktop
   - Settings → General → 确保"Use the WSL 2 based engine"已启用
   - Settings → Resources → WSL Integration → 启用Ubuntu-22.04

### **步骤3: 配置开发环境**

1. **在WSL2中安装Node.js**
   ```bash
   # 进入WSL2
   wsl
   
   # 安装Node.js 18
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # 验证
   node --version
   npm --version
   ```

### **步骤4: 执行测试**

1. **进入WSL2并克隆项目**
   ```bash
   wsl
   cd ~
   # 假设项目已经在Windows中，映射到WSL2
   cd /mnt/e/football\ manager/server-new
   ```

2. **安装依赖并构建**
   ```bash
   npm install
   npm run build
   ```

3. **执行测试**
   ```bash
   # 给脚本执行权限
   chmod +x scripts/run-comprehensive-tests.sh
   
   # 执行完整测试
   ./scripts/run-comprehensive-tests.sh all
   ```

## 🚀 **方案B: 纯Windows PowerShell (备选)**

### **步骤1: 安装Docker Desktop**
- 同方案A的步骤2

### **步骤2: 在Windows中安装Node.js**
1. **下载Node.js**
   - 访问: https://nodejs.org/
   - 下载LTS版本并安装

2. **验证安装**
   ```powershell
   node --version
   npm --version
   ```

### **步骤3: 执行测试**
```powershell
# 在项目根目录下
cd "E:\football manager\server-new"

# 安装依赖
npm install

# 构建项目
npm run build

# 执行测试
.\scripts\run-comprehensive-tests.ps1 all
```

## 🛠️ **手动测试步骤 (如果自动化脚本有问题)**

### **步骤1: 启动测试环境**

1. **启动数据库**
   ```powershell
   docker-compose -f docker-compose.test.yml up -d redis-test mongodb-test
   ```

2. **等待数据库启动**
   ```powershell
   # 等待30秒
   Start-Sleep -Seconds 30
   
   # 验证Redis
   docker exec (docker-compose -f docker-compose.test.yml ps -q redis-test) redis-cli -a test123 ping
   
   # 验证MongoDB
   docker exec (docker-compose -f docker-compose.test.yml ps -q mongodb-test) mongosh --eval "db.runCommand('ping')"
   ```

3. **构建并启动服务**
   ```powershell
   # 构建项目
   npm run build
   
   # 构建Docker镜像
   docker-compose -f docker-compose.test.yml build
   
   # 启动所有服务
   docker-compose -f docker-compose.test.yml up -d
   ```

4. **验证服务启动**
   ```powershell
   # 等待服务启动
   Start-Sleep -Seconds 60
   
   # 检查Auth服务
   Invoke-RestMethod -Uri "http://localhost:3002/health"
   
   # 检查Gateway服务
   Invoke-RestMethod -Uri "http://localhost:3001/health"
   ```

### **步骤2: 执行基础功能测试**

1. **测试Auth服务注册**
   ```powershell
   $registerData = @{
       username = "testuser"
       email = "<EMAIL>"
       password = "Test123!@#"
       confirmPassword = "Test123!@#"
       firstName = "Test"
       lastName = "User"
   } | ConvertTo-Json
   
   Invoke-RestMethod -Uri "http://localhost:3002/auth/register" -Method Post -Body $registerData -ContentType "application/json"
   ```

2. **测试Auth服务登录**
   ```powershell
   $loginData = @{
       identifier = "testuser"
       password = "Test123!@#"
   } | ConvertTo-Json
   
   $loginResponse = Invoke-RestMethod -Uri "http://localhost:3002/auth/login" -Method Post -Body $loginData -ContentType "application/json"
   $token = $loginResponse.data.tokens.accessToken
   ```

3. **测试Gateway代理**
   ```powershell
   $headers = @{
       "Authorization" = "Bearer $token"
   }
   
   Invoke-RestMethod -Uri "http://localhost:3001/api/users/me" -Headers $headers
   ```

### **步骤3: 清理环境**
```powershell
# 停止所有服务
docker-compose -f docker-compose.test.yml down --remove-orphans

# 清理数据卷
docker volume prune -f
```

## 🔧 **故障排除**

### **常见问题**

1. **WSL2安装失败**
   - 确保Windows版本支持WSL2
   - 在BIOS中启用虚拟化
   - 重启后重新尝试

2. **Docker启动失败**
   - 确保WSL2正常运行
   - 重启Docker Desktop
   - 检查Windows防火墙设置

3. **端口冲突**
   ```powershell
   # 检查端口占用
   netstat -ano | findstr :3001
   netstat -ano | findstr :3002
   netstat -ano | findstr :6380
   netstat -ano | findstr :27018
   ```

4. **内存不足**
   - 关闭不必要的应用程序
   - 增加Docker Desktop的内存限制
   - 考虑分批运行测试

### **性能优化**

1. **Docker Desktop设置**
   - Settings → Resources → 分配至少4GB内存
   - Settings → Resources → 分配至少2个CPU核心

2. **WSL2优化**
   ```bash
   # 在WSL2中限制内存使用
   echo '[wsl2]
   memory=4GB
   processors=2' > /mnt/c/Users/<USER>/.wslconfig
   ```

## 📊 **测试结果查看**

测试完成后，结果将保存在以下位置：
- `test-results/` - 测试结果文件
- `test-logs/` - 测试日志文件
- `test-results/test-report.html` - HTML测试报告

## 🎯 **推荐的测试执行顺序**

1. **第一次运行**: 使用方案A (WSL2)，执行基础连通性测试
2. **验证环境**: 手动执行几个基础API测试
3. **完整测试**: 运行自动化测试脚本
4. **问题修复**: 根据测试结果修复发现的问题
5. **回归测试**: 重新运行测试验证修复效果

## 💡 **小贴士**

- 首次运行可能需要较长时间下载Docker镜像
- 建议在网络状况良好时进行测试
- 可以先运行单元测试，再逐步运行其他类型的测试
- 遇到问题时，查看Docker容器日志：`docker-compose -f docker-compose.test.yml logs`

现在您可以根据这个指南配置Windows 11测试环境并开始执行测试了！

## 第一步：环境准备
1. 安装WSL2和Docker Desktop（按照 docs/windows-testing-guide.md 中的详细步骤）
2. 验证环境：
```bash
# 检查Docker
docker --version
docker-compose --version

# 检查Node.js
node --version
npm --version

wsl --version
```
## 第二步：快速验证
```bash
# 在项目根目录执行
.\scripts\quick-test.ps1
```
这个脚本会快速验证：
* ✅ 服务健康状态
* ✅ 用户注册/登录功能
* ✅ JWT令牌验证
* ✅ Gateway代理功能
* ✅ 限流机制
* ✅ 数据库连接

## 第三步：完整测试
```bash
# 执行完整的综合测试
.\scripts\run-comprehensive-tests.ps1 all

# 或者分步执行
.\scripts\run-comprehensive-tests.ps1 unit
.\scripts\run-comprehensive-tests.ps1 integration
.\scripts\run-comprehensive-tests.ps1 e2e
```
## 🛠️ 如果遇到问题的备选方案
### 手动测试方案
如果自动化脚本有问题，您可以：

1. 手动测试方案
```bash
 docker-compose -f docker-compose.test.yml up -d
```
2. 手动启动服务：
```bash
   # 测试注册
$userData = @{
username = "testuser"
email = "<EMAIL>"
password = "Test123!@#"
confirmPassword = "Test123!@#"
firstName = "Test"
lastName = "User"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3002/auth/register" -Method Post -Body $userData -ContentType "application/json"
```
## 📁 已创建的文件
1. docs/comprehensive-testing-plan.md - 完整的测试计划文档
2. docs/windows-testing-guide.md - Windows 11环境配置指南
3. scripts/run-comprehensive-tests.ps1 - PowerShell版本的完整测试脚本
4. scripts/quick-test.ps1 - 快速功能验证脚本
5. docker-compose.test.yml - 测试环境Docker配置
6. test/config/test.config.ts - 测试配置文件
7. test/utils/test-helpers.ts - 测试工具函数
## 🚀 建议的执行顺序
1. 立即开始：按照Windows指南安装WSL2和Docker Desktop
2. 第一次验证：运行快速测试脚本验证基础功能
3. 深入测试：运行完整的综合测试
4. 问题修复：根据测试结果修复发现的问题
5. 回归验证：重新运行测试确保修复有效
## 💡 重要提示
* 首次运行需要下载Docker镜像，可能需要10-20分钟
* 确保有足够的内存（建议8GB以上）
* 网络状况良好时执行测试
* 遇到问题时查看Docker日志：docker-compose -f docker-compose.test.yml logs

现在您可以开始配置Windows 11测试环境了！有任何问题随时告诉我，我会协助您解决。🎯
