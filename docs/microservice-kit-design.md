# NestJS 微服务公共库设计方案

## ⚠️ 重要约定

**@MessagePattern 使用规范**：
- ✅ **正确**：`@MessagePattern('action')` - 只使用动作名
- ❌ **错误**：`@MessagePattern('service.action')` - 不要包含服务名前缀

这是因为微服务通信时，服务名已经在路由层处理，MessagePattern 只需要匹配具体的动作名即可。

## 📋 设计目标

基于 `@nestjs/microservices` 设计一个专业优雅的微服务公共库，解决以下核心问题：

1. **消除重复代码** - 不需要每个微服务重复创建 `ClientsModule.register`、`ClientProxy` 以及 `NestFactory.createMicroservice` 等代码
2. **集成化** - 提供开箱即用的微服务基础设施
3. **易用性** - 简化微服务的开发和部署流程

## 🏗️ 核心架构

### 架构原则
- **约定优于配置** - 通过合理的默认配置减少样板代码
- **统一配置管理** - 集中管理所有微服务的连接配置
- **自动化基础设施** - 自动创建和管理微服务客户端和服务端
- **模块化设计** - 支持客户端、服务端、混合三种使用模式

### 目录结构
```
libs/microservice-kit/
├── src/
│   ├── client/                 # 客户端模块
│   │   ├── microservice-client.module.ts
│   │   └── microservice-client.service.ts
│   ├── server/                 # 服务端模块
│   │   ├── microservice-server.module.ts
│   │   └── microservice-server.service.ts
│   ├── config/                 # 配置管理
│   │   ├── microservice.config.ts
│   │   └── default.config.ts
│   ├── utils/                  # 工具函数
│   │   └── microservice-bootstrap.ts
│   ├── microservice-kit.module.ts
│   └── index.ts
└── README.md
```

## 🔧 核心组件设计

### 1. 统一配置管理

#### 配置接口定义
```typescript
export interface MicroserviceConfig {
  name: string;           // 服务注入令牌名称
  transport: Transport;   // 传输方式
  options: any;          // 传输选项
  port?: number;         // 服务端口（可选）
}

export interface MicroserviceKitConfig {
  services: Record<string, MicroserviceConfig>;  // 服务配置映射
  defaultTransport?: Transport;                   // 默认传输方式
  defaultOptions?: any;                          // 默认传输选项
}
```

#### 默认配置
```typescript
export const DEFAULT_MICROSERVICE_CONFIG: MicroserviceKitConfig = {
  services: {
    auth: {
      name: 'AUTH_SERVICE',
      transport: Transport.REDIS,
      options: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
    },
    user: {
      name: 'CHARACTER_SERVICE', 
      transport: Transport.REDIS,
      options: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
    },
    // ... 其他服务配置
  },
  defaultTransport: Transport.REDIS,
  defaultOptions: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
  },
};
```

### 2. 自动客户端模块

#### MicroserviceClientModule
- **功能**：自动为所有配置的服务创建 `ClientProxy` 实例
- **特性**：
  - 全局模块，一次导入到处使用
  - 自动读取配置并创建客户端连接
  - 提供统一的服务调用接口

#### MicroserviceClientService
- **功能**：提供统一的微服务调用接口
- **核心方法**：
  - `getClient(serviceName: string): ClientProxy` - 获取指定服务的客户端
  - `call<T>(serviceName: string, pattern: string, data?: any): Promise<T>` - 调用微服务方法
  - `emit(serviceName: string, pattern: string, data?: any): void` - 发送事件到微服务
  - `getAvailableServices(): string[]` - 获取所有可用服务列表

### 3. 自动服务端模块

#### MicroserviceServerModule
- **功能**：简化微服务服务端的配置和启动
- **特性**：
  - 根据服务名自动读取配置
  - 提供服务端配置生成
  - 支持自定义配置覆盖

#### MicroserviceServerService
- **功能**：管理微服务服务端的生命周期
- **核心方法**：
  - `getServerConfig(): MicroserviceOptions` - 获取服务端配置
  - `bootstrap(appModule: any): Promise<INestMicroservice>` - 启动微服务

### 4. 便捷启动工具

#### bootstrapMicroservice
```typescript
export async function bootstrapMicroservice(
  appModule: any,
  serviceName: string,
  customConfig?: Partial<MicroserviceOptions>
): Promise<INestMicroservice>
```
- **功能**：一行代码启动微服务
- **特性**：自动读取配置、启动服务、输出日志

#### bootstrapHybridApp
```typescript
export async function bootstrapHybridApp(
  appModule: any,
  serviceName: string,
  httpPort: number = 3000,
  customConfig?: Partial<MicroserviceOptions>
): Promise<INestApplication>
```
- **功能**：启动混合应用（HTTP + 微服务）
- **特性**：同时支持 HTTP 接口和微服务通信

### 5. 统一模块入口

#### MicroserviceKitModule
提供三种使用模式：

1. **客户端模式** - `forClient(config?)`
   - 用于网关等需要调用其他微服务的应用
   - 自动创建所有服务的客户端连接

2. **服务端模式** - `forServer(serviceName)`
   - 用于具体的微服务应用
   - 提供服务端配置和启动支持

3. **混合模式** - `forHybrid(serviceName, config?)`
   - 既是客户端又是服务端
   - 适用于需要调用其他服务的微服务

## 📚 使用指南

### 网关应用（客户端模式）

#### 方式1：连接所有微服务（适用于网关等需要访问多个服务的应用）
```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forClient(), // 连接所有配置的微服务
  ],
  controllers: [WebSocketGateway],
})
export class AppModule {}
```

#### 方式2：选择性连接（推荐 - 按需连接特定服务）
```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forClient({
      // 只连接网关需要的服务
      services: ['auth', 'user', 'notification']
    }),
  ],
  controllers: [WebSocketGateway],
})
export class AppModule {}

// apps/gateway/src/websocket.gateway.ts
@WebSocketGateway()
export class WebSocketGateway {
  constructor(private microserviceClient: MicroserviceClientService) {}

  @SubscribeMessage('auth.login')
  async handleLogin(@MessageBody() data: any) {
    // 直接调用，无需配置 ClientProxy
    return this.microserviceClient.call('auth', 'login', data);
  }
}

// apps/gateway/src/main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.listen(3000);
}
bootstrap();
```

### 微服务应用（服务端模式）

```typescript
// apps/auth/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forServer('auth'), // 一行代码搞定微服务配置
  ],
  controllers: [AuthController],
  providers: [AuthService],
})
export class AppModule {}

// apps/auth/src/main.ts
import { bootstrapMicroservice } from '@microservice-kit';

async function bootstrap() {
  await bootstrapMicroservice(AppModule, 'auth'); // 一行代码启动微服务
}
bootstrap();
```

### 混合应用（既调用又提供服务）

#### 方式1：连接所有服务（不推荐）
```typescript
// apps/user/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forHybrid('user'), // 连接所有服务
  ],
  controllers: [UserController],
  providers: [UserService],
})
export class AppModule {}
```

#### 方式2：选择性连接（推荐 - 只连接需要的服务）
```typescript
// apps/user/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forHybrid('user', {
      // 用户服务只需要调用认证服务
      services: ['auth']
    }),
  ],
  controllers: [UserController],
  providers: [UserService],
})
export class AppModule {}

// apps/user/src/user.service.ts
@Injectable()
export class UserService {
  constructor(private microserviceClient: MicroserviceClientService) {}

  async getUserWithPermissions(userId: string) {
    const user = await this.findById(userId);

    // 只调用认证服务检查权限
    const permissions = await this.microserviceClient.call(
      'auth',
      'getUserPermissions',
      { userId }
    );

    return { ...user, permissions };
  }
}
```

## 🎯 方案优势

### 1. 消除重复代码
- ✅ 无需重复配置 `ClientsModule.register`
- ✅ 无需重复创建 `ClientProxy`
- ✅ 无需重复写 `NestFactory.createMicroservice`
- ✅ 统一的服务调用接口

### 2. 集成化设计
- ✅ 开箱即用的微服务基础设施
- ✅ 自动服务发现和连接管理
- ✅ 统一的配置管理
- ✅ 完整的错误处理和日志

### 3. 易用性
- ✅ 一行代码导入模块
- ✅ 一行代码启动微服务
- ✅ 简洁的 API 设计
- ✅ 完整的 TypeScript 支持

### 4. 可扩展性
- ✅ 支持多种传输方式（Redis、TCP、gRPC 等）
- ✅ 灵活的配置覆盖机制
- ✅ 插件化的扩展支持
- ✅ 环境变量自动读取

## 🚀 实施计划

### 第一阶段：核心功能
1. 实现统一配置管理
2. 实现自动客户端模块
3. 实现自动服务端模块
4. 实现便捷启动工具

### 第二阶段：增强功能
1. 添加健康检查
2. 添加性能监控
3. 添加链路追踪
4. 添加配置热更新

### 第三阶段：生态完善
1. 编写完整文档
2. 添加单元测试
3. 性能优化
4. 社区反馈收集

## 📖 技术规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 NestJS 官方编码规范
- 完整的 JSDoc 注释
- 100% 单元测试覆盖率

### 版本管理
- 遵循语义化版本控制
- 向后兼容性保证
- 详细的变更日志
- 平滑的升级路径

### 性能要求
- 启动时间 < 2 秒
- 内存占用 < 50MB
- 连接建立时间 < 100ms
- 支持 1000+ 并发连接

## 💡 核心实现细节

### 自动客户端注册机制

```typescript
// libs/microservice-kit/src/client/microservice-client.module.ts
@Global()
@Module({})
export class MicroserviceClientModule {
  static forRoot(config: MicroserviceKitConfig): DynamicModule {
    // 自动为所有服务创建 ClientProxy
    const clientConfigs = Object.values(config.services).map(service => ({
      name: service.name,
      transport: service.transport,
      options: service.options,
    }));

    return {
      module: MicroserviceClientModule,
      imports: [
        ClientsModule.register(clientConfigs), // 一次性注册所有客户端
      ],
      providers: [
        {
          provide: 'MICROSERVICE_CONFIG',
          useValue: config,
        },
        MicroserviceClientService,
      ],
      exports: [ClientsModule, MicroserviceClientService],
    };
  }
}
```

### 统一服务调用接口

```typescript
// libs/microservice-kit/src/client/microservice-client.service.ts
@Injectable()
export class MicroserviceClientService {
  private clients = new Map<string, ClientProxy>();

  constructor(
    @Inject('MICROSERVICE_CONFIG') private config: MicroserviceKitConfig,
    private moduleRef: ModuleRef,
  ) {
    this.initializeClients();
  }

  private initializeClients() {
    Object.entries(this.config.services).forEach(([key, service]) => {
      try {
        const client = this.moduleRef.get<ClientProxy>(service.name, { strict: false });
        this.clients.set(key, client);
        console.log(`✅ 微服务客户端已连接: ${key} (${service.name})`);
      } catch (error) {
        console.error(`❌ 微服务客户端连接失败: ${key}`, error);
      }
    });
  }

  /**
   * 调用微服务方法 - 统一的调用接口
   */
  async call<T = any>(serviceName: string, pattern: string, data?: any): Promise<T> {
    const client = this.getClient(serviceName);
    return client.send<T>(pattern, data).toPromise();
  }

  /**
   * 发送事件到微服务 - 异步事件发送
   */
  emit(serviceName: string, pattern: string, data?: any): void {
    const client = this.getClient(serviceName);
    client.emit(pattern, data);
  }

  private getClient(serviceName: string): ClientProxy {
    const client = this.clients.get(serviceName);
    if (!client) {
      throw new Error(`微服务客户端未找到: ${serviceName}`);
    }
    return client;
  }
}
```

### 便捷启动工具实现

```typescript
// libs/microservice-kit/src/utils/microservice-bootstrap.ts
/**
 * 快速启动微服务的工具函数
 */
export async function bootstrapMicroservice(
  appModule: any,
  serviceName: string,
  customConfig?: Partial<MicroserviceOptions>
) {
  const serviceConfig = DEFAULT_MICROSERVICE_CONFIG.services[serviceName];

  if (!serviceConfig) {
    throw new Error(`未找到服务配置: ${serviceName}`);
  }

  const config: MicroserviceOptions = {
    transport: serviceConfig.transport,
    options: {
      ...serviceConfig.options,
      ...customConfig?.options,
    },
  };

  console.log(`🚀 启动微服务: ${serviceName}`);
  console.log(`📡 传输方式: ${config.transport}`);
  console.log(`⚙️  配置选项:`, config.options);

  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    appModule,
    config,
  );

  await app.listen();
  console.log(`✅ 微服务 ${serviceName} 启动成功`);

  return app;
}
```

## 🔄 与现有代码的对比

### 传统方式（重复代码）

```typescript
// 每个微服务都需要重复这些配置

// apps/gateway/src/app.module.ts
@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'AUTH_SERVICE',
        transport: Transport.REDIS,
        options: {
          host: 'localhost',
          port: 6379,
        },
      },
      {
        name: 'CHARACTER_SERVICE',
        transport: Transport.REDIS,
        options: {
          host: 'localhost',
          port: 6379,
        },
      },
      // ... 更多重复配置
    ]),
  ],
})
export class AppModule {}

// apps/gateway/src/some.service.ts
@Injectable()
export class SomeService {
  constructor(
    @Inject('AUTH_SERVICE') private authClient: ClientProxy,
    @Inject('CHARACTER_SERVICE') private userClient: ClientProxy,
  ) {}

  async someMethod() {
    const authResult = await this.authClient.send('verify', data).toPromise();
    const userResult = await this.userClient.send('getProfile', data).toPromise();
  }
}

// apps/auth/src/main.ts
async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.REDIS,
      options: {
        host: 'localhost',
        port: 6379,
      },
    },
  );
  await app.listen();
}
bootstrap();
```

### 使用微服务公共库（零重复）

```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    MicroserviceKitModule.forClient(), // 一行代码替代所有配置
  ],
})
export class AppModule {}

// apps/gateway/src/some.service.ts
@Injectable()
export class SomeService {
  constructor(private microserviceClient: MicroserviceClientService) {}

  async someMethod() {
    const authResult = await this.microserviceClient.call('auth', 'verify', data);
    const userResult = await this.microserviceClient.call('user', 'getProfile', data);
  }
}

// apps/auth/src/main.ts
import { bootstrapMicroservice } from '@microservice-kit';

async function bootstrap() {
  await bootstrapMicroservice(AppModule, 'auth'); // 一行代码启动
}
bootstrap();
```

## 📊 效果对比

| 对比项 | 传统方式 | 微服务公共库 | 改进效果 |
|--------|----------|--------------|----------|
| 配置代码行数 | 50+ 行 | 1 行 | 减少 98% |
| 客户端注入 | 每个服务单独注入 | 统一服务注入 | 简化 90% |
| 启动代码 | 15+ 行 | 1 行 | 减少 93% |
| 配置维护 | 分散在各个服务 | 集中统一管理 | 维护性提升 |
| 新增服务成本 | 需要修改多处 | 只需添加配置 | 扩展性提升 |

## 🛠️ 开发工具支持

### VS Code 代码片段

```json
{
  "NestJS Microservice Client": {
    "prefix": "nest-micro-client",
    "body": [
      "@Module({",
      "  imports: [",
      "    MicroserviceKitModule.forClient(),",
      "  ],",
      "})",
      "export class ${1:AppModule} {}"
    ],
    "description": "创建微服务客户端模块"
  },
  "NestJS Microservice Server": {
    "prefix": "nest-micro-server",
    "body": [
      "import { bootstrapMicroservice } from '@microservice-kit';",
      "",
      "async function bootstrap() {",
      "  await bootstrapMicroservice(AppModule, '${1:serviceName}');",
      "}",
      "bootstrap();"
    ],
    "description": "创建微服务启动代码"
  }
}
```

### CLI 工具支持

```bash
# 生成微服务模板
npx @microservice-kit/cli generate service auth

# 启动开发环境
npx @microservice-kit/cli dev --services auth,user,gateway

# 构建所有服务
npx @microservice-kit/cli build --all
```

这个设计方案彻底解决了 NestJS 微服务开发中的重复代码问题，提供了一个真正易用、集成化的解决方案。通过统一的配置管理、自动化的基础设施和便捷的启动工具，开发者可以专注于业务逻辑的实现，而不需要关心微服务的基础设施配置。
