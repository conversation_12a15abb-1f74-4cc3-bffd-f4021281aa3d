import { Injectable, Logger } from '@nestjs/common';
import { ConfigManager } from './config-manager.service';
import { CacheManager } from './cache-manager.service';
import { ConfigLoader } from './config-loader.service';
import { EventManager } from './event-manager.service';
import { ConfigPreloader } from './config-preloader.service';

/**
 * 配置健康检查服务
 * 提供全面的配置系统健康状态检查
 */
@Injectable()
export class ConfigHealthService {
  private readonly logger = new Logger(ConfigHealthService.name);

  constructor(
    private readonly configManager: ConfigManager,
    private readonly cacheManager: CacheManager,
    private readonly configLoader: ConfigLoader,
    private readonly eventManager: EventManager,
    private readonly configPreloader: ConfigPreloader,
  ) {}

  /**
   * 执行完整的健康检查
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    this.logger.log('Starting comprehensive health check...');
    const startTime = Date.now();

    const result: HealthCheckResult = {
      overall: 'healthy',
      timestamp: new Date(),
      duration: 0,
      checks: {},
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0,
      },
    };

    try {
      // 执行各项检查
      result.checks.loader = await this.checkConfigLoader();
      result.checks.cache = await this.checkCacheManager();
      result.checks.manager = await this.checkConfigManager();
      result.checks.events = await this.checkEventManager();
      result.checks.preloader = await this.checkPreloader();
      result.checks.data = await this.checkDataIntegrity();
      result.checks.performance = await this.checkPerformance();

      // 计算总体状态
      const allChecks = Object.values(result.checks);
      result.summary.total = allChecks.length;
      result.summary.passed = allChecks.filter(c => c.status === 'healthy').length;
      result.summary.failed = allChecks.filter(c => c.status === 'unhealthy').length;
      result.summary.warnings = allChecks.filter(c => c.status === 'warning').length;

      // 确定总体状态
      if (result.summary.failed > 0) {
        result.overall = 'unhealthy';
      } else if (result.summary.warnings > 0) {
        result.overall = 'warning';
      } else {
        result.overall = 'healthy';
      }

      result.duration = Date.now() - startTime;
      
      this.logger.log(`Health check completed: ${result.overall} (${result.duration}ms)`);
      return result;

    } catch (error) {
      this.logger.error('Health check failed', error.stack);
      
      result.overall = 'unhealthy';
      result.duration = Date.now() - startTime;
      result.checks.system = {
        status: 'unhealthy',
        message: `Health check system error: ${error.message}`,
        details: { error: error.message },
      };

      return result;
    }
  }

  /**
   * 检查配置加载器
   */
  private async checkConfigLoader(): Promise<HealthCheckItem> {
    try {
      const availableTables = this.configLoader.getAvailableTables();
      
      if (availableTables.length === 0) {
        return {
          status: 'unhealthy',
          message: 'No config tables available',
          details: { availableTables: 0 },
        };
      }

      // 测试加载一个配置
      const testTable = availableTables[0];
      const isValid = await this.configLoader.validateConfigFile(testTable);
      
      if (!isValid) {
        return {
          status: 'warning',
          message: `Test config file validation failed: ${testTable}`,
          details: { testTable, availableTables: availableTables.length },
        };
      }

      return {
        status: 'healthy',
        message: 'Config loader is working properly',
        details: { availableTables: availableTables.length },
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Config loader error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查缓存管理器
   */
  private async checkCacheManager(): Promise<HealthCheckItem> {
    try {
      const stats = this.cacheManager.getStats();
      const hitRatio = parseFloat(stats.hitRatio.replace('%', ''));
      
      // 检查缓存命中率
      if (hitRatio < 50) {
        return {
          status: 'warning',
          message: `Low cache hit ratio: ${stats.hitRatio}`,
          details: stats,
        };
      }

      // 检查内存使用
      const memoryUsage = (stats.memory.size / stats.memory.max) * 100;
      if (memoryUsage > 90) {
        return {
          status: 'warning',
          message: `High memory usage: ${memoryUsage.toFixed(1)}%`,
          details: stats,
        };
      }

      return {
        status: 'healthy',
        message: 'Cache manager is working properly',
        details: stats,
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Cache manager error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查配置管理器
   */
  private async checkConfigManager(): Promise<HealthCheckItem> {
    try {
      const healthStatus = await this.configManager.getHealthStatus();
      
      if (!healthStatus.isInitialized) {
        return {
          status: 'unhealthy',
          message: 'Config manager not initialized',
          details: healthStatus,
        };
      }

      if (healthStatus.availableTables < 5) {
        return {
          status: 'warning',
          message: `Low number of available tables: ${healthStatus.availableTables}`,
          details: healthStatus,
        };
      }

      // 测试基本操作
      const testResult = await this.testBasicOperations();
      if (!testResult.success) {
        return {
          status: 'unhealthy',
          message: `Basic operations test failed: ${testResult.error}`,
          details: { healthStatus, testResult },
        };
      }

      return {
        status: 'healthy',
        message: 'Config manager is working properly',
        details: healthStatus,
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Config manager error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查事件管理器
   */
  private async checkEventManager(): Promise<HealthCheckItem> {
    try {
      const eventStats = this.eventManager.getEventStats();
      
      if (!eventStats.isWatching) {
        return {
          status: 'warning',
          message: 'File watcher is not active',
          details: eventStats,
        };
      }

      return {
        status: 'healthy',
        message: 'Event manager is working properly',
        details: eventStats,
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Event manager error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查预加载器
   */
  private async checkPreloader(): Promise<HealthCheckItem> {
    try {
      const preloadHealth = await this.configPreloader.checkPreloadHealth();
      
      if (!preloadHealth.isHealthy) {
        return {
          status: 'warning',
          message: 'Preloader has issues',
          details: preloadHealth,
        };
      }

      return {
        status: 'healthy',
        message: 'Preloader is working properly',
        details: preloadHealth.stats,
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Preloader error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查数据完整性
   */
  private async checkDataIntegrity(): Promise<HealthCheckItem> {
    try {
      const issues: string[] = [];
      const availableTables = this.configLoader.getAvailableTables();
      
      // 检查核心表是否存在
      const coreTables = ['Hero', 'Item', 'HeroSkill', 'FormationCoordinate'];
      for (const table of coreTables) {
        if (!availableTables.includes(table)) {
          issues.push(`Missing core table: ${table}`);
        }
      }

      // 检查数据文件
      for (const table of availableTables.slice(0, 5)) { // 检查前5个表
        try {
          const configs = await this.configManager.getAll(table);
          if (configs.length === 0) {
            issues.push(`Empty table: ${table}`);
          }
        } catch (error) {
          issues.push(`Failed to load table: ${table}`);
        }
      }

      if (issues.length > 0) {
        return {
          status: 'warning',
          message: 'Data integrity issues found',
          details: { issues, availableTables: availableTables.length },
        };
      }

      return {
        status: 'healthy',
        message: 'Data integrity check passed',
        details: { availableTables: availableTables.length },
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Data integrity check error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 检查性能
   */
  private async checkPerformance(): Promise<HealthCheckItem> {
    try {
      const startTime = Date.now();
      
      // 执行性能测试
      const testTable = 'Hero';
      await this.configManager.getAll(testTable);
      await this.configManager.get(testTable, 1);
      
      const responseTime = Date.now() - startTime;
      
      if (responseTime > 1000) {
        return {
          status: 'warning',
          message: `Slow response time: ${responseTime}ms`,
          details: { responseTime },
        };
      }

      return {
        status: 'healthy',
        message: 'Performance check passed',
        details: { responseTime },
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Performance check error: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  /**
   * 测试基本操作
   */
  private async testBasicOperations(): Promise<{ success: boolean; error?: string }> {
    try {
      const testTable = 'Hero';
      
      // 测试getAll
      const allConfigs = await this.configManager.getAll(testTable);
      if (!Array.isArray(allConfigs)) {
        return { success: false, error: 'getAll returned non-array' };
      }

      // 测试get（如果有数据）
      if (allConfigs.length > 0) {
        const firstConfig = allConfigs[0];
        const singleConfig = await this.configManager.get(testTable, (firstConfig as any).id);
        if (!singleConfig) {
          return { success: false, error: 'get returned null for existing config' };
        }
      }

      return { success: true };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

/**
 * 健康检查结果接口
 */
export interface HealthCheckResult {
  overall: 'healthy' | 'warning' | 'unhealthy';
  timestamp: Date;
  duration: number;
  checks: Record<string, HealthCheckItem>;
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}

/**
 * 单项健康检查结果
 */
export interface HealthCheckItem {
  status: 'healthy' | 'warning' | 'unhealthy';
  message: string;
  details?: any;
}
