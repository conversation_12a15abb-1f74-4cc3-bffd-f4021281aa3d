// Auto-generated from DqCupSchedule.json
// Generated at: 2025-07-20T12:55:59.806Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface DqCupScheduleDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 报名开始(上轮比赛结束时间), 比赛报名结束，预选赛开始
  time: string; // 时间 例: 21:30, 13:00 (原: Time)
}

// 字段映射：新字段名 -> 原始字段名
export const DqCupScheduleFieldMappings = {
  time: 'Time',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const DqCupScheduleReverseFieldMappings = {
  'Time': 'time',
} as const;

export const DqCupScheduleMeta = {
  tableName: 'DqCupSchedule',
  dataFileName: 'DqCupSchedule.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 3,
  requiredFields: ['id', 'name', 'time'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: DqCupScheduleFieldMappings,
  reverseFieldMappings: DqCupScheduleReverseFieldMappings,
} as const;

export type DqCupScheduleConfigMeta = typeof DqCupScheduleMeta;
export type DqCupScheduleFieldMapping = typeof DqCupScheduleFieldMappings;
export type DqCupScheduleReverseFieldMapping = typeof DqCupScheduleReverseFieldMappings;
