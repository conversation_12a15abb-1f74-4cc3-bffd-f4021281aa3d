// Auto-generated from LeagueShop.json
// Generated at: 2025-07-20T12:56:04.844Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface LeagueShopDefinition {
  id: number; // 唯一标识符 例: 1, 2
  condition1: string; // 条件 例: 1*2*3*4*5*6 (原: Condition1)
  condition2: number; // 条件 例: 1 (原: Condition2)
  parameters: number; // 参数 例: 29991, 29992 (原: Parameters)
  price: number; // 价格 例: 35, 40 (原: Price)
  priceType: number; // 类型 例: 14 (原: PriceType)
  text: string; // 字符串 例: 加入转播联盟解锁 (原: Text)
}

// 字段映射：新字段名 -> 原始字段名
export const LeagueShopFieldMappings = {
  condition1: 'Condition1',
  condition2: 'Condition2',
  parameters: 'Parameters',
  price: 'Price',
  priceType: 'PriceType',
  text: 'Text',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const LeagueShopReverseFieldMappings = {
  'Condition1': 'condition1',
  'Condition2': 'condition2',
  'Parameters': 'parameters',
  'Price': 'price',
  'PriceType': 'priceType',
  'Text': 'text',
} as const;

export const LeagueShopMeta = {
  tableName: 'LeagueShop',
  dataFileName: 'LeagueShop.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 7,
  requiredFields: ['id', 'condition1', 'condition2', 'parameters', 'price', 'priceType', 'text'],
  optionalFields: [],
  renamedFieldsCount: 6,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: LeagueShopFieldMappings,
  reverseFieldMappings: LeagueShopReverseFieldMappings,
} as const;

export type LeagueShopConfigMeta = typeof LeagueShopMeta;
export type LeagueShopFieldMapping = typeof LeagueShopFieldMappings;
export type LeagueShopReverseFieldMapping = typeof LeagueShopReverseFieldMappings;
