// Auto-generated from Action.json
// Generated at: 2025-07-20T12:55:58.799Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ActionDefinition {
  actionId: number; // 唯一标识符 例: 1040, 1030 (原: ActionID)
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 转移边路, 底线传中
  a1: string; // 字符串 例: 发起阶段, 推进阶段 (原: A1)
  a2: string; // 字符串 例: 头球, 远射 (原: A2)
  attackMode: number; // 攻击力 例: 1, 2 (原: AttackMode)
  failComment: number; // 数值 例: 0, 30402 (原: FailComment)
  failMovieA: string; // 字符串 例: , C_3040_2 (原: FailMovie_A)
  failMovieB: string; // 字符串 例: , Z_3040_2 (原: FailMovie_B)
  period: number; // 数值 例: 1, 2 (原: Period)
  startComment: number; // 数值 例: 10400, 10300 (原: StartComment)
  startMovieA: string; // 字符串 例: A_1040_0,  (原: StartMovie_A)
  startMovieB: string; // 字符串 例: X_1040_0,  (原: StartMovie_B)
  sucComment: number; // 数值 例: 0, 10301 (原: SucComment)
  sucMovieA: string; // 字符串 例: , B_1030_1 (原: SucMovie_A)
  sucMovieB: string; // 字符串 例: , Y_1030_1 (原: SucMovie_B)
}

// 字段映射：新字段名 -> 原始字段名
export const ActionFieldMappings = {
  actionId: 'ActionID',
  a1: 'A1',
  a2: 'A2',
  attackMode: 'AttackMode',
  failComment: 'FailComment',
  failMovieA: 'FailMovie_A',
  failMovieB: 'FailMovie_B',
  period: 'Period',
  startComment: 'StartComment',
  startMovieA: 'StartMovie_A',
  startMovieB: 'StartMovie_B',
  sucComment: 'SucComment',
  sucMovieA: 'SucMovie_A',
  sucMovieB: 'SucMovie_B',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ActionReverseFieldMappings = {
  'ActionID': 'actionId',
  'A1': 'a1',
  'A2': 'a2',
  'AttackMode': 'attackMode',
  'FailComment': 'failComment',
  'FailMovie_A': 'failMovieA',
  'FailMovie_B': 'failMovieB',
  'Period': 'period',
  'StartComment': 'startComment',
  'StartMovie_A': 'startMovieA',
  'StartMovie_B': 'startMovieB',
  'SucComment': 'sucComment',
  'SucMovie_A': 'sucMovieA',
  'SucMovie_B': 'sucMovieB',
} as const;

export const ActionMeta = {
  tableName: 'Action',
  dataFileName: 'Action.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 16,
  requiredFields: ['actionId', 'id', 'name', 'a1', 'a2', 'attackMode', 'failComment', 'failMovieA', 'failMovieB', 'period', 'startComment', 'startMovieA', 'startMovieB', 'sucComment', 'sucMovieA', 'sucMovieB'],
  optionalFields: [],
  renamedFieldsCount: 14,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ActionFieldMappings,
  reverseFieldMappings: ActionReverseFieldMappings,
} as const;

export type ActionConfigMeta = typeof ActionMeta;
export type ActionFieldMapping = typeof ActionFieldMappings;
export type ActionReverseFieldMapping = typeof ActionReverseFieldMappings;
