// Auto-generated from BeliefSkillExpend.json
// Generated at: 2025-07-20T12:55:59.268Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BeliefSkillExpendDefinition {
  id: number; // 唯一标识符 例: 1, 2
  contribution: number; // 数值 例: 20, 30 (原: Contribution)
}

// 字段映射：新字段名 -> 原始字段名
export const BeliefSkillExpendFieldMappings = {
  contribution: 'Contribution',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BeliefSkillExpendReverseFieldMappings = {
  'Contribution': 'contribution',
} as const;

export const BeliefSkillExpendMeta = {
  tableName: 'BeliefSkillExpend',
  dataFileName: 'BeliefSkillExpend.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 2,
  requiredFields: ['id', 'contribution'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BeliefSkillExpendFieldMappings,
  reverseFieldMappings: BeliefSkillExpendReverseFieldMappings,
} as const;

export type BeliefSkillExpendConfigMeta = typeof BeliefSkillExpendMeta;
export type BeliefSkillExpendFieldMapping = typeof BeliefSkillExpendFieldMappings;
export type BeliefSkillExpendReverseFieldMapping = typeof BeliefSkillExpendReverseFieldMappings;
