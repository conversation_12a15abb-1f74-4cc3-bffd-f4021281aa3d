// Auto-generated from WorldBoss.json
// Generated at: 2025-07-20T12:56:07.121Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface WorldBossDefinition {
  teamId: number; // 队伍ID 例: 90120, 90222 (原: TeamID)
  id: number; // 唯一标识符 例: 1, 2
  goalFor: number; // 数值 例: 80 (原: GoalFor)
  knockoutNum1: number; // 数值 例: 100 (原: KnockoutNum1)
  knockoutNum2: number; // 数值 例: 0 (原: KnockoutNum2)
  knockoutNum3: number; // 数值 例: 0 (原: KnockoutNum3)
  knockoutReward1: number; // 奖励 例: 9 (原: KnockoutReward1)
  knockoutReward2: number; // 奖励 例: 0 (原: KnockoutReward2)
  knockoutReward3: number; // 奖励 例: 0 (原: KnockoutReward3)
  num1: number; // 数值 例: 100, 150 (原: Num1)
  num2: number; // 数值 例: 0 (原: Num2)
  num3: number; // 数值 例: 0 (原: Num3)
  reward1: number; // 奖励 例: 9 (原: Reward1)
  reward2: number; // 奖励 例: 0 (原: Reward2)
  reward3: number; // 奖励 例: 0 (原: Reward3)
}

// 字段映射：新字段名 -> 原始字段名
export const WorldBossFieldMappings = {
  teamId: 'TeamID',
  goalFor: 'GoalFor',
  knockoutNum1: 'KnockoutNum1',
  knockoutNum2: 'KnockoutNum2',
  knockoutNum3: 'KnockoutNum3',
  knockoutReward1: 'KnockoutReward1',
  knockoutReward2: 'KnockoutReward2',
  knockoutReward3: 'KnockoutReward3',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const WorldBossReverseFieldMappings = {
  'TeamID': 'teamId',
  'GoalFor': 'goalFor',
  'KnockoutNum1': 'knockoutNum1',
  'KnockoutNum2': 'knockoutNum2',
  'KnockoutNum3': 'knockoutNum3',
  'KnockoutReward1': 'knockoutReward1',
  'KnockoutReward2': 'knockoutReward2',
  'KnockoutReward3': 'knockoutReward3',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
} as const;

export const WorldBossMeta = {
  tableName: 'WorldBoss',
  dataFileName: 'WorldBoss.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 15,
  requiredFields: ['teamId', 'id', 'goalFor', 'knockoutNum1', 'knockoutNum2', 'knockoutNum3', 'knockoutReward1', 'knockoutReward2', 'knockoutReward3', 'num1', 'num2', 'num3', 'reward1', 'reward2', 'reward3'],
  optionalFields: [],
  renamedFieldsCount: 14,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: WorldBossFieldMappings,
  reverseFieldMappings: WorldBossReverseFieldMappings,
} as const;

export type WorldBossConfigMeta = typeof WorldBossMeta;
export type WorldBossFieldMapping = typeof WorldBossFieldMappings;
export type WorldBossReverseFieldMapping = typeof WorldBossReverseFieldMappings;
