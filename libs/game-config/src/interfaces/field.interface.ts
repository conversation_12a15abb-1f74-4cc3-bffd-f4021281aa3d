// Auto-generated from Field.json
// Generated at: 2025-07-20T12:56:00.706Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface FieldDefinition {
  id: number; // 唯一标识符 例: 1001, 1002
  condition: string; // 条件 例: 7*1, 7*3 (原: Condition)
  costMoney: number; // 消耗 例: 3000, 6000 (原: CostMoney)
  fans: number; // 数值 例: 0, 5000 (原: Fans)
  fansShow: number; // 数值 例: 0, 7000 (原: FansShow)
  level: number; // 等级 例: 1, 2 (原: Level)
  prestige: number; // 数值 例: 0 (原: Prestige)
  produce: string; // 字符串 例: 1*378666, 1*757332 (原: Produce)
  store: string; // 字符串 例: 1*500000, 1*1000000 (原: Store)
  time: number; // 时间 例: 15, 30 (原: Time)
  timeShow: string; // 时间 例: 15秒, 30秒 (原: TimeShow)
  type: number; // 类型 例: 1, 2 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const FieldFieldMappings = {
  condition: 'Condition',
  costMoney: 'CostMoney',
  fans: 'Fans',
  fansShow: 'FansShow',
  level: 'Level',
  prestige: 'Prestige',
  produce: 'Produce',
  store: 'Store',
  time: 'Time',
  timeShow: 'TimeShow',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const FieldReverseFieldMappings = {
  'Condition': 'condition',
  'CostMoney': 'costMoney',
  'Fans': 'fans',
  'FansShow': 'fansShow',
  'Level': 'level',
  'Prestige': 'prestige',
  'Produce': 'produce',
  'Store': 'store',
  'Time': 'time',
  'TimeShow': 'timeShow',
  'Type': 'type',
} as const;

export const FieldMeta = {
  tableName: 'Field',
  dataFileName: 'Field.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 12,
  requiredFields: ['id', 'condition', 'costMoney', 'fans', 'fansShow', 'level', 'prestige', 'produce', 'store', 'time', 'timeShow', 'type'],
  optionalFields: [],
  renamedFieldsCount: 11,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: FieldFieldMappings,
  reverseFieldMappings: FieldReverseFieldMappings,
} as const;

export type FieldConfigMeta = typeof FieldMeta;
export type FieldFieldMapping = typeof FieldFieldMappings;
export type FieldReverseFieldMapping = typeof FieldReverseFieldMappings;
