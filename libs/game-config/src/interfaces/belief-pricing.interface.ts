// Auto-generated from BeliefPricing.json
// Generated at: 2025-07-20T12:55:59.220Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BeliefPricingDefinition {
  id: number; // 唯一标识符 例: 1
  endTime: string; // 结束时间 例: 23:59:59 (原: EndTime)
  startTime: string; // 开始时间 例: 0:00:00 (原: StartTime)
  week: number; // 数值 例: 1 (原: Week)
}

// 字段映射：新字段名 -> 原始字段名
export const BeliefPricingFieldMappings = {
  endTime: 'EndTime',
  startTime: 'StartTime',
  week: 'Week',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BeliefPricingReverseFieldMappings = {
  'EndTime': 'endTime',
  'StartTime': 'startTime',
  'Week': 'week',
} as const;

export const BeliefPricingMeta = {
  tableName: 'BeliefPricing',
  dataFileName: 'BeliefPricing.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['id', 'endTime', 'startTime', 'week'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BeliefPricingFieldMappings,
  reverseFieldMappings: BeliefPricingReverseFieldMappings,
} as const;

export type BeliefPricingConfigMeta = typeof BeliefPricingMeta;
export type BeliefPricingFieldMapping = typeof BeliefPricingFieldMappings;
export type BeliefPricingReverseFieldMapping = typeof BeliefPricingReverseFieldMappings;
