// Auto-generated from FootballerEnergy.json
// Generated at: 2025-07-20T12:56:01.655Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据
// 表名已规范化: FootballerEnergy → HeroEnergy

export interface HeroEnergyDefinition {
  id: number; // 唯一标识符 例: 1, 2
  energy: number; // 精力 例: 50, 150 (原: Energy)
  gold: number; // 金币 例: 50, 147 (原: Gold)
  icon: number; // 图标 例: 50011, 50012 (原: Icon)
}

// 字段映射：新字段名 -> 原始字段名
export const HeroEnergyFieldMappings = {
  energy: 'Energy',
  gold: 'Gold',
  icon: 'Icon',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const HeroEnergyReverseFieldMappings = {
  'Energy': 'energy',
  'Gold': 'gold',
  'Icon': 'icon',
} as const;

export const HeroEnergyMeta = {
  tableName: 'HeroEnergy',
  originalTableName: 'FootballerEnergy',
  dataFileName: 'FootballerEnergy.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['id', 'energy', 'gold', 'icon'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: true,
  fieldMappings: HeroEnergyFieldMappings,
  reverseFieldMappings: HeroEnergyReverseFieldMappings,
} as const;

export type HeroEnergyConfigMeta = typeof HeroEnergyMeta;
export type HeroEnergyFieldMapping = typeof HeroEnergyFieldMappings;
export type HeroEnergyReverseFieldMapping = typeof HeroEnergyReverseFieldMappings;
