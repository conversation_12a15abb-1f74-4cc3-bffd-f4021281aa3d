// Auto-generated from DailyGift.json
// Generated at: 2025-07-20T12:55:59.750Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface DailyGiftDefinition {
  blackId: number; // 唯一标识符 例: 0, 90099 (原: BlackId)
  id: number; // 唯一标识符 例: 1, 2
  blackNumber: number; // 数值 例: 0, 1 (原: BlackNum)
  gift1: number; // 数值 例: 90081 (原: Gift1)
  gift2: number; // 数值 例: 90082 (原: Gift2)
  gold: number; // 金币 例: 10, 15 (原: Gold)
  textNum1: number; // 数值 例: 3 (原: TextNum1)
  textNum2: number; // 数值 例: 6 (原: TextNum2)
  textNum3: number; // 数值 例: 12 (原: TextNum3)
}

// 字段映射：新字段名 -> 原始字段名
export const DailyGiftFieldMappings = {
  blackId: 'BlackId',
  blackNumber: 'BlackNum',
  gift1: 'Gift1',
  gift2: 'Gift2',
  gold: 'Gold',
  textNum1: 'TextNum1',
  textNum2: 'TextNum2',
  textNum3: 'TextNum3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const DailyGiftReverseFieldMappings = {
  'BlackId': 'blackId',
  'BlackNum': 'blackNumber',
  'Gift1': 'gift1',
  'Gift2': 'gift2',
  'Gold': 'gold',
  'TextNum1': 'textNum1',
  'TextNum2': 'textNum2',
  'TextNum3': 'textNum3',
} as const;

export const DailyGiftMeta = {
  tableName: 'DailyGift',
  dataFileName: 'DailyGift.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 9,
  requiredFields: ['blackId', 'id', 'blackNumber', 'gift1', 'gift2', 'gold', 'textNum1', 'textNum2', 'textNum3'],
  optionalFields: [],
  renamedFieldsCount: 8,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: DailyGiftFieldMappings,
  reverseFieldMappings: DailyGiftReverseFieldMappings,
} as const;

export type DailyGiftConfigMeta = typeof DailyGiftMeta;
export type DailyGiftFieldMapping = typeof DailyGiftFieldMappings;
export type DailyGiftReverseFieldMapping = typeof DailyGiftReverseFieldMappings;
