// Auto-generated from Belief.json
// Generated at: 2025-07-20T12:55:59.159Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BeliefDefinition {
  id: number; // 唯一标识符 例: 1, 2
  gift: number; // 数值 例: 90083, 90084 (原: Gift)
  hallIcon: number; // 图标 例: 11319, 11325 (原: HallIcon)
  icon: number; // 图标 例: 10041, 10059 (原: Icon)
  icon1: number; // 图标 例: 11300, 11306 (原: Icon1)
  team: string; // 字符串 例: 阿森纳, 皇家马德里 (原: Team)
  text: string; // 字符串 例: 海布里的荣耀，我们的兵工厂, 伯纳乌那一抹纯白，银河战舰再次起航 (原: Text)
  text1: string; // 字符串 例: 阿森纳, 皇马 (原: Text1)
}

// 字段映射：新字段名 -> 原始字段名
export const BeliefFieldMappings = {
  gift: 'Gift',
  hallIcon: 'HallIcon',
  icon: 'Icon',
  icon1: 'Icon1',
  team: 'Team',
  text: 'Text',
  text1: 'Text1',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BeliefReverseFieldMappings = {
  'Gift': 'gift',
  'HallIcon': 'hallIcon',
  'Icon': 'icon',
  'Icon1': 'icon1',
  'Team': 'team',
  'Text': 'text',
  'Text1': 'text1',
} as const;

export const BeliefMeta = {
  tableName: 'Belief',
  dataFileName: 'Belief.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 8,
  requiredFields: ['id', 'gift', 'hallIcon', 'icon', 'icon1', 'team', 'text', 'text1'],
  optionalFields: [],
  renamedFieldsCount: 7,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BeliefFieldMappings,
  reverseFieldMappings: BeliefReverseFieldMappings,
} as const;

export type BeliefConfigMeta = typeof BeliefMeta;
export type BeliefFieldMapping = typeof BeliefFieldMappings;
export type BeliefReverseFieldMapping = typeof BeliefReverseFieldMappings;
