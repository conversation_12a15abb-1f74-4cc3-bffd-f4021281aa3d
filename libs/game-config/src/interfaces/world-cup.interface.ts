// Auto-generated from WorldCup.json
// Generated at: 2025-07-20T12:56:07.164Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface WorldCupDefinition {
  worldCupId: number; // 唯一标识符 例: 1, 2 (原: WorldCupId)
  id: number; // 唯一标识符 例: 1001, 1002
  icon: number; // 图标 例: 11031, 11032 (原: Icon)
  item1: number; // 数值 例: 90041, 90042 (原: Item1)
  item2: number; // 数值 例: 1 (原: Item2)
  item3: number; // 数值 例: 5 (原: Item3)
  item4: number; // 数值 例: 90134 (原: Item4)
  text1: string; // 字符串 例: <font size=24>第十四届世界杯于1990年..., <font size=24>第十五届世界杯于1994年...
  text2: string; // 字符串 例: <font color=0xf06f00 size=3...
  worldCup: string; // 字符串 例: 意大利之夏, 荣耀之地 (原: WorldCup)
}

// 字段映射：新字段名 -> 原始字段名
export const WorldCupFieldMappings = {
  worldCupId: 'WorldCupId',
  icon: 'Icon',
  item1: 'Item1',
  item2: 'Item2',
  item3: 'Item3',
  item4: 'Item4',
  worldCup: 'WorldCup',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const WorldCupReverseFieldMappings = {
  'WorldCupId': 'worldCupId',
  'Icon': 'icon',
  'Item1': 'item1',
  'Item2': 'item2',
  'Item3': 'item3',
  'Item4': 'item4',
  'WorldCup': 'worldCup',
} as const;

export const WorldCupMeta = {
  tableName: 'WorldCup',
  dataFileName: 'WorldCup.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 10,
  requiredFields: ['worldCupId', 'id', 'icon', 'item1', 'item2', 'item3', 'item4', 'text1', 'text2', 'worldCup'],
  optionalFields: [],
  renamedFieldsCount: 7,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: WorldCupFieldMappings,
  reverseFieldMappings: WorldCupReverseFieldMappings,
} as const;

export type WorldCupConfigMeta = typeof WorldCupMeta;
export type WorldCupFieldMapping = typeof WorldCupFieldMappings;
export type WorldCupReverseFieldMapping = typeof WorldCupReverseFieldMappings;
