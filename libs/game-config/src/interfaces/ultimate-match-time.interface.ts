// Auto-generated from UltimateMatchTime.json
// Generated at: 2025-07-20T12:56:07.063Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface UltimateMatchTimeDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 名单生成，开放竞猜, 竞猜截至，公布赛程
  time: string; // 时间 例: 12:00, 18:50 (原: Time)
}

// 字段映射：新字段名 -> 原始字段名
export const UltimateMatchTimeFieldMappings = {
  time: 'Time',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const UltimateMatchTimeReverseFieldMappings = {
  'Time': 'time',
} as const;

export const UltimateMatchTimeMeta = {
  tableName: 'UltimateMatchTime',
  dataFileName: 'UltimateMatchTime.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 3,
  requiredFields: ['id', 'name', 'time'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: UltimateMatchTimeFieldMappings,
  reverseFieldMappings: UltimateMatchTimeReverseFieldMappings,
} as const;

export type UltimateMatchTimeConfigMeta = typeof UltimateMatchTimeMeta;
export type UltimateMatchTimeFieldMapping = typeof UltimateMatchTimeFieldMappings;
export type UltimateMatchTimeReverseFieldMapping = typeof UltimateMatchTimeReverseFieldMappings;
