// Auto-generated from GiftCode.json
// Generated at: 2025-07-20T12:56:03.549Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GiftCodeDefinition {
  resId3: number; // 唯一标识符 例: 90002 (原: ResId3)
  resId2: number; // 唯一标识符 例: 90001 (原: ResId2)
  resId1: number; // 唯一标识符 例: 90000 (原: ResId1)
  id: number; // 唯一标识符 例: 1, 2
  gift: string; // 字符串 例: asdfghjkl, qwertyuiop (原: Gift)
  itemType: number; // 类型 例: 1 (原: ItemType)
  num1: number; // 数值 例: 10 (原: Num1)
  num2: number; // 数值 例: 10 (原: Num2)
  num3: number; // 数值 例: 10 (原: Num3)
  type: number; // 类型 例: 1, 2 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const GiftCodeFieldMappings = {
  resId3: 'ResId3',
  resId2: 'ResId2',
  resId1: 'ResId1',
  gift: 'Gift',
  itemType: 'ItemType',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GiftCodeReverseFieldMappings = {
  'ResId3': 'resId3',
  'ResId2': 'resId2',
  'ResId1': 'resId1',
  'Gift': 'gift',
  'ItemType': 'itemType',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Type': 'type',
} as const;

export const GiftCodeMeta = {
  tableName: 'GiftCode',
  dataFileName: 'GiftCode.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 10,
  requiredFields: ['resId3', 'resId2', 'resId1', 'id', 'gift', 'itemType', 'num1', 'num2', 'num3', 'type'],
  optionalFields: [],
  renamedFieldsCount: 9,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GiftCodeFieldMappings,
  reverseFieldMappings: GiftCodeReverseFieldMappings,
} as const;

export type GiftCodeConfigMeta = typeof GiftCodeMeta;
export type GiftCodeFieldMapping = typeof GiftCodeFieldMappings;
export type GiftCodeReverseFieldMapping = typeof GiftCodeReverseFieldMappings;
