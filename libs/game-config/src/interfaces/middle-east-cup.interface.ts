// Auto-generated from MiddleEastCup.json
// Generated at: 2025-07-20T12:56:05.053Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MiddleEastCupDefinition {
  id: number; // 唯一标识符 例: 1, 2
  icon: number; // 图标 例: 22001 (原: Icon)
  text1: string; // 字符串 例: 我喜欢崇尚进攻的球队，打对攻的比赛最好看, 防守好的球队是我的最爱，防守反击是最有趣的战术
  text2: string; // 字符串 例: 获得胜利且双方进球数大于4, 获得胜利且我方失球数少于2
  worldCup: string; // 字符串 例: 阿布扎比财团, 迪拜财团 (原: WorldCup)
}

// 字段映射：新字段名 -> 原始字段名
export const MiddleEastCupFieldMappings = {
  icon: 'Icon',
  worldCup: 'WorldCup',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MiddleEastCupReverseFieldMappings = {
  'Icon': 'icon',
  'WorldCup': 'worldCup',
} as const;

export const MiddleEastCupMeta = {
  tableName: 'MiddleEastCup',
  dataFileName: 'MiddleEastCup.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['id', 'icon', 'text1', 'text2', 'worldCup'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MiddleEastCupFieldMappings,
  reverseFieldMappings: MiddleEastCupReverseFieldMappings,
} as const;

export type MiddleEastCupConfigMeta = typeof MiddleEastCupMeta;
export type MiddleEastCupFieldMapping = typeof MiddleEastCupFieldMappings;
export type MiddleEastCupReverseFieldMapping = typeof MiddleEastCupReverseFieldMappings;
