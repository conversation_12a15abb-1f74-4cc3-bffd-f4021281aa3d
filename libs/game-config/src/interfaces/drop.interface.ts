// Auto-generated from Drop.json
// Generated at: 2025-07-20T12:56:00.167Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface DropDefinition {
  itemId: number; // 物品ID 例: 2448, 1416 (原: ItemId)
  groupId: number; // 唯一标识符 例: 1, 2 (原: GroupId)
  lootId: number; // 唯一标识符 例: 90002, 90003 (原: LootId)
  id: number; // 唯一标识符 例: 1, 2
  itemMaxNumber: number; // 数值 例: 1, 10 (原: ItemMaxnum)
  itemMinNumber: number; // 数值 例: 1, 10 (原: ItemMinnum)
  rate: number; // 比率 例: 50, 45 (原: Rate)
  remarks?: string; // 字符串 例:  (原: Remarks)
  type: number; // 类型 例: 2, 1 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const DropFieldMappings = {
  itemId: 'ItemId',
  groupId: 'GroupId',
  lootId: 'LootId',
  itemMaxNumber: 'ItemMaxnum',
  itemMinNumber: 'ItemMinnum',
  rate: 'Rate',
  remarks: 'Remarks',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const DropReverseFieldMappings = {
  'ItemId': 'itemId',
  'GroupId': 'groupId',
  'LootId': 'lootId',
  'ItemMaxnum': 'itemMaxNumber',
  'ItemMinnum': 'itemMinNumber',
  'Rate': 'rate',
  'Remarks': 'remarks',
  'Type': 'type',
} as const;

export const DropMeta = {
  tableName: 'Drop',
  dataFileName: 'Drop.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 9,
  requiredFields: ['itemId', 'groupId', 'lootId', 'id', 'itemMaxNumber', 'itemMinNumber', 'rate', 'type'],
  optionalFields: ['remarks'],
  renamedFieldsCount: 8,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: DropFieldMappings,
  reverseFieldMappings: DropReverseFieldMappings,
} as const;

export type DropConfigMeta = typeof DropMeta;
export type DropFieldMapping = typeof DropFieldMappings;
export type DropReverseFieldMapping = typeof DropReverseFieldMappings;
