// Auto-generated from GulfTeam.json
// Generated at: 2025-07-20T12:56:03.650Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GulfTeamDefinition {
  offensiveId: number; // 唯一标识符 例: 615 (原: OffensiveID)
  iconId: number; // 唯一标识符 例: 10041, 10042 (原: IconID)
  id: number; // 唯一标识符 例: 101, 102
  name: string; // 名称 例: 沙特阿拉伯, 阿联酋
  formation: number; // 数值 例: 442401 (原: Formation)
  num1: number; // 数值 例: 1 (原: Num1)
  num2: number; // 数值 例: 0, 2 (原: Num2)
  num3: number; // 数值 例: 0, 1 (原: Num3)
  probability1: number; // 数值 例: 200 (原: Probability1)
  probability2: number; // 数值 例: 0, 100 (原: Probability2)
  probability3: number; // 数值 例: 0, 200 (原: Probability3)
  reward1: number; // 奖励 例: 90001 (原: Reward1)
  reward2: number; // 奖励 例: 0, 90002 (原: Reward2)
  reward3: number; // 奖励 例: 0, 11008 (原: Reward3)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
}

// 字段映射：新字段名 -> 原始字段名
export const GulfTeamFieldMappings = {
  offensiveId: 'OffensiveID',
  iconId: 'IconID',
  formation: 'Formation',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  probability1: 'Probability1',
  probability2: 'Probability2',
  probability3: 'Probability3',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GulfTeamReverseFieldMappings = {
  'OffensiveID': 'offensiveId',
  'IconID': 'iconId',
  'Formation': 'formation',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Probability1': 'probability1',
  'Probability2': 'probability2',
  'Probability3': 'probability3',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
} as const;

export const GulfTeamMeta = {
  tableName: 'GulfTeam',
  dataFileName: 'GulfTeam.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 17,
  requiredFields: ['offensiveId', 'iconId', 'id', 'name', 'formation', 'num1', 'num2', 'num3', 'probability1', 'probability2', 'probability3', 'reward1', 'reward2', 'reward3', 'rewardType1', 'rewardType2', 'rewardType3'],
  optionalFields: [],
  renamedFieldsCount: 15,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GulfTeamFieldMappings,
  reverseFieldMappings: GulfTeamReverseFieldMappings,
} as const;

export type GulfTeamConfigMeta = typeof GulfTeamMeta;
export type GulfTeamFieldMapping = typeof GulfTeamFieldMappings;
export type GulfTeamReverseFieldMapping = typeof GulfTeamReverseFieldMappings;
