// Auto-generated from TaskMark.json
// Generated at: 2025-07-20T12:56:06.489Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TaskMarkDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 传奇之路, 日常任务
  itemType: number[]; // 类型 例: 1,2,0,0,0,0,0,0,0,0, 3,0,0,0,0,0,0,0,0,0 (原: ItemType)
  numPageTasks: number; // 数值 例: 5, 16 (原: NumPageTasks)
  type: number; // 类型 例: 1, 2 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const TaskMarkFieldMappings = {
  itemType: 'ItemType',
  numPageTasks: 'NumPageTasks',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TaskMarkReverseFieldMappings = {
  'ItemType': 'itemType',
  'NumPageTasks': 'numPageTasks',
  'Type': 'type',
} as const;

export const TaskMarkMeta = {
  tableName: 'TaskMark',
  dataFileName: 'TaskMark.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 5,
  requiredFields: ['id', 'name', 'itemType', 'numPageTasks', 'type'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TaskMarkFieldMappings,
  reverseFieldMappings: TaskMarkReverseFieldMappings,
} as const;

export type TaskMarkConfigMeta = typeof TaskMarkMeta;
export type TaskMarkFieldMapping = typeof TaskMarkFieldMappings;
export type TaskMarkReverseFieldMapping = typeof TaskMarkReverseFieldMappings;
