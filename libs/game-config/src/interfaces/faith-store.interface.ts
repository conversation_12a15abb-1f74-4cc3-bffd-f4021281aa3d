// Auto-generated from FaithStore.json
// Generated at: 2025-07-20T12:56:00.664Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface FaithStoreDefinition {
  id: number; // 唯一标识符 例: 1, 2
  customer: number; // 数值 例: 0, 1 (原: Customer)
  disCount: number; // 数量 例: 0 (原: Discount)
  discountPrice: number; // 数量 例: 0 (原: DiscountPrice)
  itemType: number; // 类型 例: 0 (原: ItemType)
  lv: number; // 数值 例: 0, 4 (原: Lv)
  num: number; // 数值 例: 0, 12 (原: Num)
  pageSign: number; // 数值 例: 10, 12 (原: PageSign)
  parameters: number; // 参数 例: 42803, 32804 (原: Parameters)
  price: number; // 价格 例: 135000, 13500 (原: Price)
  priceType: number; // 类型 例: 16 (原: PriceType)
  purchase: number; // 数值 例: 0, 1 (原: Purchase)
  refresh: number; // 数值 例: 0, 1 (原: Refresh)
  refreshType: number; // 类型 例: 0, 2 (原: RefreshType)
  yNDisCount: number; // 数量 例: 0 (原: YNDiscount)
}

// 字段映射：新字段名 -> 原始字段名
export const FaithStoreFieldMappings = {
  customer: 'Customer',
  disCount: 'Discount',
  discountPrice: 'DiscountPrice',
  itemType: 'ItemType',
  lv: 'Lv',
  num: 'Num',
  pageSign: 'PageSign',
  parameters: 'Parameters',
  price: 'Price',
  priceType: 'PriceType',
  purchase: 'Purchase',
  refresh: 'Refresh',
  refreshType: 'RefreshType',
  yNDisCount: 'YNDiscount',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const FaithStoreReverseFieldMappings = {
  'Customer': 'customer',
  'Discount': 'disCount',
  'DiscountPrice': 'discountPrice',
  'ItemType': 'itemType',
  'Lv': 'lv',
  'Num': 'num',
  'PageSign': 'pageSign',
  'Parameters': 'parameters',
  'Price': 'price',
  'PriceType': 'priceType',
  'Purchase': 'purchase',
  'Refresh': 'refresh',
  'RefreshType': 'refreshType',
  'YNDiscount': 'yNDisCount',
} as const;

export const FaithStoreMeta = {
  tableName: 'FaithStore',
  dataFileName: 'FaithStore.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 15,
  requiredFields: ['id', 'customer', 'disCount', 'discountPrice', 'itemType', 'lv', 'num', 'pageSign', 'parameters', 'price', 'priceType', 'purchase', 'refresh', 'refreshType', 'yNDisCount'],
  optionalFields: [],
  renamedFieldsCount: 14,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: FaithStoreFieldMappings,
  reverseFieldMappings: FaithStoreReverseFieldMappings,
} as const;

export type FaithStoreConfigMeta = typeof FaithStoreMeta;
export type FaithStoreFieldMapping = typeof FaithStoreFieldMappings;
export type FaithStoreReverseFieldMapping = typeof FaithStoreReverseFieldMappings;
