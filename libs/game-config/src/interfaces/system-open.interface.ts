// Auto-generated from SystemOpen.json
// Generated at: 2025-07-20T12:56:06.046Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface SystemOpenDefinition {
  id: number; // 唯一标识符 例: 1, 2
  icon: string; // 图标 例: atlas_pitch_json.btn_qc_min..., atlas_pitch_json.btn_qc_foo...
  iconSize: number; // 图标 例: 0.5, 1
  level: number; // 等级 例: 3, 4 (原: Level)
  text1: string; // 字符串 例: 球场（行政楼）开放, 球场（主球场）开放
  text2: string; // 字符串 例: 行政楼会产出欧元，并控制着球场其他建筑的升级，还能领取..., 主球场能增加球迷数量，球迷越多则球票收入越高
  tip: string; // 字符串 例: 行政楼3级开放, 主球场4级开放
}

// 字段映射：新字段名 -> 原始字段名
export const SystemOpenFieldMappings = {
  level: 'Level',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const SystemOpenReverseFieldMappings = {
  'Level': 'level',
} as const;

export const SystemOpenMeta = {
  tableName: 'SystemOpen',
  dataFileName: 'SystemOpen.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 7,
  requiredFields: ['id', 'icon', 'iconSize', 'level', 'text1', 'text2', 'tip'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: SystemOpenFieldMappings,
  reverseFieldMappings: SystemOpenReverseFieldMappings,
} as const;

export type SystemOpenConfigMeta = typeof SystemOpenMeta;
export type SystemOpenFieldMapping = typeof SystemOpenFieldMappings;
export type SystemOpenReverseFieldMapping = typeof SystemOpenReverseFieldMappings;
