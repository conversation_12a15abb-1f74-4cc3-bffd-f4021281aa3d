// Auto-generated from UltimateMatchAward.json
// Generated at: 2025-07-20T12:56:07.046Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface UltimateMatchAwardDefinition {
  id: number; // 唯一标识符 例: 1, 2
  showName: string; // 名称 例: 冠军, 晋级决赛 (原: ShowName)
  num1: number; // 数值 例: 3600, 3000 (原: Num1)
  num2: number; // 数值 例: 3600, 3000 (原: Num2)
  num3: number; // 数值 例: 0, 10 (原: Num3)
  num4: number; // 数值 例: 0 (原: Num4)
  reward1: number; // 奖励 例: 16, 17 (原: Reward1)
  reward2: number; // 奖励 例: 18, 0 (原: Reward2)
  reward3: number; // 奖励 例: 0, 17 (原: Reward3)
  reward4: number; // 奖励 例: 0 (原: Reward4)
  topMax: number; // 数值 例: 1, 2 (原: TopMax)
  topMin: number; // 数值 例: 1 (原: TopMin)
}

// 字段映射：新字段名 -> 原始字段名
export const UltimateMatchAwardFieldMappings = {
  showName: 'ShowName',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  num4: 'Num4',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  reward4: 'Reward4',
  topMax: 'TopMax',
  topMin: 'TopMin',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const UltimateMatchAwardReverseFieldMappings = {
  'ShowName': 'showName',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Num4': 'num4',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'Reward4': 'reward4',
  'TopMax': 'topMax',
  'TopMin': 'topMin',
} as const;

export const UltimateMatchAwardMeta = {
  tableName: 'UltimateMatchAward',
  dataFileName: 'UltimateMatchAward.json',
  primaryKey: 'id',
  searchFields: ['showName'],
  fieldsCount: 12,
  requiredFields: ['id', 'showName', 'num1', 'num2', 'num3', 'num4', 'reward1', 'reward2', 'reward3', 'reward4', 'topMax', 'topMin'],
  optionalFields: [],
  renamedFieldsCount: 11,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: UltimateMatchAwardFieldMappings,
  reverseFieldMappings: UltimateMatchAwardReverseFieldMappings,
} as const;

export type UltimateMatchAwardConfigMeta = typeof UltimateMatchAwardMeta;
export type UltimateMatchAwardFieldMapping = typeof UltimateMatchAwardFieldMappings;
export type UltimateMatchAwardReverseFieldMapping = typeof UltimateMatchAwardReverseFieldMappings;
