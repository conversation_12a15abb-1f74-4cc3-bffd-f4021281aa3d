// Auto-generated from ActivityConfig.json
// Generated at: 2025-07-20T12:55:59.000Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ActivityConfigDefinition {
  activityId: number; // 唯一标识符 例: 1, 2 (原: ActivityId)
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 签到, 新手七天签到
  endTime: number; // 结束时间 例: 999999, 7 (原: EndTime)
  startTime: number; // 开始时间 例: 0, 1574784000 (原: StartTime)
  timeType: number; // 类型 例: 1, 2 (原: TimeType)
}

// 字段映射：新字段名 -> 原始字段名
export const ActivityConfigFieldMappings = {
  activityId: 'ActivityId',
  endTime: 'EndTime',
  startTime: 'StartTime',
  timeType: 'TimeType',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ActivityConfigReverseFieldMappings = {
  'ActivityId': 'activityId',
  'EndTime': 'endTime',
  'StartTime': 'startTime',
  'TimeType': 'timeType',
} as const;

export const ActivityConfigMeta = {
  tableName: 'ActivityConfig',
  dataFileName: 'ActivityConfig.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 6,
  requiredFields: ['activityId', 'id', 'name', 'endTime', 'startTime', 'timeType'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ActivityConfigFieldMappings,
  reverseFieldMappings: ActivityConfigReverseFieldMappings,
} as const;

export type ActivityConfigConfigMeta = typeof ActivityConfigMeta;
export type ActivityConfigFieldMapping = typeof ActivityConfigFieldMappings;
export type ActivityConfigReverseFieldMapping = typeof ActivityConfigReverseFieldMappings;
