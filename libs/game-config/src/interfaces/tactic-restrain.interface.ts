// Auto-generated from TacticRestrain.json
// Generated at: 2025-07-20T12:56:06.157Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TacticRestrainDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 两翼齐飞, 后排插上
  beRestrainType1: number; // 类型 例: 14, 12 (原: BeRestrainType1)
  beRestrainType2: number; // 类型 例: 17, 13 (原: BeRestrainType2)
  beRestrainType3: number; // 类型 例: 18, 17 (原: BeRestrainType3)
  restrainType1: number; // 类型 例: 11, 14 (原: RestrainType1)
  restrainType2: number; // 类型 例: 12, 15 (原: RestrainType2)
  restrainType3: number; // 类型 例: 13, 16 (原: RestrainType3)
}

// 字段映射：新字段名 -> 原始字段名
export const TacticRestrainFieldMappings = {
  beRestrainType1: 'BeRestrainType1',
  beRestrainType2: 'BeRestrainType2',
  beRestrainType3: 'BeRestrainType3',
  restrainType1: 'RestrainType1',
  restrainType2: 'RestrainType2',
  restrainType3: 'RestrainType3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TacticRestrainReverseFieldMappings = {
  'BeRestrainType1': 'beRestrainType1',
  'BeRestrainType2': 'beRestrainType2',
  'BeRestrainType3': 'beRestrainType3',
  'RestrainType1': 'restrainType1',
  'RestrainType2': 'restrainType2',
  'RestrainType3': 'restrainType3',
} as const;

export const TacticRestrainMeta = {
  tableName: 'TacticRestrain',
  dataFileName: 'TacticRestrain.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 8,
  requiredFields: ['id', 'name', 'beRestrainType1', 'beRestrainType2', 'beRestrainType3', 'restrainType1', 'restrainType2', 'restrainType3'],
  optionalFields: [],
  renamedFieldsCount: 6,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TacticRestrainFieldMappings,
  reverseFieldMappings: TacticRestrainReverseFieldMappings,
} as const;

export type TacticRestrainConfigMeta = typeof TacticRestrainMeta;
export type TacticRestrainFieldMapping = typeof TacticRestrainFieldMappings;
export type TacticRestrainReverseFieldMapping = typeof TacticRestrainReverseFieldMappings;
