// Auto-generated from LeagueCopy.json
// Generated at: 2025-07-20T12:56:04.698Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface LeagueCopyDefinition {
  flagId: number; // 唯一标识符 例: 20002, 20003 (原: FlagID)
  id: number; // 唯一标识符 例: 90100, 90200
  leagueName: string; // 名称 例: 法乙, 西乙 (原: LeagueName)
  introduce: string; // 字符串 例: <font color=0x9f9f9f>法乙是法国仅..., <font color=0x9f9f9f>西乙西班牙仅... (原: Introduce)
  introduce1: string; // 字符串 例: <font color=0x547fc7>成立时间：<..., <font color=0x547fc7>成立时间：<... (原: Introduce1)
  introduce2: string; // 字符串 例: <font color=0x547fc7>较成功队伍：..., <font color=0x547fc7>上赛季冠军：... (原: Introduce2)
  leagueIcon: number; // 图标 例: 40004, 40006 (原: LeagueIcon)
  leagueWord: number; // 数值 例: 30003, 30006 (原: LeagueWord)
  num1: number; // 数值 例: 1 (原: Num1)
  num2: number; // 数值 例: 1, 2 (原: Num2)
  num3: number; // 数值 例: 1 (原: Num3)
  position: string; // 位置 例: /159_/87, /136_/147 (原: Position)
  reward1: number; // 奖励 例: 90002, 90080 (原: Reward1)
  reward2: number; // 奖励 例: 11006 (原: Reward2)
  reward3: number; // 奖励 例: 42708, 42709 (原: Reward3)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
}

// 字段映射：新字段名 -> 原始字段名
export const LeagueCopyFieldMappings = {
  flagId: 'FlagID',
  leagueName: 'LeagueName',
  introduce: 'Introduce',
  introduce1: 'Introduce1',
  introduce2: 'Introduce2',
  leagueIcon: 'LeagueIcon',
  leagueWord: 'LeagueWord',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  position: 'Position',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const LeagueCopyReverseFieldMappings = {
  'FlagID': 'flagId',
  'LeagueName': 'leagueName',
  'Introduce': 'introduce',
  'Introduce1': 'introduce1',
  'Introduce2': 'introduce2',
  'LeagueIcon': 'leagueIcon',
  'LeagueWord': 'leagueWord',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Position': 'position',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
} as const;

export const LeagueCopyMeta = {
  tableName: 'LeagueCopy',
  dataFileName: 'LeagueCopy.json',
  primaryKey: 'id',
  searchFields: ['leagueName'],
  fieldsCount: 18,
  requiredFields: ['flagId', 'id', 'leagueName', 'introduce', 'introduce1', 'introduce2', 'leagueIcon', 'leagueWord', 'num1', 'num2', 'num3', 'position', 'reward1', 'reward2', 'reward3', 'rewardType1', 'rewardType2', 'rewardType3'],
  optionalFields: [],
  renamedFieldsCount: 17,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: LeagueCopyFieldMappings,
  reverseFieldMappings: LeagueCopyReverseFieldMappings,
} as const;

export type LeagueCopyConfigMeta = typeof LeagueCopyMeta;
export type LeagueCopyFieldMapping = typeof LeagueCopyFieldMappings;
export type LeagueCopyReverseFieldMapping = typeof LeagueCopyReverseFieldMappings;
