[{"id": 1, "ActivityType": 1, "UiType": 0, "ActivityName": "每日充值礼包", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 1, "RefreshCycle": 1, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 1, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 2, "ActivityType": 2, "UiType": 1, "ActivityName": "累计充值活动", "ActivityIcon": 0, "ActivityDepict": "活动期间累计充值达到指定额度即可领取奖励！", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 2, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 3, "ActivityType": 3, "UiType": 1, "ActivityName": "累计消耗活动", "ActivityIcon": 0, "ActivityDepict": "活动期间累计消耗达到指定额度即可领取奖励！", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 3, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 4, "ActivityType": 4, "UiType": 0, "ActivityName": "首冲礼包", "ActivityIcon": 0, "ActivityDepict": "累计充值#0#元，送限量阿扎尔！", "Jump": 0, "TimeType": 0, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 1, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 5, "ActivityType": 5, "UiType": 0, "ActivityName": "月卡礼包", "ActivityIcon": 0, "ActivityDepict": "每日可领取<font color=#FFC04F>40</font>球币及<font color=#FFC04F>100万</font>欧元，\\n持续<font color=#FFC04F>30天</font>。共计可获得<font color=#FFC04F>1200</font>球币和\\n<font color=#FFC04F>3000万欧元</font>，可随时续费延长时间!", "Jump": 0, "TimeType": 1, "RefreshCycle": 1, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 6, "ActivityType": 6, "UiType": 0, "ActivityName": "金牌教练", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 2, "Periods": 26, "StartTime": "2020/6/15 00:0:00", "EndTime": "2020/7/12 23:59:59", "ShowStartTime": "2020/6/15 00:0:00", "ShowEndTime": "2020/7/12 23:59:59", "Order": 0, "PushWay": 1, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 7, "ActivityType": 7, "UiType": 0, "ActivityName": "球员获取途径", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 8, "ActivityType": 8, "UiType": 0, "ActivityName": "连续储值", "ActivityIcon": 0, "ActivityDepict": "充值5天，领取超值大礼！", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 9, "ActivityType": 9, "UiType": 0, "ActivityName": "七天登录领好礼", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 10, "ActivityType": 2, "UiType": 1, "ActivityName": "累计充值活动", "ActivityIcon": 0, "ActivityDepict": "活动期间累计充值达到指定额度即可领取奖励！", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 11, "ActivityType": 3, "UiType": 1, "ActivityName": "累计消耗活动", "ActivityIcon": 0, "ActivityDepict": "活动期间累计消耗达到指定额度即可领取奖励！", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 12, "ActivityType": 8, "UiType": 0, "ActivityName": "连续储值", "ActivityIcon": 0, "ActivityDepict": "充值5天，领取超值大礼！", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 4, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 13, "ActivityType": 10, "UiType": 0, "ActivityName": "累充挂件", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 1, "FirstLoginShow": 1, "ComBoxOrder": 0}, {"id": 14, "ActivityType": 11, "UiType": 0, "ActivityName": "世界BOSS", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 1, "RefreshCycle": 1, "Periods": 0, "StartTime": "2019/12/15 12:30:00", "EndTime": "2099/12/15 13:00:00", "ShowStartTime": "2019/12/15 12:30:00", "ShowEndTime": "2099/12/15 13:00:00", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 15, "ActivityType": 11, "UiType": 0, "ActivityName": "世界BOSS", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 1, "RefreshCycle": 1, "Periods": 0, "StartTime": "2019/12/15 19:30:00 ", "EndTime": "2019/12/15 20:00:00 ", "ShowStartTime": "2019/12/15 19:30:00 ", "ShowEndTime": "2019/12/15 20:00:00 ", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 16, "ActivityType": 20, "UiType": 0, "ActivityName": "限时助力", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 1, "StartTime": "2020-3-1 00:00:00", "EndTime": "2020-6-1 23:59:59", "ShowStartTime": "2020-3-1 00:00:00", "ShowEndTime": "2020-6-1 23:59:59", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 17, "ActivityType": 21, "UiType": 1, "ActivityName": "名人堂", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/6/30 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/6/30 23:59:59", "Order": 6, "PushWay": 2, "FirstLoginShow": 2, "ComBoxOrder": 0}, {"id": 18, "ActivityType": 22, "UiType": 2, "ActivityName": "累计充值活动", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 19, "ActivityType": 23, "UiType": 2, "ActivityName": "消耗回馈", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/6/30 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/6/30 23:59:59", "Order": 5, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 20, "ActivityType": 26, "UiType": 0, "ActivityName": "MVP球员", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "2020/3/16 00:0:00", "EndTime": "2020/3/22 23:59:59", "ShowStartTime": "2020/3/16 00:0:00", "ShowEndTime": "2020/3/22 23:59:59", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 21, "ActivityType": 27, "UiType": 0, "ActivityName": "MVP返利购买", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "2020/3/16 00:0:00", "EndTime": "2020/3/17 17:00:00", "ShowStartTime": "2020/3/16 00:0:00", "ShowEndTime": "2020/3/17 17:00:00", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 22, "ActivityType": 28, "UiType": 0, "ActivityName": "MVP邮件发送时间", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 0, "RefreshCycle": 0, "Periods": 0, "StartTime": "2020/3/17 21:00:00", "EndTime": "2020/3/17 21:40:00", "ShowStartTime": "2020/3/17 21:00:00", "ShowEndTime": "2020/3/17 21:40:00", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 23, "ActivityType": 25, "UiType": 0, "ActivityName": "幸运摇摇乐", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 28, "StartTime": "2020/7/1 00:0:00", "EndTime": "2020/7/3 23:59:59", "ShowStartTime": "2020/7/1 00:0:00", "ShowEndTime": "2020/7/3 23:59:59", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 24, "ActivityType": 29, "UiType": 0, "ActivityName": "开服大礼包", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 2, "StartTime": "2020/5/28 00:00:00 ", "EndTime": "2099/3/2 00:00:00 ", "ShowStartTime": "2020/3/2 00:00:00 ", "ShowEndTime": "2099/3/2 00:00:00 ", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 25, "ActivityType": 30, "UiType": 0, "ActivityName": "大牌青训", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/6/29 00:00:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/6/29 00:00:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 8, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 26, "ActivityType": 31, "UiType": 0, "ActivityName": "球队强化", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 28, "StartTime": "2020/7/2 00:0:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/7/2 00:0:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 3, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 27, "ActivityType": 32, "UiType": 0, "ActivityName": "限时教练", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/7/1 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/7/1 23:59:59", "Order": 3, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 28, "ActivityType": 33, "UiType": 0, "ActivityName": "限时购买", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 28, "StartTime": "2020/7/1 00:00:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/7/1 00:00:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 7, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 29, "ActivityType": 34, "UiType": 0, "ActivityName": "限时重置大礼包", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 4, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 30, "ActivityType": 35, "UiType": 0, "ActivityName": "隐藏大礼包", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 28, "StartTime": "2020/6/29 00:0:00", "EndTime": "2020/7/5 23:59:59", "ShowStartTime": "2020/6/29 00:0:00", "ShowEndTime": "2020/7/5 23:59:59", "Order": 11, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 31, "ActivityType": 36, "UiType": 0, "ActivityName": "68球员", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 9, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 32, "ActivityType": 37, "UiType": 0, "ActivityName": "西甲mvp", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 0, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 10, "PushWay": 2, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 33, "ActivityType": 38, "UiType": 0, "ActivityName": "球员球币折扣", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 0, "StartTime": "2000/7/2 00:0:00", "EndTime": "2000/7/5 23:59:59", "ShowStartTime": "2000/7/2 00:0:00", "ShowEndTime": "2000/7/5 23:59:59", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}, {"id": 34, "ActivityType": 39, "UiType": 0, "ActivityName": "幸运转圈圈", "ActivityIcon": 0, "ActivityDepict": "", "Jump": 0, "TimeType": 2, "RefreshCycle": 1, "Periods": 0, "StartTime": "", "EndTime": "", "ShowStartTime": "", "ShowEndTime": "", "Order": 0, "PushWay": 0, "FirstLoginShow": 0, "ComBoxOrder": 0}]