/**
 * Service模块统一导出
 * 
 * 提供通用的BaseService基类和相关类型定义
 * 用于6个微服务的业务逻辑层统一实现
 */

// 基础Service类
export { BaseService } from './base-service';

// 类型定义
export type {
  BusinessOperationOptions,
  MicroserviceCallOptions,
  ServicePaginationOptions
} from './base-service';

// Result类型（从types模块重新导出，方便使用）
export type {
  XResult,
  SuccessResult,
  FailureResult
} from '../types/result.type';

// Result工具类（从types模块重新导出，方便使用）
export {
  XResultUtils,
  ServiceResultHandler
} from '../types/result.type';

/**
 * 使用示例：
 * 
 * ```typescript
 * import { BaseService, XResult, XResultUtils } from '@libs/common/service';
 * 
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepository: CharacterRepository,
 *     microserviceClient: MicroserviceClientService
 *   ) {
 *     super('CharacterService', microserviceClient);
 *   }
 * 
 *   async createCharacter(createDto: CreateCharacterDto): Promise<XResult<Character>> {
 *     return this.executeBusinessOperation(async () => {
 *       const result = await this.characterRepository.createOne(createDto);
 *       return this.handleRepositoryResult(result);
 *     }, { reason: 'create_character' });
 *   }
 * }
 * ```
 */
