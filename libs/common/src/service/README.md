# BaseService 通用业务逻辑基类

## 🎯 设计目标

BaseService是为6个微服务（social、match、hero、economy、character、activity）设计的通用业务逻辑基类，具有以下特点：

- **统一的业务逻辑处理模式** - 标准化的业务操作流程
- **完整的Result模式集成** - 类型安全的错误处理
- **标准化的微服务调用** - 统一的服务间通信
- **通用的错误处理和日志记录** - 完整的操作追踪
- **性能监控支持** - 慢操作检测和性能分析
- **缓存集成框架** - 支持业务级缓存策略

## 🚀 核心特性

### 1. 统一的业务操作框架
- `executeBusinessOperation` - 带性能监控的业务操作执行
- `handleRepositoryResult` - Repository结果统一处理
- `validateBusinessRules` - 业务规则验证框架

### 2. 标准化微服务调用
- `callMicroservice` - 单个微服务调用（带错误处理）
- `batchCallMicroservices` - 批量微服务调用
- 自动日志记录和性能监控

### 3. 通用业务方法
- `getCharacterInfo` - 获取角色信息
- `checkCharacterResource` - 检查角色资源
- `deductCharacterResource` - 扣除角色资源
- `addCharacterResource` - 添加角色资源

### 4. 完整的日志和监控
- 操作级别的性能监控
- 慢操作自动检测（>5秒）
- 微服务调用链追踪
- 敏感数据自动脱敏

## 📖 使用示例

### 基础使用

```typescript
import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class CharacterService extends BaseService {
  constructor(
    private readonly characterRepository: CharacterRepository,
    microserviceClient: MicroserviceClientService
  ) {
    super('CharacterService', microserviceClient);
  }

  async createCharacter(createDto: CreateCharacterDto): Promise<XResult<Character>> {
    return this.executeBusinessOperation(async () => {
      // 1. 验证业务规则
      const validationResult = await this.validateBusinessRules([
        () => this.validateCharacterName(createDto.name),
        () => this.validateServerCapacity(createDto.serverId)
      ]);

      if (XResultUtils.isFailure(validationResult)) {
        return validationResult as any;
      }

      // 2. 创建角色
      const createResult = await this.characterRepository.createOne(createDto);
      if (XResultUtils.isFailure(createResult)) {
        return this.handleRepositoryResult(createResult);
      }

      // 3. 初始化相关数据
      await this.initializeCharacterData(createResult.data.characterId);

      return createResult;
    }, { reason: 'create_character', metadata: { userId: createDto.userId } });
  }

  private async initializeCharacterData(characterId: string): Promise<void> {
    // 批量调用其他微服务初始化数据
    await this.batchCallMicroservices([
      {
        serviceName: MICROSERVICE_NAMES.HERO_SERVICE,
        pattern: 'hero.createInitial',
        payload: { characterId },
        key: 'heroes'
      },
      {
        serviceName: MICROSERVICE_NAMES.ECONOMY_SERVICE,
        pattern: 'shop.initialize',
        payload: { characterId },
        key: 'shops'
      }
    ]);
  }
}
```

### 微服务调用示例

```typescript
export class ShopService extends BaseService {
  constructor(
    private readonly shopRepository: ShopRepository,
    microserviceClient: MicroserviceClientService
  ) {
    super('ShopService', microserviceClient);
  }

  async purchaseItem(characterId: string, itemId: string, quantity: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 1. 获取商品配置
      const itemConfig = await this.getItemConfig(itemId);
      if (XResultUtils.isFailure(itemConfig)) {
        return itemConfig;
      }

      const totalCost = itemConfig.data.price * quantity;

      // 2. 检查角色资源
      const resourceCheck = await this.checkCharacterResource(characterId, 'gold', totalCost);
      if (XResultUtils.isFailure(resourceCheck)) {
        return resourceCheck as any;
      }

      // 3. 扣除资源
      const deductResult = await this.deductCharacterResource(
        characterId, 
        'gold', 
        totalCost, 
        `购买商品${itemId}`
      );
      if (XResultUtils.isFailure(deductResult)) {
        return deductResult as any;
      }

      // 4. 添加物品到背包
      const addItemResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'inventory.addItem',
        { characterId, itemId, quantity }
      );

      return addItemResult;
    }, { reason: 'purchase_item' });
  }

  private async getItemConfig(itemId: string): Promise<XResult<any>> {
    // 调用配置服务获取商品信息
    return this.callMicroservice(
      MICROSERVICE_NAMES.CONFIG_SERVICE,
      'item.getConfig',
      { itemId }
    );
  }
}
```

### 业务规则验证示例

```typescript
export class HeroService extends BaseService {
  async levelUpHero(heroId: string, characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 验证多个业务规则
      const validationResult = await this.validateBusinessRules([
        () => this.validateHeroExists(heroId),
        () => this.validateHeroOwnership(heroId, characterId),
        () => this.validateLevelUpRequirements(heroId),
        () => this.checkLevelUpResources(heroId, characterId)
      ]);

      if (XResultUtils.isFailure(validationResult)) {
        return validationResult as any;
      }

      // 执行升级逻辑
      return this.performLevelUp(heroId);
    }, { reason: 'hero_level_up' });
  }

  private async validateHeroExists(heroId: string): Promise<XResult<void>> {
    const hero = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(hero) || !hero.data) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }
    return XResultUtils.ok(undefined);
  }

  private async validateLevelUpRequirements(heroId: string): Promise<XResult<void>> {
    // 检查升级条件
    const hero = await this.heroRepository.findById(heroId);
    if (XResultUtils.isSuccess(hero) && hero.data) {
      if (hero.data.level >= 100) {
        return XResultUtils.error('球员已达到最高等级', 'MAX_LEVEL_REACHED');
      }
    }
    return XResultUtils.ok(undefined);
  }
}
```

## 🔧 高级功能

### 1. 自定义业务操作选项

```typescript
const result = await this.executeBusinessOperation(
  async () => {
    // 业务逻辑
  },
  {
    reason: 'custom_operation',
    skipValidation: false,
    timeout: 10000,
    metadata: {
      userId: 'user123',
      source: 'web_client'
    }
  }
);
```

### 2. 微服务调用选项

```typescript
const result = await this.callMicroservice(
  MICROSERVICE_NAMES.HERO_SERVICE,
  'hero.getInfo',
  { heroId },
  {
    timeout: 5000,
    retries: 3,
    logCall: true
  }
);
```

### 3. 批量操作

```typescript
const results = await this.batchCallMicroservices([
  {
    serviceName: MICROSERVICE_NAMES.CHARACTER_SERVICE,
    pattern: 'character.getInfo',
    payload: { characterId },
    key: 'character'
  },
  {
    serviceName: MICROSERVICE_NAMES.HERO_SERVICE,
    pattern: 'hero.getList',
    payload: { characterId },
    key: 'heroes'
  }
]);

if (XResultUtils.isSuccess(results)) {
  const character = results.data.character;
  const heroes = results.data.heroes;
}
```

## 📊 性能监控

### 自动性能监控
- 所有业务操作自动记录执行时间
- 慢操作自动告警（>5秒）
- 微服务调用链追踪

### 日志示例
```
[CharacterService] 业务操作开始: CharacterService_1640995200000_abc123 - reason: create_character
[CharacterService] 微服务调用开始: call_1640995200100_def456 - hero.createInitial
[CharacterService] 微服务调用完成: call_1640995200100_def456 - 耗时 150ms
[CharacterService] 业务操作成功: CharacterService_1640995200000_abc123 - 耗时 300ms
```

## 🚨 注意事项

1. **Result模式**：所有方法都返回`XResult<T>`，需要检查`success`字段
2. **微服务调用**：需要注入`MicroserviceClientService`
3. **业务规则验证**：使用`validateBusinessRules`进行统一验证
4. **性能监控**：自动记录慢操作，建议关注日志输出
5. **错误处理**：统一使用Result模式，避免直接抛出异常

## 🔗 相关文档

- [BaseController文档](../controller/README.md)
- [Result模式详细文档](../types/result.type.ts)
- [微服务通信文档](../../service-mesh/README.md)
