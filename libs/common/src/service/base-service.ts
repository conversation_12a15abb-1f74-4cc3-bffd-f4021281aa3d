import { Injectable, Logger } from '@nestjs/common';
import { XResult, XResultUtils, ServiceResultHandler } from '../types/result.type';

/**
 * 业务操作选项
 */
export interface BusinessOperationOptions {
  /** 操作原因/来源 */
  reason?: string;
  /** 是否跳过验证 */
  skipValidation?: boolean;
  /** 额外的元数据 */
  metadata?: Record<string, any>;
  /** 操作超时时间（毫秒） */
  timeout?: number;
}

/**
 * 微服务调用选项
 */
export interface MicroserviceCallOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 是否记录调用日志 */
  logCall?: boolean;
}

/**
 * 微服务客户端接口（抽象接口，避免具体依赖）
 */
export interface IMicroserviceClient {
  call<T = any>(serviceName: string, pattern: string, payload: any): Promise<any>;
}

/**
 * 分页查询选项
 */
export interface ServicePaginationOptions {
  /** 页码（从1开始） */
  page: number;
  /** 每页记录数 */
  limit: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 搜索关键词 */
  search?: string;
  /** 额外的过滤条件 */
  filters?: Record<string, any>;
}

/**
 * 通用BaseService基类
 * 
 * 🎯 设计目标：
 * - 统一的业务逻辑处理模式
 * - 标准化的微服务调用
 * - 完整的Result模式集成
 * - 通用的错误处理和日志记录
 * - 缓存集成支持
 * - 业务验证框架
 * 
 * 🚀 核心特性：
 * - 统一的Result模式错误处理
 * - 标准化的微服务通信
 * - 通用的业务操作模式
 * - 完整的日志记录
 * - 性能监控支持
 * - 缓存集成框架
 * 
 * 📖 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepository: CharacterRepository,
 *     microserviceClient: MicroserviceClientService
 *   ) {
 *     super('CharacterService', microserviceClient);
 *   }
 * 
 *   async createCharacter(createDto: CreateCharacterDto): Promise<XResult<Character>> {
 *     return this.executeBusinessOperation(async () => {
 *       // 业务逻辑实现
 *       const result = await this.characterRepository.createOne(createDto);
 *       return this.handleRepositoryResult(result);
 *     }, { reason: 'create_character' });
 *   }
 * }
 * ```
 */
@Injectable()
export abstract class BaseService {
  protected readonly logger: Logger;

  constructor(
    protected readonly serviceName: string,
    protected readonly microserviceClient?: IMicroserviceClient
  ) {
    this.logger = new Logger(serviceName);
  }

  // ========== 核心业务操作方法 ==========

  /**
   * 执行业务操作（带性能监控和错误处理）
   * @param operation 业务操作函数
   * @param options 操作选项
   */
  protected async executeBusinessOperation<T>(
    operation: () => Promise<XResult<T>>,
    options: BusinessOperationOptions = {}
  ): Promise<XResult<T>> {
    const startTime = Date.now();
    const operationId = this.generateOperationId();

    try {
      this.logOperationStart(operationId, options);

      const result = await operation();

      this.logOperationEnd(operationId, startTime, XResultUtils.isSuccess(result));
      return result;

    } catch (error) {
      this.logOperationError(operationId, startTime, error);
      return XResultUtils.error(
        `业务操作失败: ${error.message}`,
        'BUSINESS_OPERATION_ERROR'
      );
    }
  }

  /**
   * 处理Repository结果
   * @param repositoryResult Repository返回的结果
   * @param errorMessage 自定义错误消息
   */
  protected handleRepositoryResult<T>(
    repositoryResult: XResult<T>,
    errorMessage?: string
  ): XResult<T> {
    if (XResultUtils.isFailure(repositoryResult)) {
      const message = errorMessage || `数据访问失败: ${repositoryResult.message}`;
      this.logger.error(message, { originalError: repositoryResult });
      return XResultUtils.error(message, repositoryResult.code);
    }

    return repositoryResult;
  }

  /**
   * 验证业务规则
   * @param validations 验证函数数组
   */
  protected async validateBusinessRules(
    validations: Array<() => Promise<XResult<void>> | XResult<void>>
  ): Promise<XResult<void>> {
    for (const validation of validations) {
      const result = await validation();
      if (XResultUtils.isFailure(result)) {
        return result;
      }
    }
    return XResultUtils.ok(undefined);
  }

  // ========== 微服务调用方法 ==========

  /**
   * 调用微服务（带错误处理和日志）
   * @param serviceName 服务名称
   * @param pattern 消息模式
   * @param payload 请求数据
   * @param options 调用选项
   */
  protected async callMicroservice<T = any>(
    serviceName: string,
    pattern: string,
    payload: any,
    options: MicroserviceCallOptions = {}
  ): Promise<XResult<T>> {
    if (!this.microserviceClient) {
      return XResultUtils.error('微服务客户端未初始化', 'MICROSERVICE_CLIENT_NOT_INITIALIZED');
    }

    const startTime = Date.now();
    const callId = this.generateCallId();

    try {
      if (options.logCall !== false) {
        this.logger.log(`微服务调用开始: ${callId} - ${serviceName}.${pattern}`, {
          payload: this.sanitizePayload(payload)
        });
      }

      const result = await this.microserviceClient.call(serviceName, pattern, payload);

      const duration = Date.now() - startTime;
      if (options.logCall !== false) {
        this.logger.log(`微服务调用完成: ${callId} - 耗时 ${duration}ms`, {
          success: result?.code === 0
        });
      }

      // 转换微服务响应为Result格式
      if (result && result.code === 0) {
        return XResultUtils.ok(result.data);
      } else {
        const errorMessage = result?.message || '微服务调用失败';
        return XResultUtils.error(errorMessage, `MICROSERVICE_ERROR_${result?.code || 'UNKNOWN'}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`微服务调用失败: ${callId} - 耗时 ${duration}ms`, error);
      return XResultUtils.error(
        `微服务调用异常: ${error.message}`,
        'MICROSERVICE_CALL_EXCEPTION'
      );
    }
  }

  /**
   * 批量调用微服务
   * @param calls 调用配置数组
   */
  protected async batchCallMicroservices<T = any>(
    calls: Array<{
      serviceName: string;
      pattern: string;
      payload: any;
      key?: string;
    }>
  ): Promise<XResult<Record<string, T>>> {
    try {
      const promises = calls.map(async (call, index) => {
        const key = call.key || `call_${index}`;
        const result = await this.callMicroservice(call.serviceName, call.pattern, call.payload);
        return { key, result };
      });

      const results = await Promise.all(promises);
      const successResults: Record<string, T> = {};
      const errors: string[] = [];

      for (const { key, result } of results) {
        if (XResultUtils.isSuccess(result)) {
          successResults[key] = result.data;
        } else {
          errors.push(`${key}: ${result.message}`);
        }
      }

      if (errors.length > 0) {
        return XResultUtils.error(
          `批量调用部分失败: ${errors.join(', ')}`,
          'BATCH_CALL_PARTIAL_FAILURE'
        );
      }

      return XResultUtils.ok(successResults);

    } catch (error) {
      this.logger.error('批量微服务调用失败', error);
      return XResultUtils.error(
        `批量调用异常: ${error.message}`,
        'BATCH_CALL_EXCEPTION'
      );
    }
  }

  // ========== 通用业务方法 ==========

  /**
   * 获取角色信息（通用方法）
   * @param characterId 角色ID
   */
  protected async getCharacterInfo(characterId: string): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.getInfo',
      { characterId }
    );
  }

  /**
   * 检查角色资源是否足够
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 需要的数量
   */
  protected async checkCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number
  ): Promise<XResult<boolean>> {
    const characterResult = await this.getCharacterInfo(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult as any;
    }

    const character = characterResult.data;
    const currentAmount = character[resourceType] || 0;
    const sufficient = currentAmount >= amount;

    if (!sufficient) {
      return XResultUtils.error(
        `${resourceType}不足，需要${amount}，当前${currentAmount}`,
        'INSUFFICIENT_RESOURCE'
      );
    }

    return XResultUtils.ok(true);
  }

  /**
   * 扣除角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 扣除数量
   * @param reason 扣除原因
   */
  protected async deductCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.deductCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }

  /**
   * 添加角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 添加数量
   * @param reason 添加原因
   */
  protected async addCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.addCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }

  // ========== 工具方法 ==========

  /**
   * 生成操作ID
   */
  protected generateOperationId(): string {
    return `${this.serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成调用ID
   */
  protected generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 清理敏感数据
   * @param payload 请求数据
   */
  protected sanitizePayload(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    const sanitized = { ...payload };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  /**
   * 记录操作开始
   */
  protected logOperationStart(operationId: string, options: BusinessOperationOptions): void {
    this.logger.log(`业务操作开始: ${operationId}`, {
      reason: options.reason,
      metadata: options.metadata
    });
  }

  /**
   * 记录操作结束
   */
  protected logOperationEnd(operationId: string, startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    const level = success ? 'log' : 'warn';
    this.logger[level](`业务操作${success ? '成功' : '失败'}: ${operationId} - 耗时 ${duration}ms`);

    if (duration > 5000) {
      this.logger.warn(`慢操作检测: ${operationId} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录操作错误
   */
  protected logOperationError(operationId: string, startTime: number, error: any): void {
    const duration = Date.now() - startTime;
    this.logger.error(`业务操作异常: ${operationId} - 耗时 ${duration}ms`, {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 构建分页查询条件
   * @param options 分页选项
   */
  protected buildPaginationQuery(options: ServicePaginationOptions): any {
    const query: any = {
      page: options.page,
      limit: options.limit
    };

    if (options.sortBy) {
      query.sortBy = options.sortBy;
      query.sortOrder = options.sortOrder || 'desc';
    }

    if (options.search) {
      query.search = options.search;
    }

    if (options.filters) {
      Object.assign(query, options.filters);
    }

    return query;
  }

  /**
   * 获取服务名称
   */
  protected getServiceName(): string {
    return this.serviceName;
  }
}
