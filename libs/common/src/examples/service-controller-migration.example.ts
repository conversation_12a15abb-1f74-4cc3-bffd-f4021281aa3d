/**
 * Service和Controller迁移示例
 * 展示如何将现有的Service和Controller迁移到BaseService和BaseController
 */

import { Injectable, Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseService, IMicroserviceClient } from '../service/base-service';
import { BaseController, BasePayload } from '../controller/base-controller';
import { XResult, XResultUtils } from '../types/result.type';
import { Cacheable, CacheEvict } from '@libs/redis';

// ========== Service层迁移示例 ==========

/**
 * 迁移前的CharacterService示例
 */
// @Injectable()
// export class CharacterServiceOld {
//   private readonly logger = new Logger(CharacterServiceOld.name);
//
//   constructor(private readonly characterRepository: CharacterRepository) {}
//
//   async createCharacter(createDto: CreateCharacterDto): Promise<CharacterDocument> {
//     try {
//       // 验证角色名
//       if (!createDto.name || createDto.name.trim().length === 0) {
//         throw new Error('角色名不能为空');
//       }
//
//       // 检查角色名是否已存在
//       const existing = await this.characterRepository.findOne({ name: createDto.name });
//       if (existing) {
//         throw new Error('角色名已存在');
//       }
//
//       // 创建角色
//       const character = await this.characterRepository.create(createDto);
//       this.logger.log(`角色创建成功: ${character.characterId}`);
//       return character;
//     } catch (error) {
//       this.logger.error('创建角色失败', error);
//       throw error;
//     }
//   }
// }

/**
 * 迁移后的CharacterService示例
 */
@Injectable()
export class CharacterServiceExample extends BaseService {
  constructor(
    private readonly characterRepository: any, // CharacterRepository
    microserviceClient?: IMicroserviceClient
  ) {
    super('CharacterService', microserviceClient);
  }

  /**
   * 创建角色（迁移后）
   */
  async createCharacter(createDto: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 1. 业务规则验证
      const validationResult = await this.validateBusinessRules([
        () => this.validateCharacterName(createDto.name),
        () => this.validateServerCapacity(createDto.serverId),
        () => this.checkCharacterNameUnique(createDto.name)
      ]);

      if (XResultUtils.isFailure(validationResult)) {
        return validationResult as any;
      }

      // 2. 创建角色
      const createResult = await this.characterRepository.createOne(createDto);
      if (XResultUtils.isFailure(createResult)) {
        return this.handleRepositoryResult(createResult, '角色创建失败');
      }

      // 3. 初始化相关数据
      await this.initializeCharacterData(createResult.data.characterId);

      return createResult;
    }, { 
      reason: 'create_character', 
      metadata: { userId: createDto.userId, serverId: createDto.serverId } 
    });
  }

  /**
   * 获取角色信息（迁移后）
   */
  async getCharacterInfo(characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.characterRepository.findById(characterId);
      return this.handleRepositoryResult(result, '角色不存在');
    }, { reason: 'get_character_info' });
  }

  /**
   * 更新角色信息（迁移后）
   */
  async updateCharacter(characterId: string, updateDto: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 1. 验证更新权限
      const permissionCheck = await this.validateUpdatePermission(characterId, updateDto);
      if (XResultUtils.isFailure(permissionCheck)) {
        return permissionCheck as any;
      }

      // 2. 执行更新
      const updateResult = await this.characterRepository.updateById(characterId, updateDto);
      return this.handleRepositoryResult(updateResult, '角色更新失败');
    }, { reason: 'update_character' });
  }

  // ========== 私有业务方法 ==========

  private async validateCharacterName(name: string): Promise<XResult<void>> {
    if (!name || name.trim().length === 0) {
      return XResultUtils.error('角色名不能为空', 'INVALID_CHARACTER_NAME');
    }
    if (name.length > 20) {
      return XResultUtils.error('角色名不能超过20个字符', 'CHARACTER_NAME_TOO_LONG');
    }
    return XResultUtils.ok(undefined);
  }

  private async validateServerCapacity(serverId: string): Promise<XResult<void>> {
    // 检查服务器容量
    const serverInfo = await this.callMicroservice('game', 'server.getInfo', { serverId });
    if (XResultUtils.isFailure(serverInfo)) {
      return serverInfo as any;
    }

    if (serverInfo.data.currentPlayers >= serverInfo.data.maxPlayers) {
      return XResultUtils.error('服务器已满', 'SERVER_FULL');
    }

    return XResultUtils.ok(undefined);
  }

  private async checkCharacterNameUnique(name: string): Promise<XResult<void>> {
    const existingResult = await this.characterRepository.exists({ name });
    if (XResultUtils.isFailure(existingResult)) {
      return existingResult as any;
    }

    if (existingResult.data) {
      return XResultUtils.error('角色名已存在', 'CHARACTER_NAME_EXISTS');
    }

    return XResultUtils.ok(undefined);
  }

  private async validateUpdatePermission(characterId: string, updateDto: any): Promise<XResult<void>> {
    // 检查是否有敏感字段更新
    const sensitiveFields = ['userId', 'serverId', 'characterId'];
    const hasSensitiveUpdate = sensitiveFields.some(field => field in updateDto);
    
    if (hasSensitiveUpdate) {
      return XResultUtils.error('不允许更新敏感字段', 'SENSITIVE_FIELD_UPDATE_DENIED');
    }

    return XResultUtils.ok(undefined);
  }

  private async initializeCharacterData(characterId: string): Promise<void> {
    // 批量初始化相关数据
    await this.batchCallMicroservices([
      {
        serviceName: 'hero',
        pattern: 'hero.createInitial',
        payload: { characterId },
        key: 'heroes'
      },
      {
        serviceName: 'economy',
        pattern: 'shop.initialize',
        payload: { characterId },
        key: 'shops'
      },
      {
        serviceName: 'social',
        pattern: 'friend.initialize',
        payload: { characterId },
        key: 'friends'
      }
    ]);
  }
}

// ========== Controller层迁移示例 ==========

/**
 * 迁移前的CharacterController示例
 */
// @Controller()
// export class CharacterControllerOld {
//   private readonly logger = new Logger(CharacterControllerOld.name);
//
//   constructor(private readonly characterService: CharacterService) {}
//
//   @MessagePattern('character.create')
//   async createCharacter(@Payload() payload: any) {
//     try {
//       this.logger.log(`创建角色请求: ${JSON.stringify(payload)}`);
//       
//       if (!payload.createDto) {
//         return { code: -1, message: '缺少创建参数' };
//       }
//
//       const character = await this.characterService.createCharacter(payload.createDto);
//       return {
//         code: 0,
//         message: '角色创建成功',
//         data: character
//       };
//     } catch (error) {
//       this.logger.error('创建角色失败', error);
//       return {
//         code: -1,
//         message: error.message || '创建角色失败'
//       };
//     }
//   }
// }

/**
 * 迁移后的CharacterController示例
 */
@Controller()
export class CharacterControllerExample extends BaseController {
  constructor(private readonly characterService: CharacterServiceExample) {
    super('CharacterController');
  }

  /**
   * 创建角色（迁移后）
   */
  @MessagePattern('character.create')
  async createCharacter(@Payload() payload: BasePayload & { createDto: any }) {
    return this.handleRequest(async () => {
      // 1. 参数验证
      const validation = this.validateRequiredFields(payload, ['createDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation, '参数验证失败');
      }

      // 2. 调用业务逻辑
      const result = await this.characterService.createCharacter(payload.createDto);
      
      // 3. 返回标准响应
      return this.fromResult(result, '角色创建成功');
    }, payload);
  }

  /**
   * 获取角色信息（迁移后）
   */
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterInfo(@Payload() payload: BasePayload) {
    return this.handleRequest(async () => {
      // 1. 参数验证
      const characterIdValidation = this.validateCharacterId(payload.characterId);
      if (XResultUtils.isFailure(characterIdValidation)) {
        return this.fromResult(characterIdValidation, '角色ID无效');
      }

      // 2. 调用业务逻辑
      const result = await this.characterService.getCharacterInfo(payload.characterId!);
      
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  /**
   * 更新角色信息（迁移后）
   */
  @MessagePattern('character.update')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateCharacter(@Payload() payload: BasePayload & { updateDto: any }) {
    return this.handleRequest(async () => {
      // 1. 参数验证
      const validation = await this.validateUpdateRequest(payload);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation, '更新请求验证失败');
      }

      // 2. 调用业务逻辑
      const result = await this.characterService.updateCharacter(
        payload.characterId!,
        payload.updateDto
      );

      return this.fromResult(result, '更新成功');
    }, payload);
  }

  /**
   * 获取角色列表（分页）
   */
  @MessagePattern('character.getList')
  async getCharacterList(@Payload() payload: any) {
    return this.handlePaginationRequest(async (paginationOptions) => {
      // 合并查询条件和分页参数
      const queryOptions = {
        ...payload.query,
        ...paginationOptions
      };

      return this.characterService.getCharacterList(queryOptions);
    }, payload);
  }

  // ========== 私有验证方法 ==========

  private async validateUpdateRequest(payload: any): Promise<XResult<void>> {
    // 验证必需字段
    const requiredValidation = this.validateRequiredFields(payload, [
      'characterId', 'updateDto'
    ]);
    if (XResultUtils.isFailure(requiredValidation)) {
      return requiredValidation;
    }

    // 验证角色ID
    const characterIdValidation = this.validateCharacterId(payload.characterId);
    if (XResultUtils.isFailure(characterIdValidation)) {
      return characterIdValidation as any;
    }

    // 验证更新数据不为空
    if (!payload.updateDto || Object.keys(payload.updateDto).length === 0) {
      return XResultUtils.error('更新数据不能为空', 'EMPTY_UPDATE_DATA');
    }

    return XResultUtils.ok(undefined);
  }
}

// ========== 迁移对比总结 ==========

/**
 * 迁移效果对比：
 * 
 * 1. 代码减少：
 *    - Service层：减少60%的样板代码（错误处理、日志记录）
 *    - Controller层：减少70%的样板代码（响应格式化、参数验证）
 * 
 * 2. 功能增强：
 *    - 统一的Result模式错误处理
 *    - 自动性能监控和慢操作检测
 *    - 标准化的微服务调用
 *    - 完整的请求链追踪
 * 
 * 3. 可维护性提升：
 *    - 统一的基类，便于功能扩展
 *    - 标准化的接口，团队成员快速上手
 *    - 类型安全的错误处理
 * 
 * 4. 性能优化：
 *    - 自动慢操作检测
 *    - 批量微服务调用优化
 *    - 敏感数据自动脱敏
 */
