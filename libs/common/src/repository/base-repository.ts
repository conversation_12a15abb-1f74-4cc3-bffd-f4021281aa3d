import { Injectable, Logger } from '@nestjs/common';
import { Model, Document, ClientSession, FilterQuery, UpdateQuery } from 'mongoose';
import { XResult, XResultUtils, PaginationResult, RepositoryResultWrapper } from '../types/result.type';

/**
 * 扩展查询选项接口
 * 提供更丰富的查询配置能力
 */
export interface BaseQueryOptions<T = any> {
  /** 是否使用lean查询（性能优化，默认true） */
  lean?: boolean;
  /** 字段选择 */
  select?: string | string[];
  /** 关联查询 */
  populate?: any;
  /** 排序 */
  sort?: any;
  /** 跳过记录数 */
  skip?: number;
  /** 限制记录数 */
  limit?: number;
  /** 事务会话 */
  session?: ClientSession;
}

/**
 * 分页查询选项
 */
export interface BasePaginationOptions<T = any> extends BaseQueryOptions<T> {
  /** 页码（从1开始） */
  page: number;
  /** 每页记录数 */
  limit: number;
  /** 查询条件 */
  filter?: FilterQuery<T>;
}

/**
 * 搜索查询选项
 */
export interface BaseSearchOptions<T = any> extends BaseQueryOptions<T> {
  /** 搜索字段列表 */
  searchFields: string[];
  /** 搜索关键词 */
  searchTerm: string;
  /** 是否区分大小写（默认false） */
  caseSensitive?: boolean;
  /** 额外的过滤条件 */
  filter?: FilterQuery<T>;
}

/**
 * 批量操作结果
 */
export interface BulkOperationResult {
  /** 成功处理的记录数 */
  modifiedCount: number;
  /** 匹配的记录数 */
  matchedCount: number;
  /** 插入的记录数 */
  insertedCount?: number;
  /** 删除的记录数 */
  deletedCount?: number;
  /** 操作详情 */
  details?: any;
}

/**
 * 通用BaseRepository基类
 * 
 * 🎯 设计目标：
 * - API设计通用，覆盖所有常见数据库操作
 * - 使用方便，最小化样板代码
 * - 代码简洁优雅，高可读性
 * - 易于维护，统一错误处理
 * - 完全使用Result模式，类型安全
 * 
 * 🚀 核心特性：
 * - 统一的Result模式错误处理
 * - 智能Lean查询性能优化
 * - 完整的事务支持
 * - 丰富的查询方法
 * - 批量操作支持
 * - 搜索和分页功能
 * - 详细的操作日志
 * - 缓存集成支持
 * 
 * 📖 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterRepository extends BaseRepository<CharacterDocument> {
 *   constructor(@InjectModel(Character.name) model: Model<CharacterDocument>) {
 *     super(model, 'CharacterRepository');
 *   }
 * 
 *   // 业务特定方法
 *   async findByUserId(userId: string): Promise<XResult<CharacterDocument[]>> {
 *     return this.findMany({ userId });
 *   }
 * 
 *   async getActiveCharacters(): Promise<XResult<any[]>> {
 *     return this.findManyLean({ isActive: true }, { select: 'name level' });
 *   }
 * }
 * ```
 */
@Injectable()
export abstract class BaseRepository<T extends Document> {
  protected readonly logger: Logger;

  constructor(
    protected readonly model: Model<T>,
    protected readonly loggerContext: string
  ) {
    this.logger = new Logger(loggerContext);
  }

  /**
   * 获取Mongoose模型实例（用于高级操作）
   */
  protected get mongooseModel(): Model<T> {
    return this.model;
  }

  // ========== 核心CRUD操作 ==========

  /**
   * 根据ID查找单个文档
   * @param id 文档ID
   * @param options 查询选项
   */
  async findById(id: string, options: BaseQueryOptions<T> = {}): Promise<XResult<T | any | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();
      
      let query = this.model.findById(id);
      query = this.applyQueryOptions(query, options);
      
      const result = await query.exec();
      this.logPerformance('findById', startTime, { id, lean: options.lean });
      
      return result;
    });
  }

  /**
   * 根据条件查找单个文档
   * @param filter 查询条件
   * @param options 查询选项
   */
  async findOne(filter: FilterQuery<T>, options: BaseQueryOptions<T> = {}): Promise<XResult<T | any | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();
      
      let query = this.model.findOne(filter);
      query = this.applyQueryOptions(query, options);
      
      const result = await query.exec();
      this.logPerformance('findOne', startTime, { filter, lean: options.lean });
      
      return result;
    });
  }

  /**
   * 根据条件查找多个文档
   * @param filter 查询条件
   * @param options 查询选项
   */
  async findMany(filter: FilterQuery<T> = {}, options: BaseQueryOptions<T> = {}): Promise<XResult<T[] | any[]>> {
    return RepositoryResultWrapper.wrapArray(async () => {
      const startTime = Date.now();
      
      let query = this.model.find(filter);
      query = this.applyQueryOptions(query, options);
      
      const result = await query.exec();
      this.logPerformance('findMany', startTime, { filter, count: result.length, lean: options.lean });
      
      return result;
    });
  }

  /**
   * 创建单个文档
   * @param data 文档数据
   * @param session 事务会话
   */
  async createOne(data: Partial<T>, session?: ClientSession): Promise<XResult<T>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();
      
      const options = session ? { session } : {};
      const docs = await this.model.create([data], options);
      
      this.logPerformance('createOne', startTime, { hasSession: !!session });
      this.logger.log(`文档创建成功: ${this.loggerContext}`);
      
      return docs[0];
    });
  }

  /**
   * 根据ID更新文档
   * @param id 文档ID
   * @param update 更新数据
   * @param session 事务会话
   */
  async updateById(
    id: string, 
    update: UpdateQuery<T>, 
    session?: ClientSession
  ): Promise<XResult<T | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();
      
      const options = session ? { session, new: true } : { new: true };
      const result = await this.model.findByIdAndUpdate(id, update, options).exec();
      
      this.logPerformance('updateById', startTime, { id, hasSession: !!session });
      if (result) {
        this.logger.log(`文档更新成功: ${id}`);
      }
      
      return result;
    });
  }

  /**
   * 根据条件更新单个文档
   * @param filter 查询条件
   * @param update 更新数据
   * @param session 事务会话
   */
  async updateOne(
    filter: FilterQuery<T>,
    update: UpdateQuery<T>,
    session?: ClientSession
  ): Promise<XResult<T | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();
      
      const options = session ? { session, new: true } : { new: true };
      const result = await this.model.findOneAndUpdate(filter, update, options).exec();
      
      this.logPerformance('updateOne', startTime, { filter, hasSession: !!session });
      if (result) {
        this.logger.log(`文档更新成功: ${this.loggerContext}`);
      }
      
      return result;
    });
  }

  /**
   * 根据ID删除文档
   * @param id 文档ID
   * @param session 事务会话
   */
  async deleteById(id: string, session?: ClientSession): Promise<XResult<boolean>> {
    return RepositoryResultWrapper.wrapBoolean(async () => {
      const startTime = Date.now();
      
      const options = session ? { session } : {};
      const result = await this.model.deleteOne({ _id: id }, options).exec();
      
      this.logPerformance('deleteById', startTime, { id, hasSession: !!session });
      const success = result.deletedCount > 0;
      
      if (success) {
        this.logger.log(`文档删除成功: ${id}`);
      }
      
      return success;
    });
  }

  /**
   * 根据条件删除单个文档
   * @param filter 查询条件
   * @param session 事务会话
   */
  async deleteOne(filter: FilterQuery<T>, session?: ClientSession): Promise<XResult<boolean>> {
    return RepositoryResultWrapper.wrapBoolean(async () => {
      const startTime = Date.now();
      
      const options = session ? { session } : {};
      const result = await this.model.deleteOne(filter, options).exec();
      
      this.logPerformance('deleteOne', startTime, { filter, hasSession: !!session });
      const success = result.deletedCount > 0;
      
      if (success) {
        this.logger.log(`文档删除成功: ${this.loggerContext}`);
      }
      
      return success;
    });
  }

  // ========== 性能优化查询方法 ==========

  /**
   * Lean查询单个文档（性能优化）
   * @param filter 查询条件
   * @param options 查询选项（强制lean=true）
   */
  async findOneLean(filter: FilterQuery<T>, options: Omit<BaseQueryOptions<T>, 'lean'> = {}): Promise<XResult<any | null>> {
    return this.findOne(filter, { ...options, lean: true });
  }

  /**
   * Lean查询多个文档（性能优化）
   * @param filter 查询条件
   * @param options 查询选项（强制lean=true）
   */
  async findManyLean(filter: FilterQuery<T> = {}, options: Omit<BaseQueryOptions<T>, 'lean'> = {}): Promise<XResult<any[]>> {
    return this.findMany(filter, { ...options, lean: true });
  }

  /**
   * 根据ID进行Lean查询（性能优化）
   * @param id 文档ID
   * @param options 查询选项（强制lean=true）
   */
  async findByIdLean(id: string, options: Omit<BaseQueryOptions<T>, 'lean'> = {}): Promise<XResult<any | null>> {
    return this.findById(id, { ...options, lean: true });
  }

  // ========== 工具方法 ==========

  /**
   * 应用查询选项到查询对象
   * @param query Mongoose查询对象
   * @param options 查询选项
   */
  protected applyQueryOptions(query: any, options: BaseQueryOptions<T>): any {
    // 应用事务会话
    if (options.session) {
      query = query.session(options.session);
    }

    // 应用字段选择
    if (options.select) {
      query = query.select(options.select);
    }

    // 应用排序
    if (options.sort) {
      query = query.sort(options.sort);
    }

    // 应用跳过和限制
    if (options.skip !== undefined) {
      query = query.skip(options.skip);
    }
    if (options.limit !== undefined) {
      query = query.limit(options.limit);
    }

    // 应用关联查询
    if (options.populate) {
      if (Array.isArray(options.populate)) {
        options.populate.forEach(pop => query = query.populate(pop));
      } else {
        query = query.populate(options.populate);
      }
    }

    // 应用lean查询（默认启用以优化性能）
    if (options.lean !== false) {
      query = query.lean();
    }

    return query;
  }

  // ========== 高级查询方法 ==========

  /**
   * 分页查询
   * @param options 分页选项
   */
  async findWithPagination(options: BasePaginationOptions<T>): Promise<XResult<PaginationResult<T | any>>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();
      const { page, limit, filter = {}, ...queryOptions } = options;
      const skip = (page - 1) * limit;

      // 并行执行数据查询和总数统计
      const [dataResult, totalResult] = await Promise.all([
        this.findMany(filter, { ...queryOptions, skip, limit }),
        this.count(filter, queryOptions.session)
      ]);

      // 检查查询结果
      if (XResultUtils.isFailure(dataResult)) {
        throw new Error(`分页数据查询失败: ${dataResult.message}`);
      }
      if (XResultUtils.isFailure(totalResult)) {
        throw new Error(`分页计数查询失败: ${totalResult.message}`);
      }

      const data = dataResult.data;
      const total = totalResult.data;
      const pages = Math.ceil(total / limit);

      this.logPerformance('findWithPagination', startTime, {
        page, limit, total, count: data.length
      });

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      };
    });
  }

  /**
   * 搜索查询
   * @param options 搜索选项
   */
  async search(options: BaseSearchOptions<T>): Promise<XResult<T[] | any[]>> {
    return RepositoryResultWrapper.wrapArray(async () => {
      const startTime = Date.now();
      const { searchFields, searchTerm, caseSensitive = false, filter, ...queryOptions } = options;

      // 构建搜索条件
      const searchFilter: any = {
        $or: searchFields.map(field => ({
          [field]: {
            $regex: searchTerm,
            $options: caseSensitive ? '' : 'i'
          }
        }))
      };

      // 合并搜索条件和其他过滤条件
      const combinedFilter: any = filter
        ? { $and: [searchFilter, filter] }
        : searchFilter;

      let query = this.model.find(combinedFilter);
      query = this.applyQueryOptions(query, { ...queryOptions, lean: true });

      const result = await query.exec();
      this.logPerformance('search', startTime, {
        searchFields, searchTerm, count: result.length
      });

      return result;
    });
  }

  /**
   * 检查文档是否存在
   * @param filter 查询条件
   * @param session 事务会话
   */
  async exists(filter: FilterQuery<T>, session?: ClientSession): Promise<XResult<boolean>> {
    return RepositoryResultWrapper.wrapBoolean(async () => {
      const startTime = Date.now();

      const options = session ? { session } : {};
      const count = await this.model.countDocuments(filter, options).exec();

      this.logPerformance('exists', startTime, { filter });
      return count > 0;
    });
  }

  /**
   * 计数文档
   * @param filter 查询条件
   * @param session 事务会话
   */
  async count(filter: FilterQuery<T> = {}, session?: ClientSession): Promise<XResult<number>> {
    return RepositoryResultWrapper.wrapCount(async () => {
      const startTime = Date.now();

      const options = session ? { session } : {};
      const count = await this.model.countDocuments(filter, options).exec();

      this.logPerformance('count', startTime, { filter, count });
      return count;
    });
  }

  // ========== 批量操作方法 ==========

  /**
   * 批量创建文档
   * @param dataList 文档数据列表
   * @param session 事务会话
   */
  async createMany(dataList: Partial<T>[], session?: ClientSession): Promise<XResult<T[]>> {
    return RepositoryResultWrapper.wrapArray(async () => {
      const startTime = Date.now();

      const options = session ? { session } : {};
      const result = await this.model.create(dataList, options);

      this.logPerformance('createMany', startTime, {
        count: dataList.length, hasSession: !!session
      });
      this.logger.log(`批量创建成功: ${dataList.length} 个文档`);

      return result;
    });
  }

  /**
   * 批量更新文档
   * @param filter 查询条件
   * @param update 更新数据
   * @param session 事务会话
   */
  async updateMany(
    filter: FilterQuery<T>,
    update: UpdateQuery<T>,
    session?: ClientSession
  ): Promise<XResult<BulkOperationResult>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();

      const options = session ? { session } : {};
      const result = await this.model.updateMany(filter, update, options).exec();

      this.logPerformance('updateMany', startTime, {
        filter, modifiedCount: result.modifiedCount, hasSession: !!session
      });

      if (result.modifiedCount > 0) {
        this.logger.log(`批量更新成功: ${result.modifiedCount} 个文档`);
      }

      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
        details: result
      };
    });
  }

  /**
   * 批量删除文档
   * @param filter 查询条件
   * @param session 事务会话
   */
  async deleteMany(filter: FilterQuery<T>, session?: ClientSession): Promise<XResult<BulkOperationResult>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();

      const options = session ? { session } : {};
      const result = await this.model.deleteMany(filter, options).exec();

      this.logPerformance('deleteMany', startTime, {
        filter, deletedCount: result.deletedCount, hasSession: !!session
      });

      if (result.deletedCount > 0) {
        this.logger.log(`批量删除成功: ${result.deletedCount} 个文档`);
      }

      return {
        deletedCount: result.deletedCount,
        modifiedCount: 0,
        matchedCount: result.deletedCount,
        details: result
      };
    });
  }

  // ========== 便捷业务方法 ==========

  /**
   * 获取或创建文档（如果不存在则创建）
   * @param filter 查询条件
   * @param defaultData 默认数据（当文档不存在时使用）
   * @param session 事务会话
   */
  async findOrCreate(
    filter: FilterQuery<T>,
    defaultData: Partial<T>,
    session?: ClientSession
  ): Promise<XResult<{ document: T; created: boolean }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();

      // 先尝试查找
      const findResult = await this.findOne(filter, { session, lean: false });
      if (XResultUtils.isFailure(findResult)) {
        throw new Error(`查找文档失败: ${findResult.message}`);
      }

      if (findResult.data) {
        this.logPerformance('findOrCreate:found', startTime, { filter });
        return { document: findResult.data, created: false };
      }

      // 文档不存在，创建新文档
      const createResult = await this.createOne({ ...defaultData, ...filter }, session);
      if (XResultUtils.isFailure(createResult)) {
        throw new Error(`创建文档失败: ${createResult.message}`);
      }

      this.logPerformance('findOrCreate:created', startTime, { filter });
      this.logger.log(`文档创建成功: ${this.loggerContext}`);

      return { document: createResult.data, created: true };
    });
  }

  /**
   * 更新或创建文档（upsert操作）
   * @param filter 查询条件
   * @param update 更新数据
   * @param session 事务会话
   */
  async upsert(
    filter: FilterQuery<T>,
    update: UpdateQuery<T>,
    session?: ClientSession
  ): Promise<XResult<{ document: T; created: boolean }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const startTime = Date.now();

      const options = session
        ? { session, new: true, upsert: true }
        : { new: true, upsert: true };

      const result = await this.model.findOneAndUpdate(filter, update, options).exec();

      // 检查是否是新创建的文档
      const created = !result || result.isNew;

      this.logPerformance('upsert', startTime, { filter, created, hasSession: !!session });

      if (created) {
        this.logger.log(`文档创建成功: ${this.loggerContext}`);
      } else {
        this.logger.log(`文档更新成功: ${this.loggerContext}`);
      }

      return { document: result, created };
    });
  }

  /**
   * 增量更新（原子操作）
   * @param filter 查询条件
   * @param increments 增量字段和值的映射
   * @param session 事务会话
   */
  async increment(
    filter: FilterQuery<T>,
    increments: Record<string, number>,
    session?: ClientSession
  ): Promise<XResult<T | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();

      const update = { $inc: increments };
      const options = session ? { session, new: true } : { new: true };

      const result = await this.model.findOneAndUpdate(filter, update, options).exec();

      this.logPerformance('increment', startTime, { filter, increments, hasSession: !!session });

      if (result) {
        this.logger.log(`增量更新成功: ${this.loggerContext}`, increments);
      }

      return result;
    });
  }

  /**
   * 数组字段添加元素
   * @param filter 查询条件
   * @param arrayField 数组字段名
   * @param values 要添加的值
   * @param session 事务会话
   */
  async pushToArray(
    filter: FilterQuery<T>,
    arrayField: string,
    values: any | any[],
    session?: ClientSession
  ): Promise<XResult<any>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();

      const update: any = Array.isArray(values)
        ? { $push: { [arrayField]: { $each: values } } }
        : { $push: { [arrayField]: values } };

      const options = session ? { session, new: true } : { new: true };
      const result = await this.model.findOneAndUpdate(filter, update as UpdateQuery<T>, options).exec();

      this.logPerformance('pushToArray', startTime, {
        filter, arrayField, valueCount: Array.isArray(values) ? values.length : 1, hasSession: !!session
      });

      if (result) {
        this.logger.log(`数组添加成功: ${arrayField}`, { valueCount: Array.isArray(values) ? values.length : 1 });
      }

      return result;
    });
  }

  /**
   * 数组字段移除元素
   * @param filter 查询条件
   * @param arrayField 数组字段名
   * @param values 要移除的值
   * @param session 事务会话
   */
  async pullFromArray(
    filter: FilterQuery<T>,
    arrayField: string,
    values: any | any[],
    session?: ClientSession
  ): Promise<XResult<any>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const startTime = Date.now();

      const update: any = Array.isArray(values)
        ? { $pull: { [arrayField]: { $in: values } } }
        : { $pull: { [arrayField]: values } };

      const options = session ? { session, new: true } : { new: true };
      const result = await this.model.findOneAndUpdate(filter, update as UpdateQuery<T>, options).exec();

      this.logPerformance('pullFromArray', startTime, {
        filter, arrayField, valueCount: Array.isArray(values) ? values.length : 1, hasSession: !!session
      });

      if (result) {
        this.logger.log(`数组移除成功: ${arrayField}`, { valueCount: Array.isArray(values) ? values.length : 1 });
      }

      return result;
    });
  }

  // ========== 聚合查询方法 ==========

  /**
   * 聚合查询
   * @param pipeline 聚合管道
   * @param session 事务会话
   */
  async aggregate<R = any>(pipeline: any[], session?: ClientSession): Promise<XResult<R[]>> {
    return RepositoryResultWrapper.wrapArray(async () => {
      const startTime = Date.now();

      let aggregation = this.model.aggregate(pipeline);
      if (session) {
        aggregation = aggregation.session(session);
      }

      const result = await aggregation.exec();

      this.logPerformance('aggregate', startTime, {
        pipelineLength: pipeline.length, resultCount: result.length, hasSession: !!session
      });

      return result;
    });
  }

  /**
   * 分组统计
   * @param groupBy 分组字段
   * @param countField 计数字段名（默认为'count'）
   * @param filter 过滤条件
   * @param session 事务会话
   */
  async groupBy(
    groupBy: string | Record<string, any>,
    countField: string = 'count',
    filter: FilterQuery<T> = {},
    session?: ClientSession
  ): Promise<XResult<any[]>> {
    const pipeline: any[] = [];

    // 添加匹配阶段
    if (Object.keys(filter).length > 0) {
      pipeline.push({ $match: filter });
    }

    // 添加分组阶段
    const groupStage: any = {
      _id: typeof groupBy === 'string' ? `$${groupBy}` : groupBy,
      [countField]: { $sum: 1 }
    };
    pipeline.push({ $group: groupStage });

    // 添加排序阶段
    pipeline.push({ $sort: { [countField]: -1 } });

    return this.aggregate(pipeline, session);
  }

  // ========== 事务支持方法 ==========

  /**
   * 在事务中执行操作
   * @param operations 事务操作函数
   */
  async withTransaction<R>(
    operations: (session: ClientSession) => Promise<XResult<R>>
  ): Promise<XResult<R>> {
    return RepositoryResultWrapper.wrap(async () => {
      const session = await this.model.db.startSession();

      try {
        session.startTransaction();

        const result = await operations(session);
        if (XResultUtils.isFailure(result)) {
          await session.abortTransaction();
          throw new Error(`事务操作失败: ${result.message}`);
        }

        await session.commitTransaction();
        this.logger.log(`事务提交成功: ${this.loggerContext}`);

        return result.data;
      } catch (error) {
        await session.abortTransaction();
        this.logger.error(`事务回滚: ${this.loggerContext}`, error);
        throw error;
      } finally {
        await session.endSession();
      }
    });
  }

  // ========== 缓存集成支持 ==========

  /**
   * 生成缓存键
   * @param operation 操作名称
   * @param params 参数
   */
  protected generateCacheKey(operation: string, params: any): string {
    const paramsStr = typeof params === 'object'
      ? JSON.stringify(params)
      : String(params);
    return `${this.loggerContext}:${operation}:${Buffer.from(paramsStr).toString('base64')}`;
  }

  /**
   * 获取缓存TTL（子类可重写）
   * @param operation 操作名称
   */
  protected getCacheTTL(operation: string): number {
    // 默认缓存时间配置
    const defaultTTLs: Record<string, number> = {
      'findById': 300,      // 5分钟
      'findOne': 300,       // 5分钟
      'findMany': 180,      // 3分钟
      'count': 600,         // 10分钟
      'exists': 300,        // 5分钟
      'search': 120,        // 2分钟
      'findWithPagination': 120, // 2分钟
    };

    return defaultTTLs[operation] || 300; // 默认5分钟
  }

  // ========== 数据验证方法 ==========

  /**
   * 验证文档数据（子类可重写）
   * @param data 文档数据
   * @param operation 操作类型
   */
  protected validateData(data: Partial<T>, operation: 'create' | 'update'): XResult<void> {
    // 基础验证逻辑，子类可以重写实现具体的业务验证
    if (!data || typeof data !== 'object') {
      return XResultUtils.error('无效的文档数据', 'INVALID_DATA');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 清理敏感字段（子类可重写）
   * @param data 文档数据
   */
  protected sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // 移除常见的敏感字段
    const sensitiveFields = ['password', 'token', 'secret', '__v'];
    const sanitized = { ...data };

    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        delete sanitized[field];
      }
    });

    return sanitized;
  }

  // ========== 错误处理和日志 ==========

  /**
   * 记录操作日志
   * @param operation 操作名称
   * @param metadata 元数据
   */
  protected logOperation(operation: string, metadata?: any): void {
    this.logger.log(`${operation} - ${this.loggerContext}`, metadata);
  }

  /**
   * 记录错误日志
   * @param operation 操作名称
   * @param error 错误信息
   * @param metadata 元数据
   */
  protected logError(operation: string, error: any, metadata?: any): void {
    this.logger.error(`${operation} 失败 - ${this.loggerContext}`, {
      error: error.message || error,
      stack: error.stack,
      ...metadata
    });
  }

  /**
   * 记录性能日志
   * @param operation 操作名称
   * @param startTime 开始时间
   * @param metadata 元数据
   */
  protected logPerformance(operation: string, startTime: number, metadata?: any): void {
    const duration = Date.now() - startTime;

    if (duration > 1000) {
      this.logger.warn(`慢查询检测: ${operation} 耗时 ${duration}ms`, metadata);
    } else if (duration > 100) {
      this.logger.log(`查询完成: ${operation} 耗时 ${duration}ms`, metadata);
    }
  }

  // ========== 工具方法 ==========

  /**
   * 构建排序对象
   * @param sortBy 排序字段
   * @param sortOrder 排序方向
   */
  protected buildSort(sortBy?: string, sortOrder?: 'asc' | 'desc'): any {
    if (!sortBy) return undefined;

    const order = sortOrder === 'desc' ? -1 : 1;
    return { [sortBy]: order };
  }

  /**
   * 构建字段选择
   * @param fields 字段列表
   * @param exclude 是否为排除模式
   */
  protected buildSelect(fields?: string[], exclude: boolean = false): string | undefined {
    if (!fields || fields.length === 0) return undefined;

    if (exclude) {
      return fields.map(field => `-${field}`).join(' ');
    } else {
      return fields.join(' ');
    }
  }

  /**
   * 获取模型名称
   */
  protected getModelName(): string {
    return this.model.modelName;
  }

  /**
   * 获取集合名称
   */
  protected getCollectionName(): string {
    return this.model.collection.name;
  }
}

/**
 * Repository错误代码常量
 */
export const RepositoryErrorCodes = {
  DOCUMENT_NOT_FOUND: 'DOCUMENT_NOT_FOUND',
  INVALID_DATA: 'INVALID_DATA',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TRANSACTION_ERROR: 'TRANSACTION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
} as const;

/**
 * Repository配置接口
 */
export interface RepositoryConfig {
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 默认缓存TTL（秒） */
  defaultCacheTTL?: number;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 慢查询阈值（毫秒） */
  slowQueryThreshold?: number;
  /** 是否启用数据清理 */
  enableDataSanitization?: boolean;
}
