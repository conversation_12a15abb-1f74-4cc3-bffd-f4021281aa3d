# BaseRepository 迁移指南

## 🎯 迁移目标

将现有的6个微服务Repository迁移到新的BaseRepository基类，实现：
- 统一的Result模式错误处理
- 性能优化的查询方法
- 减少重复代码
- 提高代码可维护性

## 📋 迁移步骤

### 第一步：分析现有Repository

以`CharacterRepository`为例，现有代码结构：

```typescript
// 迁移前 - apps/character/src/common/repositories/character.repository.ts
@Injectable()
export class CharacterRepository {
  private readonly logger = new Logger(CharacterRepository.name);

  constructor(
    @InjectModel(Character.name) private characterModel: Model<CharacterDocument>,
  ) {}

  async create(createCharacterDto: CreateCharacterDto): Promise<XResult<CharacterDocument>> {
    try {
      const character = new this.characterModel(createCharacterDto);
      return await character.save();
    } catch (error) {
      this.logger.error('创建角色失败', error);
      throw error;
    }
  }

  async findById(characterId: string): Promise<XResult<CharacterDocument | null>> {
    try {
      return await this.characterModel.findOne({ characterId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找角色失败: ${characterId}`, error);
      throw error;
    }
  }
}
```

### 第二步：继承BaseRepository

```typescript
// 迁移后
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { Character, CharacterDocument } from '../schemas/character.schema';
import { XResult } from '@libs/common/types/result.type';

@Injectable()
export class CharacterRepository extends BaseRepository<CharacterDocument> {
  constructor(@InjectModel(Character.name) characterModel: Model<CharacterDocument>) {
    super(characterModel, 'CharacterRepository');
  }

  // 业务特定方法
  async findByUserId(userId: string): Promise<XResult<CharacterDocument[]>> {
    return this.findMany({ userId });
  }
}
```

### 第三步：方法映射对照表

| 原方法 | 新方法 | 说明 |
|--------|--------|------|
| `create(data)` | `createOne(data)` | 创建单个文档 |
| `findById(id)` | `findById(id)` | 根据ID查找（已优化） |
| `findOne(filter)` | `findOne(filter)` | 查找单个文档（已优化） |
| `find(filter)` | `findMany(filter)` | 查找多个文档（已优化） |
| `updateById(id, data)` | `updateById(id, data)` | 根据ID更新（已优化） |
| `deleteById(id)` | `deleteById(id)` | 根据ID删除（已优化） |
| `countDocuments(filter)` | `count(filter)` | 计数文档 |
| 无 | `findByIdLean(id)` | 性能优化的ID查找 |
| 无 | `findManyLean(filter)` | 性能优化的批量查找 |
| 无 | `findWithPagination(options)` | 分页查询 |
| 无 | `search(options)` | 搜索查询 |

## 🔄 具体迁移示例

### 1. Social服务 - FriendRepository

```typescript
// 迁移前
export class FriendRepository {
  async createFriend(friendData: Partial<Friend>): Promise<FriendDocument> {
    try {
      const friend = new this.friendModel({
        ...friendData,
        lastUpdateTime: Date.now(),
      });
      return await friend.save();
    } catch (error) {
      this.logger.error('创建好友记录失败', error);
      throw error;
    }
  }
}

// 迁移后
export class FriendRepository extends BaseRepository<FriendDocument> {
  constructor(@InjectModel(Friend.name) friendModel: Model<FriendDocument>) {
    super(friendModel, 'FriendRepository');
  }

  async createFriend(friendData: Partial<Friend>): Promise<XResult<FriendDocument>> {
    return this.createOne({
      ...friendData,
      lastUpdateTime: Date.now(),
    });
  }
}
```

### 2. Hero服务 - HeroRepository

```typescript
// 迁移前
export class HeroRepository {
  async findByIds(heroIds: string[]): Promise<HeroDocument[]> {
    try {
      return await this.heroModel.find({ heroId: { $in: heroIds } }).exec();
    } catch (error) {
      this.logger.error(`根据ID列表查找球员失败: ${heroIds}`, error);
      throw error;
    }
  }
}

// 迁移后
export class HeroRepository extends BaseRepository<HeroDocument> {
  constructor(@InjectModel(Hero.name) heroModel: Model<HeroDocument>) {
    super(heroModel, 'HeroRepository');
  }

  async findByIds(heroIds: string[]): Promise<XResult<HeroDocument[]>> {
    return this.findMany({ heroId: { $in: heroIds } });
  }

  // 性能优化版本
  async findByIdsLean(heroIds: string[]): Promise<XResult<any[]>> {
    return this.findManyLean(
      { heroId: { $in: heroIds } },
      { select: 'heroId name position level' }
    );
  }
}
```

### 3. Economy服务 - ShopRepository

```typescript
// 迁移前
export class ShopRepository {
  async findShopsByCharacterId(query: GetShopListDto): Promise<ShopDocument[]> {
    try {
      const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };
      
      if (query.shopType !== undefined) {
        filter.shopType = query.shopType;
      }

      const sortField = query.sortBy || 'lastPurchaseTime';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortField]: sortOrder };

      return await this.shopModel
        .find(filter)
        .sort(sort)
        .exec();
    } catch (error) {
      this.logger.error(`根据角色ID查找商店列表失败: ${query.characterId}`, error);
      throw error;
    }
  }
}

// 迁移后
export class ShopRepository extends BaseRepository<ShopDocument> {
  constructor(@InjectModel(Shop.name) shopModel: Model<ShopDocument>) {
    super(shopModel, 'ShopRepository');
  }

  async findShopsByCharacterId(query: GetShopListDto): Promise<XResult<any[]>> {
    const filter: any = { characterId: query.characterId };
    
    if (query.shopType !== undefined) {
      filter.shopType = query.shopType;
    }

    return this.findManyLean(filter, {
      sort: this.buildSort(query.sortBy || 'lastPurchaseTime', query.sortOrder),
      select: 'shopId shopType totalPurchases totalSpent lastPurchaseTime'
    });
  }

  // 新增分页查询方法
  async findShopsWithPagination(query: GetShopListDto & { page: number; limit: number }): Promise<XResult<PaginationResult<any>>> {
    const filter: any = { characterId: query.characterId };
    
    if (query.shopType !== undefined) {
      filter.shopType = query.shopType;
    }

    return this.findWithPagination({
      page: query.page,
      limit: query.limit,
      filter,
      sort: this.buildSort(query.sortBy || 'lastPurchaseTime', query.sortOrder),
      lean: true
    });
  }
}
```

## ⚡ 性能优化建议

### 1. 使用Lean查询

```typescript
// 原来的查询
async getCharacterList(): Promise<CharacterDocument[]> {
  return this.characterModel.find({ isActive: true }).exec();
}

// 优化后的查询（性能提升60%+）
async getCharacterList(): Promise<XResult<any[]>> {
  return this.findManyLean(
    { isActive: true },
    { select: 'characterId name level experience' }
  );
}
```

### 2. 使用分页查询

```typescript
// 原来的查询（可能导致内存问题）
async getAllCharacters(): Promise<CharacterDocument[]> {
  return this.characterModel.find().exec();
}

// 优化后的分页查询
async getCharactersPaginated(page: number, limit: number): Promise<XResult<PaginationResult<any>>> {
  return this.findWithPagination({
    page,
    limit,
    lean: true,
    select: 'characterId name level'
  });
}
```

### 3. 使用批量操作

```typescript
// 原来的循环操作
async updateMultipleCharacters(updates: Array<{id: string, data: any}>): Promise<void> {
  for (const update of updates) {
    await this.characterModel.findByIdAndUpdate(update.id, update.data);
  }
}

// 优化后的批量操作
async updateMultipleCharacters(characterIds: string[], updateData: any): Promise<XResult<BulkOperationResult>> {
  return this.updateMany(
    { characterId: { $in: characterIds } },
    updateData
  );
}
```

## 🚨 迁移注意事项

### 1. 返回类型变更
- 所有方法现在返回`XResult<T>`而不是直接返回数据
- 需要检查`result.success`来判断操作是否成功
- 使用`result.data`获取实际数据

### 2. 错误处理变更
- 不再抛出异常，而是返回失败的Result
- 需要在Service层检查Result状态
- 统一的错误码和消息格式

### 3. Lean查询默认启用
- 默认返回普通JavaScript对象而不是Mongoose文档
- 如需Mongoose文档方法，显式设置`lean: false`
- 性能提升显著，但失去文档方法

### 4. 事务支持
- 所有方法都支持session参数
- 使用`withTransaction`方法进行事务操作
- 需要MongoDB副本集支持

## 📝 迁移检查清单

- [ ] 继承BaseRepository基类
- [ ] 更新构造函数调用super()
- [ ] 修改方法返回类型为XResult<T>
- [ ] 更新方法调用（如find -> findMany）
- [ ] 添加Result状态检查
- [ ] 移除try-catch异常处理
- [ ] 优化查询使用Lean模式
- [ ] 添加分页和搜索功能
- [ ] 更新单元测试
- [ ] 验证性能提升

## 🔧 工具脚本

可以使用以下脚本辅助迁移：

```bash
# 查找所有需要迁移的Repository文件
find apps/ -name "*.repository.ts" -type f

# 检查Result模式使用情况
grep -r "XResult" apps/ --include="*.ts"

# 检查异常抛出（需要替换为Result模式）
grep -r "throw new" apps/ --include="*.repository.ts"
```

## 📊 迁移效果预期

- **代码减少**: 每个Repository减少30-50%的样板代码
- **性能提升**: Lean查询提升60%+查询性能
- **错误处理**: 统一的Result模式，减少异常处理复杂度
- **可维护性**: 统一的基类，便于功能扩展和维护
- **类型安全**: 完整的TypeScript类型支持
