/**
 * Repository模块统一导出
 * 
 * 提供通用的BaseRepository基类和相关类型定义
 * 用于6个微服务的数据访问层统一实现
 */

// 基础Repository类
export { BaseRepository } from './base-repository';

// 类型定义
export type {
  BaseQueryOptions,
  BasePaginationOptions,
  BaseSearchOptions,
  BulkOperationResult,
  RepositoryConfig
} from './base-repository';

// 错误代码常量
export { RepositoryErrorCodes } from './base-repository';

// Result类型（从types模块重新导出，方便使用）
export type {
  XResult,
  SuccessResult,
  FailureResult,
  PaginationResult,
  PaginatedResult
} from '../types/result.type';

// Result工具类（从types模块重新导出，方便使用）
export {
  XResultUtils,
  RepositoryResultWrapper,
  ServiceResultHandler,
  ExceptionToResultUtils,
  XResponseUtils
} from '../types/result.type';

/**
 * 使用示例：
 * 
 * ```typescript
 * import { BaseRepository, XResult, XResultUtils } from '@libs/common/repository';
 * 
 * @Injectable()
 * export class CharacterRepository extends BaseRepository<CharacterDocument> {
 *   constructor(@InjectModel(Character.name) model: Model<CharacterDocument>) {
 *     super(model, 'CharacterRepository');
 *   }
 * 
 *   async findByUserId(userId: string): Promise<XResult<CharacterDocument[]>> {
 *     return this.findMany({ userId });
 *   }
 * }
 * ```
 */
