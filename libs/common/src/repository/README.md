# BaseRepository 通用数据访问基类

## 🎯 设计目标

BaseRepository是为6个微服务（social、match、hero、economy、character、activity）设计的通用数据访问基类，具有以下特点：

- **API设计通用** - 覆盖所有常见数据库操作模式
- **使用方便** - 最小化样板代码，简化继承流程
- **代码简洁优雅** - 高可读性，易于理解和维护
- **Result模式** - 完全替代异常throw，提供类型安全的错误处理
- **性能优化** - 智能Lean查询，性能监控，慢查询检测

## 🚀 核心特性

### 1. 统一的Result模式
- 所有方法返回`XResult<T>`类型，避免异常抛出
- 类型安全的错误处理
- 统一的错误码和消息格式

### 2. 智能性能优化
- 默认启用Lean查询，提升60%+查询性能
- 自动性能监控和慢查询检测
- 并行查询优化（分页查询）

### 3. 完整的CRUD操作
- 基础CRUD：`findById`, `findOne`, `findMany`, `createOne`, `updateById`, `deleteById`
- 性能优化版本：`findByIdLean`, `findOneLean`, `findManyLean`
- 批量操作：`createMany`, `updateMany`, `deleteMany`

### 4. 高级查询功能
- 分页查询：`findWithPagination`
- 搜索查询：`search`
- 聚合查询：`aggregate`, `groupBy`
- 存在性检查：`exists`, `count`

### 5. 便捷业务方法
- 获取或创建：`findOrCreate`
- 更新或创建：`upsert`
- 原子增量：`increment`
- 数组操作：`pushToArray`, `pullFromArray`

### 6. 事务支持
- 完整的事务支持：`withTransaction`
- 所有方法支持session参数
- 自动事务回滚和错误处理

## 📖 使用示例

### 基础使用

```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { Character, CharacterDocument } from '../schemas/character.schema';
import { XResult } from '@libs/common/types/result.type';

@Injectable()
export class CharacterRepository extends BaseRepository<CharacterDocument> {
  constructor(@InjectModel(Character.name) characterModel: Model<CharacterDocument>) {
    super(characterModel, 'CharacterRepository');
  }

  // 业务特定方法
  async findByUserId(userId: string): Promise<XResult<CharacterDocument[]>> {
    return this.findMany({ userId });
  }

  async getActiveCharacters(): Promise<XResult<any[]>> {
    return this.findManyLean(
      { isActive: true }, 
      { select: 'name level experience', sort: { level: -1 } }
    );
  }

  async getCharacterProfile(characterId: string): Promise<XResult<any | null>> {
    return this.findByIdLean(characterId, {
      select: 'name level experience serverId lastLoginTime'
    });
  }
}
```

### 分页查询示例

```typescript
// Service层使用
async getCharacterList(query: GetCharacterListDto): Promise<XResult<PaginationResult<any>>> {
  const options: BasePaginationOptions<CharacterDocument> = {
    page: query.page || 1,
    limit: query.limit || 20,
    filter: { serverId: query.serverId },
    sort: { level: -1 },
    select: 'name level experience',
    lean: true
  };

  return this.characterRepository.findWithPagination(options);
}
```

### 搜索查询示例

```typescript
async searchCharacters(searchTerm: string): Promise<XResult<any[]>> {
  const options: BaseSearchOptions<CharacterDocument> = {
    searchFields: ['name', 'nickname'],
    searchTerm,
    filter: { isActive: true },
    sort: { level: -1 },
    limit: 50,
    lean: true
  };

  return this.characterRepository.search(options);
}
```

### 事务操作示例

```typescript
async transferResources(fromId: string, toId: string, amount: number): Promise<XResult<void>> {
  return this.characterRepository.withTransaction(async (session) => {
    // 扣除源角色资源
    const deductResult = await this.characterRepository.increment(
      { characterId: fromId },
      { gold: -amount },
      session
    );
    
    if (XResultUtils.isFailure(deductResult)) {
      return deductResult;
    }

    // 增加目标角色资源
    const addResult = await this.characterRepository.increment(
      { characterId: toId },
      { gold: amount },
      session
    );

    if (XResultUtils.isFailure(addResult)) {
      return addResult;
    }

    return XResultUtils.ok(undefined);
  });
}
```

### 批量操作示例

```typescript
async createInitialCharacters(userIds: string[]): Promise<XResult<CharacterDocument[]>> {
  const characterData = userIds.map(userId => ({
    userId,
    name: `Player_${userId.slice(-6)}`,
    level: 1,
    experience: 0,
    serverId: 'server_001'
  }));

  return this.characterRepository.createMany(characterData);
}
```

## 🔧 高级配置

### 自定义验证

```typescript
export class CharacterRepository extends BaseRepository<CharacterDocument> {
  // 重写数据验证方法
  protected validateData(data: Partial<CharacterDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create' && !data.name) {
      return XResultUtils.error('角色名称不能为空', 'NAME_REQUIRED');
    }

    if (data.level && data.level < 1) {
      return XResultUtils.error('角色等级不能小于1', 'INVALID_LEVEL');
    }

    return XResultUtils.ok(undefined);
  }

  // 重写缓存TTL配置
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findById': 600,        // 角色信息缓存10分钟
      'findByUserId': 300,    // 用户角色列表缓存5分钟
      'getActiveCharacters': 120, // 活跃角色列表缓存2分钟
    };
    
    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
```

## 📊 性能优化建议

### 1. 查询优化
- 优先使用`findManyLean`等Lean查询方法
- 合理使用`select`参数只查询需要的字段
- 为常用查询条件添加数据库索引

### 2. 分页查询
- 使用`findWithPagination`进行分页查询
- 避免使用skip进行深度分页，考虑基于游标的分页

### 3. 批量操作
- 使用`createMany`、`updateMany`等批量方法
- 避免在循环中进行单个数据库操作

### 4. 事务使用
- 只在必要时使用事务
- 保持事务操作的简短和快速
- 避免在事务中进行长时间的外部调用

## 🚨 注意事项

1. **Result模式**：所有方法都返回`XResult<T>`，需要检查`success`字段
2. **Lean查询**：默认启用lean查询，返回的是普通对象而非Mongoose文档
3. **事务支持**：需要MongoDB副本集才能使用事务功能
4. **性能监控**：自动记录慢查询，建议关注日志输出
5. **错误处理**：统一使用Result模式，避免直接抛出异常

## 🔗 相关文档

- [Result模式详细文档](../types/result.type.ts)
- [事务管理文档](../transaction/transaction-manager.ts)
- [微服务开发规范](../../../docs/development/microservice-development-standards.md)
