/**
 * CharacterRepository 使用示例
 * 展示如何使用BaseRepository重构现有的Repository类
 */

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository, BasePaginationOptions, BaseSearchOptions } from '../base-repository';
import { XResult, XResultUtils, PaginationResult } from '../../types/result.type';

// 假设的Schema定义
interface Character {
  characterId: string;
  userId: string;
  name: string;
  level: number;
  experience: number;
  serverId: string;
  isActive: boolean;
  lastLoginTime: Date;
  gold: number;
  items: string[];
}

interface CharacterDocument extends Character, Document {}

/**
 * 角色Repository示例
 * 展示BaseRepository的完整使用方式
 */
@Injectable()
export class CharacterRepositoryExample extends BaseRepository<CharacterDocument> {
  constructor(@InjectModel('Character') characterModel: Model<CharacterDocument>) {
    super(characterModel, 'CharacterRepository');
  }

  // ========== 基础业务方法 ==========

  /**
   * 根据用户ID查找角色列表
   */
  async findByUserId(userId: string, serverId?: string): Promise<XResult<CharacterDocument[]>> {
    const filter: any = { userId };
    if (serverId) {
      filter.serverId = serverId;
    }
    
    return this.findMany(filter, {
      sort: { level: -1 },
      lean: false // 需要Mongoose文档方法时使用false
    });
  }

  /**
   * 获取角色基础信息（性能优化版本）
   */
  async getCharacterProfile(characterId: string): Promise<XResult<any | null>> {
    return this.findByIdLean(characterId, {
      select: 'characterId name level experience serverId lastLoginTime'
    });
  }

  /**
   * 获取活跃角色列表
   */
  async getActiveCharacters(serverId: string, limit: number = 100): Promise<XResult<any[]>> {
    return this.findManyLean(
      { serverId, isActive: true },
      {
        select: 'characterId name level',
        sort: { level: -1 },
        limit
      }
    );
  }

  // ========== 分页查询示例 ==========

  /**
   * 分页获取角色列表
   */
  async getCharacterList(
    serverId: string,
    page: number = 1,
    limit: number = 20,
    sortBy: string = 'level',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<XResult<PaginationResult<any>>> {
    const options: BasePaginationOptions<CharacterDocument> = {
      page,
      limit,
      filter: { serverId, isActive: true },
      sort: this.buildSort(sortBy, sortOrder),
      select: 'characterId name level experience lastLoginTime',
      lean: true
    };

    return this.findWithPagination(options);
  }

  // ========== 搜索功能示例 ==========

  /**
   * 搜索角色
   */
  async searchCharacters(
    searchTerm: string,
    serverId?: string,
    limit: number = 50
  ): Promise<XResult<any[]>> {
    const options: BaseSearchOptions<CharacterDocument> = {
      searchFields: ['name', 'characterId'],
      searchTerm,
      filter: serverId ? { serverId, isActive: true } : { isActive: true },
      sort: { level: -1 },
      limit,
      lean: true,
      caseSensitive: false
    };

    return this.search(options);
  }

  // ========== 业务逻辑方法 ==========

  /**
   * 创建新角色
   */
  async createCharacter(characterData: Partial<Character>): Promise<XResult<CharacterDocument>> {
    // 使用内置验证
    const validationResult = this.validateData(characterData, 'create');
    if (XResultUtils.isFailure(validationResult)) {
      return validationResult as any;
    }

    // 检查角色名是否已存在
    const existsResult = await this.exists({ 
      name: characterData.name, 
      serverId: characterData.serverId 
    });
    
    if (XResultUtils.isFailure(existsResult)) {
      return existsResult as any;
    }
    
    if (existsResult.data) {
      return XResultUtils.error('角色名已存在', 'CHARACTER_NAME_EXISTS');
    }

    // 创建角色
    return this.createOne({
      ...characterData,
      level: 1,
      experience: 0,
      gold: 1000,
      items: [],
      isActive: true,
      lastLoginTime: new Date()
    });
  }

  /**
   * 升级角色
   */
  async levelUpCharacter(characterId: string, expGain: number): Promise<XResult<CharacterDocument | null>> {
    return this.increment(
      { characterId },
      { experience: expGain }
    );
  }

  /**
   * 添加物品到角色背包
   */
  async addItemToCharacter(characterId: string, itemId: string): Promise<XResult<CharacterDocument | null>> {
    return this.pushToArray(
      { characterId },
      'items',
      itemId
    );
  }

  /**
   * 从角色背包移除物品
   */
  async removeItemFromCharacter(characterId: string, itemId: string): Promise<XResult<CharacterDocument | null>> {
    return this.pullFromArray(
      { characterId },
      'items',
      itemId
    );
  }

  // ========== 事务操作示例 ==========

  /**
   * 角色间转账（事务操作）
   */
  async transferGold(
    fromCharacterId: string,
    toCharacterId: string,
    amount: number
  ): Promise<XResult<void>> {
    if (amount <= 0) {
      return XResultUtils.error('转账金额必须大于0', 'INVALID_AMOUNT');
    }

    return this.withTransaction(async (session) => {
      // 检查源角色金币是否足够
      const fromCharacterResult = await this.findById(fromCharacterId, { session, lean: false });
      if (XResultUtils.isFailure(fromCharacterResult)) {
        return fromCharacterResult;
      }

      const fromCharacter = fromCharacterResult.data;
      if (!fromCharacter || fromCharacter.gold < amount) {
        return XResultUtils.error('金币不足', 'INSUFFICIENT_GOLD');
      }

      // 扣除源角色金币
      const deductResult = await this.increment(
        { characterId: fromCharacterId },
        { gold: -amount },
        session
      );

      if (XResultUtils.isFailure(deductResult)) {
        return deductResult as any;
      }

      // 增加目标角色金币
      const addResult = await this.increment(
        { characterId: toCharacterId },
        { gold: amount },
        session
      );

      if (XResultUtils.isFailure(addResult)) {
        return addResult as any;
      }

      this.logger.log(`转账成功: ${fromCharacterId} -> ${toCharacterId}, 金额: ${amount}`);
      return XResultUtils.ok(undefined);
    });
  }

  // ========== 批量操作示例 ==========

  /**
   * 批量创建初始角色
   */
  async createInitialCharacters(userIds: string[], serverId: string): Promise<XResult<CharacterDocument[]>> {
    const characterData = userIds.map((userId, index) => ({
      characterId: `char_${userId}_${Date.now()}_${index}`,
      userId,
      name: `Player_${userId.slice(-6)}`,
      level: 1,
      experience: 0,
      serverId,
      isActive: true,
      lastLoginTime: new Date(),
      gold: 1000,
      items: []
    }));

    return this.createMany(characterData);
  }

  /**
   * 批量更新角色登录时间
   */
  async updateLastLoginTime(characterIds: string[]): Promise<XResult<any>> {
    return this.updateMany(
      { characterId: { $in: characterIds } },
      { lastLoginTime: new Date() }
    );
  }

  // ========== 聚合查询示例 ==========

  /**
   * 获取服务器角色等级分布
   */
  async getCharacterLevelDistribution(serverId: string): Promise<XResult<any[]>> {
    const pipeline = [
      { $match: { serverId, isActive: true } },
      {
        $group: {
          _id: {
            $switch: {
              branches: [
                { case: { $lt: ['$level', 10] }, then: '1-9' },
                { case: { $lt: ['$level', 20] }, then: '10-19' },
                { case: { $lt: ['$level', 30] }, then: '20-29' },
                { case: { $lt: ['$level', 50] }, then: '30-49' },
              ],
              default: '50+'
            }
          },
          count: { $sum: 1 },
          avgLevel: { $avg: '$level' }
        }
      },
      { $sort: { '_id': 1 } }
    ];

    return this.aggregate(pipeline);
  }

  /**
   * 获取服务器统计信息
   */
  async getServerStats(serverId: string): Promise<XResult<any>> {
    const pipeline = [
      { $match: { serverId } },
      {
        $group: {
          _id: null,
          totalCharacters: { $sum: 1 },
          activeCharacters: {
            $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
          },
          avgLevel: { $avg: '$level' },
          maxLevel: { $max: '$level' },
          totalGold: { $sum: '$gold' }
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }
    
    return XResultUtils.ok({
      totalCharacters: 0,
      activeCharacters: 0,
      avgLevel: 0,
      maxLevel: 0,
      totalGold: 0
    });
  }

  // ========== 重写基类方法示例 ==========

  /**
   * 重写数据验证方法
   */
  protected validateData(data: Partial<Character>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.name || data.name.trim().length === 0) {
        return XResultUtils.error('角色名称不能为空', 'NAME_REQUIRED');
      }
      
      if (data.name.length > 20) {
        return XResultUtils.error('角色名称不能超过20个字符', 'NAME_TOO_LONG');
      }
      
      if (!data.userId) {
        return XResultUtils.error('用户ID不能为空', 'USER_ID_REQUIRED');
      }
      
      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    if (data.level !== undefined && data.level < 1) {
      return XResultUtils.error('角色等级不能小于1', 'INVALID_LEVEL');
    }

    if (data.gold !== undefined && data.gold < 0) {
      return XResultUtils.error('金币数量不能为负数', 'INVALID_GOLD');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findById': 600,           // 角色详情缓存10分钟
      'getCharacterProfile': 300, // 角色简介缓存5分钟
      'findByUserId': 180,       // 用户角色列表缓存3分钟
      'getActiveCharacters': 120, // 活跃角色列表缓存2分钟
    };
    
    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
