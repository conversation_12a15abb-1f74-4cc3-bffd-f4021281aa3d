/**
 * Controller模块统一导出
 * 
 * 提供通用的BaseController基类和相关类型定义
 * 用于6个微服务的控制器层统一实现
 */

// 基础Controller类
export { BaseController } from './base-controller';

// 类型定义
export type {
  BasePayload,
  PaginationPayload,
  ControllerOptions
} from './base-controller';

// Result类型（从types模块重新导出，方便使用）
export type {
  XResult,
  XResponse,
  SuccessResult,
  FailureResult
} from '../types/result.type';

// Result工具类（从types模块重新导出，方便使用）
export {
  XResultUtils,
  XResponseUtils
} from '../types/result.type';

/**
 * 使用示例：
 *
 * ```typescript
 * import { BaseController, BasePayload, XResult, XResponse } from '@libs/common/controller';
 *
 * @Controller()
 * export class CharacterController extends BaseController {
 *   constructor(private readonly characterService: CharacterService) {
 *     super('CharacterController');
 *   }
 *
 *   @MessagePattern('character.create')
 *   async createCharacter(@Payload() payload: BasePayload & { createDto: CreateCharacterDto }): Promise<XResponse<Character>> {
 *     return this.handleRequest(async () => {
 *       const result = await this.characterService.createCharacter(payload.createDto);
 *       return this.fromResult(result, '角色创建成功');
 *     }, payload);
 *   }
 * }
 * ```
 */
