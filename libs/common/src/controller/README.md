# BaseController 通用控制器基类

## 🎯 设计目标

BaseController是为6个微服务（social、match、hero、economy、character、activity）设计的通用控制器基类，具有以下特点：

- **统一的微服务接口处理** - 标准化的MessagePattern处理
- **标准化的响应格式** - 统一的微服务响应结构
- **完整的Result模式集成** - 自动Result到Response转换
- **通用的错误处理和日志记录** - 完整的请求追踪
- **参数验证框架** - 统一的参数提取和验证
- **性能监控支持** - 慢请求检测和性能分析

## 🚀 核心特性

### 1. 统一的请求处理框架
- `handleRequest` - 带日志和异常处理的请求处理
- `handlePaginationRequest` - 分页请求专用处理
- 自动请求ID生成和追踪

### 2. 标准化响应格式
- `fromResult` - Result自动转换为微服务响应
- `toSuccessResponse` - 成功响应格式化
- `toErrorResponse` - 错误响应格式化
- `toPaginationResponse` - 分页响应格式化

### 3. 参数提取和验证
- `extractBaseParams` - 基础参数提取
- `extractPaginationOptions` - 分页参数提取
- `validateRequiredFields` - 必需字段验证
- `validateCharacterId` / `validateServerId` - 常用参数验证

### 4. 完整的日志和监控
- 请求级别的性能监控
- 慢请求自动检测（>3秒）
- 敏感数据自动脱敏
- 完整的请求链追踪

## 📖 使用示例

### 基础使用

```typescript
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller/base-controller';
import { Cacheable, CacheEvict } from '@libs/redis';

interface CreateCharacterPayload extends BasePayload {
  createDto: CreateCharacterDto;
}

@Controller()
export class CharacterController extends BaseController {
  constructor(private readonly characterService: CharacterService) {
    super('CharacterController');
  }

  @MessagePattern('character.create')
  async createCharacter(@Payload() payload: CreateCharacterPayload) {
    return this.handleRequest(async () => {
      // 1. 参数验证
      const validation = this.validateRequiredFields(payload, ['createDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation, '参数验证失败');
      }

      // 2. 调用业务逻辑
      const result = await this.characterService.createCharacter(payload.createDto);
      
      // 3. 返回标准响应
      return this.fromResult(result, '角色创建成功');
    }, payload);
  }

  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterInfo(@Payload() payload: BasePayload) {
    return this.handleRequest(async () => {
      // 1. 参数验证
      const characterIdValidation = this.validateCharacterId(payload.characterId);
      if (XResultUtils.isFailure(characterIdValidation)) {
        return this.fromResult(characterIdValidation, '角色ID无效');
      }

      // 2. 调用业务逻辑
      const result = await this.characterService.getCharacterInfo(payload.characterId!);
      
      return this.fromResult(result, '获取成功');
    }, payload);
  }
}
```

### 分页查询示例

```typescript
@Controller()
export class HeroController extends BaseController {
  constructor(private readonly heroService: HeroService) {
    super('HeroController');
  }

  @MessagePattern('hero.getList')
  async getHeroList(@Payload() payload: PaginationPayload & { query: GetHeroListDto }) {
    return this.handlePaginationRequest(async (paginationOptions) => {
      // 合并查询条件和分页参数
      const queryOptions = {
        ...payload.query,
        ...paginationOptions
      };

      return this.heroService.getHeroList(queryOptions);
    }, payload);
  }
}
```

### 复杂业务逻辑示例

```typescript
@Controller()
export class ShopController extends BaseController {
  constructor(private readonly shopService: ShopService) {
    super('ShopController');
  }

  @MessagePattern('shop.purchase')
  @CacheEvict({
    key: 'character:inventory:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async purchaseItem(@Payload() payload: BasePayload & { purchaseDto: PurchaseItemDto }) {
    return this.handleRequest(async () => {
      // 1. 多字段验证
      const validation = await this.validatePurchaseRequest(payload);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation, '购买请求验证失败');
      }

      // 2. 执行购买逻辑
      const result = await this.shopService.purchaseItem(
        payload.characterId!,
        payload.purchaseDto.itemId,
        payload.purchaseDto.quantity
      );

      return this.fromResult(result, '购买成功');
    }, payload);
  }

  private async validatePurchaseRequest(payload: any): Promise<XResult<void>> {
    // 验证必需字段
    const requiredValidation = this.validateRequiredFields(payload, [
      'characterId', 'serverId', 'purchaseDto'
    ]);
    if (XResultUtils.isFailure(requiredValidation)) {
      return requiredValidation;
    }

    // 验证购买数量
    if (payload.purchaseDto.quantity <= 0) {
      return XResultUtils.error('购买数量必须大于0', 'INVALID_QUANTITY');
    }

    return XResultUtils.ok(undefined);
  }
}
```

### 批量操作示例

```typescript
@Controller()
export class MatchController extends BaseController {
  constructor(private readonly matchService: MatchService) {
    super('MatchController');
  }

  @MessagePattern('match.batchResult')
  async getBatchMatchResults(@Payload() payload: BasePayload & { matchIds: string[] }) {
    return this.handleRequest(async () => {
      // 1. 验证参数
      if (!payload.matchIds || payload.matchIds.length === 0) {
        return this.toErrorResponse('比赛ID列表不能为空', 1001);
      }

      if (payload.matchIds.length > 50) {
        return this.toErrorResponse('单次查询比赛数量不能超过50个', 1002);
      }

      // 2. 批量查询
      const result = await this.matchService.getBatchMatchResults(payload.matchIds);
      
      return this.fromResult(result, '批量查询成功');
    }, payload);
  }
}
```

## 🔧 高级功能

### 1. 自定义控制器选项

```typescript
export class CustomController extends BaseController {
  constructor(private readonly customService: CustomService) {
    super('CustomController', {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      defaultTimeout: 10000,
      autoHandleExceptions: true
    });
  }
}
```

### 2. 自定义错误码映射

```typescript
protected getErrorCode(resultCode: string): number {
  const customErrorCodes = {
    'CUSTOM_ERROR_1': 2001,
    'CUSTOM_ERROR_2': 2002,
    ...super.getErrorCode(resultCode)
  };
  
  return customErrorCodes[resultCode] || -1;
}
```

### 3. 响应数据处理

```typescript
@MessagePattern('hero.getDetailedInfo')
async getDetailedHeroInfo(@Payload() payload: BasePayload & { heroId: string }) {
  return this.handleRequest(async () => {
    const result = await this.heroService.getDetailedInfo(payload.heroId);
    
    if (XResultUtils.isSuccess(result)) {
      // 自定义响应数据处理
      const processedData = {
        ...result.data,
        calculatedStats: this.calculateHeroStats(result.data),
        recommendations: this.getHeroRecommendations(result.data)
      };
      
      return this.toSuccessResponse(processedData, '获取详细信息成功');
    }
    
    return this.fromResult(result, '获取失败');
  }, payload);
}
```

## 📊 响应格式标准

### 成功响应格式
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    // 实际数据
  },
  "timestamp": 1640995200000,
  "requestId": "CharacterController_1640995200000_abc123"
}
```

### 错误响应格式
```json
{
  "code": -1,
  "message": "操作失败的具体原因",
  "data": null,
  "timestamp": 1640995200000,
  "requestId": "CharacterController_1640995200000_abc123"
}
```

### 分页响应格式
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "data": [/* 实际数据数组 */],
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5,
    "hasNext": true,
    "hasPrev": false
  },
  "timestamp": 1640995200000,
  "requestId": "HeroController_1640995200000_def456"
}
```

## 🔍 性能监控

### 自动性能监控
- 所有请求自动记录执行时间
- 慢请求自动告警（>3秒）
- 完整的请求链追踪

### 日志示例
```
[CharacterController] 请求开始: CharacterController_1640995200000_abc123 - payload: {"characterId":"char123"}
[CharacterController] 请求成功: CharacterController_1640995200000_abc123 - 耗时 150ms
[CharacterController] 慢请求检测: CharacterController_1640995200000_def456 耗时 3500ms
```

## 🚨 注意事项

1. **MessagePattern命名**：遵循`服务名.操作名`格式
2. **参数验证**：使用内置验证方法，确保参数安全
3. **响应格式**：统一使用`fromResult`或`toSuccessResponse`
4. **错误处理**：避免直接抛出异常，使用Result模式
5. **缓存装饰器**：合理使用`@Cacheable`、`@CacheEvict`等装饰器
6. **性能监控**：关注慢请求日志，及时优化性能

## 🔗 相关文档

- [BaseService文档](../service/README.md)
- [Result模式详细文档](../types/result.type.ts)
- [缓存装饰器文档](../../redis/README.md)
