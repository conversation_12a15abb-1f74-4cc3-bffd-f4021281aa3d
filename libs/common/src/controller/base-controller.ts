import { Controller, Logger } from '@nestjs/common';
import { Payload } from '@nestjs/microservices';
import { XResult, XResultUtils, XResponseUtils, XResponse, PaginationResult } from '../types/result.type';
import { InjectedContext } from '../types';

/**
 * 标准Payload接口
 */
export interface BasePayload {
  /** 角色ID */
  characterId?: string;
  /** 服务器ID */
  serverId?: string;
  /** 注入的上下文 */
  injectedContext?: InjectedContext;
}

/**
 * 分页Payload接口
 */
export interface PaginationPayload extends BasePayload {
  /** 页码 */
  page?: number;
  /** 每页记录数 */
  limit?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 控制器选项
 */
export interface ControllerOptions {
  /** 是否启用请求日志 */
  enableRequestLogging?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 默认超时时间（毫秒） */
  defaultTimeout?: number;
  /** 是否自动处理异常 */
  autoHandleExceptions?: boolean;
}

/**
 * 通用BaseController基类
 * 
 * 🎯 设计目标：
 * - 统一的微服务接口处理
 * - 标准化的响应格式
 * - 完整的Result模式集成
 * - 通用的错误处理和日志记录
 * - 缓存装饰器支持
 * - 参数验证框架
 * 
 * 🚀 核心特性：
 * - 统一的MessagePattern处理
 * - 标准化的响应格式转换
 * - 完整的请求日志记录
 * - 性能监控支持
 * - 异常处理框架
 * - 参数提取和验证
 * 
 * 📖 使用示例：
 * ```typescript
 * @Controller()
 * export class CharacterController extends BaseController {
 *   constructor(private readonly characterService: CharacterService) {
 *     super('CharacterController');
 *   }
 * 
 *   @MessagePattern('character.create')
 *   async createCharacter(@Payload() payload: CreateCharacterPayload) {
 *     return this.handleRequest(async () => {
 *       const result = await this.characterService.createCharacter(payload.createDto);
 *       return this.toSuccessResponse(result, '角色创建成功');
 *     }, payload);
 *   }
 * }
 * ```
 */
@Controller()
export abstract class BaseController {
  protected readonly logger: Logger;
  protected readonly options: ControllerOptions;

  constructor(
    protected readonly controllerName: string,
    options: ControllerOptions = {}
  ) {
    this.logger = new Logger(controllerName);
    this.options = {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      defaultTimeout: 30000,
      autoHandleExceptions: true,
      ...options
    };
  }

  // ========== 核心请求处理方法 ==========

  /**
   * 处理微服务请求（带日志和异常处理）
   * @param handler 请求处理函数
   * @param payload 请求数据
   * @param requestId 请求ID
   */
  protected async handleRequest<T>(
    handler: () => Promise<XResponse<T>>,
    payload?: any,
    requestId?: string
  ): Promise<XResponse<T>> {
    const startTime = Date.now();
    const reqId = requestId || this.generateRequestId();

    try {
      if (this.options.enableRequestLogging) {
        this.logRequestStart(reqId, payload);
      }

      const response = await handler();

      if (this.options.enablePerformanceMonitoring) {
        this.logRequestEnd(reqId, startTime, true);
      }

      return {
        ...response,
        timestamp: Date.now(),
        requestId: reqId
      };

    } catch (error) {
      this.logRequestError(reqId, startTime, error);

      if (this.options.autoHandleExceptions) {
        return this.toErrorResponse(
          `请求处理失败: ${error.message}`,
          -1,
          reqId
        );
      }
      throw error;
    }
  }

  /**
   * 处理分页请求
   * @param handler 分页处理函数
   * @param payload 分页请求数据
   */
  protected async handlePaginationRequest<T>(
    handler: (paginationOptions: any) => Promise<XResult<any>>,
    payload: PaginationPayload
  ): Promise<XResponse<T>> {
    return this.handleRequest(async () => {
      const paginationOptions = this.extractPaginationOptions(payload);
      const result = await handler(paginationOptions);
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  // ========== 响应格式转换方法 ==========

  /**
   * 从Result转换为微服务响应
   * @param result Result对象
   * @param requestId 请求ID
   */
  protected fromResult<T>(
    result: XResult<T>,
    requestId?: string
  ): XResponse<T> {
    return XResponseUtils.fromResult(result, requestId);
  }

  /**
   * 创建成功响应
   * @param data 响应数据
   * @param message 响应消息
   * @param requestId 请求ID
   */
  protected toSuccessResponse<T>(
    data: T,
    message: string = '操作成功',
    requestId?: string
  ): XResponse<T> {
    return XResponseUtils.success(data, message, requestId);
  }

  /**
   * 创建错误响应
   * @param message 错误消息
   * @param code 错误码
   * @param requestId 请求ID
   */
  protected toErrorResponse<T = null>(
    message: string,
    code: number = -1,
    requestId?: string
  ): XResponse<T> {
    return XResponseUtils.error(code, message, requestId);
  }

  /**
   * 创建分页响应
   * @param data 分页数据
   * @param message 响应消息
   * @param requestId 请求ID
   */
  protected toPaginationResponse<T>(
    data: PaginationResult<T>,
    message: string = '获取成功',
    requestId?: string
  ): XResponse<PaginationResult<T>> {
    return XResponseUtils.success(data, message, requestId);
  }

  // ========== 参数提取和验证方法 ==========

  /**
   * 提取基础参数
   * @param payload 请求数据
   */
  protected extractBaseParams(payload: BasePayload): {
    characterId?: string;
    serverId?: string;
    injectedContext?: InjectedContext;
  } {
    return {
      characterId: payload.characterId,
      serverId: payload.serverId,
      injectedContext: payload.injectedContext
    };
  }

  /**
   * 提取分页参数
   * @param payload 分页请求数据
   */
  protected extractPaginationOptions(payload: PaginationPayload): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    return {
      page: payload.page || 1,
      limit: Math.min(payload.limit || 20, 100), // 限制最大每页数量
      sortBy: payload.sortBy,
      sortOrder: payload.sortOrder || 'desc'
    };
  }

  /**
   * 验证必需参数
   * @param payload 请求数据
   * @param requiredFields 必需字段列表
   */
  protected validateRequiredFields(
    payload: any,
    requiredFields: string[]
  ): XResult<void> {
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      if (!payload || payload[field] === undefined || payload[field] === null) {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return XResultUtils.error(
        `缺少必需参数: ${missingFields.join(', ')}`,
        'MISSING_REQUIRED_FIELDS'
      );
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证角色ID
   * @param characterId 角色ID
   */
  protected validateCharacterId(characterId?: string): XResult<string> {
    if (!characterId || characterId.trim().length === 0) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }
    return XResultUtils.ok(characterId);
  }

  /**
   * 验证服务器ID
   * @param serverId 服务器ID
   */
  protected validateServerId(serverId?: string): XResult<string> {
    if (!serverId || serverId.trim().length === 0) {
      return XResultUtils.error('服务器ID不能为空', 'INVALID_SERVER_ID');
    }
    return XResultUtils.ok(serverId);
  }

  // ========== 工具方法 ==========

  /**
   * 生成请求ID
   */
  protected generateRequestId(): string {
    return `${this.controllerName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取错误码
   * @param resultCode Result错误码
   */
  protected getErrorCode(resultCode: string): number {
    // 错误码映射表
    const errorCodeMap: Record<string, number> = {
      'SUCCESS': 0,
      'ERROR': -1,
      'INVALID_PARAMETER': 1001,
      'MISSING_REQUIRED_FIELDS': 1002,
      'INVALID_CHARACTER_ID': 1003,
      'INVALID_SERVER_ID': 1004,
      'CHARACTER_NOT_FOUND': 2001,
      'INSUFFICIENT_RESOURCE': 3001,
      'BUSINESS_OPERATION_ERROR': 4001,
      'MICROSERVICE_ERROR': 5001,
      'DATABASE_ERROR': 6001,
    };

    return errorCodeMap[resultCode] || -1;
  }

  /**
   * 清理敏感数据（用于日志记录）
   * @param payload 请求数据
   */
  protected sanitizeForLogging(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'injectedContext'];
    const sanitized = { ...payload };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  // ========== 日志记录方法 ==========

  /**
   * 记录请求开始
   */
  protected logRequestStart(requestId: string, payload?: any): void {
    this.logger.log(`请求开始: ${requestId}`, {
      payload: this.sanitizeForLogging(payload)
    });
  }

  /**
   * 记录请求结束
   */
  protected logRequestEnd(requestId: string, startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    const level = success ? 'log' : 'warn';
    this.logger[level](`请求${success ? '成功' : '失败'}: ${requestId} - 耗时 ${duration}ms`);

    if (duration > 3000) {
      this.logger.warn(`慢请求检测: ${requestId} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录请求错误
   */
  protected logRequestError(requestId: string, startTime: number, error: any): void {
    const duration = Date.now() - startTime;
    this.logger.error(`请求异常: ${requestId} - 耗时 ${duration}ms`, {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 获取控制器名称
   */
  protected getControllerName(): string {
    return this.controllerName;
  }
}
