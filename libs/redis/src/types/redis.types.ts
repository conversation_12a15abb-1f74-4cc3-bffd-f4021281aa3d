/**
 * Redis前缀架构v3.0类型定义
 */

/**
 * 数据类型枚举
 */
export type DataType = 'global' | 'cross' | 'server';

/**
 * 数据类型常量
 */
export const DATA_TYPES = {
  GLOBAL: 'global' as const,
  CROSS: 'cross' as const,
  SERVER: 'server' as const,
} as const;

/**
 * Redis键构建选项
 */
export interface RedisKeyOptions {
  dataType?: DataType;
  serverId?: string;
  serviceContext?: string;
}

/**
 * Redis前缀配置
 */
export interface RedisPrefixConfig {
  environment: string;
  project: string;
  basePrefix: string;
}
