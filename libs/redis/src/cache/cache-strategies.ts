import { Injectable, Logger } from '@nestjs/common';
import {
  CacheStrategy,
  CacheRepository,
  CacheResult,
  CacheOptions,
  CacheSyncStrategy,
} from './cache.interfaces';

/**
 * Cache-Aside 策略实现
 */
@Injectable()
export class CacheAsideStrategy implements CacheStrategy {
  readonly name = 'cache-aside';
  private readonly logger = new Logger(CacheAsideStrategy.name);

  async get<T>(key: string, repository: CacheRepository<T>, options?: CacheOptions): Promise<CacheResult<T>> {
    return await repository.get(key, options);
  }

  async set<T>(
    key: string,
    data: T,
    repository: CacheRepository<T>,
    options?: CacheOptions,
  ): Promise<void> {
    await repository.set(key, data, options);
  }

  async delete(key: string, repository: CacheRepository<any>, options?: CacheOptions): Promise<boolean> {
    return await repository.delete(key, options);
  }
}

/**
 * Write-Through 策略实现
 */
@Injectable()
export class WriteThroughStrategy implements CacheStrategy {
  readonly name = 'write-through';
  private readonly logger = new Logger(WriteThroughStrategy.name);

  async get<T>(key: string, repository: CacheRepository<T>, options?: CacheOptions): Promise<CacheResult<T>> {
    return await repository.get(key, options);
  }

  async set<T>(
    key: string,
    data: T,
    repository: CacheRepository<T>,
    options?: CacheOptions,
  ): Promise<void> {
    // Write-Through: 同步写入缓存和数据源
    await repository.setThrough(key, data, options);
  }

  async delete(key: string, repository: CacheRepository<any>, options?: CacheOptions): Promise<boolean> {
    return await repository.delete(key, options);
  }
}

/**
 * Write-Behind (Write-Back) 策略实现
 */
@Injectable()
export class WriteBehindStrategy implements CacheStrategy {
  readonly name = 'write-behind';
  private readonly logger = new Logger(WriteBehindStrategy.name);

  async get<T>(key: string, repository: CacheRepository<T>, options?: CacheOptions): Promise<CacheResult<T>> {
    return await repository.get(key, options);
  }

  async set<T>(
    key: string,
    data: T,
    repository: CacheRepository<T>,
    options?: CacheOptions,
  ): Promise<void> {
    // Write-Behind: 异步写入数据源
    await repository.setBehind(key, data, options);
  }

  async delete(key: string, repository: CacheRepository<any>, options?: CacheOptions): Promise<boolean> {
    return await repository.delete(key, options);
  }
}

/**
 * Refresh-Ahead 策略实现
 */
@Injectable()
export class RefreshAheadStrategy implements CacheStrategy {
  readonly name = 'refresh-ahead';
  private readonly logger = new Logger(RefreshAheadStrategy.name);
  private readonly refreshThreshold = 0.8; // 当 TTL 剩余 80% 时开始刷新

  async get<T>(key: string, repository: CacheRepository<T>): Promise<CacheResult<T>> {
    const result = await repository.get(key);
    
    if (result.hit && result.ttl) {
      // 检查是否需要预刷新
      await this.checkAndRefresh(key, repository, result.ttl);
    }
    
    return result;
  }

  async set<T>(
    key: string,
    data: T,
    repository: CacheRepository<T>,
    options?: CacheOptions,
  ): Promise<void> {
    await repository.set(key, data, options);
  }

  async delete(key: string, repository: CacheRepository<any>): Promise<boolean> {
    return await repository.delete(key);
  }

  private async checkAndRefresh<T>(
    key: string,
    repository: CacheRepository<T>,
    currentTTL: number,
  ): Promise<void> {
    // 这里需要获取原始 TTL，简化实现
    const originalTTL = 3600; // 假设原始 TTL 为 1 小时
    const remainingRatio = currentTTL / originalTTL;

    if (remainingRatio <= this.refreshThreshold) {
      // 异步刷新缓存
      this.refreshAsync(key, repository).catch(error => {
        this.logger.error(`Async refresh failed for key ${key}: ${error.message}`);
      });
    }
  }

  private async refreshAsync<T>(key: string, repository: CacheRepository<T>): Promise<void> {
    try {
      // 这里需要重新加载数据，简化实现
      this.logger.debug(`Refreshing cache for key: ${key}`);
      // await repository.getOrLoad(key, loader);
    } catch (error) {
      this.logger.error(`Cache refresh failed for key ${key}: ${error.message}`);
    }
  }
}

/**
 * 多级缓存策略
 */
@Injectable()
export class MultiLevelStrategy implements CacheStrategy {
  readonly name = 'multi-level';
  private readonly logger = new Logger(MultiLevelStrategy.name);

  constructor(
    private readonly l1Repository: CacheRepository<any>, // L1: 内存缓存
    private readonly l2Repository: CacheRepository<any>, // L2: Redis 缓存
  ) {}

  async get<T>(key: string, repository: CacheRepository<T>): Promise<CacheResult<T>> {
    // 先检查 L1 缓存
    const l1Result = await this.l1Repository.get(key);
    if (l1Result.hit && l1Result.data !== null) {
      return l1Result;
    }

    // 检查 L2 缓存
    const l2Result = await this.l2Repository.get(key);
    if (l2Result.hit && l2Result.data !== null) {
      // 回写到 L1 缓存
      await this.l1Repository.set(key, l2Result.data, { ttl: 300 }); // L1 缓存 5 分钟
      return l2Result;
    }

    return l2Result;
  }

  async set<T>(
    key: string,
    data: T,
    repository: CacheRepository<T>,
    options?: CacheOptions,
  ): Promise<void> {
    // 同时写入 L1 和 L2 缓存
    await Promise.all([
      this.l1Repository.set(key, data, { ...options, ttl: 300 }), // L1 缓存时间较短
      this.l2Repository.set(key, data, options), // L2 缓存时间较长
    ]);
  }

  async delete(key: string, repository: CacheRepository<any>): Promise<boolean> {
    // 同时删除 L1 和 L2 缓存
    const [l1Deleted, l2Deleted] = await Promise.all([
      this.l1Repository.delete(key),
      this.l2Repository.delete(key),
    ]);

    return l1Deleted || l2Deleted;
  }
}

/**
 * 缓存同步策略：主从同步
 */
@Injectable()
export class MasterSlaveSync implements CacheSyncStrategy {
  readonly name = 'master-slave';
  private readonly logger = new Logger(MasterSlaveSync.name);

  async sync<T>(key: string, data: T, repositories: CacheRepository<T>[]): Promise<void> {
    if (repositories.length === 0) return;

    const [master, ...slaves] = repositories;

    try {
      // 先写入主缓存
      await master.set(key, data);

      // 异步同步到从缓存
      const syncPromises = slaves.map(slave =>
        slave.set(key, data).catch(error => {
          this.logger.error(`Slave sync failed for key ${key}: ${error.message}`);
        })
      );

      await Promise.allSettled(syncPromises);
    } catch (error) {
      this.logger.error(`Master sync failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async invalidate(key: string, repositories: CacheRepository<any>[]): Promise<void> {
    const deletePromises = repositories.map(repo =>
      repo.delete(key).catch(error => {
        this.logger.error(`Invalidation failed for key ${key}: ${error.message}`);
      })
    );

    await Promise.allSettled(deletePromises);
  }
}

/**
 * 缓存同步策略：最终一致性
 */
@Injectable()
export class EventualConsistencySync implements CacheSyncStrategy {
  readonly name = 'eventual-consistency';
  private readonly logger = new Logger(EventualConsistencySync.name);

  async sync<T>(key: string, data: T, repositories: CacheRepository<T>[]): Promise<void> {
    // 并行写入所有缓存，允许部分失败
    const syncPromises = repositories.map(async (repo, index) => {
      try {
        await repo.set(key, data);
        return { index, success: true };
      } catch (error) {
        this.logger.warn(`Repository ${index} sync failed for key ${key}: ${error.message}`);
        return { index, success: false, error };
      }
    });

    const results = await Promise.allSettled(syncPromises);
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

    this.logger.debug(`Synced key ${key} to ${successCount}/${repositories.length} repositories`);
  }

  async invalidate(key: string, repositories: CacheRepository<any>[]): Promise<void> {
    // 并行删除，不等待所有完成
    repositories.forEach(repo => {
      repo.delete(key).catch(error => {
        this.logger.warn(`Invalidation failed for key ${key}: ${error.message}`);
      });
    });
  }
}

/**
 * 缓存策略工厂
 */
@Injectable()
export class CacheStrategyFactory {
  private readonly strategies = new Map<string, CacheStrategy>();
  private readonly syncStrategies = new Map<string, CacheSyncStrategy>();

  constructor() {
    // 注册默认策略
    this.registerStrategy(new CacheAsideStrategy());
    this.registerStrategy(new WriteThroughStrategy());
    this.registerStrategy(new WriteBehindStrategy());
    this.registerStrategy(new RefreshAheadStrategy());

    // 注册同步策略
    this.registerSyncStrategy(new MasterSlaveSync());
    this.registerSyncStrategy(new EventualConsistencySync());
  }

  registerStrategy(strategy: CacheStrategy): void {
    this.strategies.set(strategy.name, strategy);
  }

  registerSyncStrategy(strategy: CacheSyncStrategy): void {
    this.syncStrategies.set(strategy.name, strategy);
  }

  getStrategy(name: string): CacheStrategy | undefined {
    return this.strategies.get(name);
  }

  getSyncStrategy(name: string): CacheSyncStrategy | undefined {
    return this.syncStrategies.get(name);
  }

  getAvailableStrategies(): string[] {
    return Array.from(this.strategies.keys());
  }

  getAvailableSyncStrategies(): string[] {
    return Array.from(this.syncStrategies.keys());
  }

  createMultiLevelStrategy(
    l1Repository: CacheRepository<any>,
    l2Repository: CacheRepository<any>,
  ): MultiLevelStrategy {
    return new MultiLevelStrategy(l1Repository, l2Repository);
  }
}
