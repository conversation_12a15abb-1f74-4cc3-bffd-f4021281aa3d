import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis.service';
import { RedisProtectionService } from '../redis-protection.service';
import { RedisPubSubService } from '../redis-pubsub.service';
import {
  CacheRepository,
  CacheResult,
  CacheOptions,
  DataSource,
  CacheEvent,
  CacheMode,
  CacheMetadata,
} from './cache.interfaces';

@Injectable()
export abstract class BaseCacheRepository<T> implements CacheRepository<T> {
  protected readonly logger = new Logger(this.constructor.name);
  protected stats = {
    requests: 0,
    hits: 0,
    misses: 0,
    loads: 0,
    errors: 0,
    totalLoadTime: 0,
  };

  constructor(
    protected readonly name: string,
    protected readonly redisService: RedisService,
    protected readonly protectionService: RedisProtectionService,
    protected readonly pubSubService: RedisPubSubService,
    protected readonly dataSource?: DataSource<T>,
  ) {}

  // ==================== 基础缓存操作 ====================

  async get(key: string, options?: CacheOptions): Promise<CacheResult<T>> {
    const startTime = Date.now();
    this.stats.requests++;

    try {
      const cacheKey = this.buildCacheKey(key);
      // 传递dataType到RedisService
      const cached = await this.redisService.get<T>(cacheKey, options?.dataType, options?.serverId);

      if (cached !== null) {
        this.stats.hits++;
        this.emitEvent('hit', key, cached, Date.now() - startTime);

        return {
          hit: true,
          data: cached,
          source: 'cache',
          loadTime: Date.now() - startTime,
        };
      }

      this.stats.misses++;
      this.emitEvent('miss', key, null, Date.now() - startTime);

      return {
        hit: false,
        data: null,
        source: 'cache',
        loadTime: Date.now() - startTime,
      };
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', key, null, Date.now() - startTime, error);
      throw error;
    }
  }

  async set(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now();

    try {
      const cacheKey = this.buildCacheKey(key);
      const ttl = this.calculateTTL(options);

      if (options.enableProtection) {
        // 架构修复：让 RedisCacheService 负责添加完整前缀
        // 传递原始键和完整前缀，避免重复添加
        await this.protectionService.setProtected(key, data, {
          ttl,
          ttlVariance: options.ttlVariance,
          enableAvalancheProtection: options.enableAvalancheProtection,
          enableBreakdownProtection: options.enableBreakdownProtection,
          enablePenetrationProtection: options.enablePenetrationProtection,
          prefix: `cache:${this.name}:`, // 指定完整前缀，RedisCacheService不再添加默认前缀
          dataType: options.dataType, // 传递dataType
          serverId: options.serverId,  // 传递serverId
        });
      } else {
        // 传递dataType到RedisService
        await this.redisService.set(cacheKey, data, ttl, options.dataType, options.serverId);
      }

      // 更新元数据
      await this.updateMetadata(key, options);

      this.emitEvent('set', key, data, Date.now() - startTime);
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', key, data, Date.now() - startTime, error);
      throw error;
    }
  }

  async delete(key: string, options?: CacheOptions): Promise<boolean> {
    const startTime = Date.now();

    try {
      const cacheKey = this.buildCacheKey(key);
      // 传递dataType到RedisService
      const result = await this.redisService.del(cacheKey, options?.dataType, options?.serverId);

      // 删除元数据
      await this.deleteMetadata(key);

      this.emitEvent('delete', key, null, Date.now() - startTime);
      return result > 0;
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', key, null, Date.now() - startTime, error);
      throw error;
    }
  }

  async exists(key: string, options?: CacheOptions): Promise<boolean> {
    const cacheKey = this.buildCacheKey(key);
    // 传递dataType到RedisService
    return await this.redisService.exists(cacheKey, options?.dataType, options?.serverId);
  }

  async clear(pattern?: string, options?: CacheOptions): Promise<number> {
    const startTime = Date.now();

    try {
      const searchPattern = pattern
        ? this.buildCacheKey(pattern)
        : this.buildCacheKey('*');

      // 使用RedisService的keys方法统一处理前缀，传递dataType
      const keys = await this.redisService.keys(searchPattern, options?.dataType, options?.serverId);
      let deletedCount = 0;

      for (const key of keys) {
        // 传递dataType到RedisService
        const result = await this.redisService.del(key, options?.dataType, options?.serverId);
        if (result > 0) deletedCount++;
      }

      this.emitEvent('clear', pattern || '*', null, Date.now() - startTime);
      return deletedCount;
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', pattern || '*', null, Date.now() - startTime, error);
      throw error;
    }
  }

  // ==================== Cache-Aside 模式 ====================

  async getOrLoad(key: string, loader: () => Promise<T>, options: CacheOptions = {}): Promise<T> {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

    this.logger.debug(`[${requestId}] 🚀 getOrLoad started for key: ${key}`);

    try {
      // 1. 尝试从缓存获取
      this.logger.debug(`[${requestId}] 🔍 Checking cache for key: ${key}`);
      const cacheCheckStart = Date.now();
      const cacheResult = await this.get(key);
      const cacheCheckTime = Date.now() - cacheCheckStart;

      if (cacheResult.hit && cacheResult.data !== null) {
        this.logger.debug(`[${requestId}] ✅ Cache HIT! Returning cached data (check took ${cacheCheckTime}ms)`);
        return cacheResult.data;
      }

      this.logger.debug(`[${requestId}] ❌ Cache MISS! Cache check took ${cacheCheckTime}ms`);

      // 2. 缓存未命中，从数据源加载
      this.stats.loads++;
      this.logger.debug(`[${requestId}] 📊 Loading data from source (total loads: ${this.stats.loads})`);

      const loadStartTime = Date.now();
      let data: T | null = null;

      // 优先使用传入的 loader
      if (loader) {
        this.logger.debug(`[${requestId}] 🎯 Calling provided loader function`);
        data = await loader();
      } else if (this.dataSource?.load) {
        this.logger.debug(`[${requestId}] 🗄️  Calling registered data source`);
        data = await this.dataSource.load(key);
      } else {
        const error = new Error(`No data loader available for key: ${key}`);
        this.logger.error(`[${requestId}] 💥 ${error.message}`);
        throw error;
      }

      const loadTime = Date.now() - loadStartTime;
      this.stats.totalLoadTime += loadTime;
      this.logger.debug(`[${requestId}] ⏱️  Data loading completed in ${loadTime}ms`);

      // 3. 如果数据存在，写入缓存
      if (data !== null) {
        this.logger.debug(`[${requestId}] 💾 Caching loaded data`);
        const cacheWriteStart = Date.now();

        await this.set(key, data, options);

        const cacheWriteTime = Date.now() - cacheWriteStart;
        this.logger.debug(`[${requestId}] ✅ Data cached successfully (write took ${cacheWriteTime}ms)`);

        this.emitEvent('load', key, data, Date.now() - startTime);

        const totalTime = Date.now() - startTime;
        this.logger.debug(`[${requestId}] 🎉 getOrLoad completed successfully in ${totalTime}ms (load: ${loadTime}ms, cache: ${cacheWriteTime}ms)`);

        return data;
      }

      // 4. 如果启用了空值缓存
      if (options.cacheNullValues) {
        const nullTTL = options.nullValueTTL || 300; // 默认5分钟
        const cacheKey = this.buildCacheKey(key);

        this.logger.debug(`[${requestId}] 🚫 Caching null value to prevent cache penetration (TTL: ${nullTTL}s)`);
        await this.redisService.set(cacheKey, null, nullTTL);
      }

      const error = new Error(`Data not found for key: ${key}`);
      this.logger.warn(`[${requestId}] ❌ ${error.message}`);
      throw error;

    } catch (error) {
      this.stats.errors++;
      const totalTime = Date.now() - startTime;
      this.logger.error(`[${requestId}] 💥 getOrLoad failed after ${totalTime}ms: ${error.message}`);

      this.emitEvent('error', key, null, Date.now() - startTime, error);
      throw error;
    }
  }

  // ==================== Write-Through 模式 ====================

  async setThrough(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now();

    try {
      // 1. 同步写入数据源
      if (this.dataSource?.save) {
        await this.dataSource.save(key, data);
      }

      // 2. 写入缓存
      await this.set(key, data, options);

      this.logger.debug(`Write-through completed for key: ${key}`);
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', key, data, Date.now() - startTime, error);
      throw error;
    }
  }

  // ==================== Write-Behind 模式 ====================

  async setBehind(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const startTime = Date.now();

    try {
      // 1. 立即写入缓存
      await this.set(key, data, options);

      // 2. 异步写入数据源（通过事件）
      this.emitAsyncWrite(key, data);

      this.logger.debug(`Write-behind initiated for key: ${key}`);
    } catch (error) {
      this.stats.errors++;
      this.emitEvent('error', key, data, Date.now() - startTime, error);
      throw error;
    }
  }

  // ==================== 统计和监控 ====================

  getStats() {
    const hitRate = this.stats.requests > 0 ? this.stats.hits / this.stats.requests : 0;
    const avgLoadTime = this.stats.loads > 0 ? this.stats.totalLoadTime / this.stats.loads : 0;

    return {
      repository: this.name,
      requests: this.stats.requests,
      hits: this.stats.hits,
      misses: this.stats.misses,
      loads: this.stats.loads,
      errors: this.stats.errors,
      hitRate,
      avgLoadTime,
    };
  }

  resetStats(): void {
    this.stats = {
      requests: 0,
      hits: 0,
      misses: 0,
      loads: 0,
      errors: 0,
      totalLoadTime: 0,
    };
  }

  // ==================== 受保护的方法 ====================

  protected buildCacheKey(key: string): string {
    // 注意：不在这里添加前缀，让RedisService自动处理
    // RedisService会自动添加 {env}:{project}:{service}: 前缀
    return `cache:${this.name}:${key}`;
  }

  protected buildMetadataKey(key: string): string {
    return `meta:${this.name}:${key}`;
  }

  protected calculateTTL(options: CacheOptions): number {
    const baseTTL = options.ttl || 3600; // 默认1小时
    const variance = options.ttlVariance || 0;

    if (variance > 0) {
      const randomFactor = 1 + (Math.random() - 0.5) * 2 * variance;
      return Math.floor(baseTTL * randomFactor);
    }

    return baseTTL;
  }

  protected async updateMetadata(key: string, options: CacheOptions): Promise<void> {
    const metadataKey = this.buildMetadataKey(key);
    const now = new Date();

    const existing = await this.redisService.get<CacheMetadata>(metadataKey);
    
    const metadata: CacheMetadata = {
      key,
      repository: this.name,
      mode: CacheMode.CACHE_ASIDE, // 默认模式
      options,
      createdAt: existing?.createdAt || now,
      updatedAt: now,
      accessCount: (existing?.accessCount || 0) + 1,
      lastAccessed: now,
      version: undefined, // version 属性在 CacheKeyOptions 中，不在 CacheOptions 中
    };

    await this.redisService.set(metadataKey, metadata, 86400); // 元数据保存24小时
  }

  protected async deleteMetadata(key: string): Promise<void> {
    const metadataKey = this.buildMetadataKey(key);
    await this.redisService.del(metadataKey);
  }

  protected emitEvent(
    type: CacheEvent['type'],
    key: string,
    data: any,
    duration: number,
    error?: Error
  ): void {
    const event: CacheEvent = {
      type,
      repository: this.name,
      key,
      data,
      error,
      timestamp: new Date(),
      duration,
    };

    // 发布事件到 Redis PubSub
    this.pubSubService.publishEvent('cache', type, event).catch(err => {
      this.logger.warn(`Failed to publish cache event: ${err.message}`);
    });
  }

  protected emitAsyncWrite(key: string, data: T): void {
    // 发布异步写入事件
    this.pubSubService.publishEvent('cache', 'async-write', {
      repository: this.name,
      key,
      data,
      timestamp: new Date(),
    }).catch(err => {
      this.logger.warn(`Failed to publish async write event: ${err.message}`);
    });
  }
}
