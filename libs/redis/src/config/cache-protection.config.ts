/**
 * 缓存防护配置
 * 定义各种缓存防护策略的配置参数
 */

export interface CacheProtectionConfig {
  // 全局开关
  enableProtection: boolean;
  
  // 缓存雪崩防护
  avalancheProtection: {
    enabled: boolean;
    defaultTTLVariance: number; // 默认TTL随机变化比例
    maxVariance: number; // 最大变化比例
  };
  
  // 缓存击穿防护
  breakdownProtection: {
    enabled: boolean;
    defaultLockTimeout: number; // 默认锁超时时间（秒）
    maxLockTimeout: number; // 最大锁超时时间
    defaultRetryDelay: number; // 默认重试延迟（毫秒）
    maxRetries: number; // 最大重试次数
  };
  
  // 缓存穿透防护
  penetrationProtection: {
    enabled: boolean;
    useBloomFilter: boolean; // 是否使用布隆过滤器
    cacheNullValues: boolean; // 是否缓存空值
    defaultNullValueTTL: number; // 空值默认TTL
    maxNullValueTTL: number; // 空值最大TTL
  };
  
  // 布隆过滤器配置
  bloomFilter: {
    defaultFalsePositiveRate: number; // 默认误判率
    maxFalsePositiveRate: number; // 最大误判率
    defaultTTL: number; // 默认过期时间
    cleanupInterval: number; // 清理间隔（秒）
  };
  
  // 监控配置
  monitoring: {
    enableStats: boolean; // 是否启用统计
    statsResetInterval: number; // 统计重置间隔（秒）
    alertThresholds: {
      hitRateBelow: number; // 命中率低于此值时告警
      penetrationAttacksAbove: number; // 穿透攻击超过此值时告警
      breakdownAttacksAbove: number; // 击穿攻击超过此值时告警
    };
  };
}

// 开发环境配置
export const developmentConfig: CacheProtectionConfig = {
  enableProtection: true,
  
  avalancheProtection: {
    enabled: true,
    defaultTTLVariance: 0.2, // ±20%
    maxVariance: 0.5, // ±50%
  },
  
  breakdownProtection: {
    enabled: true,
    defaultLockTimeout: 30, // 30秒
    maxLockTimeout: 120, // 2分钟
    defaultRetryDelay: 100, // 100毫秒
    maxRetries: 3,
  },
  
  penetrationProtection: {
    enabled: true,
    useBloomFilter: true,
    cacheNullValues: true,
    defaultNullValueTTL: 300, // 5分钟
    maxNullValueTTL: 3600, // 1小时
  },
  
  bloomFilter: {
    defaultFalsePositiveRate: 0.01, // 1%
    maxFalsePositiveRate: 0.05, // 5%
    defaultTTL: 604800, // 7天
    cleanupInterval: 86400, // 1天
  },
  
  monitoring: {
    enableStats: true,
    statsResetInterval: 3600, // 1小时
    alertThresholds: {
      hitRateBelow: 0.8, // 80%
      penetrationAttacksAbove: 1000, // 1000次/小时
      breakdownAttacksAbove: 100, // 100次/小时
    },
  },
};

// 生产环境配置
export const productionConfig: CacheProtectionConfig = {
  enableProtection: true,
  
  avalancheProtection: {
    enabled: true,
    defaultTTLVariance: 0.4, // ±40%
    maxVariance: 0.8, // ±80%
  },
  
  breakdownProtection: {
    enabled: true,
    defaultLockTimeout: 60, // 1分钟
    maxLockTimeout: 300, // 5分钟
    defaultRetryDelay: 50, // 50毫秒
    maxRetries: 10,
  },
  
  penetrationProtection: {
    enabled: true,
    useBloomFilter: true,
    cacheNullValues: true,
    defaultNullValueTTL: 1800, // 30分钟
    maxNullValueTTL: 14400, // 4小时
  },
  
  bloomFilter: {
    defaultFalsePositiveRate: 0.001, // 0.1%
    maxFalsePositiveRate: 0.01, // 1%
    defaultTTL: 604800, // 7天
    cleanupInterval: 21600, // 6小时
  },
  
  monitoring: {
    enableStats: true,
    statsResetInterval: 900, // 15分钟
    alertThresholds: {
      hitRateBelow: 0.9, // 90%
      penetrationAttacksAbove: 10000, // 10000次/15分钟
      breakdownAttacksAbove: 1000, // 1000次/15分钟
    },
  },
};

// 游戏场景特定配置
export const gameScenarioConfigs = {
  // 用户数据缓存
  userData: {
    ttl: 3600, // 1小时
    ttlVariance: 0.3,
    enableAllProtections: true,
    bloomFilterExpectedElements: 1000000,
    bloomFilterFalsePositiveRate: 0.01,
  },
  
  // 俱乐部数据缓存（热点数据）
  clubData: {
    ttl: 1800, // 30分钟
    ttlVariance: 0.2,
    enableBreakdownProtection: true, // 重点防击穿
    lockTimeout: 60,
    maxRetries: 5,
    bloomFilterExpectedElements: 100000,
    bloomFilterFalsePositiveRate: 0.005,
  },
  
  // 球员搜索缓存（防穿透重点）
  playerSearch: {
    ttl: 600, // 10分钟
    ttlVariance: 0.4,
    enablePenetrationProtection: true, // 重点防穿透
    cacheNullValues: true,
    nullValueTTL: 60,
    bloomFilterExpectedElements: 5000000,
    bloomFilterFalsePositiveRate: 0.01,
  },
  
  // 实时比赛数据（超高频访问）
  liveMatchData: {
    ttl: 30, // 30秒
    ttlVariance: 0.1,
    enableBreakdownProtection: true,
    lockTimeout: 5,
    maxRetries: 1,
    retryDelay: 10,
  },
  
  // 历史数据缓存（低频访问）
  historicalData: {
    ttl: 86400, // 24小时
    ttlVariance: 0.2,
    enablePenetrationProtection: true,
    cacheNullValues: true,
    nullValueTTL: 3600,
  },
  
  // 排行榜缓存（高并发访问）
  leaderboard: {
    ttl: 300, // 5分钟
    ttlVariance: 0.3,
    enableBreakdownProtection: true,
    lockTimeout: 30,
    maxRetries: 3,
  },
  
  // 游戏配置缓存（很少变化）
  gameConfig: {
    ttl: 3600, // 1小时
    ttlVariance: 0.1,
    enableAvalancheProtection: true,
  },
};

// 根据环境获取配置
export function getCacheProtectionConfig(environment: string = 'development'): CacheProtectionConfig {
  switch (environment) {
    case 'production':
      return productionConfig;
    case 'testing':
    case 'test':
      return productionConfig; // 测试环境使用生产配置
    case 'development':
    case 'dev':
    default:
      return developmentConfig;
  }
}

// 获取场景特定配置
export function getScenarioConfig(scenario: keyof typeof gameScenarioConfigs) {
  return gameScenarioConfigs[scenario];
}

// 验证配置
export function validateConfig(config: CacheProtectionConfig): string[] {
  const errors: string[] = [];
  
  // 验证TTL变化范围
  if (config.avalancheProtection.defaultTTLVariance < 0 || config.avalancheProtection.defaultTTLVariance > 1) {
    errors.push('avalancheProtection.defaultTTLVariance must be between 0 and 1');
  }
  
  if (config.avalancheProtection.maxVariance < config.avalancheProtection.defaultTTLVariance) {
    errors.push('avalancheProtection.maxVariance must be >= defaultTTLVariance');
  }
  
  // 验证锁超时时间
  if (config.breakdownProtection.defaultLockTimeout <= 0) {
    errors.push('breakdownProtection.defaultLockTimeout must be > 0');
  }
  
  if (config.breakdownProtection.maxLockTimeout < config.breakdownProtection.defaultLockTimeout) {
    errors.push('breakdownProtection.maxLockTimeout must be >= defaultLockTimeout');
  }
  
  // 验证重试配置
  if (config.breakdownProtection.maxRetries < 0) {
    errors.push('breakdownProtection.maxRetries must be >= 0');
  }
  
  if (config.breakdownProtection.defaultRetryDelay <= 0) {
    errors.push('breakdownProtection.defaultRetryDelay must be > 0');
  }
  
  // 验证布隆过滤器配置
  if (config.bloomFilter.defaultFalsePositiveRate <= 0 || config.bloomFilter.defaultFalsePositiveRate >= 1) {
    errors.push('bloomFilter.defaultFalsePositiveRate must be between 0 and 1');
  }
  
  if (config.bloomFilter.maxFalsePositiveRate < config.bloomFilter.defaultFalsePositiveRate) {
    errors.push('bloomFilter.maxFalsePositiveRate must be >= defaultFalsePositiveRate');
  }
  
  // 验证监控配置
  if (config.monitoring.alertThresholds.hitRateBelow <= 0 || config.monitoring.alertThresholds.hitRateBelow > 1) {
    errors.push('monitoring.alertThresholds.hitRateBelow must be between 0 and 1');
  }
  
  return errors;
}
