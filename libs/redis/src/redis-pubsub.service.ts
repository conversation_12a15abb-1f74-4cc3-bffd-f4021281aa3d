import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { RedisService } from './redis.service';
import Redis from 'ioredis';
import { DataType } from './types/redis.types';

export interface PubSubMessage {
  channel: string;
  pattern?: string;
  data: any;
  timestamp: Date;
  messageId?: string;
}

export interface SubscriptionInfo {
  channel: string;
  pattern?: string;
  callback: (message: PubSubMessage) => void;
  subscribed: boolean;
  subscribedAt: Date;
}

export interface PubSubStats {
  totalPublished: number;
  totalReceived: number;
  activeSubscriptions: number;
  channels: string[];
  patterns: string[];
}

@Injectable()
export class RedisPubSubService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisPubSubService.name);
  private subscriber: Redis;
  private publisher: Redis;
  private subscriptions = new Map<string, SubscriptionInfo>();
  private stats: PubSubStats = {
    totalPublished: 0,
    totalReceived: 0,
    activeSubscriptions: 0,
    channels: [],
    patterns: [],
  };

  constructor(private readonly redisService: RedisService) {
    // 不在构造函数中初始化客户端，等待 onModuleInit
  }

  async onModuleInit() {
    // 🔧 等待RedisService完全初始化完成
    await this.redisService.waitForInitialization();
    this.initializeClients();
  }

  async onModuleDestroy() {
    await this.cleanup();
  }

  // ==================== 发布消息 ====================

  /**
   * 构建频道名 
   * 使用RedisService的前缀机制确保一致性
   */
  private buildChannelName(channel: string, dataType?: DataType, serverId?: string): string {
    // 使用RedisService的buildDataTypeKey方法确保前缀一致性
    return this.redisService.buildDataTypeKey(`pubsub:${channel}`, dataType, serverId);
  }

  /**
   * 发布消息到指定频道 
   */
  async publish(
    channel: string,
    data: any,
    messageId?: string,
    dataType?: DataType,
    serverId?: string
  ): Promise<number> {
    try {
      const fullChannel = this.buildChannelName(channel, dataType, serverId);
      const message: PubSubMessage = {
        channel: fullChannel,
        data,
        timestamp: new Date(),
        messageId: messageId || this.generateMessageId(),
      };

      const subscriberCount = await this.publisher.publish(fullChannel, JSON.stringify(message));
      this.stats.totalPublished++;

      this.logger.debug(`Published message to channel ${fullChannel} (dataType: ${dataType || 'server'}), ${subscriberCount} subscribers`);
      return subscriberCount;
    } catch (error) {
      this.logger.error(`Failed to publish to channel ${channel}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量发布消息
   */
  async publishBatch(messages: Array<{ channel: string; data: any; messageId?: string }>): Promise<number[]> {
    const results = await Promise.all(
      messages.map(msg => this.publish(msg.channel, msg.data, msg.messageId))
    );
    
    this.logger.log(`Published ${messages.length} messages in batch`);
    return results;
  }

  // ==================== 通用发布方法 ====================

  /**
   * 发布事件到指定频道（带命名空间）
   */
  async publishEvent(namespace: string, eventType: string, data: any, messageId?: string, dataType?: DataType, serverId?: string): Promise<number> {
    const channel = `${namespace}:${eventType}`;
    return await this.publish(channel, data, messageId, dataType, serverId);
  }

  /**
   * 发布到实体频道（如 user:123, club:456）
   */
  async publishToEntity(entityType: string, entityId: string, eventType: string, data: any, dataType?: DataType, serverId?: string): Promise<number> {
    const channel = `${entityType}:${entityId}:${eventType}`;
    return await this.publish(channel, data, undefined, dataType, serverId);
  }

  /**
   * 发布到全局频道 - 自动使用global数据类型
   */
  async publishGlobal(eventType: string, data: any, messageId?: string): Promise<number> {
    const channel = `global:${eventType}`;
    return await this.publish(channel, data, messageId, 'global');
  }

  // ==================== 订阅消息 ====================

  /**
   * 订阅指定频道 
   */
  async subscribe(
    channel: string,
    callback: (message: PubSubMessage) => void,
    dataType?: DataType,
    serverId?: string
  ): Promise<void> {
    try {
      // 🔧 检查订阅客户端状态
      if (!this.subscriber) {
        throw new Error('Redis subscriber not initialized. Cannot subscribe to channel.');
      }

      const fullChannel = this.buildChannelName(channel, dataType, serverId);
      await this.subscriber.subscribe(fullChannel);

      const subscription: SubscriptionInfo = {
        channel: fullChannel,
        callback,
        subscribed: true,
        subscribedAt: new Date(),
      };

      this.subscriptions.set(fullChannel, subscription);
      this.updateStats();

      this.logger.log(`Subscribed to channel: ${fullChannel} (dataType: ${dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`Failed to subscribe to channel ${channel}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 模式订阅
   */
  async psubscribe(pattern: string, callback: (message: PubSubMessage) => void): Promise<void> {
    try {
      await this.subscriber.psubscribe(pattern);
      
      const subscription: SubscriptionInfo = {
        channel: pattern,
        pattern,
        callback,
        subscribed: true,
        subscribedAt: new Date(),
      };
      
      this.subscriptions.set(pattern, subscription);
      this.updateStats();
      
      this.logger.log(`Subscribed to pattern: ${pattern}`);
    } catch (error) {
      this.logger.error(`Failed to subscribe to pattern ${pattern}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 取消订阅频道
   */
  async unsubscribe(channel: string): Promise<void> {
    try {
      await this.subscriber.unsubscribe(channel);
      this.subscriptions.delete(channel);
      this.updateStats();
      
      this.logger.log(`Unsubscribed from channel: ${channel}`);
    } catch (error) {
      this.logger.error(`Failed to unsubscribe from channel ${channel}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 取消模式订阅
   */
  async punsubscribe(pattern: string): Promise<void> {
    try {
      await this.subscriber.punsubscribe(pattern);
      this.subscriptions.delete(pattern);
      this.updateStats();
      
      this.logger.log(`Unsubscribed from pattern: ${pattern}`);
    } catch (error) {
      this.logger.error(`Failed to unsubscribe from pattern ${pattern}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 通用订阅方法 ====================

  /**
   * 订阅事件（带命名空间）
   */
  async subscribeToEvent(namespace: string, eventType: string, callback: (data: any) => void): Promise<void> {
    const channel = `${namespace}:${eventType}`;
    await this.subscribe(channel, (message) => {
      callback(message.data);
    });
  }

  /**
   * 订阅实体事件
   */
  async subscribeToEntity(entityType: string, entityId: string, eventType: string, callback: (data: any) => void): Promise<void> {
    const channel = `${entityType}:${entityId}:${eventType}`;
    await this.subscribe(channel, (message) => {
      callback(message.data);
    });
  }

  /**
   * 订阅全局事件
   */
  async subscribeToGlobal(eventType: string, callback: (data: any) => void): Promise<void> {
    const channel = `global:${eventType}`;
    await this.subscribe(channel, (message) => {
      callback(message.data);
    });
  }

  /**
   * 订阅模式（支持通配符）
   */
  async subscribeToPattern(pattern: string, callback: (data: any, channel?: string) => void): Promise<void> {
    await this.psubscribe(pattern, (message) => {
      callback(message.data, message.channel);
    });
  }

  /**
   * 订阅实体类型的所有事件
   */
  async subscribeToEntityType(entityType: string, eventType: string, callback: (data: any, entityId?: string) => void): Promise<void> {
    const pattern = `${entityType}:*:${eventType}`;
    await this.psubscribe(pattern, (message) => {
      const parts = message.channel.split(':');
      const entityId = parts[1];
      callback(message.data, entityId);
    });
  }

  // ==================== 管理功能 ====================

  /**
   * 获取订阅统计信息
   */
  getStats(): PubSubStats {
    return { ...this.stats };
  }

  /**
   * 获取活跃订阅列表
   */
  getActiveSubscriptions(): SubscriptionInfo[] {
    return Array.from(this.subscriptions.values()).filter(sub => sub.subscribed);
  }

  /**
   * 检查是否订阅了指定频道
   */
  isSubscribed(channel: string): boolean {
    const subscription = this.subscriptions.get(channel);
    return subscription ? subscription.subscribed : false;
  }

  /**
   * 取消所有订阅
   */
  async unsubscribeAll(): Promise<void> {
    try {
      await this.subscriber.unsubscribe();
      await this.subscriber.punsubscribe();
      this.subscriptions.clear();
      this.updateStats();
      
      this.logger.log('Unsubscribed from all channels and patterns');
    } catch (error) {
      this.logger.error(`Failed to unsubscribe all: ${error.message}`);
      throw error;
    }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats.totalPublished = 0;
    this.stats.totalReceived = 0;
    this.logger.log('PubSub statistics reset');
  }

  // ==================== 私有方法 ====================

  private initializeClients(): void {
    try {
      // 创建发布客户端
      this.publisher = this.redisService.getClient();

      // 创建订阅客户端
      this.subscriber = this.redisService.getSubscriber();

      // 🔧 验证客户端初始化
      if (!this.publisher) {
        throw new Error('Failed to initialize Redis publisher client');
      }
      if (!this.subscriber) {
        throw new Error('Failed to initialize Redis subscriber client');
      }

      // 设置消息处理器
      this.subscriber.on('message', (channel, message) => {
        this.handleMessage(channel, message);
      });

      this.subscriber.on('pmessage', (pattern, channel, message) => {
        this.handlePatternMessage(pattern, channel, message);
      });

      this.logger.log('PubSub clients initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize PubSub clients: ${error.message}`);
      throw error;
    }
    
    this.subscriber.on('subscribe', (channel, count) => {
      this.logger.debug(`Subscribed to ${channel}, total subscriptions: ${count}`);
    });
    
    this.subscriber.on('unsubscribe', (channel, count) => {
      this.logger.debug(`Unsubscribed from ${channel}, total subscriptions: ${count}`);
    });
    
    this.logger.log('PubSub clients initialized');
  }

  private handleMessage(channel: string, message: string): void {
    try {
      const parsedMessage: PubSubMessage = JSON.parse(message);
      const subscription = this.subscriptions.get(channel);
      
      if (subscription && subscription.callback) {
        subscription.callback(parsedMessage);
        this.stats.totalReceived++;
      }
    } catch (error) {
      this.logger.error(`Failed to handle message from channel ${channel}: ${error.message}`);
    }
  }

  private handlePatternMessage(pattern: string, channel: string, message: string): void {
    try {
      const parsedMessage: PubSubMessage = JSON.parse(message);
      parsedMessage.pattern = pattern;
      
      const subscription = this.subscriptions.get(pattern);
      
      if (subscription && subscription.callback) {
        subscription.callback(parsedMessage);
        this.stats.totalReceived++;
      }
    } catch (error) {
      this.logger.error(`Failed to handle pattern message from ${pattern}/${channel}: ${error.message}`);
    }
  }

  private updateStats(): void {
    const activeSubscriptions = Array.from(this.subscriptions.values()).filter(sub => sub.subscribed);
    
    this.stats.activeSubscriptions = activeSubscriptions.length;
    this.stats.channels = activeSubscriptions
      .filter(sub => !sub.pattern)
      .map(sub => sub.channel);
    this.stats.patterns = activeSubscriptions
      .filter(sub => sub.pattern)
      .map(sub => sub.pattern!);
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 专用方法 ====================

  /**
   * 发布全局事件（所有区服可见）
   */
  async publishGlobalEvent(eventType: string, data: any): Promise<number> {
    return await this.publish(`events:${eventType}`, data, undefined, 'global');
  }

  /**
   * 发布跨服事件（跨服功能使用）
   */
  async publishCrossServerEvent(eventType: string, data: any): Promise<number> {
    return await this.publish(`cross:${eventType}`, data, undefined, 'cross');
  }

  /**
   * 发布区服事件（当前区服内）
   */
  async publishServerEvent(eventType: string, data: any): Promise<number> {
    return await this.publish(`server:${eventType}`, data, undefined, 'server');
  }

  /**
   * 订阅全局事件
   */
  async subscribeGlobalEvent(eventType: string, callback: (message: PubSubMessage) => void): Promise<void> {
    return await this.subscribe(`events:${eventType}`, callback, 'global');
  }

  /**
   * 订阅跨服事件
   */
  async subscribeCrossServerEvent(eventType: string, callback: (message: PubSubMessage) => void): Promise<void> {
    return await this.subscribe(`cross:${eventType}`, callback, 'cross');
  }

  /**
   * 订阅区服事件
   */
  async subscribeServerEvent(eventType: string, callback: (message: PubSubMessage) => void): Promise<void> {
    return await this.subscribe(`server:${eventType}`, callback, 'server');
  }

  private async cleanup(): Promise<void> {
    try {
      await this.unsubscribeAll();
      await this.subscriber.quit();
      this.logger.log('PubSub service cleaned up');
    } catch (error) {
      this.logger.error(`Failed to cleanup PubSub service: ${error.message}`);
    }
  }
}
