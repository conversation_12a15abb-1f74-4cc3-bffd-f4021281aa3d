# 缓存未命中流程详细解析

## 🎯 概述

本文档详细解析"缓存未命中时从数据源获取"的完整流程，包括代码执行路径、方法调用链、错误处理等。

## 🔄 完整流程图

```mermaid
graph TD
    A[用户请求] --> B[缓存拦截器/装饰器]
    B --> C{检查缓存}
    C -->|命中| D[返回缓存数据]
    C -->|未命中| E[调用数据源加载器]
    E --> F[DataSource.load()]
    F --> G[查询数据库/API]
    G --> H{数据存在?}
    H -->|存在| I[转换数据格式]
    H -->|不存在| J[返回 null]
    I --> K[写入缓存]
    J --> L[缓存空值]
    K --> M[返回数据]
    L --> M
    M --> N[响应用户]
```

## 📋 详细代码流程

### 1. 装饰器方式流程

#### 步骤1: 用户调用方法
```typescript
// 用户代码
const user = await characterService.getUserById('user123');
```

#### 步骤2: 缓存拦截器介入
```typescript
// CacheInterceptor.intercept()
async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
  const cacheableMetadata = this.reflector.get<CacheableMetadata>(CACHEABLE_METADATA, handler);
  
  if (cacheableMetadata) {
    // 🔍 检查缓存
    const cachedResult = await this.handleCacheable(cacheableMetadata, args, methodName);
    if (cachedResult !== undefined) {
      // ✅ 缓存命中，直接返回
      return of(cachedResult);
    }
  }
  
  // ❌ 缓存未命中，执行原方法
  return next.handle().pipe(
    tap(async (result) => {
      // 💾 缓存结果
      await this.handleCacheableResult(cacheableMetadata, args, result, methodName);
    })
  );
}
```

#### 步骤3: 缓存检查详细流程
```typescript
// CacheInterceptor.handleCacheable()
private async handleCacheable(metadata: CacheableMetadata, args: any[], methodName: string): Promise<any> {
  // 🔑 生成缓存键
  const key = this.generateKey(metadata.key, args); // "user:123"
  
  // 📦 获取缓存存储库
  const repository = this.cacheManager.getRepository(metadata.repository!);
  
  try {
    // 🔍 检查缓存
    console.log(`🔍 Checking cache for key: ${key}`);
    const result = await repository.get(key);
    
    if (result.hit && result.data !== null) {
      // ✅ 缓存命中
      console.log(`✅ Cache HIT for key: ${key}`);
      return result.data;
    }
    
    // ❌ 缓存未命中
    console.log(`❌ Cache MISS for key: ${key}`);
    return undefined; // 表示需要执行原方法
    
  } catch (error) {
    console.error(`💥 Cache check failed: ${error.message}`);
    return undefined; // 降级到原方法
  }
}
```

#### 步骤4: 执行原方法（数据源加载）
```typescript
// UserService.getUserById() - 被装饰的方法
@Cacheable({ repository: 'users', key: (args) => `user:${args[0]}` })
async getUserById(userId: string): Promise<User | null> {
  console.log(`📊 Cache miss! Loading user ${userId} from data source`);
  
  // 这里会调用数据源
  return await this.loadUserFromDatabase(userId);
}
```

#### 步骤5: 数据源加载详细流程
```typescript
// UserDataSource.load()
async load(key: string): Promise<User | null> {
  console.log(`🗄️  DataSource.load() called for key: ${key}`);
  
  try {
    // 1️⃣ 解析缓存键
    const userId = this.extractUserIdFromKey(key); // "user:123" -> "123"
    console.log(`📝 Extracted userId: ${userId}`);
    
    // 2️⃣ 查询数据库
    console.log(`🔍 Querying database for user: ${userId}`);
    const startTime = Date.now();
    
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['profile', 'club'],
    });
    
    const queryTime = Date.now() - startTime;
    console.log(`⏱️  Database query completed in ${queryTime}ms`);
    
    // 3️⃣ 处理结果
    if (!user) {
      console.log(`❌ User not found: ${userId}`);
      return null;
    }
    
    // 4️⃣ 数据转换
    const userData = this.transformUserData(user);
    console.log(`✅ User data loaded: ${userId}`);
    
    return userData;
    
  } catch (error) {
    console.error(`💥 Data source load failed: ${error.message}`);
    throw error;
  }
}
```

#### 步骤6: 缓存结果
```typescript
// CacheInterceptor.handleCacheableResult()
private async handleCacheableResult(metadata: CacheableMetadata, args: any[], result: any, methodName: string): Promise<void> {
  const key = this.generateKey(metadata.key, args);
  const repository = this.cacheManager.getRepository(metadata.repository!);
  
  try {
    console.log(`💾 Caching result for key: ${key}`);
    
    // 写入缓存
    await repository.set(key, result, metadata.options);
    
    console.log(`✅ Result cached successfully for key: ${key}`);
  } catch (error) {
    console.error(`💥 Failed to cache result: ${error.message}`);
    // 缓存失败不影响业务逻辑
  }
}
```

### 2. 手动方式流程

#### 使用 getOrLoad 方法
```typescript
async getUserByIdManual(userId: string): Promise<User | null> {
  const repository = this.cacheManager.getRepository<User>('users');
  const cacheKey = `user:${userId}`;
  
  // 🎯 getOrLoad 内部流程
  return await repository.getOrLoad(
    cacheKey,
    async () => {
      // 📊 这个 loader 函数只在缓存未命中时被调用
      console.log(`Cache miss! Loading user ${userId} from data source`);
      return await this.dataSource.load(cacheKey);
    },
    { ttl: 3600, enableProtection: true }
  );
}
```

#### getOrLoad 内部实现
```typescript
// BaseCacheRepository.getOrLoad()
async getOrLoad(key: string, loader: () => Promise<T>, options: CacheOptions = {}): Promise<T> {
  const startTime = Date.now();
  
  try {
    // 1️⃣ 检查缓存
    console.log(`🔍 Checking cache for key: ${key}`);
    const cacheResult = await this.get(key);
    
    if (cacheResult.hit && cacheResult.data !== null) {
      // ✅ 缓存命中
      console.log(`✅ Cache HIT for key: ${key}`);
      return cacheResult.data;
    }
    
    // 2️⃣ 缓存未命中，调用加载器
    console.log(`❌ Cache MISS for key: ${key}`);
    this.stats.loads++;
    
    const loadStartTime = Date.now();
    const data = await loader(); // 🎯 调用用户提供的 loader 函数
    const loadTime = Date.now() - loadStartTime;
    
    console.log(`⏱️  Data loaded in ${loadTime}ms`);
    
    // 3️⃣ 缓存数据
    if (data !== null) {
      await this.set(key, data, options);
      console.log(`💾 Data cached for key: ${key}`);
      return data;
    }
    
    // 4️⃣ 处理空值
    if (options.cacheNullValues) {
      const nullTTL = options.nullValueTTL || 300;
      await this.redisService.set(this.buildCacheKey(key), null, nullTTL);
      console.log(`🚫 Null value cached for key: ${key}`);
    }
    
    throw new Error(`Data not found for key: ${key}`);
    
  } catch (error) {
    console.error(`💥 getOrLoad failed for key: ${key}: ${error.message}`);
    throw error;
  }
}
```

## 🛡️ 错误处理流程

### 1. 缓存检查失败
```typescript
try {
  const cacheResult = await repository.get(key);
  // ... 正常流程
} catch (cacheError) {
  console.warn(`⚠️  Cache check failed, falling back to data source: ${cacheError.message}`);
  // 降级到数据源
  return await this.loadFromDataSource(key);
}
```

### 2. 数据源加载失败
```typescript
try {
  const data = await this.dataSource.load(key);
  return data;
} catch (dataSourceError) {
  console.error(`💥 Data source failed: ${dataSourceError.message}`);
  
  // 可以尝试其他数据源或返回默认值
  if (this.fallbackDataSource) {
    return await this.fallbackDataSource.load(key);
  }
  
  throw dataSourceError;
}
```

### 3. 缓存写入失败
```typescript
try {
  await repository.set(key, data, options);
} catch (cacheWriteError) {
  console.warn(`⚠️  Failed to cache data, but returning loaded data: ${cacheWriteError.message}`);
  // 缓存失败不影响数据返回
  return data;
}
```

## 📊 性能监控

### 1. 缓存命中率统计
```typescript
// 在每次缓存操作时更新统计
private updateStats(hit: boolean, loadTime?: number) {
  this.stats.requests++;
  
  if (hit) {
    this.stats.hits++;
    console.log(`📈 Cache hit rate: ${(this.stats.hits / this.stats.requests * 100).toFixed(2)}%`);
  } else {
    this.stats.misses++;
    if (loadTime) {
      this.stats.totalLoadTime += loadTime;
      this.stats.loads++;
      console.log(`📉 Average load time: ${(this.stats.totalLoadTime / this.stats.loads).toFixed(2)}ms`);
    }
  }
}
```

### 2. 慢查询检测
```typescript
async load(key: string): Promise<T | null> {
  const startTime = Date.now();
  
  try {
    const data = await this.actualLoad(key);
    const loadTime = Date.now() - startTime;
    
    // 检测慢查询
    if (loadTime > 1000) { // 超过1秒
      console.warn(`🐌 Slow query detected for key ${key}: ${loadTime}ms`);
      // 可以发送告警或记录日志
    }
    
    return data;
  } catch (error) {
    const loadTime = Date.now() - startTime;
    console.error(`💥 Query failed after ${loadTime}ms for key ${key}: ${error.message}`);
    throw error;
  }
}
```

## 🔧 调试技巧

### 1. 启用详细日志
```typescript
// 在环境变量中设置
process.env.CACHE_DEBUG = 'true';

// 在代码中检查
if (process.env.CACHE_DEBUG === 'true') {
  console.log(`🔍 [DEBUG] Cache operation: ${operation} for key: ${key}`);
}
```

### 2. 缓存键追踪
```typescript
// 为每个请求生成唯一ID
const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

console.log(`[${requestId}] 🔍 Checking cache for key: ${key}`);
console.log(`[${requestId}] ❌ Cache miss, loading from data source`);
console.log(`[${requestId}] ✅ Data loaded and cached`);
```

### 3. 性能分析
```typescript
// 使用 performance API
const mark1 = performance.mark('cache-check-start');
const cacheResult = await repository.get(key);
const mark2 = performance.mark('cache-check-end');

const measure = performance.measure('cache-check', 'cache-check-start', 'cache-check-end');
console.log(`⏱️  Cache check took: ${measure.duration.toFixed(2)}ms`);
```

通过这个详细的流程解析，您可以清楚地了解缓存未命中时数据是如何从数据源获取的，以及整个过程中的每一个步骤和可能的错误处理情况。
