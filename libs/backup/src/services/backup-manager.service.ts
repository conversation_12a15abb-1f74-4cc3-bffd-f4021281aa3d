import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  BackupResult, 
  RestoreResult, 
  RestoreOptions,
  BackupConfig,
  BackupHealthStatus 
} from '../interfaces/backup.interface';
import { DatabaseBackupService } from './database-backup.service';
import { RedisBackupService } from './redis-backup.service';
import { FileBackupService } from './file-backup.service';
import { StorageProviderService } from './storage-provider.service';
import { BackupMetadataService } from './backup-metadata.service';

@Injectable()
export class BackupManagerService {
  private readonly logger = new Logger(BackupManagerService.name);
  private readonly config: BackupConfig;

  constructor(
    private readonly databaseBackup: DatabaseBackupService,
    private readonly redisBackup: RedisBackupService,
    private readonly fileBackup: FileBackupService,
    private readonly storageProvider: StorageProviderService,
    private readonly metadataService: BackupMetadataService,
    private readonly configService: ConfigService,
  ) {
    this.config = this.configService.get<BackupConfig>('backup');
    if (!this.config) {
      throw new Error('备份配置未找到，请检查环境变量配置');
    }
  }

  /**
   * 执行完整备份
   */
  async executeFullBackup(): Promise<BackupResult> {
    if (!this.config.enabled) {
      throw new Error('备份功能未启用');
    }

    const backupId = this.generateBackupId();
    const startTime = Date.now();
    
    this.logger.log(`开始执行完整备份: ${backupId}`);

    try {
      const results: BackupResult[] = [];

      // 并行执行各类备份
      const backupPromises: Promise<BackupResult>[] = [];

      // 数据库备份
      backupPromises.push(this.databaseBackup.createBackup(backupId));

      // Redis备份
      backupPromises.push(this.redisBackup.createBackup(backupId));

      // 文件备份（如果启用）
      if (this.config.files?.enabled) {
        backupPromises.push(this.fileBackup.createBackup(backupId));
      }

      // 添加超时控制
      const timeoutMs = 3600000; // 默认1小时
      const backupResults = await Promise.allSettled(
        backupPromises.map(promise =>
          Promise.race([
            promise,
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('备份超时')), timeoutMs)
            )
          ])
        )
      );
      
      // 处理备份结果
      for (const result of backupResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value as BackupResult);
        } else {
          this.logger.error(`备份失败: ${result.reason}`);
          results.push({
            id: backupId,
            type: 'mongodb', // 默认类型
            status: 'failed',
            error: result.reason.message,
            timestamp: new Date().toISOString(),
          });
        }
      }

      // 检查是否有失败的备份
      const failedBackups = results.filter(r => r.status === 'failed');
      if (failedBackups.length > 0) {
        throw new Error(`${failedBackups.length} 个备份失败`);
      }

      // 上传到云存储
      if (this.config.storageProvider !== 'local') {
        await this.uploadBackupsToCloud(results);
      }

      // 保存备份元数据
      await this.metadataService.saveBackupMetadata(backupId, {
        type: 'full',
        results,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      });

      const totalSize = results.reduce((sum, r) => sum + (r.size || 0), 0);
      const duration = Date.now() - startTime;

      this.logger.log(`完整备份完成: ${backupId}, 大小: ${this.formatBytes(totalSize)}, 耗时: ${duration}ms`);

      return {
        id: backupId,
        type: 'mongodb', // 主要类型
        status: 'success',
        size: totalSize,
        duration,
        timestamp: new Date().toISOString(),
        metadata: {
          size: totalSize,
          checksum: await this.calculateChecksum(results),
          compression: this.config.compressionEnabled ? 'gzip' : 'none',
          encryption: this.config.encryptionEnabled,
          recordCount: results.reduce((sum, r) => sum + (r.metadata?.recordCount || 0), 0),
        },
      };

    } catch (error) {
      this.logger.error(`完整备份失败: ${backupId}`, error);
      
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 执行增量备份
   */
  async executeIncrementalBackup(): Promise<BackupResult> {
    if (!this.config.incrementalEnabled) {
      throw new Error('增量备份功能未启用');
    }

    const lastBackup = await this.metadataService.getLastBackupInfo();
    if (!lastBackup) {
      this.logger.warn('未找到上次备份信息，执行完整备份');
      return await this.executeFullBackup();
    }

    return await this.executeBackupSince(new Date(lastBackup.timestamp));
  }

  /**
   * 从指定时间点开始备份
   */
  async executeBackupSince(since: Date): Promise<BackupResult> {
    const backupId = this.generateBackupId('incremental');
    const startTime = Date.now();
    
    this.logger.log(`开始执行增量备份: ${backupId}, 起始时间: ${since.toISOString()}`);

    try {
      const results: BackupResult[] = [];

      // 数据库增量备份
      const dbResult = await this.databaseBackup.createIncrementalBackup(backupId, since);
      results.push(dbResult);

      // Redis增量备份（导出变更的键）
      const redisResult = await this.redisBackup.createIncrementalBackup(backupId, since);
      results.push(redisResult);

      // 上传到云存储
      if (this.config.storageProvider !== 'local') {
        await this.uploadBackupsToCloud(results);
      }

      // 保存备份元数据
      await this.metadataService.saveBackupMetadata(backupId, {
        type: 'incremental',
        results,
        since: since.toISOString(),
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      });

      const totalSize = results.reduce((sum, r) => sum + (r.size || 0), 0);
      const duration = Date.now() - startTime;

      this.logger.log(`增量备份完成: ${backupId}, 大小: ${this.formatBytes(totalSize)}, 耗时: ${duration}ms`);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        size: totalSize,
        duration,
        timestamp: new Date().toISOString(),
        metadata: {
          size: totalSize,
          checksum: await this.calculateChecksum(results),
          compression: this.config.compressionEnabled ? 'gzip' : 'none',
          encryption: this.config.encryptionEnabled,
        },
      };

    } catch (error) {
      this.logger.error(`增量备份失败: ${backupId}`, error);
      
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 恢复数据
   */
  async restoreFromBackup(backupId: string, options: RestoreOptions = {}): Promise<RestoreResult> {
    this.logger.log(`开始恢复备份: ${backupId}`);
    
    const backupInfo = await this.metadataService.getBackupInfo(backupId);
    if (!backupInfo) {
      throw new Error(`备份 ${backupId} 不存在`);
    }

    const startTime = Date.now();
    const results: RestoreResult[] = [];

    try {
      // 按依赖顺序恢复
      if (options.includeDatabase !== false) {
        const dbResult = await this.databaseBackup.restore(backupId, options);
        results.push(dbResult);
      }
      
      if (options.includeRedis !== false) {
        const redisResult = await this.redisBackup.restore(backupId, options);
        results.push(redisResult);
      }
      
      if (options.includeFiles !== false) {
        const fileResult = await this.fileBackup.restore(backupId, options);
        results.push(fileResult);
      }

      const duration = Date.now() - startTime;
      const totalRestored = results.reduce((sum, r) => sum + (r.restoredRecords || 0), 0);

      this.logger.log(`备份恢复完成: ${backupId}, 恢复记录数: ${totalRestored}, 耗时: ${duration}ms`);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        timestamp: new Date().toISOString(),
        duration,
        restoredRecords: totalRestored,
      };

    } catch (error) {
      this.logger.error(`备份恢复失败: ${backupId}`, error);
      
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * 获取备份健康状态
   */
  async getBackupHealth(): Promise<BackupHealthStatus> {
    try {
      const lastBackup = await this.metadataService.getLastSuccessfulBackup();
      const now = new Date();
      
      if (!lastBackup) {
        return {
          status: 'unhealthy',
          reason: 'no_backup_found',
        };
      }

      const timeSinceLastBackup = now.getTime() - new Date(lastBackup.timestamp).getTime();
      const maxInterval = 24 * 60 * 60 * 1000; // 24小时

      if (timeSinceLastBackup > maxInterval) {
        return {
          status: 'unhealthy',
          reason: 'backup_overdue',
          lastBackupTime: new Date(lastBackup.timestamp),
        };
      }

      // 检查存储使用情况
      const storageUsage = await this.getStorageUsage();

      return {
        status: 'healthy',
        lastBackupTime: new Date(lastBackup.timestamp),
        storageUsage,
      };

    } catch (error) {
      this.logger.error('检查备份健康状态失败', error);
      return {
        status: 'unhealthy',
        reason: 'health_check_failed',
      };
    }
  }

  /**
   * 清理过期备份
   */
  async cleanupExpiredBackups(): Promise<{ deleted: number; errors: string[] }> {
    const retentionDate = new Date();
    retentionDate.setDate(retentionDate.getDate() - this.config.retentionDays);

    this.logger.log(`清理 ${retentionDate.toISOString()} 之前的备份`);

    const expiredBackups = await this.metadataService.getExpiredBackups(retentionDate);
    const errors: string[] = [];
    let deleted = 0;

    for (const backup of expiredBackups) {
      try {
        await this.deleteBackup(backup.id);
        deleted++;
      } catch (error) {
        errors.push(`删除备份 ${backup.id} 失败: ${error.message}`);
      }
    }

    this.logger.log(`清理完成: 删除 ${deleted} 个备份, ${errors.length} 个错误`);
    return { deleted, errors };
  }

  // 私有方法
  private generateBackupId(type: string = 'full'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    return `backup-${type}-${timestamp}-${random}`;
  }

  private async uploadBackupsToCloud(results: BackupResult[]): Promise<void> {
    for (const result of results) {
      if (result.status === 'success' && result.path) {
        try {
          await this.storageProvider.uploadBackup(result.path, result.id);
        } catch (error) {
          this.logger.error(`上传备份到云存储失败: ${result.id}`, error);
          throw error;
        }
      }
    }
  }

  private async calculateChecksum(results: BackupResult[]): Promise<string> {
    // 简单的校验和计算
    const data = results.map(r => `${r.id}-${r.size}-${r.timestamp}`).join('|');
    return Buffer.from(data).toString('base64');
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private async getStorageUsage(): Promise<{ used: number; available: number; percentage: number }> {
    // 这里应该实现实际的存储使用情况检查
    // 暂时返回模拟数据
    return {
      used: 1024 * 1024 * 1024, // 1GB
      available: 10 * 1024 * 1024 * 1024, // 10GB
      percentage: 10,
    };
  }

  private async deleteBackup(backupId: string): Promise<void> {
    // 删除本地文件
    await this.storageProvider.deleteBackup(backupId);
    
    // 删除元数据
    await this.metadataService.deleteBackupMetadata(backupId);
  }
}
