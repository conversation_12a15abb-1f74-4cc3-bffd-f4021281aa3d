import { registerAs } from '@nestjs/config';
import { BackupConfig } from '../interfaces/backup.interface';

export default registerAs('backup', (): BackupConfig => ({
  enabled: process.env.BACKUP_ENABLED === 'true',
  incrementalEnabled: process.env.BACKUP_INCREMENTAL_ENABLED === 'true',
  retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30', 10),
  compressionEnabled: process.env.BACKUP_COMPRESSION_ENABLED !== 'false',
  encryptionEnabled: process.env.BACKUP_ENCRYPTION_ENABLED === 'true',
  encryptionKey: process.env.BACKUP_ENCRYPTION_KEY || '',
  storageProvider: (process.env.BACKUP_STORAGE_PROVIDER as any) || 'local',
  localPath: process.env.BACKUP_LOCAL_PATH || '/app/backups',

  s3Config: process.env.BACKUP_STORAGE_PROVIDER === 'aws-s3' ? {
    bucket: process.env.BACKUP_S3_BUCKET || '',
    region: process.env.BACKUP_S3_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  } : undefined,

  ossConfig: process.env.BACKUP_STORAGE_PROVIDER === 'aliyun-oss' ? {
    bucket: process.env.BACKUP_OSS_BUCKET || '',
    region: process.env.BACKUP_OSS_REGION || 'oss-cn-hangzhou',
    accessKeyId: process.env.BACKUP_OSS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.BACKUP_OSS_ACCESS_KEY_SECRET || '',
  } : undefined,

  // 调度配置
  schedule: {
    fullBackup: process.env.BACKUP_FULL_SCHEDULE || '0 2 * * *',
    cleanup: process.env.BACKUP_CLEANUP_SCHEDULE || '0 4 * * 0',
    enabled: process.env.BACKUP_SCHEDULER_ENABLED !== 'false',
  },

  // Redis备份配置
  redis: {
    mode: (process.env.REDIS_BACKUP_MODE as 'rdb' | 'export') || 'rdb',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB || '0', 10),
    timeout: parseInt(process.env.REDIS_RDB_BACKUP_TIMEOUT || '300000', 10),
    batchSize: parseInt(process.env.REDIS_EXPORT_BATCH_SIZE || '1000', 10),
  },

  // MongoDB备份配置
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/football_manager',
    mongodumpPath: process.env.MONGODUMP_PATH || 'mongodump',
    mongorestorePath: process.env.MONGORESTORE_PATH || 'mongorestore',
  },

  // 文件备份配置
  files: {
    enabled: process.env.FILE_BACKUP_ENABLED === 'true',
    includePaths: process.env.FILE_BACKUP_PATHS?.split(',') || [],
    excludePatterns: process.env.FILE_BACKUP_EXCLUDE?.split(',') || ['*.tmp', '*.log', 'node_modules/**'],
  },
}));

/**
 * 备份配置验证
 */
export function validateBackupConfig(config: any): string[] {
  const errors: string[] = [];

  if (!config.enabled) {
    return errors; // 如果备份未启用，跳过验证
  }

  if (!config.localPath) {
    errors.push('必须提供本地备份路径');
  }

  // 验证Redis配置
  if (config.redis) {
    if (!['rdb', 'export'].includes(config.redis.mode)) {
      errors.push(`无效的Redis备份模式: ${config.redis.mode}`);
    }
  }

  if (config.encryptionEnabled && !config.encryptionKey) {
    errors.push('启用加密时必须提供加密密钥');
  }

  switch (config.storageProvider) {
    case 'aws-s3':
      if (!config.s3Config) {
        errors.push('使用AWS S3时必须提供S3配置');
      } else {
        if (!config.s3Config.bucket) errors.push('S3 bucket不能为空');
        if (!config.s3Config.accessKeyId) errors.push('S3 accessKeyId不能为空');
        if (!config.s3Config.secretAccessKey) errors.push('S3 secretAccessKey不能为空');
      }
      break;

    case 'aliyun-oss':
      if (!config.ossConfig) {
        errors.push('使用阿里云OSS时必须提供OSS配置');
      } else {
        if (!config.ossConfig.bucket) errors.push('OSS bucket不能为空');
        if (!config.ossConfig.accessKeyId) errors.push('OSS accessKeyId不能为空');
        if (!config.ossConfig.accessKeySecret) errors.push('OSS accessKeySecret不能为空');
      }
      break;

    case 'local':
      if (!config.localPath) {
        errors.push('使用本地存储时必须提供本地路径');
      }
      break;

    default:
      errors.push(`不支持的存储提供者: ${config.storageProvider}`);
  }

  return errors;
}
