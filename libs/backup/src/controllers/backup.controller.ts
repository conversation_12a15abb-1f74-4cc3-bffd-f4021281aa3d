import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { IsOptional, IsString, IsArray, IsIn, IsBoolean, IsNumber } from 'class-validator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { BackupManagerService } from '../services/backup-manager.service';
import { BackupMetadataService, BackupInfo } from '../services/backup-metadata.service';
import { BackupMonitoringService } from '../services/backup-monitoring.service';
import { BackupSchedulerService } from '../services/backup-scheduler.service';
import { RestoreOptions } from '../interfaces/backup.interface';

// 这些需要根据实际的认证模块调整
// import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
// import { RolesGuard } from '../../auth/guards/roles.guard';
// import { Roles } from '../../auth/decorators/roles.decorator';

class CreateBackupDto {
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsIn(['full', 'incremental'])
  type?: 'full' | 'incremental';
}

class RestoreBackupDto {
  @IsOptional()
  @IsBoolean()
  dropBeforeRestore?: boolean;

  @IsOptional()
  @IsBoolean()
  includeDatabase?: boolean;

  @IsOptional()
  @IsBoolean()
  includeRedis?: boolean;

  @IsOptional()
  @IsBoolean()
  includeFiles?: boolean;

  @IsOptional()
  @IsIn(['rdb', 'keys'])
  restoreMethod?: 'rdb' | 'keys';

  @IsOptional()
  @IsString()
  targetDatabase?: string;

  @IsOptional()
  @IsNumber()
  targetRedisDb?: number;
}

@ApiTags('备份管理')
@Controller('backup')
// @UseGuards(JwtAuthGuard, RolesGuard)
// @ApiBearerAuth('JWT-auth')
export class BackupController {
  constructor(
    private readonly backupManager: BackupManagerService,
    private readonly metadataService: BackupMetadataService,
    private readonly monitoringService: BackupMonitoringService,
    private readonly schedulerService: BackupSchedulerService,
  ) {}

  /**
   * 创建备份
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '创建备份' })
  @ApiResponse({
    status: 201,
    description: '备份创建成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 403,
    description: '权限不足',
  })
  async createBackup(
    @Body() createBackupDto: CreateBackupDto,
  ) {
    const { type = 'full', description, tags } = createBackupDto;

    let backupId: string;

    if (type === 'full') {
      backupId = await this.schedulerService.triggerManualFullBackup(description);
    } else {
      backupId = await this.schedulerService.triggerManualIncrementalBackup(description);
    }

    // 添加标签
    if (tags && tags.length > 0) {
      await this.monitoringService.addBackupTags(backupId, tags);
    }

    return {
      success: true,
      data: { backupId },
      message: '备份任务已启动',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取备份列表
   */
  @Get()
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '获取备份列表' })
  @ApiQuery({ name: 'type', required: false, enum: ['full', 'incremental'] })
  @ApiQuery({ name: 'status', required: false, enum: ['success', 'failed', 'partial'] })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: '获取备份列表成功',
  })
  async getBackups(
    @Query('type') type?: 'full' | 'incremental',
    @Query('status') status?: 'success' | 'failed' | 'partial',
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    const backups = await this.metadataService.listBackups({
      type,
      status,
      limit: limit || 20,
      offset: offset || 0,
    });

    return {
      success: true,
      data: backups,
      message: '获取备份列表成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取备份详情
   */
  @Get(':id')
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '获取备份详情' })
  @ApiParam({ name: 'id', description: '备份ID' })
  @ApiResponse({
    status: 200,
    description: '获取备份详情成功',
  })
  @ApiResponse({
    status: 404,
    description: '备份不存在',
  })
  async getBackupById(@Param('id') id: string) {
    const backup = await this.metadataService.getBackupInfo(id);
    
    if (!backup) {
      return {
        success: false,
        message: '备份不存在',
        timestamp: new Date().toISOString(),
      };
    }

    return {
      success: true,
      data: backup,
      message: '获取备份详情成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 恢复备份
   */
  @Post(':id/restore')
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '恢复备份' })
  @ApiParam({ name: 'id', description: '备份ID' })
  @ApiResponse({
    status: 200,
    description: '备份恢复成功',
  })
  @ApiResponse({
    status: 404,
    description: '备份不存在',
  })
  async restoreBackup(
    @Param('id') id: string,
    @Body() restoreDto: RestoreBackupDto,
  ) {
    const options: RestoreOptions = {
      dropBeforeRestore: restoreDto.dropBeforeRestore,
      includeDatabase: restoreDto.includeDatabase,
      includeRedis: restoreDto.includeRedis,
      includeFiles: restoreDto.includeFiles,
      restoreMethod: restoreDto.restoreMethod,
      targetDatabase: restoreDto.targetDatabase,
      targetRedisDb: restoreDto.targetRedisDb,
    };

    const result = await this.backupManager.restoreFromBackup(id, options);

    return {
      success: result.status === 'success',
      data: result,
      message: result.status === 'success' ? '备份恢复成功' : '备份恢复失败',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 删除备份
   */
  @Delete(':id')
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '删除备份' })
  @ApiParam({ name: 'id', description: '备份ID' })
  @ApiResponse({
    status: 200,
    description: '备份删除成功',
  })
  @ApiResponse({
    status: 404,
    description: '备份不存在',
  })
  async deleteBackup(@Param('id') id: string) {
    const backup = await this.metadataService.getBackupInfo(id);
    
    if (!backup) {
      return {
        success: false,
        message: '备份不存在',
        timestamp: new Date().toISOString(),
      };
    }

    // 检查备份是否受保护
    if (backup.retention.protected) {
      return {
        success: false,
        message: '备份受保护，无法删除',
        timestamp: new Date().toISOString(),
      };
    }

    await this.metadataService.deleteBackupMetadata(id);

    return {
      success: true,
      message: '备份删除成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取备份统计信息
   */
  @Get('statistics/overview')
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '获取备份统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取统计信息成功',
  })
  async getStatistics() {
    const statistics = this.monitoringService.getBackupStatistics();
    const healthStatus = await this.monitoringService.checkBackupHealth();

    return {
      success: true,
      data: {
        ...statistics,
        health: healthStatus,
      },
      message: '获取统计信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取备份健康状态
   */
  @Get('health/status')
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '获取备份健康状态' })
  @ApiResponse({
    status: 200,
    description: '获取健康状态成功',
  })
  async getHealthStatus() {
    const healthStatus = await this.backupManager.getBackupHealth();

    return {
      success: true,
      data: healthStatus,
      message: '获取健康状态成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取调度器状态
   */
  @Get('scheduler/status')
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '获取调度器状态' })
  @ApiResponse({
    status: 200,
    description: '获取调度器状态成功',
  })
  async getSchedulerStatus() {
    const status = this.schedulerService.getSchedulerStatus();

    return {
      success: true,
      data: status,
      message: '获取调度器状态成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 生成备份报告
   */
  @Get('reports/summary')
  // @Roles('admin', 'backup_operator', 'backup_viewer')
  @ApiOperation({ summary: '生成备份报告' })
  @ApiQuery({ name: 'days', required: false, type: Number, description: '报告天数' })
  @ApiResponse({
    status: 200,
    description: '生成报告成功',
  })
  async generateReport(@Query('days') days?: number) {
    const report = await this.monitoringService.generateBackupReport(days || 30);

    return {
      success: true,
      data: report,
      message: '生成报告成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 保护/取消保护备份
   */
  @Post(':id/protect')
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '保护/取消保护备份' })
  @ApiParam({ name: 'id', description: '备份ID' })
  @ApiResponse({
    status: 200,
    description: '操作成功',
  })
  async protectBackup(
    @Param('id') id: string,
    @Body() body: { protect: boolean },
  ) {
    await this.metadataService.protectBackup(id, body.protect);

    return {
      success: true,
      message: body.protect ? '备份已保护' : '备份保护已取消',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 验证备份完整性
   */
  @Post(':id/verify')
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '验证备份完整性' })
  @ApiParam({ name: 'id', description: '备份ID' })
  @ApiResponse({
    status: 200,
    description: '验证完成',
  })
  async verifyBackup(@Param('id') id: string) {
    const isValid = await this.monitoringService.verifyBackupIntegrity(id);

    return {
      success: true,
      data: { valid: isValid },
      message: isValid ? '备份完整性验证通过' : '备份完整性验证失败',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 清理过期备份
   */
  @Post('cleanup/expired')
  // @Roles('admin', 'backup_operator')
  @ApiOperation({ summary: '清理过期备份' })
  @ApiResponse({
    status: 200,
    description: '清理完成',
  })
  async cleanupExpiredBackups() {
    const result = await this.backupManager.cleanupExpiredBackups();

    return {
      success: true,
      data: result,
      message: `清理完成: 删除 ${result.deleted} 个备份`,
      timestamp: new Date().toISOString(),
    };
  }
}
