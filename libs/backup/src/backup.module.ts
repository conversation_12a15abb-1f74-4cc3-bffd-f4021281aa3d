import { Module, DynamicModule, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

// 配置
import backupConfig, { validateBackupConfig } from './config/backup.config';

// 服务
import { BackupManagerService } from './services/backup-manager.service';
import { DatabaseBackupService } from './services/database-backup.service';
import { RedisBackupService } from './services/redis-backup.service';
import { FileBackupService } from './services/file-backup.service';
import { StorageProviderService } from './services/storage-provider.service';
import { BackupMetadataService } from './services/backup-metadata.service';
import { BackupSchedulerService } from './services/backup-scheduler.service';
import { BackupMonitoringService } from './services/backup-monitoring.service';
import { CompressionService } from './services/compression.service';
import { EncryptionService } from './services/encryption.service';

// 控制器
import { BackupController } from './controllers/backup.controller';

// 其他模块
import { RedisModule } from '@libs/redis';

export interface BackupModuleOptions {
  enabled?: boolean;
  schedulerEnabled?: boolean;
  global?: boolean;
}

@Global()
@Module({})
export class BackupModule {
  static forRoot(options: BackupModuleOptions = {}): DynamicModule {
    const providers: any[] = [
      // 核心服务
      CompressionService,
      EncryptionService,
      StorageProviderService,
      BackupMetadataService,
      BackupMonitoringService,

      // 备份服务
      DatabaseBackupService,
      RedisBackupService,
      FileBackupService,

      // 管理服务
      BackupManagerService,
    ];

    // 如果启用调度器，添加调度器服务
    if (options.schedulerEnabled !== false) {
      providers.push(BackupSchedulerService);
    }

    return {
      module: BackupModule,
      imports: [
        ConfigModule.forFeature(backupConfig),
        ScheduleModule.forRoot(),
        RedisModule, // 依赖Redis模块
      ],
      controllers: [BackupController],
      providers,
      exports: [
        BackupManagerService,
        BackupMetadataService,
        BackupMonitoringService,
        BackupSchedulerService,
      ],
      global: options.global !== false,
    };
  }

  static forFeature(): DynamicModule {
    return {
      module: BackupModule,
      providers: [
        BackupManagerService,
        BackupMetadataService,
        BackupMonitoringService,
      ],
      exports: [
        BackupManagerService,
        BackupMetadataService,
        BackupMonitoringService,
      ],
    };
  }

  constructor(private readonly configService: ConfigService) {
    this.validateConfiguration();
  }

  private validateConfiguration(): void {
    const config = this.configService.get('backup');
    if (!config) {
      return; // 配置未加载，跳过验证
    }

    const errors = validateBackupConfig(config);
    if (errors.length > 0) {
      throw new Error(`备份配置验证失败:\n${errors.join('\n')}`);
    }
  }
}
