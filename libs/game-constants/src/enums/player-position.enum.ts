// /**
//  * 球员位置统一定义
//  *
//  * 🚨 重要：这是项目中唯一权威的位置定义文件
//  * 所有其他位置相关的枚举、类型、配置都应该基于此文件
//  *
//  * ⚠️ 基于现有项目和old项目的实际12个位置，保持完全兼容
//  * 不增加新位置，避免破坏现有配置表和数据库结构
//  */
//
// /**
//  * TODO:备用勿删
//  * 球员位置枚举 - 权威定义
//  * 使用国际标准的位置缩写
//  */
// // export enum PlayerPosition {
// //   // 门将
// //   GK = 'GK',   // Goalkeeper - 门将
// //
// //   // 后卫线
// //   CB = 'CB',   // Center Back - 中后卫
// //   LB = 'LB',   // Left Back - 左后卫
// //   RB = 'RB',   // Right Back - 右后卫
// //   LWB = 'LWB', // Left Wing Back - 左翼卫
// //   RWB = 'RWB', // Right Wing Back - 右翼卫
// //
// //   // 中场线
// //   CDM = 'CDM', // Central Defensive Midfielder - 后腰/防守型中场
// //   CM = 'CM',   // Central Midfielder - 中场
// //   CAM = 'CAM', // Central Attacking Midfielder - 前腰/攻击型中场
// //   LM = 'LM',   // Left Midfielder - 左中场
// //   RM = 'RM',   // Right Midfielder - 右中场
// //
// //   // 边锋线
// //   LW = 'LW',   // Left Winger - 左边锋
// //   RW = 'RW',   // Right Winger - 右边锋
// //
// //   // 前锋线
// //   CF = 'CF',   // Center Forward - 中锋
// //   ST = 'ST',   // Striker - 前锋
// //   LF = 'LF',   // Left Forward - 左前锋
// //   RF = 'RF',   // Right Forward - 右前锋
// // }
//
// /**
//  * 球员位置枚举 - 权威定义
//  * 严格基于old项目和现有配置的12个位置
//  */
// export enum PlayerPosition {
//   // 门将 (1个)
//   GK = 'GK',   // Goalkeeper - 门将
//
//   // 后卫线 (3个)
//   CB = 'CB',   // Center Back - 中后卫 (对应old项目的DC)
//   LB = 'LB',   // Left Back - 左后卫 (对应old项目的DL)
//   RB = 'RB',   // Right Back - 右后卫 (对应old项目的DR)
//
//   // 中场线 (5个)
//   CDM = 'CDM', // Central Defensive Midfielder - 后腰 (对应old项目的DM)
//   CM = 'CM',   // Central Midfielder - 中场 (对应old项目的MC)
//   CAM = 'CAM', // Central Attacking Midfielder - 前腰 (对应old项目的AM)
//   LM = 'LM',   // Left Midfielder - 左中场 (对应old项目的ML)
//   RM = 'RM',   // Right Midfielder - 右中场 (对应old项目的MR)
//
//   // 边锋线 (2个)
//   LW = 'LW',   // Left Winger - 左边锋 (对应old项目的WL)
//   RW = 'RW',   // Right Winger - 右边锋 (对应old项目的WR)
//
//   // 前锋线 (1个)
//   ST = 'ST',   // Striker - 前锋 (对应old项目的ST)
// }
//
// /**
//  * 位置ID映射 - 严格基于配置表PositionMatch.json的ID
//  * 与old项目和现有配置完全兼容
//  */
// export const POSITION_ID_MAP: Record<PlayerPosition, number> = {
//   [PlayerPosition.GK]: 1,   // 门将
//   [PlayerPosition.CB]: 2,   // 中后卫 (对应配置表ID=2)
//   [PlayerPosition.LB]: 3,   // 左后卫 (对应配置表ID=3)
//   [PlayerPosition.RB]: 4,   // 右后卫 (对应配置表ID=4)
//   [PlayerPosition.CDM]: 5,  // 后腰 (对应配置表ID=5)
//   [PlayerPosition.CM]: 6,   // 中场 (对应配置表ID=6)
//   [PlayerPosition.CAM]: 7,  // 前腰 (对应配置表ID=7)
//   [PlayerPosition.LM]: 8,   // 左中场 (对应配置表ID=8)
//   [PlayerPosition.RM]: 9,   // 右中场 (对应配置表ID=9)
//   [PlayerPosition.LW]: 10,  // 左边锋 (对应配置表ID=10)
//   [PlayerPosition.RW]: 11,  // 右边锋 (对应配置表ID=11)
//   [PlayerPosition.ST]: 12,  // 前锋 (对应配置表ID=12)
// };
//
// /**
//  * 反向ID映射 - 从数字ID获取位置
//  */
// export const ID_POSITION_MAP: Record<number, PlayerPosition> = Object.fromEntries(
//   Object.entries(POSITION_ID_MAP).map(([position, id]) => [id, position as PlayerPosition])
// );
//
// /**
//  * 位置中文名称映射 - 严格基于配置表中的中文名称
//  */
// export const POSITION_NAME_MAP: Record<PlayerPosition, string> = {
//   [PlayerPosition.GK]: '门将',
//   [PlayerPosition.CB]: '中后卫',
//   [PlayerPosition.LB]: '左后卫',
//   [PlayerPosition.RB]: '右后卫',
//   [PlayerPosition.CDM]: '后腰',
//   [PlayerPosition.CM]: '中前卫',  // 配置表中使用"中前卫"
//   [PlayerPosition.CAM]: '前腰',
//   [PlayerPosition.LM]: '左前卫',  // 配置表中使用"左前卫"
//   [PlayerPosition.RM]: '右前卫',  // 配置表中使用"右前卫"
//   [PlayerPosition.LW]: '左边锋',
//   [PlayerPosition.RW]: '右边锋',
//   [PlayerPosition.ST]: '中锋',    // 配置表中使用"中锋"
// };
//
// /**
//  * 位置分组 - 按场上区域分类（基于12个位置）
//  */
// export const POSITION_GROUPS = {
//   GOALKEEPER: [PlayerPosition.GK],
//   DEFENDERS: [PlayerPosition.CB, PlayerPosition.LB, PlayerPosition.RB],
//   MIDFIELDERS: [PlayerPosition.CDM, PlayerPosition.CM, PlayerPosition.CAM, PlayerPosition.LM, PlayerPosition.RM],
//   WINGERS: [PlayerPosition.LW, PlayerPosition.RW],
//   FORWARDS: [PlayerPosition.ST],
// } as const;
//
// /**
//  * 阵型位置映射 - 严格基于formation.schema.ts中的PositionToHerosObject
//  * 将数据库字段映射到标准位置枚举
//  */
// export const FORMATION_POSITION_MAP: Record<string, PlayerPosition> = {
//   // 严格对应formation.schema.ts中的12个字段
//   'gk': PlayerPosition.GK,     // 门将
//   'cb': PlayerPosition.CB,     // 中后卫
//   'lb': PlayerPosition.LB,     // 左后卫
//   'rb': PlayerPosition.RB,     // 右后卫
//   'cdm': PlayerPosition.CDM,   // 后腰
//   'cm': PlayerPosition.CM,     // 中场
//   'cam': PlayerPosition.CAM,   // 前腰
//   'lm': PlayerPosition.LM,     // 左中场
//   'rm': PlayerPosition.RM,     // 右中场
//   'lw': PlayerPosition.LW,     // 左边锋
//   'rw': PlayerPosition.RW,     // 右边锋
//   'st': PlayerPosition.ST,     // 前锋
// };
//
// /**
//  * 反向阵型位置映射
//  */
// export const REVERSE_FORMATION_POSITION_MAP: { [p: string]: PlayerPosition } = Object.fromEntries(
//   Object.entries(FORMATION_POSITION_MAP).map(([key, position]) => [position, key])
// );
//
// /**
//  * old项目兼容性映射 - 完整的12个位置映射
//  * 将old项目的位置标识映射到新的标准位置
//  */
// export const OLD_PROJECT_COMPATIBILITY_MAP: Record<string, PlayerPosition> = {
//   // old项目的大写格式 (基于PositionMatch.json)
//   'GK': PlayerPosition.GK,   // 门将
//   'DC': PlayerPosition.CB,   // old项目用DC表示中后卫
//   'DL': PlayerPosition.LB,   // old项目用DL表示左后卫
//   'DR': PlayerPosition.RB,   // old项目用DR表示右后卫
//   'DM': PlayerPosition.CDM,  // old项目用DM表示后腰
//   'MC': PlayerPosition.CM,   // old项目用MC表示中前卫
//   'ML': PlayerPosition.LM,   // old项目用ML表示左前卫
//   'MR': PlayerPosition.RM,   // old项目用MR表示右前卫
//   'AM': PlayerPosition.CAM,  // old项目用AM表示前腰
//   'WL': PlayerPosition.LW,   // old项目用WL表示左边锋
//   'WR': PlayerPosition.RW,   // old项目用WR表示右边锋
//   'ST': PlayerPosition.ST,   // 前锋保持一致
//
//   // 小写格式兼容 (基于formation.schema.ts)
//   'gk': PlayerPosition.GK,
//   'cb': PlayerPosition.CB,   // 新项目直接使用cb
//   'lb': PlayerPosition.LB,   // 新项目直接使用lb
//   'rb': PlayerPosition.RB,   // 新项目直接使用rb
//   'cdm': PlayerPosition.CDM, // 新项目直接使用cdm
//   'cm': PlayerPosition.CM,   // 新项目直接使用cm
//   'lm': PlayerPosition.LM,   // 新项目直接使用lm
//   'rm': PlayerPosition.RM,   // 新项目直接使用rm
//   'cam': PlayerPosition.CAM, // 新项目直接使用cam
//   'lw': PlayerPosition.LW,   // 新项目直接使用lw
//   'rw': PlayerPosition.RW,   // 新项目直接使用rw
//   'st': PlayerPosition.ST,   // 新项目直接使用st
//
//   // 兼容old项目的小写格式
//   'dc': PlayerPosition.CB,
//   'dl': PlayerPosition.LB,
//   'dr': PlayerPosition.RB,
//   'dm': PlayerPosition.CDM,
//   'mc': PlayerPosition.CM,
//   'ml': PlayerPosition.LM,
//   'mr': PlayerPosition.RM,
//   'am': PlayerPosition.CAM,
//   'wl': PlayerPosition.LW,
//   'wr': PlayerPosition.RW,
// };
//
// /**
//  * 工具函数：获取位置中文名称
//  */
// export function getPositionName(position: PlayerPosition | string): string {
//   // 如果是字符串，先尝试转换为标准位置
//   if (typeof position === 'string') {
//     const standardPosition = OLD_PROJECT_COMPATIBILITY_MAP[position] ||
//                            FORMATION_POSITION_MAP[position.toLowerCase()];
//     if (standardPosition) {
//       return POSITION_NAME_MAP[standardPosition];
//     }
//   }
//
//   return POSITION_NAME_MAP[position as PlayerPosition] || '未知位置';
// }
//
// /**
//  * 工具函数：获取位置ID
//  */
// export function getPositionId(position: PlayerPosition | string): number {
//   // 如果是字符串，先尝试转换为标准位置
//   if (typeof position === 'string') {
//     const standardPosition = OLD_PROJECT_COMPATIBILITY_MAP[position] ||
//                            FORMATION_POSITION_MAP[position.toLowerCase()];
//     if (standardPosition) {
//       return POSITION_ID_MAP[standardPosition];
//     }
//   }
//
//   return POSITION_ID_MAP[position as PlayerPosition] || 0;
// }
//
// /**
//  * 工具函数：从ID获取位置
//  */
// export function getPositionFromId(id: number): PlayerPosition | null {
//   return ID_POSITION_MAP[id] || null;
// }
//
// /**
//  * 工具函数：检查位置是否属于某个分组
//  */
// export function isPositionInGroup(position: PlayerPosition, group: keyof typeof POSITION_GROUPS): boolean {
//   return POSITION_GROUPS[group].includes(position);
// }
//
// /**
//  * 工具函数：获取位置的阵型映射键
//  */
// export function getFormationKey(position: PlayerPosition): string {
//   return REVERSE_FORMATION_POSITION_MAP[position] || position.toLowerCase();
// }
//
// /**
//  * 类型定义：位置分组类型
//  */
// export type PositionGroup = keyof typeof POSITION_GROUPS;
//
// /**
//  * 类型定义：阵型位置键类型
//  */
// export type FormationPositionKey = keyof typeof FORMATION_POSITION_MAP;
