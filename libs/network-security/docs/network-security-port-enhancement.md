# Network-Security 端口安全增强文档

## 📋 **概述**

本文档描述了Network-Security库的端口安全增强功能，这些功能专门为动态端口架构设计，提供额外的安全保护层。

## 🎯 **核心功能**

### **1. 端口范围验证**
- 验证端口是否在允许的范围内
- 支持多个端口范围配置
- 支持单个端口和端口范围混合配置

### **2. 动态端口安全验证**
- 专门针对动态计算端口的验证
- 集成PortManager进行端口计算验证
- 支持区服和实例级别的端口验证

### **3. 端口安全监控**
- 实时监控端口访问统计
- 记录端口违规行为
- 提供安全健康状态检查

## ⚙️ **配置说明**

### **环境变量配置**

```bash
# 端口安全功能开关
PORT_SECURITY_ENABLED=true

# 允许的端口范围（支持多种格式）
ALLOWED_PORT_RANGES=3000-4000,8000-9000,5432,6379

# 动态端口验证开关
VALIDATE_DYNAMIC_PORTS=true

# 是否阻断未授权端口访问
BLOCK_UNAUTHORIZED_PORTS=true

# 是否记录端口违规日志
LOG_PORT_VIOLATIONS=true
```

### **端口范围配置格式**

支持以下格式：
- **端口范围**: `3000-4000` (端口3000到4000)
- **单个端口**: `5432` (仅端口5432)
- **混合配置**: `3000-4000,8000-9000,5432,6379`

### **推荐配置示例**

#### **开发环境**
```bash
PORT_SECURITY_ENABLED=false
VALIDATE_DYNAMIC_PORTS=false
BLOCK_UNAUTHORIZED_PORTS=false
LOG_PORT_VIOLATIONS=true
```

#### **测试环境**
```bash
PORT_SECURITY_ENABLED=true
ALLOWED_PORT_RANGES=3000-5000,8000-9000
VALIDATE_DYNAMIC_PORTS=true
BLOCK_UNAUTHORIZED_PORTS=false
LOG_PORT_VIOLATIONS=true
```

#### **生产环境**
```bash
PORT_SECURITY_ENABLED=true
ALLOWED_PORT_RANGES=3200-3800,8080,8443
VALIDATE_DYNAMIC_PORTS=true
BLOCK_UNAUTHORIZED_PORTS=true
LOG_PORT_VIOLATIONS=true
```

## 🔧 **使用方法**

### **1. 基础集成**

```typescript
// 在服务的main.ts中集成端口安全中间件
import { PortSecurityMiddleware } from '@libs/network-security';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 应用端口安全中间件（可选）
  app.use(new PortSecurityMiddleware(
    app.get(ConfigService),
    app.get(PortSecurityValidator)
  ));
  
  await app.listen(port);
}
```

### **2. 端口验证服务**

```typescript
import { PortSecurityValidator } from '@libs/network-security';

@Injectable()
export class MyService {
  constructor(
    private readonly portSecurityValidator: PortSecurityValidator
  ) {}

  async validateServicePort(port: number, serviceName: string): Promise<boolean> {
    return this.portSecurityValidator.validatePortRange(port, serviceName);
  }

  async validateDynamicPort(port: number, serverId: string, serviceName: string): Promise<boolean> {
    return this.portSecurityValidator.validateDynamicPort(port, serverId, serviceName);
  }
}
```

### **3. 端口安全监控**

```typescript
import { PortSecurityMonitorService } from '@libs/network-security';

@Injectable()
export class SecurityService {
  constructor(
    private readonly portMonitor: PortSecurityMonitorService
  ) {}

  getSecurityStats() {
    return this.portMonitor.getStats();
  }

  getHealthStatus() {
    return this.portMonitor.getHealthStatus();
  }

  getRecentViolations() {
    return this.portMonitor.getRecentViolations(20);
  }
}
```

## 📊 **监控和统计**

### **统计指标**

- `totalValidations`: 总验证次数
- `successfulValidations`: 成功验证次数
- `failedValidations`: 失败验证次数
- `blockedPorts`: 阻断的端口访问次数
- `violationsByType`: 按类型分组的违规统计
- `mostViolatedPorts`: 最常违规的端口列表

### **违规类型**

- `INVALID_PORT`: 端口号无效
- `OUT_OF_RANGE`: 端口不在允许范围内
- `DYNAMIC_INVALID`: 动态端口验证失败
- `UNAUTHORIZED`: 未授权的端口访问

### **健康检查**

端口安全监控服务提供健康状态检查：

```typescript
const health = portMonitor.getHealthStatus();
console.log('健康状态:', health.healthy);
console.log('问题列表:', health.issues);
console.log('统计数据:', health.stats);
```

## 🚨 **安全建议**

### **生产环境最佳实践**

1. **启用端口安全**: 在生产环境中始终启用端口安全功能
2. **严格的端口范围**: 配置最小必要的端口范围
3. **启用阻断功能**: 在生产环境中启用未授权端口阻断
4. **监控告警**: 设置端口违规的监控告警
5. **定期审查**: 定期审查端口访问日志和统计数据

### **端口范围规划**

- **微服务端口**: 3200-3800 (为8个微服务预留，每个服务100个端口)
- **数据库端口**: 5432, 6379, 27017
- **监控端口**: 9090, 9100, 3000
- **负载均衡**: 80, 443, 8080, 8443

### **安全配置检查清单**

- [ ] 端口安全功能已启用
- [ ] 配置了合适的端口范围
- [ ] 生产环境启用了端口阻断
- [ ] 端口违规日志记录已启用
- [ ] 设置了端口安全监控告警
- [ ] 定期检查端口访问统计

## 🔍 **故障排除**

### **常见问题**

#### **Q: 端口验证总是失败**
A: 检查 `ALLOWED_PORT_RANGES` 配置是否包含目标端口范围

#### **Q: 动态端口验证不工作**
A: 确保 `VALIDATE_DYNAMIC_PORTS=true` 且PortManager正确配置

#### **Q: 监控数据不更新**
A: 检查端口安全功能是否启用，以及是否有实际的端口验证请求

### **调试方法**

1. **启用调试日志**:
   ```bash
   SECURITY_LOG_LEVEL=debug
   ```

2. **检查配置**:
   ```typescript
   const validator = app.get(PortSecurityValidator);
   console.log('允许的端口范围:', validator.getAllowedPortRanges());
   ```

3. **查看统计数据**:
   ```typescript
   const monitor = app.get(PortSecurityMonitorService);
   console.log('安全统计:', monitor.getStats());
   ```

## 📈 **性能影响**

端口安全增强功能的性能影响极小：

- **端口验证**: < 1ms per validation
- **内存占用**: < 10MB for monitoring data
- **CPU开销**: < 0.1% additional overhead

## 🔄 **版本兼容性**

- **向后兼容**: 完全向后兼容现有的Network-Security功能
- **可选功能**: 所有端口安全功能都是可选的，默认禁用
- **渐进式启用**: 可以逐步启用各项安全功能

## 📝 **更新日志**

### **v1.1.0 - 端口安全增强**
- ✅ 新增端口范围验证功能
- ✅ 新增动态端口安全验证
- ✅ 新增端口安全监控服务
- ✅ 新增端口安全中间件
- ✅ 扩展配置接口支持端口安全
- ✅ 完善文档和使用示例

---

**Network-Security库现在提供了完整的端口安全保护，为动态端口架构提供了额外的安全保障层。**
