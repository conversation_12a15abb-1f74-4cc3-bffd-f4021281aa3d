import { Injectable, NestMiddleware, ForbiddenException, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { isIP } from 'net';

interface IPNetwork {
  network: string;
  cidr?: number;
  isRange: boolean;
}

@Injectable()
export class IPWhitelistMiddleware implements NestMiddleware {
  private readonly logger = new Logger(IPWhitelistMiddleware.name);
  private readonly allowedNetworks: IPNetwork[];
  private readonly trustedProxies: Set<string>;

  constructor(private readonly configService: ConfigService) {
    // 从环境变量加载IP白名单配置
    const allowedIPsStr = this.configService.get<string>('ALLOWED_IPS', '127.0.0.1');
    const trustedProxiesStr = this.configService.get<string>('TRUSTED_PROXIES', '');
    
    this.allowedNetworks = this.parseNetworkConfig(allowedIPsStr);
    this.trustedProxies = new Set(trustedProxiesStr.split(',').map(ip => ip.trim()).filter(Boolean));

    this.logger.log(`Loaded ${this.allowedNetworks.length} allowed networks`);
    this.logger.log(`Loaded ${this.trustedProxies.size} trusted proxies`);
  }

  use(req: Request, res: Response, next: NextFunction) {
    const clientIP = this.extractClientIP(req);

    // 开发环境跳过检查
    if (this.isDevelopmentMode() && this.isLocalIP(clientIP)) {
      this.logger.debug(`Development mode: allowing local IP ${clientIP}`);
      next();
      return;
    }

    const isAllowed = this.isIPAllowed(clientIP);

    if (!isAllowed) {
      this.logger.warn(`Access denied for IP: ${clientIP}, User-Agent: ${req.headers['user-agent']}`);
      throw new ForbiddenException(`Access denied for IP: ${clientIP}`);
    }

    // 记录成功访问
    this.logger.debug(`Access granted for IP: ${clientIP}`);
    next();
  }

  /**
   * 增强的IP提取逻辑，支持代理链和IPv6
   */
  private extractClientIP(req: Request): string {
    // 1. 检查 X-Forwarded-For 头（支持代理链）
    const xForwardedFor = req.headers['x-forwarded-for'];
    if (xForwardedFor) {
      const ips = xForwardedFor.toString().split(',').map(ip => ip.trim());
      // 从右到左查找第一个非信任代理的IP
      for (let i = ips.length - 1; i >= 0; i--) {
        const ip = this.normalizeIP(ips[i]);
        if (this.isValidIP(ip) && !this.trustedProxies.has(ip)) {
          return ip;
        }
      }
      // 如果都是信任代理，返回最左边的IP
      const firstIP = this.normalizeIP(ips[0]);
      if (this.isValidIP(firstIP)) {
        return firstIP;
      }
    }

    // 2. 检查 X-Real-IP 头
    const xRealIP = req.headers['x-real-ip'];
    if (xRealIP) {
      const ip = this.normalizeIP(xRealIP.toString());
      if (this.isValidIP(ip)) {
        return ip;
      }
    }

    // 3. 检查连接信息
    const connectionIP = req.connection?.remoteAddress || req.socket?.remoteAddress;
    if (connectionIP) {
      const ip = this.normalizeIP(connectionIP);
      if (this.isValidIP(ip)) {
        return ip;
      }
    }

    // 4. 默认返回本地IP
    return '127.0.0.1';
  }

  /**
   * 解析网络配置，支持CIDR和IP范围
   */
  private parseNetworkConfig(configStr: string): IPNetwork[] {
    return configStr.split(',')
      .map(item => item.trim())
      .filter(Boolean)
      .map(item => {
        // 支持CIDR格式 (***********/24)
        if (item.includes('/')) {
          const [network, cidr] = item.split('/');
          return {
            network: this.normalizeIP(network),
            cidr: parseInt(cidr, 10),
            isRange: true
          };
        }
        // 单个IP地址
        return {
          network: this.normalizeIP(item),
          isRange: false
        };
      })
      .filter(item => this.isValidIP(item.network));
  }

  /**
   * 检查IP是否在允许列表中
   */
  private isIPAllowed(ip: string): boolean {
    const normalizedIP = this.normalizeIP(ip);
    
    for (const network of this.allowedNetworks) {
      if (!network.isRange) {
        // 精确匹配
        if (network.network === normalizedIP) {
          return true;
        }
      } else {
        // CIDR匹配
        if (this.isIPInCIDR(normalizedIP, network.network, network.cidr!)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 规范化IP地址（移除IPv6的::ffff:前缀）
   */
  private normalizeIP(ip: string): string {
    if (!ip) return '';
    
    // 移除IPv4映射的IPv6前缀
    if (ip.startsWith('::ffff:')) {
      return ip.substring(7);
    }
    
    // 移除端口号
    if (ip.includes(':') && !ip.includes('::')) {
      const lastColonIndex = ip.lastIndexOf(':');
      const possiblePort = ip.substring(lastColonIndex + 1);
      if (/^\d+$/.test(possiblePort)) {
        return ip.substring(0, lastColonIndex);
      }
    }
    
    return ip;
  }

  /**
   * 验证IP地址格式
   */
  private isValidIP(ip: string): boolean {
    return isIP(ip) !== 0;
  }

  /**
   * 检查是否为本地IP
   */
  private isLocalIP(ip: string): boolean {
    return ip === '127.0.0.1' || ip === '::1' || ip === 'localhost';
  }

  /**
   * 检查是否为开发模式
   */
  private isDevelopmentMode(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * 检查IP是否在CIDR网段内
   */
  private isIPInCIDR(ip: string, network: string, cidr: number): boolean {
    // 简化实现，仅支持IPv4 CIDR
    if (isIP(ip) !== 4 || isIP(network) !== 4) {
      return false;
    }

    const ipNum = this.ipToNumber(ip);
    const networkNum = this.ipToNumber(network);
    const mask = (0xffffffff << (32 - cidr)) >>> 0;

    return (ipNum & mask) === (networkNum & mask);
  }

  /**
   * 将IPv4地址转换为数字
   */
  private ipToNumber(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
  }
}
