import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoadBalancingUtils } from './load-balancing.utils';

/**
 * 通用负载均衡策略接口
 */
export interface BaseLoadBalancingStrategy<T> {
  name: string;
  selectInstance(instances: T[], context?: any): T | null;
}

/**
 * 负载均衡基类
 * 
 * 提供通用的负载均衡功能：
 * - 通用策略实现（轮询、加权、随机、哈希等）
 * - 策略管理和注册
 * - 轮询计数器管理
 * - 统计信息收集
 */
export abstract class BaseLoadBalancerService<T, S extends BaseLoadBalancingStrategy<T>> {
  protected readonly logger = new Logger(this.constructor.name);
  
  // 负载均衡策略注册表
  protected readonly strategies = new Map<string, S>();
  
  // 轮询计数器：Map<counterKey, counter>
  protected readonly roundRobinCounters = new Map<string, number>();
  
  // 默认策略
  protected readonly defaultStrategy: string;
  
  constructor(
    protected readonly configService: ConfigService,
    defaultStrategyConfigKey: string,
    defaultStrategyValue: string = 'round-robin'
  ) {
    this.defaultStrategy = this.configService.get<string>(defaultStrategyConfigKey, defaultStrategyValue);
    this.initializeCommonStrategies();
    this.initializeSpecificStrategies();
    
    this.logger.log(`🎯 负载均衡器启动，默认策略: ${this.defaultStrategy}`);
  }

  /**
   * 📋 获取可用策略列表
   */
  getAvailableStrategies(): string[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * ➕ 注册自定义策略
   */
  registerStrategy(strategy: S): void {
    this.strategies.set(strategy.name, strategy);
    this.logger.log(`📝 注册负载均衡策略: ${strategy.name}`);
  }

  /**
   * ➖ 移除策略
   */
  removeStrategy(strategyName: string): boolean {
    const removed = this.strategies.delete(strategyName);
    if (removed) {
      this.logger.log(`🗑️ 移除负载均衡策略: ${strategyName}`);
    }
    return removed;
  }

  /**
   * 🔧 初始化通用策略 - 所有负载均衡器共享
   */
  protected initializeCommonStrategies(): void {
    // 轮询策略
    this.strategies.set('round-robin', this.createStrategy('round-robin', (instances: T[], context?: any) => {
      if (instances.length === 0) return null;
      
      const counterKey = context?.counterKey || context?.serviceName || 'default';
      const counter = this.roundRobinCounters.get(counterKey) || 0;
      const selectedInstance = LoadBalancingUtils.roundRobinSelect(instances, counter);
      
      this.roundRobinCounters.set(counterKey, counter + 1);
      return selectedInstance;
    }));

    // 加权随机策略
    this.strategies.set('weighted', this.createStrategy('weighted', (instances: T[]) => {
      return LoadBalancingUtils.weightedRandomSelect(instances as any) as T;
    }));

    // 随机策略
    this.strategies.set('random', this.createStrategy('random', (instances: T[]) => {
      const shuffled = LoadBalancingUtils.shuffle(instances);
      return shuffled[0] || null;
    }));

    // IP哈希策略
    this.strategies.set('ip-hash', this.createStrategy('ip-hash', (instances: T[], context?: any) => {
      const ip = context?.ip || '127.0.0.1';
      return LoadBalancingUtils.consistentHashSelect(instances, ip);
    }));

    // 用户哈希策略（会话保持）
    this.strategies.set('user-hash', this.createStrategy('user-hash', (instances: T[], context?: any) => {
      const userId = context?.userId || context?.sessionId || '0';
      return LoadBalancingUtils.consistentHashSelect(instances, userId);
    }));

    // 最少连接策略（如果实例支持connections字段）
    this.strategies.set('least-connections', this.createStrategy('least-connections', (instances: T[]) => {
      const sorted = LoadBalancingUtils.sortByConnections(instances as any, true);
      return (sorted[0] || null) as T;
    }));

    // 响应时间策略（如果实例支持responseTime字段）
    this.strategies.set('response-time', this.createStrategy('response-time', (instances: T[]) => {
      const sorted = LoadBalancingUtils.sortByResponseTime(instances as any, true);
      return (sorted[0] || null) as T;
    }));

    // 权重优先策略
    this.strategies.set('weight-priority', this.createStrategy('weight-priority', (instances: T[]) => {
      const sorted = LoadBalancingUtils.sortByWeight(instances as any, false);
      return (sorted[0] || null) as T;
    }));

    // 健康度策略（综合评分）
    this.strategies.set('health-score', this.createStrategy('health-score', (instances: T[]) => {
      if (instances.length === 0) return null;
      
      const scoredInstances = instances.map(instance => ({
        instance,
        score: LoadBalancingUtils.calculateHealthScore(instance as any, {
          responseTimeWeight: 1,
          connectionWeight: 10,
          weightBonus: 5
        })
      }));

      // 选择分数最低的（最健康的）
      const best = scoredInstances.reduce((min, current) => 
        current.score < min.score ? current : min
      );

      return best.instance;
    }));

    this.logger.log(`🔧 通用策略初始化完成，共 ${this.strategies.size} 个策略`);
  }

  /**
   * 🚀 智能选择算法 - 多维度排序（子类可重写）
   */
  protected intelligentSelect(instances: T[], context?: any): T | null {
    if (instances.length === 0) return null;

    // 默认智能选择：健康度 > 权重 > 连接数 > 响应时间
    const sortRules = this.getIntelligentSortRules(context);
    const sorted = LoadBalancingUtils.multiDimensionSort(instances as any, sortRules);
    return (sorted[0] || null) as T;
  }

  /**
   * 📊 获取智能排序规则 - 子类可重写
   */
  protected getIntelligentSortRules(context?: any): Array<{
    key: any;
    ascending?: boolean;
    weight?: number;
  }> {
    return [
      { key: 'healthy', ascending: false, weight: 1000 },      // 健康度优先
      { key: 'weight', ascending: false, weight: 100 },        // 权重优先
      { key: 'connections', ascending: true, weight: 10 },     // 少连接优先
      { key: 'responseTime', ascending: true, weight: 5 },     // 快响应优先
    ];
  }

  /**
   * 🏭 策略工厂方法 - 子类实现
   */
  protected abstract createStrategy(name: string, selectFn: (instances: T[], context?: any) => T | null): S;

  /**
   * 🔧 初始化特定策略 - 子类实现
   */
  protected abstract initializeSpecificStrategies(): void;

  /**
   * 🎯 选择实例的核心逻辑 - 子类实现
   */
  abstract selectInstance(...args: any[]): Promise<T | null>;
}
