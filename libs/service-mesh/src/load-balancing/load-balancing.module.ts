import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GlobalServiceLoadBalancerService } from './global-service-load-balancer.service';
import { ServerAwareLoadBalancerService } from './server-aware-load-balancer.service';

/**
 * 负载均衡模块
 * 
 * 提供：
 * - 全局服务负载均衡器
 * - 区服感知负载均衡器
 * - 通用负载均衡工具和接口
 */
@Module({
  imports: [
    ConfigModule,
  ],
  providers: [
    GlobalServiceLoadBalancerService,
    ServerAwareLoadBalancerService,
  ],
  exports: [
    GlobalServiceLoadBalancerService,
    ServerAwareLoadBalancerService,
  ],
})
export class LoadBalancingModule {}
