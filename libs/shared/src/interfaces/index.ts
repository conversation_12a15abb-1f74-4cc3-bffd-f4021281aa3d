// Core interfaces
import {HeroPosition} from "@libs/game-constants";

export interface BaseEntity {
  _id?: string;
  createdAt: Date;
  updatedAt: Date;
}

// WebSocket Message Interface
export interface WSMessage<T = any> {
  id: string;
  type: MessageType;
  service: string;
  action: string;
  payload: T;
  timestamp: number;
  userId?: string;
}

export enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  NOTIFICATION = 'notification',
  ERROR = 'error',
}

// User Interfaces
export interface User extends BaseEntity {
  username: string;
  email: string;
  passwordHash: string;
  profile: UserProfile;
  assets: UserAssets;
  status: UserStatus;
}

export interface UserProfile {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  country?: string;
  language: string;
  timezone: string;
}

export interface UserAssets {
  coins: number;
  gems: number;
  experience: number;
  level: number;
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  SUSPENDED = 'suspended',
}

// Club Interfaces
export interface Club extends BaseEntity {
  userId: string;
  name: string;
  logo?: string;
  level: number;
  budget: number;
  reputation: number;
  players: Player[];
  facilities: Facility[];
  staff: Staff[];
  formation: Formation;
}

export interface Player extends BaseEntity {
  name: string;
  position: HeroPosition;
  age: number;
  nationality: string;
  stats: PlayerStats;
  skills: Skill[];
  value: number;
  salary: number;
  contract: Contract;
  injury?: Injury;
}

export interface PlayerStats {
  overall: number;
  pace: number;
  shooting: number;
  passing: number;
  dribbling: number;
  defending: number;
  physical: number;
}

export interface Skill {
  id: string;
  name: string;
  level: number;
  maxLevel: number;
}

export interface Contract {
  startDate: Date;
  endDate: Date;
  salary: number;
  bonuses: ContractBonus[];
}

export interface ContractBonus {
  type: BonusType;
  condition: string;
  amount: number;
}

export enum BonusType {
  GOAL = 'goal',
  ASSIST = 'assist',
  CLEAN_SHEET = 'clean_sheet',
  WIN = 'win',
  APPEARANCE = 'appearance',
}

export interface Injury {
  type: string;
  severity: InjurySeverity;
  recoveryDate: Date;
}

export enum InjurySeverity {
  MINOR = 'minor',
  MODERATE = 'moderate',
  MAJOR = 'major',
  SEVERE = 'severe',
}

export interface Formation {
  name: string;
  positions: FormationPosition[];
}

export interface FormationPosition {
  position: HeroPosition;
  x: number;
  y: number;
  playerId?: string;
}

export interface Facility extends BaseEntity {
  type: FacilityType;
  level: number;
  maxLevel: number;
  effect: FacilityEffect;
  maintenanceCost: number;
}

export enum FacilityType {
  TRAINING_GROUND = 'training_ground',
  YOUTH_ACADEMY = 'youth_academy',
  MEDICAL_CENTER = 'medical_center',
  STADIUM = 'stadium',
  SCOUTING_NETWORK = 'scouting_network',
}

export interface FacilityEffect {
  type: string;
  value: number;
}

export interface Staff extends BaseEntity {
  name: string;
  role: StaffRole;
  level: number;
  salary: number;
  contract: Contract;
  specialties: string[];
}

export enum StaffRole {
  HEAD_COACH = 'head_coach',
  ASSISTANT_COACH = 'assistant_coach',
  FITNESS_COACH = 'fitness_coach',
  GOALKEEPER_COACH = 'goalkeeper_coach',
  SCOUT = 'scout',
  DOCTOR = 'doctor',
  PHYSIOTHERAPIST = 'physiotherapist',
}

// Match Interfaces
export interface Match extends BaseEntity {
  homeClubId: string;
  awayClubId: string;
  matchType: MatchType;
  competition?: string;
  round?: number;
  status: MatchStatus;
  scheduledAt: Date;
  kickoffAt?: Date;
  completedAt?: Date;
  venue: string;
  weather?: Weather;
  result?: MatchResult;
  events: MatchEvent[];
  lineups: MatchLineup[];
  statistics: MatchStatistics;
}

export enum MatchType {
  FRIENDLY = 'friendly',
  LEAGUE = 'league',
  CUP = 'cup',
  TOURNAMENT = 'tournament',
  PLAYOFF = 'playoff',
}

export enum MatchStatus {
  SCHEDULED = 'scheduled',
  LIVE = 'live',
  HALF_TIME = 'half_time',
  COMPLETED = 'completed',
  POSTPONED = 'postponed',
  CANCELLED = 'cancelled',
}

export interface Weather {
  condition: WeatherCondition;
  temperature: number;
  humidity: number;
  windSpeed: number;
}

export enum WeatherCondition {
  SUNNY = 'sunny',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
}

export interface MatchResult {
  homeScore: number;
  awayScore: number;
  winner?: string; // clubId or 'draw'
  penalties?: PenaltyShootout;
}

export interface PenaltyShootout {
  homeScore: number;
  awayScore: number;
  penalties: PenaltyAttempt[];
}

export interface PenaltyAttempt {
  playerId: string;
  clubId: string;
  scored: boolean;
  order: number;
}

export interface MatchEvent {
  id: string;
  type: MatchEventType;
  minute: number;
  playerId?: string;
  clubId: string;
  description: string;
  data?: any;
}

export enum MatchEventType {
  GOAL = 'goal',
  ASSIST = 'assist',
  YELLOW_CARD = 'yellow_card',
  RED_CARD = 'red_card',
  SUBSTITUTION = 'substitution',
  INJURY = 'injury',
  OFFSIDE = 'offside',
  FOUL = 'foul',
  CORNER = 'corner',
  FREE_KICK = 'free_kick',
  PENALTY = 'penalty',
  SAVE = 'save',
}

export interface MatchLineup {
  clubId: string;
  formation: string;
  players: LineupHero[];
  substitutes: LineupHero[];
}

export interface LineupHero {
  playerId: string;
  position: HeroPosition;
  jerseyNumber: number;
  captain?: boolean;
}

export interface MatchStatistics {
  homeStats: TeamStatistics;
  awayStats: TeamStatistics;
}

export interface TeamStatistics {
  possession: number;
  shots: number;
  shotsOnTarget: number;
  corners: number;
  fouls: number;
  yellowCards: number;
  redCards: number;
  passes: number;
  passAccuracy: number;
  tackles: number;
  interceptions: number;
}

// Card Interfaces
export interface Card extends BaseEntity {
  playerId: string;
  rarity: CardRarity;
  stats: PlayerStats;
  skills: Skill[];
  specialAbilities: SpecialAbility[];
  condition: number;
  level: number;
  experience: number;
}

export enum CardRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHIC = 'mythic',
}

export interface SpecialAbility {
  id: string;
  name: string;
  description: string;
  effect: AbilityEffect;
  cooldown: number;
}

export interface AbilityEffect {
  type: string;
  value: number;
  duration?: number;
  target: string;
}

// Game Session Interface
export interface GameSession {
  userId: string;
  sessionId: string;
  socketId: string;
  connectedAt: Date;
  lastActivity: Date;
  status: SessionStatus;
}

export enum SessionStatus {
  ACTIVE = 'active',
  IDLE = 'idle',
  DISCONNECTED = 'disconnected',
}

// Service Response Interface
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  message?: string;
  timestamp: number;
}

export interface ServiceError {
  code: string;
  message: string;
  details?: any;
}

// 分页接口 - 统一版本，包含完整功能
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
