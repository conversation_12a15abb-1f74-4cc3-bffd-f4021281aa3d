import { IsString, IsOptional, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>, IsIn, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MessageType } from '../interfaces';

// 抽象层DTO - 只包含与业务逻辑无关的通用组件

// 基础实体抽象
export class BaseDto {
  @IsOptional()
  @IsString()
  _id?: string;
}

// 通用分页抽象 - 完整版本，包含API文档和验证
export class PaginationDto {
  @ApiPropertyOptional({ description: '页码', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

// WebSocket消息格式抽象
export class WSMessageDto<T = any> {
  @IsString()
  id: string;

  @IsEnum(MessageType)
  type: MessageType;

  @IsString()
  service: string;

  @IsString()
  action: string;

  payload: T;

  @IsNumber()
  timestamp: number;

  @IsOptional()
  @IsString()
  userId?: string;
}

// 基础响应格式抽象 - 完整版本，包含构造函数和静态方法
export class BaseResponseDto<T = any> {
  @ApiProperty({ description: '请求是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiPropertyOptional({ description: '响应数据' })
  data?: T;

  @ApiProperty({ description: '时间戳' })
  timestamp: string;

  @ApiPropertyOptional({ description: '请求ID' })
  requestId?: string;

  constructor(success: boolean, message: string, data?: T, requestId?: string) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
    this.requestId = requestId;
  }

  static success<T>(message: string, data?: T, requestId?: string): BaseResponseDto<T> {
    return new BaseResponseDto(true, message, data, requestId);
  }

  static error(message: string, requestId?: string): BaseResponseDto {
    return new BaseResponseDto(false, message, undefined, requestId);
  }
}

// 分页响应格式抽象 - 完整版本，包含便利属性和构造函数
export class PaginationResponseDto<T> {
  @ApiProperty({ description: '数据项' })
  items: T[];

  @ApiProperty({ description: '总数量' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;

  constructor(items: T[], total: number, page: number, limit: number) {
    this.items = items;
    this.total = total;
    this.page = page;
    this.limit = limit;
    this.totalPages = Math.ceil(total / limit);
    this.hasNext = page < this.totalPages;
    this.hasPrev = page > 1;
  }
}

// 错误响应格式抽象 - 完整版本，包含HTTP状态码和路径
export class ErrorResponseDto {
  @ApiProperty({ description: 'HTTP状态码' })
  statusCode: number;

  @ApiProperty({ description: '错误消息' })
  message: string;

  @ApiProperty({ description: '错误类型' })
  error: string;

  @ApiProperty({ description: '时间戳' })
  timestamp: string;

  @ApiProperty({ description: '请求路径' })
  path: string;

  @ApiPropertyOptional({ description: '请求ID' })
  requestId?: string;

  @ApiPropertyOptional({ description: '错误详情' })
  details?: any;

  constructor(
    statusCode: number,
    message: string,
    error: string,
    path: string,
    requestId?: string,
    details?: any
  ) {
    this.statusCode = statusCode;
    this.message = message;
    this.error = error;
    this.timestamp = new Date().toISOString();
    this.path = path;
    this.requestId = requestId;
    this.details = details;
  }
}

// 健康检查响应格式抽象 - 完整版本，包含构造函数
export class HealthCheckResponseDto {
  @ApiProperty({ description: '服务状态' })
  status: 'ok' | 'error' | 'shutting_down';

  @ApiPropertyOptional({ description: '服务信息' })
  info?: Record<string, any>;

  @ApiPropertyOptional({ description: '错误信息' })
  error?: Record<string, any>;

  @ApiPropertyOptional({ description: '详细信息' })
  details?: Record<string, any>;

  constructor(
    status: 'ok' | 'error' | 'shutting_down',
    info?: Record<string, any>,
    error?: Record<string, any>,
    details?: Record<string, any>
  ) {
    this.status = status;
    this.info = info;
    this.error = error;
    this.details = details;
  }
}

// 注意：业务相关的DTO（如LoginDto、RegisterDto、UserDto、PlayerDto等）
// 已移除，这些应该保留在各自的业务服务中，不属于抽象层
