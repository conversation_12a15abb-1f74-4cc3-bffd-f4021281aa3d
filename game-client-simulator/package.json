{"name": "game-client-simulator", "version": "1.0.0", "description": "游戏客户端模拟器 - 模拟真实玩家操作流程", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "scenario": "node src/cli.js scenario", "example": "node examples/basic-usage.js"}, "keywords": ["game", "testing", "simulation", "client", "websocket", "automation"], "author": "Game Development Team", "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "^8.38.0", "axios": "^1.11.0", "chalk": "^4.1.2", "commander": "^11.1.0", "glob": "^11.0.3", "lodash": "^4.17.21", "socket.io-client": "^4.8.1", "ts-morph": "^26.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "yaml": "^2.3.4"}, "devDependencies": {"@eslint/js": "^8.56.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!src/cli.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}