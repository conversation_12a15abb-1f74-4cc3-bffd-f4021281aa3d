/**
 * 最佳11人抽奖
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.buyBestFootball
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.227Z
 */

const BaseAction = require('../../../core/base-action');

class EventbuyBestFootballAction extends BaseAction {
  static metadata = {
    name: '最佳11人抽奖',
    description: '最佳11人抽奖',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.buyBestFootball',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, index, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      index,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '最佳11人抽奖成功'
      };
    } else {
      throw new Error(`最佳11人抽奖失败: ${response.message}`);
    }
  }
}

module.exports = EventbuyBestFootballAction;