/**
 * 获取荣誉排行榜
 * 
 * 微服务: activity
 * 模块: honor
 * Controller: honor
 * Pattern: honor.getRanking
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.250Z
 */

const BaseAction = require('../../../core/base-action');

class HonorgetRankingAction extends BaseAction {
  static metadata = {
    name: '获取荣誉排行榜',
    description: '获取荣誉排行榜',
    category: 'activity',
    serviceName: 'activity',
    module: 'honor',
    actionName: 'honor.getRanking',
    prerequisites: ["login","character"],
    params: {
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取荣誉排行榜成功'
      };
    } else {
      throw new Error(`获取荣誉排行榜失败: ${response.message}`);
    }
  }
}

module.exports = HonorgetRankingAction;