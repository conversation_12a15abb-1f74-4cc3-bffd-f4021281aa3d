/**
 * 缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作
 * 
 * 微服务: auth
 * 模块: health
 * Controller: health
 * Pattern: cache-expression-test
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.298Z
 */

const BaseAction = require('../../core/base-action');

class CacheExpressionTestAction extends BaseAction {
  static metadata = {
    name: '缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作',
    description: '缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作',
    category: 'auth',
    serviceName: 'auth',
    module: 'health',
    actionName: 'cache-expression-test',
    prerequisites: ["login"],
    params: {
      "testKey": {
            "type": "string",
            "required": true,
            "description": "testKey参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { testKey } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      testKey
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作成功'
      };
    } else {
      throw new Error(`缓存表达式测试 - 微服务调用（用于测试WebSocket代理缓存装饰器） 注意：这个方法使用@MessagePattern，不会触发全局拦截器， 因此@Cacheable装饰器不会工作失败: ${response.message}`);
    }
  }
}

module.exports = CacheExpressionTestAction;