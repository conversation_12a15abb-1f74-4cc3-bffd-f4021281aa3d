/**
 * 刷新商店
 * 
 * 微服务: economy
 * 模块: shop
 * Controller: shop
 * Pattern: shop.refresh
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.463Z
 */

const BaseAction = require('../../../core/base-action');

class ShoprefreshAction extends BaseAction {
  static metadata = {
    name: '刷新商店',
    description: '刷新商店',
    category: 'economy',
    serviceName: 'economy',
    module: 'shop',
    actionName: 'shop.refresh',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "refreshDto": {
            "type": "object",
            "required": true,
            "description": "refreshDto参数",
            "properties": {
                  "shopType": {
                        "type": "enum",
                        "required": true,
                        "description": "shopType参数"
                  },
                  "cycle": {
                        "type": "enum",
                        "required": false,
                        "description": "cycle参数"
                  },
                  "forceRefresh": {
                        "type": "boolean",
                        "required": false,
                        "description": "forceRefresh参数"
                  }
            },
            "example": {
                  "shopType": {},
                  "cycle": {},
                  "forceRefresh": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, refreshDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      refreshDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '刷新商店成功'
      };
    } else {
      throw new Error(`刷新商店失败: ${response.message}`);
    }
  }
}

module.exports = ShoprefreshAction;