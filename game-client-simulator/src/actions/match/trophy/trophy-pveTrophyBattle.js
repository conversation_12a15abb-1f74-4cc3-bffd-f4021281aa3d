/**
 * PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口
 * 
 * 微服务: match
 * 模块: trophy
 * Controller: trophy
 * Pattern: trophy.pveTrophyBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.701Z
 */

const BaseAction = require('../../../core/base-action');

class TrophypveTrophyBattleAction extends BaseAction {
  static metadata = {
    name: 'PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口',
    description: 'PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口',
    category: 'match',
    serviceName: 'match',
    module: 'trophy',
    actionName: 'trophy.pveTrophyBattle',
    prerequisites: ["login","character"],
    params: {
      "pveTrophyBattleDto": {
            "type": "object",
            "required": true,
            "description": "pveTrophyBattleDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "trophyId": {
                        "type": "number",
                        "required": true,
                        "description": "trophyId参数"
                  },
                  "teamCopyId": {
                        "type": "number",
                        "required": true,
                        "description": "teamCopyId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "trophyId": 1,
                  "teamCopyId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { pveTrophyBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      pveTrophyBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: 'PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口成功'
      };
    } else {
      throw new Error(`PVE杯赛战斗 基于old项目的pveTrophyCopyBattle接口失败: ${response.message}`);
    }
  }
}

module.exports = TrophypveTrophyBattleAction;