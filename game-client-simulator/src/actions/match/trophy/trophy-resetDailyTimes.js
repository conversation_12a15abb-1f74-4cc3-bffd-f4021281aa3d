/**
 * 重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式
 * 
 * 微服务: match
 * 模块: trophy
 * Controller: trophy
 * Pattern: trophy.resetDailyTimes
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.709Z
 */

const BaseAction = require('../../../core/base-action');

class TrophyresetDailyTimesAction extends BaseAction {
  static metadata = {
    name: '重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式',
    description: '重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式',
    category: 'match',
    serviceName: 'match',
    module: 'trophy',
    actionName: 'trophy.resetDailyTimes',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": false,
            "description": "characterId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式成功'
      };
    } else {
      throw new Error(`重置每日杯赛次数（管理接口） 修复：添加真正的重置逻辑和正确的返回格式失败: ${response.message}`);
    }
  }
}

module.exports = TrophyresetDailyTimesAction;