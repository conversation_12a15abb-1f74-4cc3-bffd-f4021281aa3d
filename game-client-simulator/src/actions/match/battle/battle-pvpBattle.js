/**
 * PVP战斗 基于old项目的pvpMatchBattle接口
 * 
 * 微服务: match
 * 模块: battle
 * Controller: battle
 * Pattern: battle.pvpBattle
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.621Z
 */

const BaseAction = require('../../../core/base-action');

class BattlepvpBattleAction extends BaseAction {
  static metadata = {
    name: 'PVP战斗 基于old项目的pvpMatchBattle接口',
    description: 'PVP战斗 基于old项目的pvpMatchBattle接口',
    category: 'match',
    serviceName: 'match',
    module: 'battle',
    actionName: 'battle.pvpBattle',
    prerequisites: ["login","character"],
    params: {
      "pvpBattleDto": {
            "type": "object",
            "required": true,
            "description": "pvpBattleDto参数",
            "properties": {
                  "homeCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "homeCharacterId参数"
                  },
                  "awayCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "awayCharacterId参数"
                  },
                  "homeBattleData": {
                        "type": "enum",
                        "required": true,
                        "description": "homeBattleData参数"
                  },
                  "awayBattleData": {
                        "type": "enum",
                        "required": true,
                        "description": "awayBattleData参数"
                  },
                  "battleType": {
                        "type": "string",
                        "required": true,
                        "description": "battleType参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "homeCharacterId": "示例homeCharacterId",
                  "awayCharacterId": "示例awayCharacterId",
                  "homeBattleData": {},
                  "awayBattleData": {},
                  "battleType": "示例battleType",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { pvpBattleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      pvpBattleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: 'PVP战斗 基于old项目的pvpMatchBattle接口成功'
      };
    } else {
      throw new Error(`PVP战斗 基于old项目的pvpMatchBattle接口失败: ${response.message}`);
    }
  }
}

module.exports = BattlepvpBattleAction;