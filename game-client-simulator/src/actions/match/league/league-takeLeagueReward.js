/**
 * 领取联赛奖励 基于old项目的联赛奖励发放逻辑
 * 
 * 微服务: match
 * 模块: league
 * Controller: league
 * Pattern: league.takeLeagueReward
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.655Z
 */

const BaseAction = require('../../../core/base-action');

class LeaguetakeLeagueRewardAction extends BaseAction {
  static metadata = {
    name: '领取联赛奖励 基于old项目的联赛奖励发放逻辑',
    description: '领取联赛奖励 基于old项目的联赛奖励发放逻辑',
    category: 'match',
    serviceName: 'match',
    module: 'league',
    actionName: 'league.takeLeagueReward',
    prerequisites: ["login","character"],
    params: {
      "takeLeagueRewardDto": {
            "type": "object",
            "required": true,
            "description": "takeLeagueRewardDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "leagueId": {
                        "type": "number",
                        "required": true,
                        "description": "leagueId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "leagueId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { takeLeagueRewardDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      takeLeagueRewardDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取联赛奖励 基于old项目的联赛奖励发放逻辑成功'
      };
    } else {
      throw new Error(`领取联赛奖励 基于old项目的联赛奖励发放逻辑失败: ${response.message}`);
    }
  }
}

module.exports = LeaguetakeLeagueRewardAction;