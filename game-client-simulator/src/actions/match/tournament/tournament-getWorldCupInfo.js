/**
 * 获取世界杯信息 基于old项目的getWorldCupInfo接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.getWorldCupInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.671Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentgetWorldCupInfoAction extends BaseAction {
  static metadata = {
    name: '获取世界杯信息 基于old项目的getWorldCupInfo接口',
    description: '获取世界杯信息 基于old项目的getWorldCupInfo接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.getWorldCupInfo',
    prerequisites: ["login","character"],
    params: {
      "getWorldCupInfoDto": {
            "type": "object",
            "required": true,
            "description": "getWorldCupInfoDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getWorldCupInfoDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getWorldCupInfoDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取世界杯信息 基于old项目的getWorldCupInfo接口成功'
      };
    } else {
      throw new Error(`获取世界杯信息 基于old项目的getWorldCupInfo接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentgetWorldCupInfoAction;