/**
 * 更新角色球探数据 基于old项目Scout实体
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.updateScoutData
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.336Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterupdateScoutDataAction extends BaseAction {
  static metadata = {
    name: '更新角色球探数据 基于old项目Scout实体',
    description: '更新角色球探数据 基于old项目Scout实体',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.updateScoutData',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "scoutData": {
            "type": "any",
            "required": true,
            "description": "scoutData参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, scoutData, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      scoutData,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新角色球探数据 基于old项目Scout实体成功'
      };
    } else {
      throw new Error(`更新角色球探数据 基于old项目Scout实体失败: ${response.message}`);
    }
  }
}

module.exports = CharacterupdateScoutDataAction;