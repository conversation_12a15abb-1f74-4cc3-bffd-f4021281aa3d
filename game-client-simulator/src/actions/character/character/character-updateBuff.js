/**
 * 更新持续buff
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.updateBuff
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.325Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterupdateBuffAction extends BaseAction {
  static metadata = {
    name: '更新持续buff',
    description: '更新持续buff',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.updateBuff',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "buffDuration": {
            "type": "number",
            "required": true,
            "description": "buffDuration参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, buffDuration } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      buffDuration
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新持续buff成功'
      };
    } else {
      throw new Error(`更新持续buff失败: ${response.message}`);
    }
  }
}

module.exports = CharacterupdateBuffAction;