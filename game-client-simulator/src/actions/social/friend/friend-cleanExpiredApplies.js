/**
 * 清理过期申请
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.cleanExpiredApplies
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.777Z
 */

const BaseAction = require('../../../core/base-action');

class FriendcleanExpiredAppliesAction extends BaseAction {
  static metadata = {
    name: '清理过期申请',
    description: '清理过期申请',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.cleanExpiredApplies',
    prerequisites: ["login","character"],
    params: {
      "expireDays": {
            "type": "number",
            "required": false,
            "description": "expireDays参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { expireDays } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      expireDays
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '清理过期申请成功'
      };
    } else {
      throw new Error(`清理过期申请失败: ${response.message}`);
    }
  }
}

module.exports = FriendcleanExpiredAppliesAction;