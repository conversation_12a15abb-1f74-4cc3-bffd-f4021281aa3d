/**
 * 处理好友申请
 * 
 * 微服务: social
 * 模块: friend
 * Controller: friend
 * Pattern: friend.handleApply
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.732Z
 */

const BaseAction = require('../../../core/base-action');

class FriendhandleApplyAction extends BaseAction {
  static metadata = {
    name: '处理好友申请',
    description: '处理好友申请',
    category: 'social',
    serviceName: 'social',
    module: 'friend',
    actionName: 'friend.handleApply',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "handleDto": {
            "type": "object",
            "required": true,
            "description": "handleDto参数",
            "properties": {
                  "applicantCharacterId": {
                        "type": "string",
                        "required": true,
                        "description": "applicantCharacterId参数"
                  },
                  "accept": {
                        "type": "boolean",
                        "required": true,
                        "description": "accept参数"
                  }
            },
            "example": {
                  "applicantCharacterId": "示例applicantCharacterId",
                  "accept": true
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, handleDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      handleDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '处理好友申请成功'
      };
    } else {
      throw new Error(`处理好友申请失败: ${response.message}`);
    }
  }
}

module.exports = FriendhandleApplyAction;