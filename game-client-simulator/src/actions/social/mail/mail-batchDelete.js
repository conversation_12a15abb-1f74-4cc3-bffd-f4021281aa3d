/**
 * 批量删除邮件
 * 
 * 微服务: social
 * 模块: mail
 * Controller: mail
 * Pattern: mail.batchDelete
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.819Z
 */

const BaseAction = require('../../../core/base-action');

class MailbatchDeleteAction extends BaseAction {
  static metadata = {
    name: '批量删除邮件',
    description: '批量删除邮件',
    category: 'social',
    serviceName: 'social',
    module: 'mail',
    actionName: 'mail.batchDelete',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "mailUids": {
            "type": "array",
            "required": true,
            "description": "mailUids参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, mailUids } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      mailUids
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量删除邮件成功'
      };
    } else {
      throw new Error(`批量删除邮件失败: ${response.message}`);
    }
  }
}

module.exports = MailbatchDeleteAction;