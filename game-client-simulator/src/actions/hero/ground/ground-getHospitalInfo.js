/**
 * 获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.getHospitalInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.509Z
 */

const BaseAction = require('../../../core/base-action');

class GroundgetHospitalInfoAction extends BaseAction {
  static metadata = {
    name: '获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo',
    description: '获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.getHospitalInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo成功'
      };
    } else {
      throw new Error(`获取医疗中心信息 对应old项目: game.groundService.getHospitalPosInfo失败: ${response.message}`);
    }
  }
}

module.exports = GroundgetHospitalInfoAction;