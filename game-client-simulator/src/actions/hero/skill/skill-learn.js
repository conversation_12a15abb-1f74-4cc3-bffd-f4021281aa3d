/**
 * 球员学习技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.learn
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.596Z
 */

const BaseAction = require('../../../core/base-action');

class SkilllearnAction extends BaseAction {
  static metadata = {
    name: '球员学习技能',
    description: '球员学习技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.learn',
    prerequisites: ["login","character"],
    params: {
      "learnDto": {
            "type": "object",
            "required": true,
            "description": "learnDto参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "configId": {
                        "type": "number",
                        "required": true,
                        "description": "configId参数"
                  },
                  "obtainSource": {
                        "type": "string",
                        "required": false,
                        "description": "obtainSource参数"
                  },
                  "obtainCost": {
                        "type": "number",
                        "required": false,
                        "description": "obtainCost参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "configId": 1,
                  "obtainSource": "示例obtainSource",
                  "obtainCost": 1
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { learnDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      learnDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员学习技能成功'
      };
    } else {
      throw new Error(`球员学习技能失败: ${response.message}`);
    }
  }
}

module.exports = SkilllearnAction;