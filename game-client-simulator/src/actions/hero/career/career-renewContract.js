/**
 * 续约球员 对应old项目: renewTheContract（支持批量续约）
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.renewContract
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.484Z
 */

const BaseAction = require('../../../core/base-action');

class CareerrenewContractAction extends BaseAction {
  static metadata = {
    name: '续约球员 对应old项目: renewTheContract（支持批量续约）',
    description: '续约球员 对应old项目: renewTheContract（支持批量续约）',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.renewContract',
    prerequisites: ["login","character"],
    params: {
      "heroIds": {
            "type": "array",
            "required": true,
            "description": "heroIds参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroIds, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroIds,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '续约球员 对应old项目: renewTheContract（支持批量续约）成功'
      };
    } else {
      throw new Error(`续约球员 对应old项目: renewTheContract（支持批量续约）失败: ${response.message}`);
    }
  }
}

module.exports = CareerrenewContractAction;