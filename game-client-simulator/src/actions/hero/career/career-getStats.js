/**
 * 获取球员生涯统计
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.getStats
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.488Z
 */

const BaseAction = require('../../../core/base-action');

class CareergetStatsAction extends BaseAction {
  static metadata = {
    name: '获取球员生涯统计',
    description: '获取球员生涯统计',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.getStats',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球员生涯统计成功'
      };
    } else {
      throw new Error(`获取球员生涯统计失败: ${response.message}`);
    }
  }
}

module.exports = CareergetStatsAction;