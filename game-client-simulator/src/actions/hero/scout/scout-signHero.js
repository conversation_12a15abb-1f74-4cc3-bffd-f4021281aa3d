/**
 * 签约球探球员 对应old项目: signScoutHero（使用resId而不是index）
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.signHero
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.583Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutsignHeroAction extends BaseAction {
  static metadata = {
    name: '签约球探球员 对应old项目: signScoutHero（使用resId而不是index）',
    description: '签约球探球员 对应old项目: signScoutHero（使用resId而不是index）',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.signHero',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "resId": {
            "type": "number",
            "required": true,
            "description": "resId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, resId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      resId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '签约球探球员 对应old项目: signScoutHero（使用resId而不是index）成功'
      };
    } else {
      throw new Error(`签约球探球员 对应old项目: signScoutHero（使用resId而不是index）失败: ${response.message}`);
    }
  }
}

module.exports = ScoutsignHeroAction;