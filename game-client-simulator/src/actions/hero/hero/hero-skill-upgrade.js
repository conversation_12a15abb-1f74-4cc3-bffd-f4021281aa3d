/**
 * 技能升级
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.skill.upgrade
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.532Z
 */

const BaseAction = require('../../../core/base-action');

class HeroskillupgradeAction extends BaseAction {
  static metadata = {
    name: '技能升级',
    description: '技能升级',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.skill.upgrade',
    prerequisites: ["login","character"],
    params: {
      "skillDto": {
            "type": "object",
            "required": true,
            "description": "skillDto参数",
            "properties": {
                  "skillId": {
                        "type": "string",
                        "required": true,
                        "description": "skillId参数"
                  },
                  "targetLevel": {
                        "type": "number",
                        "required": true,
                        "description": "targetLevel参数"
                  },
                  "useItems": {
                        "type": "boolean",
                        "required": false,
                        "description": "useItems参数"
                  },
                  "items": {
                        "type": "array",
                        "required": false,
                        "description": "items参数"
                  }
            },
            "example": {
                  "skillId": "示例skillId",
                  "targetLevel": 1,
                  "useItems": true,
                  "items": []
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { skillDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      skillDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '技能升级成功'
      };
    } else {
      throw new Error(`技能升级失败: ${response.message}`);
    }
  }
}

module.exports = HeroskillupgradeAction;