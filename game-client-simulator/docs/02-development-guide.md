# 游戏客户端模拟器 - 开发指南

## 🚀 快速开始

### 环境准备
```bash
# 1. 创建项目目录
mkdir game-client-simulator
cd game-client-simulator

# 2. 初始化项目
npm init -y

# 3. 安装依赖
npm install ws yaml chalk commander lodash winston
npm install --save-dev jest eslint prettier nodemon

# 4. 创建目录结构
mkdir -p src/{core,actions/{auth,character,hero,social,economy,match},scenarios,utils,config}
mkdir -p scenarios tests/{unit,integration,scenarios} examples docs
```

### 项目配置
```json
// package.json
{
  "name": "game-client-simulator",
  "version": "1.0.0",
  "description": "游戏客户端模拟器 - 模拟真实玩家操作流程",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  },
  "keywords": ["game", "testing", "simulation", "client"],
  "author": "Game Development Team",
  "license": "MIT"
}
```

## 🏗️ 核心组件实现

### 1. WebSocket管理器
```javascript
// src/core/websocket-manager.js
const WebSocket = require('ws');
const EventEmitter = require('events');
const logger = require('../utils/logger');

class WebSocketManager extends EventEmitter {
  constructor(url, options = {}) {
    super();
    this.url = url;
    this.options = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      timeout: 30000,
      ...options
    };
    
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
    this.pendingRequests = new Map();
    this.requestId = 0;
  }

  /**
   * 连接到WebSocket服务器
   */
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        logger.info(`🔌 连接到WebSocket服务器: ${this.url}`);
        
        this.ws = new WebSocket(this.url);
        
        this.ws.on('open', () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          logger.info('✅ WebSocket连接成功');
          
          // 发送队列中的消息
          this.flushMessageQueue();
          
          this.emit('connected');
          resolve();
        });
        
        this.ws.on('message', (data) => {
          this.handleMessage(data);
        });
        
        this.ws.on('close', () => {
          this.isConnected = false;
          logger.warn('⚠️ WebSocket连接关闭');
          this.emit('disconnected');
          
          // 自动重连
          this.attemptReconnect();
        });
        
        this.ws.on('error', (error) => {
          logger.error('❌ WebSocket连接错误:', error);
          this.emit('error', error);
          reject(error);
        });
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 发送消息
   */
  async sendMessage(command, data = {}) {
    return new Promise((resolve, reject) => {
      const requestId = ++this.requestId;
      const message = {
        id: requestId,
        command,
        data,
        timestamp: Date.now()
      };
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`请求超时: ${command}`));
      }, this.options.timeout);
      
      // 保存请求回调
      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout,
        command
      });
      
      if (this.isConnected) {
        this.ws.send(JSON.stringify(message));
        logger.debug(`📤 发送消息: ${command}`, { requestId, data });
      } else {
        // 连接断开时加入队列
        this.messageQueue.push(message);
        logger.warn(`📋 消息加入队列: ${command}`);
      }
    });
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(rawData) {
    try {
      const message = JSON.parse(rawData.toString());
      logger.debug(`📥 接收消息:`, message);
      
      if (message.id && this.pendingRequests.has(message.id)) {
        const request = this.pendingRequests.get(message.id);
        clearTimeout(request.timeout);
        this.pendingRequests.delete(message.id);
        
        if (message.error) {
          request.reject(new Error(message.error));
        } else {
          request.resolve(message);
        }
      } else {
        // 主动推送的消息
        this.emit('message', message);
      }
    } catch (error) {
      logger.error('❌ 消息解析失败:', error);
    }
  }

  /**
   * 刷新消息队列
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.ws.send(JSON.stringify(message));
      logger.debug(`📤 发送队列消息: ${message.command}`);
    }
  }

  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      logger.error('❌ 达到最大重连次数，停止重连');
      return;
    }
    
    this.reconnectAttempts++;
    logger.info(`🔄 尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        logger.error('❌ 重连失败:', error);
      });
    }, this.options.reconnectInterval);
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    
    // 清理待处理的请求
    for (const [id, request] of this.pendingRequests) {
      clearTimeout(request.timeout);
      request.reject(new Error('连接已断开'));
    }
    this.pendingRequests.clear();
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      connected: this.isConnected,
      url: this.url,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size,
      queuedMessages: this.messageQueue.length
    };
  }
}

module.exports = WebSocketManager;
```

### 2. 会话管理器
```javascript
// src/core/session-manager.js
const logger = require('../utils/logger');

class SessionManager {
  constructor() {
    this.session = {
      token: null,
      userId: null,
      characterId: null,
      serverId: null,
      loginTime: null,
      lastActivity: null
    };
    
    this.gameState = {
      character: null,
      heroes: [],
      guild: null,
      inventory: {},
      formation: null
    };
  }

  /**
   * 设置认证令牌
   */
  setToken(token) {
    this.session.token = token;
    this.session.loginTime = new Date();
    this.updateActivity();
    logger.debug('🔑 设置认证令牌');
  }

  /**
   * 获取认证令牌
   */
  getToken() {
    this.updateActivity();
    return this.session.token;
  }

  /**
   * 设置用户ID
   */
  setUserId(userId) {
    this.session.userId = userId;
    logger.debug(`👤 设置用户ID: ${userId}`);
  }

  /**
   * 设置角色ID
   */
  setCharacterId(characterId) {
    this.session.characterId = characterId;
    logger.debug(`🎮 设置角色ID: ${characterId}`);
  }

  /**
   * 设置服务器ID
   */
  setServerId(serverId) {
    this.session.serverId = serverId;
    logger.debug(`🖥️ 设置服务器ID: ${serverId}`);
  }

  /**
   * 更新游戏状态
   */
  updateGameState(key, value) {
    this.gameState[key] = value;
    this.updateActivity();
    logger.debug(`🎯 更新游戏状态: ${key}`);
  }

  /**
   * 获取游戏状态
   */
  getGameState(key = null) {
    this.updateActivity();
    return key ? this.gameState[key] : this.gameState;
  }

  /**
   * 更新活动时间
   */
  updateActivity() {
    this.session.lastActivity = new Date();
  }

  /**
   * 检查会话是否有效
   */
  isValid() {
    return !!(this.session.token && this.session.userId);
  }

  /**
   * 清理会话
   */
  clear() {
    this.session = {
      token: null,
      userId: null,
      characterId: null,
      serverId: null,
      loginTime: null,
      lastActivity: null
    };
    
    this.gameState = {
      character: null,
      heroes: [],
      guild: null,
      inventory: {},
      formation: null
    };
    
    logger.info('🧹 会话已清理');
  }

  /**
   * 导出会话数据
   */
  export() {
    return {
      session: { ...this.session },
      gameState: { ...this.gameState }
    };
  }

  /**
   * 导入会话数据
   */
  import(data) {
    if (data.session) {
      this.session = { ...this.session, ...data.session };
    }
    if (data.gameState) {
      this.gameState = { ...this.gameState, ...data.gameState };
    }
    logger.info('📥 会话数据已导入');
  }
}

module.exports = SessionManager;
```

## 🎮 操作系统实现

### 操作基类
```javascript
// src/core/base-action.js
const logger = require('../utils/logger');

class BaseAction {
  constructor() {
    this.metadata = this.constructor.metadata || {};
  }

  /**
   * 执行操作
   * @param {GameClient} client - 游戏客户端
   * @param {Object} params - 操作参数
   * @returns {Promise<Object>} 操作结果
   */
  async execute(client, params = {}) {
    const actionName = this.metadata.name || this.constructor.name;
    logger.info(`🎮 执行操作: ${actionName}`);
    
    try {
      // 前置检查
      await this.preCheck(client, params);
      
      // 执行主要逻辑
      const result = await this.perform(client, params);
      
      // 后置处理
      await this.postProcess(client, params, result);
      
      logger.info(`✅ 操作完成: ${actionName}`);
      return result;
      
    } catch (error) {
      logger.error(`❌ 操作失败: ${actionName} - ${error.message}`);
      throw error;
    }
  }

  /**
   * 前置检查（子类可重写）
   */
  async preCheck(client, params) {
    // 检查必需参数
    if (this.metadata.params) {
      for (const [key, config] of Object.entries(this.metadata.params)) {
        if (config.required && !params[key]) {
          throw new Error(`缺少必需参数: ${key}`);
        }
      }
    }
    
    // 检查前置条件
    if (this.metadata.prerequisites) {
      for (const prerequisite of this.metadata.prerequisites) {
        if (!client.session.getGameState(prerequisite)) {
          throw new Error(`前置条件不满足: ${prerequisite}`);
        }
      }
    }
  }

  /**
   * 执行主要逻辑（子类必须实现）
   */
  async perform(client, params) {
    throw new Error('子类必须实现 perform 方法');
  }

  /**
   * 后置处理（子类可重写）
   */
  async postProcess(client, params, result) {
    // 自动更新会话状态
    if (result.token) {
      client.session.setToken(result.token);
    }
    if (result.userId) {
      client.session.setUserId(result.userId);
    }
    if (result.characterId) {
      client.session.setCharacterId(result.characterId);
    }
  }

  /**
   * 获取操作元数据
   */
  getMetadata() {
    return this.metadata;
  }
}

module.exports = BaseAction;
```

## 📝 下一步开发计划

1. **完成核心组件** - GameClient 和 VirtualPlayer
2. **实现操作注册器** - ActionRegistry
3. **开发基础操作** - 认证、角色、球员操作
4. **实现场景系统** - 场景加载和执行
5. **添加工具模块** - API扫描器、日志工具
6. **编写测试用例** - 单元测试和集成测试

**下一个文档**：API参考文档，详细说明所有API的使用方法和参数。
