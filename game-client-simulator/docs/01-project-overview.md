# 游戏客户端模拟器 - 项目概述与架构设计

## 📋 项目概述

### 项目定位
游戏客户端模拟器是一个**独立的测试工具**，用于模拟真实玩家的游戏操作流程。它通过WebSocket连接到游戏网关，执行各种游戏API调用，验证游戏功能的正确性。

### 核心理念
```
真实玩家操作：登录 → 创建角色 → 新手引导 → 招募球员 → 组建阵容 → 参加比赛 → 加入公会
模拟器操作：  连接网关 → 调用登录API → 调用创建角色API → 调用新手引导API → 调用招募API → ...
```

### 设计原则
1. **真实性**：完全模拟真实玩家的操作流程
2. **独立性**：作为独立工具，不依赖现有scripts目录
3. **易用性**：提供简单易用的API和配置方式
4. **扩展性**：支持快速添加新的游戏操作和场景
5. **可观测性**：提供详细的日志和执行报告

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    游戏客户端模拟器                           │
├─────────────────────────────────────────────────────────────┤
│  场景层 (Scenarios)                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 新手流程     │ │ 公会创建     │ │ 日常操作     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  操作层 (Actions)                                           │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐         │
│  │认证  │ │角色  │ │球员  │ │社交  │ │经济  │ │比赛  │         │
│  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘         │
├─────────────────────────────────────────────────────────────┤
│  核心层 (Core)                                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 游戏客户端   │ │ 虚拟玩家     │ │ 会话管理     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  通信层 (Communication)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ WebSocket   │ │ API调用      │ │ 消息处理     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                      游戏网关                                │
│  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐         │
│  │认证  │ │角色  │ │球员  │ │社交  │ │经济  │ │比赛  │         │
│  │服务  │ │服务  │ │服务  │ │服务  │ │服务  │ │服务  │         │
│  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构设计
```
game-client-simulator/
├── docs/                           # 文档目录
│   ├── 01-project-overview.md      # 项目概述
│   ├── 02-development-guide.md     # 开发指南
│   ├── 03-api-reference.md         # API参考
│   └── 04-scenario-guide.md        # 场景编写指南
├── src/                            # 源代码目录
│   ├── core/                       # 核心模块
│   │   ├── game-client.js          # 游戏客户端
│   │   ├── virtual-player.js       # 虚拟玩家
│   │   ├── websocket-manager.js    # WebSocket管理
│   │   ├── session-manager.js      # 会话管理
│   │   └── action-registry.js      # 操作注册器
│   ├── actions/                    # 游戏操作
│   │   ├── auth/                   # 认证操作
│   │   │   ├── login.js
│   │   │   ├── register.js
│   │   │   └── logout.js
│   │   ├── character/              # 角色操作
│   │   │   ├── create.js
│   │   │   ├── get-info.js
│   │   │   └── update.js
│   │   ├── hero/                   # 球员操作
│   │   │   ├── recruit.js
│   │   │   ├── train.js
│   │   │   └── evolve.js
│   │   ├── social/                 # 社交操作
│   │   │   ├── create-guild.js
│   │   │   ├── join-guild.js
│   │   │   └── add-friend.js
│   │   ├── economy/                # 经济操作
│   │   │   ├── add-gold.js
│   │   │   ├── shop-purchase.js
│   │   │   └── market-trade.js
│   │   └── match/                  # 比赛操作
│   │       ├── auto-formation.js
│   │       ├── start-match.js
│   │       └── get-result.js
│   ├── scenarios/                  # 场景脚本
│   │   ├── newbie-flow.js          # 新手流程
│   │   ├── guild-creation.js       # 公会创建
│   │   ├── daily-routine.js        # 日常操作
│   │   └── match-flow.js           # 比赛流程
│   ├── utils/                      # 工具模块
│   │   ├── api-scanner.js          # API扫描器
│   │   ├── data-generator.js       # 数据生成器
│   │   ├── logger.js               # 日志工具
│   │   └── validator.js            # 数据验证器
│   └── config/                     # 配置模块
│       ├── gateway.config.js       # 网关配置
│       ├── game.config.js          # 游戏配置
│       └── test.config.js          # 测试配置
├── scenarios/                      # 场景配置文件
│   ├── newbie-complete-flow.yaml   # 新手完整流程
│   ├── guild-master-flow.yaml      # 公会会长流程
│   ├── daily-player-flow.yaml      # 日常玩家流程
│   └── competitive-flow.yaml       # 竞技流程
├── tests/                          # 测试文件
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   └── scenarios/                  # 场景测试
├── examples/                       # 示例代码
│   ├── basic-usage.js              # 基础使用示例
│   ├── custom-action.js            # 自定义操作示例
│   └── scenario-creation.js        # 场景创建示例
├── package.json                    # 项目配置
├── README.md                       # 项目说明
└── .gitignore                      # Git忽略文件
```

## 🔧 核心组件设计

### 1. 游戏客户端 (GameClient)
**职责**：作为模拟器的主入口，管理连接、操作执行和场景运行
**核心方法**：
- `connect()` - 连接游戏服务器
- `performAction(actionName, params)` - 执行游戏操作
- `playScenario(scenarioName, playerData)` - 执行游戏场景
- `callAPI(command, data)` - 调用游戏API

### 2. 虚拟玩家 (VirtualPlayer)
**职责**：模拟真实玩家，维护游戏状态和玩家数据
**核心方法**：
- `perform(actionName, params)` - 执行操作
- `updateGameState(actionName, result)` - 更新游戏状态
- `getCharacterInfo()` - 获取角色信息
- `getGameState()` - 获取游戏状态

### 3. WebSocket管理器 (WebSocketManager)
**职责**：管理与游戏网关的WebSocket连接
**核心方法**：
- `connect()` - 建立连接
- `sendMessage(command, data)` - 发送消息
- `onMessage(callback)` - 消息监听
- `disconnect()` - 断开连接

### 4. 操作注册器 (ActionRegistry)
**职责**：管理所有游戏操作的注册和查找
**核心方法**：
- `registerAction(name, actionClass)` - 注册操作
- `getAction(name)` - 获取操作
- `loadActions()` - 加载所有操作
- `scanActions()` - 扫描操作目录

## 📊 数据流设计

### 操作执行流程
```
1. 用户调用 → GameClient.performAction()
2. 查找操作 → ActionRegistry.getAction()
3. 执行操作 → Action.execute()
4. 调用API → WebSocketManager.sendMessage()
5. 处理响应 → Action.handleResponse()
6. 更新状态 → VirtualPlayer.updateGameState()
7. 返回结果 → 用户获得结果
```

### 场景执行流程
```
1. 加载场景 → ScenarioLoader.load()
2. 创建玩家 → new VirtualPlayer()
3. 执行步骤 → 循环执行每个操作
4. 状态管理 → 维护玩家状态
5. 错误处理 → 处理执行异常
6. 生成报告 → 输出执行结果
```

## 🎯 设计目标

### 功能目标
- ✅ 支持所有游戏核心功能的模拟测试
- ✅ 提供简单易用的API接口
- ✅ 支持复杂的游戏流程组合
- ✅ 自动扫描和适配游戏接口
- ✅ 生成详细的测试报告

### 性能目标
- ✅ 支持并发多个虚拟玩家
- ✅ 单个操作响应时间 < 5秒
- ✅ 场景执行成功率 > 95%
- ✅ 内存使用 < 100MB

### 可维护性目标
- ✅ 模块化设计，易于扩展
- ✅ 完善的文档和示例
- ✅ 标准化的错误处理
- ✅ 自动化的测试覆盖

## 🚀 开发阶段规划

### 阶段1：核心框架 (1-2天)
- 实现GameClient核心类
- 实现WebSocketManager
- 实现基础的操作执行机制
- 编写基础示例

### 阶段2：操作系统 (2-3天)
- 实现ActionRegistry
- 开发认证相关操作
- 开发角色相关操作
- 开发基础的游戏操作

### 阶段3：场景系统 (1-2天)
- 实现VirtualPlayer
- 实现场景加载和执行
- 开发新手流程场景
- 开发公会创建场景

### 阶段4：工具和优化 (1-2天)
- 实现API扫描器
- 添加日志和监控
- 性能优化
- 文档完善

### 阶段5：测试和发布 (1天)
- 编写单元测试
- 集成测试验证
- 示例代码完善
- 发布第一个版本

## 📝 后续文档计划

1. **开发指南** - 详细的开发步骤和代码实现
2. **API参考** - 完整的API文档和使用说明
3. **场景编写指南** - 如何编写和配置游戏场景
4. **扩展指南** - 如何添加新的操作和功能
5. **部署指南** - 如何部署和运行模拟器

---

**下一步**：编写详细的开发指南文档，包含具体的代码实现和开发步骤。

## 📋 技术选型

### 核心技术栈
- **Node.js** - 运行环境
- **WebSocket** - 与网关通信
- **YAML** - 场景配置文件格式
- **Jest** - 单元测试框架
- **Winston** - 日志管理

### 依赖库选择
- **ws** - WebSocket客户端库
- **yaml** - YAML文件解析
- **chalk** - 控制台颜色输出
- **commander** - 命令行参数解析
- **lodash** - 工具函数库

### 代码规范
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **JSDoc** - 文档注释
- **Conventional Commits** - 提交规范
