import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LeagueController } from './league.controller';
import { LeagueService } from './league.service';
import { LeagueRepository } from '../../common/repositories/league.repository';
import { League, LeagueSchema } from '../../common/schemas/league.schema';
import { BattleModule } from '../battle/battle.module';
import { BattleDataService } from '../../common/services/battle-data.service';

/**
 * 联赛系统模块
 * 基于old项目leagueCopy.js的完整功能实现
 * 
 * 功能包括：
 * - 联赛副本数据管理
 * - PVE联赛战斗系统
 * - 联赛奖励发放
 * - 联赛进度跟踪
 * 
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MicroserviceKitModule已在app.module.ts中注册，可直接使用MicroserviceClientService
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册League Schema（LeagueRepository需要）
    MongooseModule.forFeature([
      { name: League.name, schema: LeagueSchema },
    ]),
    // 导入BattleModule以使用BattleService
    BattleModule,
  ],
  controllers: [LeagueController],
  providers: [
    LeagueService,
    LeagueRepository, // 联赛数据访问层
    BattleDataService, // 战斗数据转换共享服务
  ],
  exports: [LeagueService, LeagueRepository],
})
export class LeagueModule {}
