import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TournamentController } from './tournament.controller';
import { TournamentService } from './tournament.service';
import { TournamentRepository } from '../../common/repositories/tournament.repository';
import { Tournament, TournamentSchema } from '../../common/schemas/tournament.schema';
import { BattleModule } from '../battle/battle.module';
import { BattleDataService } from '../../common/services/battle-data.service';

/**
 * 锦标赛系统模块
 * 基于old项目worldCup.js、middleEastCup.js、gulfCup.js、MLS.js的功能实现
 *
 * 核心功能：
 * - 世界杯系统
 * - 区域杯赛系统（中东杯、海湾杯）
 * - 联盟赛系统（MLS等）
 * - 锦标赛报名和赛程管理
 * - 淘汰赛制和奖励发放
 * - 每日重置机制
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MicroserviceKitModule已在app.module.ts中注册，可直接使用MicroserviceClientService
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册Tournament Schema（TournamentRepository需要）
    MongooseModule.forFeature([
      { name: Tournament.name, schema: TournamentSchema },
    ]),
    // 导入BattleModule以使用BattleService
    BattleModule,
  ],
  controllers: [TournamentController],
  providers: [
    TournamentService,
    TournamentRepository, // 锦标赛数据访问层
    BattleDataService, // 战斗数据转换共享服务
  ],
  exports: [TournamentService, TournamentRepository],
})
export class TournamentModule {}
