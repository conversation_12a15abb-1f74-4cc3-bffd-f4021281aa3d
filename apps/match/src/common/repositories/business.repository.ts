import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BusinessMatch, BusinessMatchRecord } from '../schemas/business.schema';

/**
 * 商业赛数据访问层
 * 基于Repository模式，提供商业赛数据的CRUD操作
 */
@Injectable()
export class BusinessRepository {
  private readonly logger = new Logger(BusinessRepository.name);

  constructor(
    @InjectModel(BusinessMatch.name) private readonly businessMatchModel: Model<BusinessMatch>,
  ) {}

  /**
   * 根据玩家UID查找商业赛数据
   */
  async findByUid(uid: string): Promise<BusinessMatch | null> {
    try {
      return await this.businessMatchModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`查找商业赛数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 创建新的商业赛数据
   */
  async create(businessMatchData: Partial<BusinessMatch>): Promise<BusinessMatch> {
    try {
      const businessMatch = new this.businessMatchModel(businessMatchData);
      return await businessMatch.save();
    } catch (error) {
      this.logger.error('创建商业赛数据失败', error);
      throw error;
    }
  }

  /**
   * 更新商业赛数据
   */
  async updateByUid(uid: string, updateData: Partial<BusinessMatch>): Promise<BusinessMatch | null> {
    try {
      return await this.businessMatchModel
        .findOneAndUpdate(
          { uid },
          { ...updateData, lastUpdateTime: new Date() },
          { new: true, upsert: false }
        )
        .exec();
    } catch (error) {
      this.logger.error(`更新商业赛数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 创建或更新商业赛数据
   */
  async upsert(uid: string, businessMatchData: Partial<BusinessMatch>): Promise<BusinessMatch> {
    try {
      return await this.businessMatchModel
        .findOneAndUpdate(
          { uid },
          { ...businessMatchData, lastUpdateTime: new Date() },
          { new: true, upsert: true }
        )
        .exec();
    } catch (error) {
      this.logger.error(`创建或更新商业赛数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 添加匹配记录
   */
  async addMatchRecord(uid: string, matchRecord: BusinessMatchRecord): Promise<BusinessMatch | null> {
    try {
      const businessMatch = await this.findByUid(uid);
      if (!businessMatch) {
        this.logger.error(`商业赛数据不存在: ${uid}`);
        return null;
      }

      // 限制最大记录数量为30条
      if (businessMatch.matchRecordList.length >= businessMatch.maxRecordNum) {
        const removeCount = businessMatch.matchRecordList.length - businessMatch.maxRecordNum + 1;
        businessMatch.matchRecordList.splice(0, removeCount);
      }

      // 添加新记录
      businessMatch.matchRecordList.push(matchRecord);
      
      // 按时间排序（最新的在前）
      businessMatch.matchRecordList.sort((a, b) => {
        return new Date(b.beginTime).getTime() - new Date(a.beginTime).getTime();
      });

      return await this.updateByUid(uid, { matchRecordList: businessMatch.matchRecordList });
    } catch (error) {
      this.logger.error(`添加匹配记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 根据房间UID查找匹配记录
   */
  async findMatchRecordByRoomUid(uid: string, roomUid: string): Promise<BusinessMatchRecord | null> {
    try {
      const businessMatch = await this.findByUid(uid);
      if (!businessMatch) {
        return null;
      }

      return businessMatch.matchRecordList.find(record => record.roomUid === roomUid) || null;
    } catch (error) {
      this.logger.error(`查找匹配记录失败: ${uid}, ${roomUid}`, error);
      throw error;
    }
  }

  /**
   * 获取最近的匹配记录
   */
  async getLastMatchRecords(uid: string, limit: number = 5): Promise<BusinessMatchRecord[]> {
    try {
      const businessMatch = await this.findByUid(uid);
      if (!businessMatch) {
        return [];
      }

      return businessMatch.matchRecordList.slice(0, limit);
    } catch (error) {
      this.logger.error(`获取最近匹配记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 更新战斗次数
   */
  async updateFightTimes(uid: string, fightTimes: any): Promise<BusinessMatch | null> {
    try {
      return await this.updateByUid(uid, { fightTimes });
    } catch (error) {
      this.logger.error(`更新战斗次数失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 删除商业赛数据
   */
  async deleteByUid(uid: string): Promise<boolean> {
    try {
      const result = await this.businessMatchModel.deleteOne({ uid }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      this.logger.error(`删除商业赛数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 批量查询商业赛数据
   */
  async findByUids(uids: string[]): Promise<BusinessMatch[]> {
    try {
      return await this.businessMatchModel.find({ uid: { $in: uids } }).exec();
    } catch (error) {
      this.logger.error('批量查询商业赛数据失败', error);
      throw error;
    }
  }

  /**
   * 获取商业赛统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const totalCharacters = await this.businessMatchModel.countDocuments().exec();
      const activeToday = await this.businessMatchModel
        .countDocuments({
          lastUpdateTime: {
            $gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        })
        .exec();

      // 统计总比赛数
      const pipeline = [
        { $unwind: '$matchRecordList' },
        { $group: { _id: null, totalMatches: { $sum: 1 } } }
      ];
      const matchStats = await this.businessMatchModel.aggregate(pipeline).exec();
      const totalMatches = matchStats.length > 0 ? matchStats[0].totalMatches : 0;

      return {
        totalCharacters,
        activeToday,
        totalMatches,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取商业赛统计信息失败', error);
      throw error;
    }
  }
}
