import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { HeroPosition } from '@libs/game-constants';

/**
 * 球员战斗信息
 * 基于old项目room.js的球员数据结构
 * 修复：使用项目统一的类型定义，确保架构一致性
 */
@Schema({ _id: false })
export class BattleHeroInfo {
  @Prop({ required: true })
  heroId: string;                        // 球员ID - 修复：使用统一的heroId命名

  @Prop({ default: 0 })
  attack: number;                        // 进攻值

  @Prop({ default: 0 })
  defend: number;                        // 防守值

  @Prop({ default: 0 })
  speed: number;                         // 速度值

  @Prop({ default: 0 })
  power: number;                         // 力量值

  @Prop({ default: 0 })
  technique: number;                     // 技术值

  @Prop({
    type: String,
    enum: Object.values(HeroPosition),
    required: true
  })
  position: HeroPosition;                // 位置 - 修复：使用统一的HeroPosition枚举

  @Prop({ default: 0 })
  level: number;                         // 等级

  @Prop({ type: [Number], default: [] })
  skills: number[];                      // 技能列表
}

/**
 * 队伍战斗数据
 * 基于old项目room.js的队伍数据结构
 */
@Schema({ _id: false })
export class BattleTeamData {
  @Prop({ required: true })
  characterId: string;                      // 玩家ID

  @Prop({ default: '' })
  teamName: string;                      // 队伍名称

  @Prop({ default: 0 })
  formation: number;                     // 阵型ID

  @Prop({ default: 0 })
  tactic: number;                        // 战术ID

  @Prop({ type: [BattleHeroInfo], default: [] })
  heroes: BattleHeroInfo[];              // 球员列表

  @Prop({ default: 0 })
  totalAttack: number;                   // 总进攻值

  @Prop({ default: 0 })
  totalDefend: number;                   // 总防守值

  @Prop({ default: 0 })
  morale: number;                        // 士气值

  @Prop({ default: 0 })
  score: number;                         // 比分
}

/**
 * 战斗回合信息
 * 基于old项目room.js的回合记录结构
 */
@Schema({ _id: false })
export class BattleRoundInfo {
  @Prop({ default: 0 })
  roundIndex: number;                    // 回合索引

  @Prop({ default: '' })
  attacker: string;                      // 进攻方 (teamA/teamB)

  @Prop({ default: 0 })
  attackMode: number;                    // 进攻方式

  @Prop({ default: 0 })
  battleTime: number;                    // 战斗时间

  @Prop({ default: 0 })
  scoreA: number;                        // A队比分

  @Prop({ default: 0 })
  scoreB: number;                        // B队比分

  @Prop({ type: Object, default: {} })
  periodInfo: any[];                     // 阶段信息（发起、推进、射门）
}

/**
 * 战斗结果数据
 * 基于old项目room.js的战斗结果结构
 */
@Schema({ _id: false })
export class BattleResult {
  @Prop({ required: true })
  roomUid: string;                       // 房间UID

  @Prop({ default: 0 })
  battleType: number;                    // 战斗类型

  @Prop({ default: 0 })
  homeScore: number;                     // 主队比分

  @Prop({ default: 0 })
  awayScore: number;                     // 客队比分

  @Prop({ default: 0 })
  winner: number;                        // 胜利方 (0平局 1主队 2客队)

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: [BattleRoundInfo], default: [] })
  roundInfo: BattleRoundInfo[];          // 回合信息

  @Prop({ type: Object, default: {} })
  battleEndInfo: any;                    // 战斗结束信息

  @Prop({ type: Object, default: {} })
  skillRecord: any;                      // 技能记录
}

/**
 * 战斗房间主文档
 * 基于old项目room.js的房间数据结构
 */
@Schema({
  collection: 'battle_rooms',
  timestamps: true,
  versionKey: false,
})
export class BattleRoom extends Document {
  @Prop({ required: true, unique: true })
  roomUid: string;                       // 房间UID

  @Prop({ default: 0 })
  battleType: number;                    // 战斗类型

  @Prop({ default: 'active' })
  status: string;                        // 房间状态 (active/finished/expired)

  @Prop({ type: BattleTeamData })
  teamA: BattleTeamData;                 // A队数据

  @Prop({ type: BattleTeamData })
  teamB: BattleTeamData;                 // B队数据

  @Prop({ type: BattleResult })
  result: BattleResult;                  // 战斗结果

  @Prop({ default: Date.now })
  createdAt: Date;                       // 创建时间

  @Prop({ default: Date.now })
  finishedAt: Date;                      // 完成时间
}

export const BattleRoomSchema = SchemaFactory.createForClass(BattleRoom);

// 创建索引
BattleRoomSchema.index({ roomUid: 1 });
BattleRoomSchema.index({ status: 1 });
BattleRoomSchema.index({ battleType: 1 });
BattleRoomSchema.index({ createdAt: 1 });
BattleRoomSchema.index({ 'teamA.characterId': 1 });
BattleRoomSchema.index({ 'teamB.characterId': 1 });
