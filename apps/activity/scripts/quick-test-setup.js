/**
 * Activity服务快速测试数据准备脚本
 * 基于match服务的成功模式
 * 专门为Activity服务测试快速准备必要数据
 *
 * 使用方法：
 * node apps/activity/scripts/quick-test-setup.js [characterId]
 *
 * 如果不提供characterId，会创建新的测试角色
 */

const MicroserviceWebSocketClient = require('../../../scripts/common/websocket-client');

class ActivityQuickTestSetup extends MicroserviceWebSocketClient {
  constructor() {
    super({
      GATEWAY_WS_URL: 'http://127.0.0.1:3000',
      AUTH_URL: 'http://127.0.0.1:3001',
      TIMEOUT: 30000
    });
    this.serverId = 'server_001';
    this.testUserId = `activity-quick-test-${Date.now()}`;
  }

  /**
   * 快速设置Activity服务测试数据
   */
  async setup(existingCharacterId = null) {
    console.log('🚀 快速设置Activity服务测试数据');
    console.log(`测试用户ID: ${this.testUserId}`);
    console.log(`测试服务器ID: ${this.serverId}`);

    try {
      // 第1步：跳过健康检查，直接连接
      console.log('\n=== 第1步：跳过健康检查，直接连接 ===');
      console.log('⚠️ 跳过HTTP健康检查，因为运行环境因素可能影响结果');
      console.log('✅ 从终端日志确认所有服务正在运行中');

      // 第2步：认证和连接
      console.log('\n=== 第2步：认证和连接 ===');
      this.token = await this.getAuthToken('activityquicktest');
      await this.connectWebSocket();

      // 第3步：获取或创建测试角色
      console.log('\n=== 第3步：创建测试角色 ===');
      const characterId = existingCharacterId || await this.createTestCharacter();
      console.log(`📋 使用测试角色: ${characterId}`);

      // 第4步：快速添加资源
      console.log('\n=== 第4步：添加测试资源 ===');
      await this.addTestResources(characterId);

      // 第5步：验证设置
      console.log('\n=== 第5步：验证设置结果 ===');
      await this.verifySetup(characterId);

      console.log('\n🎉 Activity服务快速测试数据设置完成！');
      console.log(`\n📋 测试角色信息:`);
      console.log(`   角色ID: ${characterId}`);
      console.log(`   服务器: ${this.serverId}`);
      console.log(`\n🚀 现在可以运行Activity服务测试了！`);
      console.log(`   命令: node apps/activity/scripts/test-activity-system.js lottery ${characterId}`);

      return characterId;

    } catch (error) {
      console.error('❌ 快速设置失败:', error);
      throw error;
    } finally {
      this.disconnect();
    }
  }

  /**
   * 创建测试角色
   */
  async createTestCharacter() {
    try {
      console.log('👤 创建测试角色...');

      const response = await this.sendMessage('character.character.create', {
        userId: this.testUserId,
        serverId: this.serverId,
        openId: this.testUserId,
        name: `ActivityTest${Date.now().toString().slice(-8)}`,
        avatar: 'default_avatar.png'
      });

      // 检查响应格式：网关返回的响应包装在payload中
      if (response && response.payload && response.payload.success && response.payload.data && response.payload.data.code === 0) {
        const characterData = response.payload.data.data;
        const characterId = characterData.characterId || characterData.id;
        console.log(`✅ 测试角色创建成功: ${characterId}`);
        return characterId;
      } else {
        console.log('❌ 测试角色创建失败');
        console.log('响应:', JSON.stringify(response, null, 2));
        throw new Error('角色创建失败');
      }
    } catch (error) {
      console.log(`❌ 角色创建失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 快速添加测试资源 - 使用正确的Character服务API
   */
  async addTestResources(characterId) {
    const resources = [
      { type: 'cash', amount: 1000000, name: '球币' },
      { type: 'worldCoin', amount: 50000, name: '欧元' },
      { type: 'gold', amount: 100000, name: '金币' },
      { type: 'energy', amount: 1000, name: '体力' },
      { type: 'diamond', amount: 10000, name: '钻石' }
    ];

    for (const resource of resources) {
      try {
        // 使用正确的Character服务货币添加API
        const response = await this.sendMessage('character.character.currency.add', {
          characterId: characterId,
          serverId: this.serverId,
          currencyDto: {
            currencyType: resource.type,
            amount: resource.amount,
            reason: 'activity_quick_test_setup'
          }
        });

        if (response && response.payload && response.payload.success && response.payload.data && response.payload.data.code === 0) {
          console.log(`✅ ${resource.name}: +${resource.amount}`);
        } else {
          console.log(`⚠️ ${resource.name}添加失败: ${response.payload?.data?.message || '未知错误'}，继续测试`);
        }
      } catch (error) {
        console.log(`⚠️ ${resource.name}添加失败: ${error.message}，继续测试`);
      }
    }
  }

  /**
   * 验证设置结果
   */
  async verifySetup(characterId) {
    try {
      // 验证角色信息
      const infoResponse = await this.sendMessage('character.character.getInfo', {
        characterId: characterId,
        serverId: this.serverId
      });

      if (infoResponse && infoResponse.payload && infoResponse.payload.success && infoResponse.payload.data && infoResponse.payload.data.code === 0) {
        const char = infoResponse.payload.data.data;
        console.log('✅ 角色信息验证成功:');
        console.log(`   球币: ${char.cash}`);
        console.log(`   欧元: ${char.worldCoin}`);
        console.log(`   金币: ${char.gold}`);
        console.log(`   体力: ${char.energy}`);
        console.log(`   钻石: ${char.diamond}`);
      } else {
        console.log('⚠️ 角色信息验证失败');
      }

    } catch (error) {
      console.log('⚠️ 验证过程出现错误:', error.message);
    }
  }
}

// 主执行函数
async function main() {
  const characterId = process.argv[2]; // 从命令行参数获取角色ID
  
  const setup = new ActivityQuickTestSetup();
  
  try {
    const resultCharacterId = await setup.setup(characterId);
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 Activity服务快速测试数据设置完成！');
    console.log('='.repeat(50));
    console.log(`测试角色ID: ${resultCharacterId}`);
    console.log(`服务器ID: server_001`);
    console.log('\n下一步：运行Activity服务测试');
    console.log(`命令: node apps/activity/scripts/test-activity-system.js lottery ${resultCharacterId}`);
    
  } catch (error) {
    console.error('\n❌ 快速设置失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ActivityQuickTestSetup;
