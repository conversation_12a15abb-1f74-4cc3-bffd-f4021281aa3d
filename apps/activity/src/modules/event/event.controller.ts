import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { EventService } from './event.service';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class EventController {
  private readonly logger = new Logger(EventController.name);

  constructor(private readonly eventService: EventService) {}

  /**
   * 获取活动列表
   */
  @MessagePattern('event.getList')
  async getEventList(@Payload() payload: { characterId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取活动列表: ${payload.characterId}`);
    const result = await this.eventService.getActivityList(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 参与活动
   */
  @MessagePattern('event.join')
  async joinEvent(@Payload() payload: { characterId: string; eventId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`参与活动: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.getActivityInfo(payload.characterId, payload.eventId);
    return {
      code: 0,
      message: '参与成功',
      data: result,
    };
  }

  /**
   * 领取活动奖励
   */
  @MessagePattern('event.claimReward')
  async claimEventReward(@Payload() payload: { characterId: string; eventId: number; rewardId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`领取活动奖励: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.claimActivityReward(payload.characterId, payload.eventId, payload.rewardId);
    return {
      code: 0,
      message: '奖励领取成功',
      data: result,
    };
  }

  /**
   * 获取活动进度
   */
  @MessagePattern('event.getProgress')
  async getEventProgress(@Payload() payload: { characterId: string; eventId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`获取活动进度: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.getActivityInfo(payload.characterId, payload.eventId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 最佳11人抽奖
   * @payload.index 抽奖类型 1=单抽, 2=十连抽
   * 基于old项目: Act.prototype.buyBestFootball
   */
  @MessagePattern('event.buyBestFootball')
  async buyBestFootball(@Payload() payload: { characterId: string; index: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`最佳11人抽奖: ${JSON.stringify(payload)}`);

    try {
      const result = await this.eventService.buyBestFootball(
        payload.characterId,
        payload.index
      );

      return result;
    } catch (error) {
      this.logger.error('最佳11人抽奖失败', error);
      return {
        code: -1,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 老虎机抽奖
   * @payload.index 抽奖类型 1=单抽, 2=十连抽
   * 基于old项目: Act.prototype.buyTurntable
   */
  @MessagePattern('event.buyTurntable')
  async buyTurntable(@Payload() payload: { characterId: string; frequencyType: number; injectedContext?: InjectedContext }) {
    this.logger.log(`老虎机抽奖: ${JSON.stringify(payload)}`);

    try {
      const result = await this.eventService.buyTurntable(
        payload.characterId,
        payload.frequencyType
      );

      return result;
    } catch (error) {
      this.logger.error('老虎机抽奖失败', error);
      return {
        code: -1,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 拉霸抽奖
   * @payload.frequencyType 抽奖类型 1=单抽, 2=十连抽
   * @payload.securityMoney 保底金额
   * 基于old项目: Act.prototype.buySlots
   */
  @MessagePattern('event.buySlots')
  async buySlots(@Payload() payload: { characterId: string; frequencyType: number; securityMoney: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`拉霸抽奖: ${JSON.stringify(payload)}`);

    try {
      const result = await this.eventService.buySlots(
        payload.characterId,
        payload.frequencyType,
        payload.securityMoney || 0
      );

      return result;
    } catch (error) {
      this.logger.error('拉霸抽奖失败', error);
      return {
        code: -1,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 周末返场抽奖
   * 基于old项目: Act.prototype.weekDayEncore
   */
  @MessagePattern('event.weekDayEncore')
  async weekDayEncore(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`周末返场抽奖: ${JSON.stringify(payload)}`);

    try {
      const result = await this.eventService.weekDayEncore(payload.characterId);

      return result;
    } catch (error) {
      this.logger.error('周末返场抽奖失败', error);
      return {
        code: -1,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }
}
