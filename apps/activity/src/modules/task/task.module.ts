/**
 * 任务模块
 * 基于old项目tasks.js和newerTask.js实体迁移
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Task相关组件
import { TaskController } from './task.controller';
import { TaskService } from './task.service';
import { Task, TaskSchema } from '@activity/common/schemas/task.schema';
import { TaskRepository } from '@activity/common/repositories/task.repository';

@Module({
  imports: [
    // 注册Task Schema
    MongooseModule.forFeature([
      { name: Task.name, schema: TaskSchema },
    ]),
  ],
  controllers: [TaskController],
  providers: [TaskService, TaskRepository],
  exports: [TaskService, TaskRepository],
})
export class TaskModule {}
