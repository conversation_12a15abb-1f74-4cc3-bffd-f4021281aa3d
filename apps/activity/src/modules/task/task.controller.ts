import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TaskService } from './task.service';
import { 
  UpdateTaskProgressDto, 
  ClaimTaskRewardDto, 
  GetTaskListDto,
  RefreshTaskDto,
  TaskStatsDto,
  BatchUpdateTaskProgressDto,
  AddTaskDto
} from '@activity/common/dto/task.dto';
import { TaskType } from '@activity/common/schemas/task.schema';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class TaskController {
  private readonly logger = new Logger(TaskController.name);

  constructor(private readonly taskService: TaskService) {}

  // ==================== 任务列表管理 ====================

  /**
   * 获取任务列表
   */
  @MessagePattern('task.getList')
  @Cacheable({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTaskList(@Payload() payload: { characterId: string; serverId: string; query?: GetTaskListDto; injectedContext?: InjectedContext }) {
    this.logger.log(`获取任务列表: ${payload.characterId}`);
    const taskList = await this.taskService.getTaskList(payload.characterId, payload.serverId, payload.query);
    return {
      code: 0,
      message: '获取成功',
      data: taskList,
    };
  }

  /**
   * 更新任务进度
   */
  @MessagePattern('task.updateProgress')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateTaskProgress(@Payload() payload: { characterId: string; serverId: string; updateDto: UpdateTaskProgressDto; injectedContext?: InjectedContext }) {
    this.logger.log(`更新任务进度: ${payload.characterId}, 任务: ${payload.updateDto.taskId}, 进度: ${payload.updateDto.value}`);
    const result = await this.taskService.updateTaskProgress(payload.characterId, payload.serverId, payload.updateDto);
    return {
      code: 0,
      message: '更新成功',
      data: result,
    };
  }

  /**
   * 批量更新任务进度
   */
  @MessagePattern('task.batchUpdateProgress')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchUpdateTaskProgress(@Payload() payload: { characterId: string; serverId: string; batchDto: BatchUpdateTaskProgressDto; injectedContext?: InjectedContext }) {
    this.logger.log(`批量更新任务进度: ${payload.characterId}, 数量: ${payload.batchDto.updates.length}`);
    const result = await this.taskService.batchUpdateTaskProgress(payload.characterId, payload.serverId, payload.batchDto);
    return {
      code: 0,
      message: '批量更新成功',
      data: result,
    };
  }

  /**
   * 领取任务奖励
   */
  @MessagePattern('task.claimReward')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async claimTaskReward(@Payload() payload: { characterId: string; serverId: string; claimDto: ClaimTaskRewardDto; injectedContext?: InjectedContext }) {
    this.logger.log(`领取任务奖励: ${payload.characterId}, 任务: ${payload.claimDto.taskId}`);
    const result = await this.taskService.claimTaskReward(payload.characterId, payload.serverId, payload.claimDto);
    return {
      code: 0,
      message: '奖励领取成功',
      data: result,
    };
  }

  // ==================== 任务刷新管理 ====================

  /**
   * 刷新任务
   */
  @MessagePattern('task.refresh')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async refreshTasks(@Payload() payload: { characterId: string; serverId: string; refreshDto: RefreshTaskDto; injectedContext?: InjectedContext }) {
    this.logger.log(`刷新任务: ${payload.characterId}, 类型: ${payload.refreshDto.taskType}`);
    const result = await this.taskService.refreshTasks(payload.characterId, payload.serverId, payload.refreshDto);
    return {
      code: 0,
      message: '刷新成功',
      data: result,
    };
  }

  /**
   * 添加新任务
   */
  @MessagePattern('task.add')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addTask(@Payload() payload: { characterId: string; serverId: string; addDto: AddTaskDto; injectedContext?: InjectedContext }) {
    this.logger.log(`添加新任务: ${payload.characterId}, 任务: ${payload.addDto.taskId}, 类型: ${payload.addDto.taskType}`);
    const result = await this.taskService.addTask(payload.characterId, payload.serverId, payload.addDto);
    return {
      code: 0,
      message: '任务添加成功',
      data: result,
    };
  }

  // ==================== 任务统计 ====================

  /**
   * 获取任务统计
   */
  @MessagePattern('task.getStats')
  @Cacheable({ 
    key: 'task:stats:#{payload.query.characterId}:#{payload.query.days}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getTaskStats(@Payload() payload: { serverId: string; query: TaskStatsDto; injectedContext?: InjectedContext }) {
    this.logger.log(`获取任务统计: ${payload.query.characterId}`);
    const stats = await this.taskService.getTaskStats(payload.query);
    return {
      code: 0,
      message: '获取成功',
      data: stats,
    };
  }

  /**
   * 获取任务排行榜
   */
  @MessagePattern('task.getLeaderboard')
  @Cacheable({ 
    key: 'task:leaderboard:#{payload.serverId}:#{payload.taskType}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getTaskLeaderboard(@Payload() payload: { serverId: string; taskType?: TaskType; limit?: number; injectedContext?: InjectedContext }) {
    this.logger.log(`获取任务排行榜: ${payload.serverId}, 类型: ${payload.taskType}`);
    const leaderboard = await this.taskService.getTaskLeaderboard(payload.serverId, payload.taskType, payload.limit);
    return {
      code: 0,
      message: '获取成功',
      data: {
        leaderboard,
        serverId: payload.serverId,
        taskType: payload.taskType,
        limit: payload.limit || 100,
      },
    };
  }

  // ==================== 管理接口 ====================

  /**
   * 批量刷新任务（定时任务用）
   */
  @MessagePattern('task.batchRefresh')
  async batchRefreshTasks(@Payload() payload: { taskType: TaskType; injectedContext?: InjectedContext }) {
    this.logger.log(`批量刷新任务: 类型 ${payload.taskType}`);
    const result = await this.taskService.batchRefreshTasks(payload.taskType);
    return {
      code: 0,
      message: '批量刷新完成',
      data: result,
    };
  }

  /**
   * 清理过期任务
   */
  @MessagePattern('task.cleanExpired')
  async cleanExpiredTasks() {
    this.logger.log('清理过期任务');
    // TODO: 实现过期任务清理
    return {
      code: 0,
      message: '清理完成',
      data: {
        cleanedCount: 0,
      },
    };
  }

  // ==================== 特殊任务类型 ====================

  /**
   * 获取每日任务
   */
  @MessagePattern('task.getDailyTasks')
  @Cacheable({ 
    key: 'task:daily:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getDailyTasks(@Payload() payload: { characterId: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取每日任务: ${payload.characterId}`);
    const taskList = await this.taskService.getTaskList(payload.characterId, payload.serverId, {
      characterId: payload.characterId,
      taskType: TaskType.DAILY,
    });
    return {
      code: 0,
      message: '获取成功',
      data: {
        dailyTasks: taskList.dailyTasks,
        dailyProgress: taskList.dailyProgress,
        needsRefresh: taskList.needsDailyRefresh,
      },
    };
  }

  /**
   * 获取新手任务
   */
  @MessagePattern('task.getNewbieTasks')
  @Cacheable({ 
    key: 'task:newbie:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getNewbieTasks(@Payload() payload: { characterId: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取新手任务: ${payload.characterId}`);
    const taskList = await this.taskService.getTaskList(payload.characterId, payload.serverId, {
      characterId: payload.characterId,
      taskType: TaskType.NEWBIE,
    });
    return {
      code: 0,
      message: '获取成功',
      data: {
        newbieTasks: taskList.newbieTasks,
        pendingRewards: taskList.pendingRewards,
      },
    };
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 触发任务进度更新
   * 对应old项目中最核心的triggerTask方法
   */
  @MessagePattern('task.trigger')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async triggerTask(@Payload() payload: { characterId: string; serverId: string; triggerType: number; arg1?: any; arg2?: any; arg3?: any; arg4?: any; injectedContext?: InjectedContext }) {
    this.logger.log(`触发任务: ${payload.characterId}, 类型: ${payload.triggerType}, 参数: ${payload.arg1}, ${payload.arg2}, ${payload.arg3}`);
    const result = await this.taskService.triggerTask(
      payload.characterId,
      payload.serverId,
      payload.triggerType,
      payload.arg1,
      payload.arg2,
      payload.arg3,
      payload.arg4
    );
    return {
      code: 0,
      message: '任务触发成功',
      data: result,
    };
  }

  /**
   * 删除任务
   */
  @MessagePattern('task.delete')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deleteTask(@Payload() payload: { characterId: string; serverId: string; taskType: TaskType; taskId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`删除任务: ${payload.characterId}, 类型: ${payload.taskType}, 任务: ${payload.taskId}`);
    const result = await this.taskService.deleteTask(
      payload.characterId,
      payload.serverId,
      payload.taskType,
      payload.taskId
    );
    return {
      code: 0,
      message: '任务删除成功',
      data: result,
    };
  }

  /**
   * 检查任务列表是否已满
   */
  @MessagePattern('task.checkFull')
  async checkTaskListFull(@Payload() payload: { characterId: string; serverId: string; taskType: TaskType; injectedContext?: InjectedContext }) {
    this.logger.log(`检查任务列表容量: ${payload.characterId}, 类型: ${payload.taskType}`);
    const isFull = await this.taskService.checkTaskListFull(
      payload.characterId,
      payload.serverId,
      payload.taskType
    );
    return {
      code: 0,
      message: '检查完成',
      data: {
        isFull,
        taskType: payload.taskType,
      },
    };
  }

  /**
   * 检查任务是否存在
   */
  @MessagePattern('task.checkExists')
  async checkTaskExists(@Payload() payload: { characterId: string; serverId: string; taskId: number; injectedContext?: InjectedContext }) {
    this.logger.log(`检查任务是否存在: ${payload.characterId}, 任务: ${payload.taskId}`);
    const exists = await this.taskService.checkTaskExists(
      payload.characterId,
      payload.serverId,
      payload.taskId
    );
    return {
      code: 0,
      message: '检查完成',
      data: {
        exists,
        taskId: payload.taskId,
      },
    };
  }

  // ==================== 活动任务功能补充 ====================

  /**
   * 更新活动任务进度
   * 对应old项目act.js中的updateTaskProgress方法
   */
  @MessagePattern('task.updateActivityProgress')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateActivityTaskProgress(@Payload() payload: { characterId: string; serverId: string; activityId: string; taskId: number; progress: number; injectedContext?: InjectedContext }) {
    this.logger.log(`更新活动任务进度: ${payload.characterId}, 活动: ${payload.activityId}, 任务: ${payload.taskId}, 进度: ${payload.progress}`);
    const result = await this.taskService.updateActivityTaskProgress(
      payload.characterId,
      payload.serverId,
      payload.activityId,
      payload.taskId,
      payload.progress
    );
    return {
      code: 0,
      message: '活动任务进度更新成功',
      data: result,
    };
  }

  /**
   * 更新金牌教练任务进度
   * 对应old项目act.js中的updateGoldCoachTaskProgress方法
   */
  @MessagePattern('task.updateGoldCoachProgress')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateGoldCoachTaskProgress(@Payload() payload: { characterId: string; serverId: string; coachAction: string; param?: any; injectedContext?: InjectedContext }) {
    this.logger.log(`更新金牌教练任务进度: ${payload.characterId}, 行为: ${payload.coachAction}`);
    const result = await this.taskService.updateGoldCoachTaskProgress(
      payload.characterId,
      payload.serverId,
      payload.coachAction,
      payload.param
    );
    return {
      code: 0,
      message: '金牌教练任务进度更新成功',
      data: result,
    };
  }

  /**
   * 批量更新活动任务
   */
  @MessagePattern('task.batchUpdateActivity')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchUpdateActivityTasks(@Payload() payload: { characterId: string; serverId: string; updates: Array<{ activityId: string; taskId: number; progress: number; }>; injectedContext?: InjectedContext }) {
    this.logger.log(`批量更新活动任务: ${payload.characterId}, 更新数量: ${payload.updates.length}`);
    const result = await this.taskService.batchUpdateActivityTasks(
      payload.characterId,
      payload.serverId,
      payload.updates
    );
    return {
      code: 0,
      message: '批量更新成功',
      data: result,
    };
  }
}
