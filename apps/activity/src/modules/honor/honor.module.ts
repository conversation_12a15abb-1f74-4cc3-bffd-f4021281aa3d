import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HonorController } from './honor.controller';
import { HonorService } from './honor.service';
import { HonorRepository } from '@activity/common/repositories/honor.repository';
import { Honor, HonorSchema } from '@activity/common/schemas/honor.schema';

/**
 * 荣誉墙模块
 * 对应old项目中的honorWall.js
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Honor.name, schema: HonorSchema },
    ]),
  ],
  controllers: [HonorController],
  providers: [HonorService, HonorRepository],
  exports: [HonorService, HonorRepository],
})
export class HonorModule {}
