# 🚀 Football Manager Gateway

一个功能强大、高性能的 API 网关，为足球经理游戏提供统一的入口点，支持 HTTP、WebSocket 和 GraphQL 协议。

## 🎯 核心特性

### 🔀 路由管理
- **动态路由配置**: 支持运行时路由更新 ✨ **新增**
- **智能路径匹配**: 精确匹配、参数匹配、通配符匹配、正则匹配
- **路由验证**: 自动配置验证和冲突检测 ✨ **新增**
- **版本控制**: API 版本路由管理
- **协议支持**: HTTP/HTTPS、WebSocket、GraphQL
- **热更新**: 无需重启的路由配置更新 ✨ **新增**

### 🔐 认证授权
- **多种认证方式**: JWT、Session、API Key、OAuth2.0
- **权限控制**: RBAC、ABAC、资源级权限
- **安全特性**: Token 刷新、黑名单、防重放攻击
- **认证守卫**: 自动令牌验证和用户注入 ✨ **新增**
- **多令牌来源**: 支持 Header、Query、Cookie 令牌 ✨ **新增**

### 🚦 限流控制
- **多维度限流**: 用户级、IP级、API级、全局限流
- **多种算法**: 滑动窗口、令牌桶、漏桶、固定窗口
- **动态限流**: 基于系统负载的自适应限流
- **限流守卫**: 自动限流检查和响应头设置 ✨ **新增**
- **自定义配置**: 路由级限流配置支持 ✨ **新增**

### ⚖️ 负载均衡
- **多种策略**: 轮询、加权轮询、最少连接、IP哈希、健康状态
- **健康检查**: 主动/被动健康检查
- **故障转移**: 自动故障检测和切换

### 🔌 实时通信
- **WebSocket 网关**: Socket.IO 高性能实时通信 ✨ **已完善**
- **微服务集成**: 统一消息路由到 6 个游戏微服务 ✨ **新增**
- **智能房间管理**: 基于微服务的权限验证和动态房间信息 ✨ **新增**
- **离线消息**: 用户离线时的消息存储和推送 ✨ **新增**
- **多设备支持**: 同一用户多设备连接管理 ✨ **新增**
- **完整守卫系统**: WsAuthGuard + WsRateLimitGuard ✨ **新增**
- **连接管理**: 连接池、心跳检测、断线重连和自动清理

### 📊 监控告警
- **性能指标**: QPS/TPS、响应时间、错误率
- **业务指标**: 用户活跃度、API使用情况
- **告警机制**: 阈值告警、异常检测
- **Prometheus 集成**: 标准指标格式输出 ✨ **新增**
- **实时统计**: 网关状态和性能统计 ✨ **新增**
- **自定义指标**: 业务指标收集支持 ✨ **新增**

## 🏗️ 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                        Gateway Layer                        │
├─────────────────────────────────────────────────────────────┤
│  WebSocket Gateway  │  HTTP Gateway   │  GraphQL Gateway   │
├─────────────────────────────────────────────────────────────┤
│           Middleware Pipeline (洋葱模型)                    │
│  Auth → RateLimit → CORS → Validation → Logging → Proxy    │
├─────────────────────────────────────────────────────────────┤
│                    Service Discovery                        │
│              Load Balancer & Health Check                   │
├─────────────────────────────────────────────────────────────┤
│                    Backend Services                         │
│   User Service  │  Club Service  │  Match Service  │ ...   │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Redis 6+
- MongoDB 5+ (可选)

### 安装依赖
```bash
npm install
```

### 配置环境
```bash
# 复制环境配置文件
cp apps/gateway/.env.example apps/gateway/.env

# 编辑配置文件
vim apps/gateway/.env
```

### 启动服务
```bash
# 开发模式
npm run start:gateway

# 监视模式
npm run start:gateway:dev

# 生产模式
npm run start:gateway:prod
```

### Docker 部署
```bash
# 构建镜像
docker build -f apps/gateway/Dockerfile -t football-manager-gateway .

# 运行容器
docker run -p 3000:3000 --env-file apps/gateway/.env football-manager-gateway
```

## 📖 API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:3000/docs
- 生产环境: https://your-domain.com/docs

## 🔧 配置说明

### 基础配置
```env
GATEWAY_PORT=3000                    # 服务端口
GATEWAY_GLOBAL_PREFIX=api            # API 前缀
GATEWAY_ENABLE_SWAGGER=true          # 启用 Swagger 文档
```

### 认证配置
```env
JWT_SECRET=your-secret-key           # JWT 密钥
JWT_EXPIRES_IN=24h                   # Token 过期时间
SESSION_SECRET=your-session-secret   # Session 密钥
```

### 限流配置
```env
RATE_LIMIT_WINDOW_MS=60000          # 限流窗口时间
RATE_LIMIT_MAX=100                  # 最大请求数
```

### Redis 配置
```env
REDIS_HOST=localhost                # Redis 主机
REDIS_PORT=6379                     # Redis 端口
REDIS_PASSWORD=                     # Redis 密码
```

## 🎮 游戏特定功能

### WebSocket 事件

#### 连接管理
```javascript
// 连接到网关
const socket = io('ws://localhost:3000', {
  auth: {
    token: 'your-jwt-token'
  }
});

// 监听连接确认
socket.on('connected', (data) => {
  console.log('Connected:', data);
});
```

#### 房间管理
```javascript
// 加入房间
socket.emit('join_room', {
  roomId: 'match_123',
  password: 'optional_password'
});

// 监听房间事件
socket.on('joined_room', (data) => {
  console.log('Joined room:', data);
});

// 离开房间
socket.emit('leave_room', {
  roomId: 'match_123'
});
```

#### 消息发送
```javascript
// 发送房间消息
socket.emit('send_message', {
  roomId: 'match_123',
  message: 'Hello everyone!',
  type: 'text'
});

// 发送私聊消息
socket.emit('send_message', {
  targetUserId: 'user_456',
  message: 'Hello!',
  type: 'text'
});
```

### HTTP API 示例

#### 认证
```bash
# 登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "player1", "password": "password123"}'

# 获取用户信息
curl -X GET http://localhost:3000/api/users/me \
  -H "Authorization: Bearer your-jwt-token"
```

#### 游戏数据
```bash
# 获取俱乐部信息
curl -X GET http://localhost:3000/api/clubs/123 \
  -H "Authorization: Bearer your-jwt-token"

# 获取球员列表
curl -X GET http://localhost:3000/api/players?position=FWD&overall=90 \
  -H "Authorization: Bearer your-jwt-token"
```

## 📊 监控和指标

### Prometheus 指标
访问 http://localhost:3000/metrics 查看 Prometheus 格式的指标

### 健康检查
访问 http://localhost:3000/health 查看服务健康状态

### 关键指标
- `gateway_http_requests_total`: HTTP 请求总数
- `gateway_http_request_duration_seconds`: 请求响应时间
- `gateway_websocket_connections`: WebSocket 连接数
- `gateway_rate_limit_total`: 限流事件总数
- `gateway_errors_total`: 错误总数

## 🔒 安全最佳实践

### 生产环境配置
1. **更改默认密钥**
   ```env
   JWT_SECRET=your-production-secret-key
   SESSION_SECRET=your-production-session-secret
   ```

2. **启用 HTTPS**
   ```env
   SSL_ENABLED=true
   SSL_KEY_PATH=/path/to/private.key
   SSL_CERT_PATH=/path/to/certificate.crt
   ```

3. **配置 CORS**
   ```env
   CORS_ORIGIN=https://your-frontend-domain.com
   CORS_CREDENTIALS=true
   ```

4. **启用安全头**
   ```env
   SECURITY_HSTS_ENABLED=true
   SECURITY_CSP_ENABLED=true
   SECURITY_FRAME_OPTIONS=DENY
   ```

### 限流策略
```env
# 严格限流（敏感操作）
RATE_LIMIT_STRICT_WINDOW_MS=60000
RATE_LIMIT_STRICT_MAX=10

# 宽松限流（公开 API）
RATE_LIMIT_LENIENT_WINDOW_MS=60000
RATE_LIMIT_LENIENT_MAX=1000
```

## 🐛 故障排除

### 常见问题

1. **连接 Redis 失败**
   ```bash
   # 检查 Redis 服务状态
   redis-cli ping
   
   # 检查配置
   echo $REDIS_HOST $REDIS_PORT
   ```

2. **WebSocket 连接失败**
   ```bash
   # 检查防火墙设置
   # 检查 CORS 配置
   # 验证认证 Token
   ```

3. **后端服务不可用**
   ```bash
   # 检查服务发现配置
   # 查看健康检查日志
   # 验证负载均衡配置
   ```

### 日志查看
```bash
# 查看实时日志
docker logs -f gateway-container

# 查看错误日志
grep "ERROR" logs/gateway.log

# 查看访问日志
grep "HTTP" logs/gateway.log
```

## 🚀 性能优化

### 生产环境优化
1. **启用集群模式**
   ```env
   PROD_ENABLE_CLUSTERING=true
   PROD_CLUSTER_WORKERS=0  # 使用所有 CPU 核心
   ```

2. **调整连接池**
   ```env
   PROXY_MAX_SOCKETS=200
   KEEP_ALIVE_TIMEOUT=5000
   ```

3. **优化缓存**
   ```env
   CACHE_ENABLED=true
   CACHE_TTL=3600
   CACHE_MAX_ITEMS=10000
   ```

### 监控告警
```env
# 启用详细监控
MONITORING_ENABLE_PROMETHEUS=true
MONITORING_ENABLE_JAEGER=true

# 配置告警阈值
ALERT_ERROR_RATE_THRESHOLD=5
ALERT_RESPONSE_TIME_THRESHOLD=1000
```

## 📝 开发指南

### 添加新路由
1. 在配置中定义路由
2. 配置认证和权限
3. 设置限流策略
4. 添加监控指标

### 自定义中间件
```typescript
@Injectable()
export class CustomMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 自定义逻辑
    next();
  }
}
```

### 扩展认证方式
```typescript
@Injectable()
export class CustomAuthStrategy {
  async validate(token: string): Promise<User> {
    // 自定义认证逻辑
  }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🆘 支持

如有问题或建议，请：
1. 查看文档和 FAQ
2. 搜索已有 Issues
3. 创建新的 Issue
4. 联系开发团队

---

## ✨ 新增功能特性

### 🔧 动态路由管理
```typescript
// 运行时添加新路由
await routeManager.addRoute({
  id: 'new-feature',
  path: '/api/features/new',
  target: { service: 'FEATURE_SERVICE' },
  config: { auth: { required: true } }
});

// 热更新路由配置
await routeManager.updateRoute('new-feature', {
  config: { rateLimit: { max: 200 } }
});
```

### 🛡️ 增强安全守卫
```typescript
// 自动认证验证
@UseGuards(AuthGuard)
@Controller('api/secure')
export class SecureController {
  @Get('data')
  async getData(@Request() req) {
    const user = req.user;  // 自动注入用户信息
  }
}

// 自定义限流配置
@RateLimit({ windowMs: 60000, max: 200 })
@Get('high-frequency')
async getHighFrequencyData() {}
```

### 📊 完整监控集成
```bash
# 查看 Prometheus 指标
curl http://localhost:3000/api/metrics

# 获取网关统计
curl http://localhost:3000/api/metrics/stats

# 重置指标
curl -X POST http://localhost:3000/api/metrics/reset
```

### 🚀 代理模块增强
- ✅ 微服务自动发现和负载均衡
- ✅ 请求/响应自动转换
- ✅ 熔断器保护和重试机制
- ✅ 完整的错误处理和日志记录

## 📚 详细文档

### WebSocket 实时通信文档
- [🔌 WebSocket 概览](./docs/websocket/overview.md) - WebSocket 网关完整介绍 ✨ **已完善**
- [🛡️ WebSocket 守卫](./docs/websocket/guards.md) - 认证和限流守卫系统 ✨ **新增**
- [🏗️ 微服务集成](./docs/websocket/microservices-integration.md) - 与游戏微服务的集成 ✨ **新增**
- [📡 WebSocket API](./docs/websocket/api.md) - 完整的 API 接口文档 ✨ **新增**

### 新增组件文档
- [📖 快速开始指南](./docs/getting-started.md) - 新功能使用指南
- [🔧 路由管理服务](./docs/services/route-manager.md) - 动态路由管理
- [🛡️ 守卫和拦截器](./docs/security/guards-interceptors.md) - 安全组件
- [🚀 代理模块](./docs/modules/proxy-module.md) - HTTP 代理功能
- [📊 指标监控模块](./docs/modules/metrics-module.md) - 监控和指标

### 系统文档
- [🏗️ 核心功能](./docs/gateway/core-features.md) - 完整功能介绍
- [🔀 路由系统](./docs/gateway/routing-system.md) - 智能路由引擎
- [🔒 安全特性](./docs/gateway/security-features.md) - 安全防护
- [⚡ 性能优化](./docs/gateway/performance-optimization.md) - 性能调优

## 🎯 实现状态

### 核心功能完成度
- ✅ **路由系统**: 100% 完成 (新增动态管理)
- ✅ **认证授权**: 100% 完成 (新增守卫组件)
- ✅ **限流保护**: 100% 完成 (新增守卫组件)
- ✅ **负载均衡**: 100% 完成
- ✅ **缓存系统**: 100% 完成
- ✅ **监控指标**: 100% 完成 (新增 Prometheus 集成)
- ✅ **安全防护**: 100% 完成
- ✅ **代理转发**: 100% 完成 (新增代理模块)

### WebSocket 功能完成度
- ✅ **WebSocket 网关**: 100% 完成 (恢复微服务架构)
- ✅ **认证和限流守卫**: 100% 完成 (WsAuthGuard + WsRateLimitGuard)
- ✅ **微服务集成**: 100% 完成 (6个游戏微服务通信)
- ✅ **房间管理**: 100% 完成 (权限验证 + 动态信息)
- ✅ **离线消息**: 100% 完成 (消息队列 + 推送)
- ✅ **多设备支持**: 100% 完成 (连接管理 + 状态同步)

### 新增功能
- ✅ **RouteManagerService** - 完整的路由管理服务
- ✅ **AuthGuard & RateLimitGuard** - 安全守卫组件
- ✅ **ProxyModule** - 完整的代理功能模块
- ✅ **MetricsModule** - Prometheus 监控集成
- ✅ **WebSocket 增强** - 微服务集成 + 完整守卫系统
- ✅ **完整文档** - 详细的使用和开发文档

**Football Manager Gateway** - 为您的足球经理游戏提供强大、可靠的网关服务！ ⚽🚀
