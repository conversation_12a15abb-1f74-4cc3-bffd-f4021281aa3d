# ===== 网关服务路由开发环境配置 =====
# 开发环境特定的路由配置

# 开发环境微服务URL
GATEWAY_AUTH_SERVICE_URL=http://localhost:3001
GATEWAY_CHARACTER_SERVICE_URL=http://localhost:3002
GATEWAY_GAME_SERVICE_URL=http://localhost:3003

# 开发环境超时配置（更宽松）
GATEWAY_REQUEST_TIMEOUT=60000         # 60秒（开发环境调试需要）
GATEWAY_CONNECT_TIMEOUT=10000         # 10秒连接超时
GATEWAY_RESPONSE_TIMEOUT=50000        # 50秒响应超时

# 开发环境重试配置（更少重试）
GATEWAY_RETRY_ATTEMPTS=1              # 开发环境减少重试
GATEWAY_RETRY_DELAY=500               # 500ms重试延迟

# 开发环境负载均衡
GATEWAY_LOAD_BALANCE_STRATEGY=round_robin
GATEWAY_HEALTH_CHECK_INTERVAL=10000   # 10秒健康检查

# 开发环境路由缓存（更短）
GATEWAY_ROUTE_CACHE_ENABLED=true
GATEWAY_ROUTE_CACHE_TTL=60            # 1分钟路由缓存

# 开发环境服务发现
GATEWAY_SERVICE_DISCOVERY_ENABLED=true
GATEWAY_SERVICE_REGISTRY_TTL=30       # 30秒服务注册TTL

# 开发环境WebSocket配置
GATEWAY_WS_ENABLED=true
GATEWAY_WS_HEARTBEAT_INTERVAL=10000   # 10秒心跳
GATEWAY_WS_MAX_CONNECTIONS=100        # 开发环境限制连接数

# 开发环境静态资源
GATEWAY_STATIC_ENABLED=true           # 开发环境启用静态资源
GATEWAY_STATIC_PATH=/public
GATEWAY_STATIC_MAX_AGE=0              # 开发环境不缓存静态资源

# 开发环境调试配置
GATEWAY_DEBUG_MODE=true
GATEWAY_VERBOSE_LOGGING=true
GATEWAY_REQUEST_ID_ENABLED=true
