# 性能优化

足球经理网关的性能优化是确保游戏流畅体验的关键。本文档详细介绍各种性能优化策略、技术和最佳实践。

## 🚀 性能目标

### 关键性能指标 (KPI)

- **响应时间**: P95 < 100ms, P99 < 200ms
- **吞吐量**: 支持 10,000+ QPS
- **并发连接**: 支持 100,000+ WebSocket 连接
- **可用性**: 99.9% 服务可用性
- **错误率**: < 0.1% 错误率

### 性能基准

```typescript
// 性能基准配置
const performanceBenchmarks = {
  http: {
    targetLatency: {
      p50: 50,    // 50ms
      p95: 100,   // 100ms
      p99: 200    // 200ms
    },
    targetThroughput: 10000,  // 10K QPS
    maxConcurrency: 1000      // 1K 并发
  },
  websocket: {
    maxConnections: 100000,   // 100K 连接
    messageLatency: 10,       // 10ms 消息延迟
    heartbeatInterval: 30000  // 30s 心跳间隔
  },
  memory: {
    maxHeapSize: '2GB',       // 最大堆内存
    gcPauseTarget: 10         // GC暂停目标 10ms
  }
};
```

## 🔧 应用层优化

### 1. 异步处理优化

#### 事件循环优化

```typescript
// 避免阻塞事件循环
class NonBlockingProcessor {
  async processLargeDataset(data: any[]): Promise<void> {
    const batchSize = 100;
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      // 处理批次
      await this.processBatch(batch);
      
      // 让出事件循环
      if (i % 1000 === 0) {
        await this.setImmediate();
      }
    }
  }
  
  private setImmediate(): Promise<void> {
    return new Promise(resolve => setImmediate(resolve));
  }
}
```

#### 并发控制

```typescript
// 并发限制器
class ConcurrencyLimiter {
  private semaphore: number;
  private queue: Array<() => void> = [];
  
  constructor(private maxConcurrency: number) {
    this.semaphore = maxConcurrency;
  }
  
  async execute<T>(task: () => Promise<T>): Promise<T> {
    await this.acquire();
    
    try {
      return await task();
    } finally {
      this.release();
    }
  }
  
  private async acquire(): Promise<void> {
    if (this.semaphore > 0) {
      this.semaphore--;
      return;
    }
    
    return new Promise(resolve => {
      this.queue.push(resolve);
    });
  }
  
  private release(): void {
    if (this.queue.length > 0) {
      const next = this.queue.shift()!;
      next();
    } else {
      this.semaphore++;
    }
  }
}
```

### 2. 内存管理优化

#### 对象池

```typescript
// 对象池实现
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(
    createFn: () => T,
    resetFn: (obj: T) => void,
    initialSize: number = 10
  ) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

// 使用示例
const requestPool = new ObjectPool(
  () => ({ headers: {}, body: null, params: {} }),
  (req) => {
    req.headers = {};
    req.body = null;
    req.params = {};
  },
  100
);
```

#### 内存泄漏检测

```typescript
// 内存监控
class MemoryMonitor {
  private intervalId: NodeJS.Timeout;
  
  start(): void {
    this.intervalId = setInterval(() => {
      const usage = process.memoryUsage();
      
      // 记录内存使用情况
      this.recordMemoryMetrics(usage);
      
      // 检查内存泄漏
      if (usage.heapUsed > 1.5 * 1024 * 1024 * 1024) { // 1.5GB
        this.logger.warn('内存使用过高', usage);
        
        // 触发垃圾回收
        if (global.gc) {
          global.gc();
        }
      }
    }, 30000); // 每30秒检查一次
  }
  
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
  
  private recordMemoryMetrics(usage: NodeJS.MemoryUsage): void {
    this.metricsService.recordGauge('memory_heap_used', usage.heapUsed);
    this.metricsService.recordGauge('memory_heap_total', usage.heapTotal);
    this.metricsService.recordGauge('memory_external', usage.external);
    this.metricsService.recordGauge('memory_rss', usage.rss);
  }
}
```

### 3. CPU 优化

#### 计算密集型任务优化

```typescript
// Worker 线程池
import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';

class WorkerPool {
  private workers: Worker[] = [];
  private queue: Array<{
    task: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];
  
  constructor(private poolSize: number, private workerScript: string) {
    this.initializeWorkers();
  }
  
  private initializeWorkers(): void {
    for (let i = 0; i < this.poolSize; i++) {
      const worker = new Worker(this.workerScript);
      
      worker.on('message', (result) => {
        const task = this.queue.shift();
        if (task) {
          task.resolve(result);
        }
        
        // 处理下一个任务
        this.processNextTask(worker);
      });
      
      worker.on('error', (error) => {
        const task = this.queue.shift();
        if (task) {
          task.reject(error);
        }
      });
      
      this.workers.push(worker);
    }
  }
  
  async execute(task: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      
      // 寻找空闲的 worker
      const availableWorker = this.workers.find(w => !w.busy);
      if (availableWorker) {
        this.processNextTask(availableWorker);
      }
    });
  }
  
  private processNextTask(worker: Worker): void {
    if (this.queue.length === 0) {
      worker.busy = false;
      return;
    }
    
    const { task } = this.queue[0];
    worker.busy = true;
    worker.postMessage(task);
  }
}
```

## 💾 缓存优化

### 1. 多级缓存策略

```typescript
// 多级缓存管理器
class MultiLevelCache {
  private l1Cache: Map<string, any> = new Map(); // 内存缓存
  private l2Cache: RedisService;                  // Redis缓存
  
  constructor(
    private redisService: RedisService,
    private l1MaxSize: number = 1000,
    private l1TTL: number = 60000 // 1分钟
  ) {
    this.l2Cache = redisService;
    this.startL1Cleanup();
  }
  
  async get(key: string): Promise<any> {
    // L1 缓存检查
    if (this.l1Cache.has(key)) {
      const item = this.l1Cache.get(key);
      if (Date.now() < item.expiry) {
        return item.value;
      }
      this.l1Cache.delete(key);
    }
    
    // L2 缓存检查
    const l2Value = await this.l2Cache.get(key);
    if (l2Value) {
      // 回填到 L1 缓存
      this.setL1(key, l2Value);
      return l2Value;
    }
    
    return null;
  }
  
  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    // 设置 L1 缓存
    this.setL1(key, value);
    
    // 设置 L2 缓存
    await this.l2Cache.set(key, value, ttl);
  }
  
  private setL1(key: string, value: any): void {
    // LRU 淘汰策略
    if (this.l1Cache.size >= this.l1MaxSize) {
      const firstKey = this.l1Cache.keys().next().value;
      this.l1Cache.delete(firstKey);
    }
    
    this.l1Cache.set(key, {
      value,
      expiry: Date.now() + this.l1TTL
    });
  }
  
  private startL1Cleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, item] of this.l1Cache.entries()) {
        if (now >= item.expiry) {
          this.l1Cache.delete(key);
        }
      }
    }, 60000); // 每分钟清理一次
  }
}
```

### 2. 缓存预热

```typescript
// 缓存预热服务
class CacheWarmupService {
  constructor(
    private cacheService: MultiLevelCache,
    private dataService: any
  ) {}
  
  async warmupCriticalData(): Promise<void> {
    const criticalKeys = [
      'user_rankings',
      'match_schedules',
      'player_market',
      'club_standings'
    ];
    
    const promises = criticalKeys.map(async (key) => {
      try {
        const data = await this.dataService.getCriticalData(key);
        await this.cacheService.set(key, data, 3600); // 1小时
        this.logger.info(`缓存预热完成: ${key}`);
      } catch (error) {
        this.logger.error(`缓存预热失败: ${key}`, error);
      }
    });
    
    await Promise.all(promises);
  }
  
  async warmupUserData(userId: string): Promise<void> {
    const userKeys = [
      `user:${userId}:profile`,
      `user:${userId}:clubs`,
      `user:${userId}:matches`,
      `user:${userId}:notifications`
    ];
    
    for (const key of userKeys) {
      try {
        const data = await this.dataService.getUserData(userId, key);
        await this.cacheService.set(key, data, 1800); // 30分钟
      } catch (error) {
        this.logger.error(`用户缓存预热失败: ${key}`, error);
      }
    }
  }
}
```

## 🌐 网络优化

### 1. 连接池管理

```typescript
// HTTP 连接池
class HttpConnectionPool {
  private pools: Map<string, any> = new Map();
  
  constructor(private defaultConfig: any) {}
  
  getAgent(targetUrl: string): any {
    const key = this.getPoolKey(targetUrl);
    
    if (!this.pools.has(key)) {
      const agent = new http.Agent({
        keepAlive: true,
        keepAliveMsecs: 30000,
        maxSockets: 50,
        maxFreeSockets: 10,
        timeout: 60000,
        freeSocketTimeout: 30000
      });
      
      this.pools.set(key, agent);
    }
    
    return this.pools.get(key);
  }
  
  private getPoolKey(url: string): string {
    const parsed = new URL(url);
    return `${parsed.protocol}//${parsed.host}`;
  }
}
```

### 2. 请求批处理

```typescript
// 请求批处理器
class RequestBatcher {
  private batches: Map<string, any[]> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(
    private batchSize: number = 10,
    private batchTimeout: number = 100
  ) {}
  
  async addRequest(batchKey: string, request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(batchKey)) {
        this.batches.set(batchKey, []);
      }
      
      const batch = this.batches.get(batchKey)!;
      batch.push({ request, resolve, reject });
      
      // 检查是否达到批次大小
      if (batch.length >= this.batchSize) {
        this.processBatch(batchKey);
      } else if (!this.timers.has(batchKey)) {
        // 设置超时处理
        const timer = setTimeout(() => {
          this.processBatch(batchKey);
        }, this.batchTimeout);
        
        this.timers.set(batchKey, timer);
      }
    });
  }
  
  private async processBatch(batchKey: string): Promise<void> {
    const batch = this.batches.get(batchKey);
    if (!batch || batch.length === 0) {
      return;
    }
    
    // 清理
    this.batches.delete(batchKey);
    const timer = this.timers.get(batchKey);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(batchKey);
    }
    
    try {
      // 批量处理请求
      const requests = batch.map(item => item.request);
      const results = await this.executeBatch(batchKey, requests);
      
      // 返回结果
      batch.forEach((item, index) => {
        item.resolve(results[index]);
      });
    } catch (error) {
      // 错误处理
      batch.forEach(item => {
        item.reject(error);
      });
    }
  }
  
  private async executeBatch(batchKey: string, requests: any[]): Promise<any[]> {
    // 根据批次键执行不同的批处理逻辑
    switch (batchKey) {
      case 'user_data':
        return this.batchUserData(requests);
      case 'match_data':
        return this.batchMatchData(requests);
      default:
        throw new Error(`未知的批次类型: ${batchKey}`);
    }
  }
}
```

## 📊 监控和分析

### 1. 性能监控

```typescript
// 性能监控服务
class PerformanceMonitor {
  private metrics: Map<string, any> = new Map();
  
  startTimer(name: string): () => void {
    const start = process.hrtime.bigint();
    
    return () => {
      const end = process.hrtime.bigint();
      const duration = Number(end - start) / 1000000; // 转换为毫秒
      
      this.recordMetric(name, duration);
    };
  }
  
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, {
        count: 0,
        sum: 0,
        min: Infinity,
        max: -Infinity,
        values: []
      });
    }
    
    const metric = this.metrics.get(name);
    metric.count++;
    metric.sum += value;
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    metric.values.push(value);
    
    // 保持最近1000个值
    if (metric.values.length > 1000) {
      metric.values.shift();
    }
  }
  
  getMetrics(name: string): any {
    const metric = this.metrics.get(name);
    if (!metric) {
      return null;
    }
    
    const sorted = [...metric.values].sort((a, b) => a - b);
    const p50 = sorted[Math.floor(sorted.length * 0.5)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const p99 = sorted[Math.floor(sorted.length * 0.99)];
    
    return {
      count: metric.count,
      avg: metric.sum / metric.count,
      min: metric.min,
      max: metric.max,
      p50,
      p95,
      p99
    };
  }
}
```

### 2. 性能分析

```typescript
// 性能分析器
class PerformanceProfiler {
  private profiles: Map<string, any> = new Map();
  
  profile(name: string, fn: Function): Function {
    return (...args: any[]) => {
      const timer = this.performanceMonitor.startTimer(name);
      
      try {
        const result = fn.apply(this, args);
        
        if (result instanceof Promise) {
          return result.finally(() => timer());
        } else {
          timer();
          return result;
        }
      } catch (error) {
        timer();
        throw error;
      }
    };
  }
  
  async analyzeBottlenecks(): Promise<any> {
    const analysis = {};
    
    for (const [name, _] of this.performanceMonitor.metrics) {
      const metrics = this.performanceMonitor.getMetrics(name);
      
      if (metrics.p95 > 100) { // P95 超过100ms
        analysis[name] = {
          severity: metrics.p95 > 500 ? 'critical' : 'warning',
          metrics,
          recommendations: this.getRecommendations(name, metrics)
        };
      }
    }
    
    return analysis;
  }
  
  private getRecommendations(name: string, metrics: any): string[] {
    const recommendations = [];
    
    if (metrics.p95 > 500) {
      recommendations.push('考虑添加缓存');
      recommendations.push('优化数据库查询');
    }
    
    if (metrics.p95 > 200) {
      recommendations.push('检查网络延迟');
      recommendations.push('考虑异步处理');
    }
    
    return recommendations;
  }
}
```

这些性能优化策略确保足球经理网关能够在高负载下保持优秀的性能表现，为用户提供流畅的游戏体验。
