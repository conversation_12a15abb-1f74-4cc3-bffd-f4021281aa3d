# 代理模块 (ProxyModule)

代理模块是网关的核心功能模块，负责HTTP请求的路由、转发和处理。

## 🎯 模块概述

### 核心功能
- **HTTP 请求代理**: 将客户端请求转发到后端服务
- **智能路由**: 基于路径、方法、头部等条件进行路由匹配
- **负载均衡**: 支持多种负载均衡算法
- **熔断保护**: 防止级联故障
- **请求转换**: 支持请求和响应的数据转换
- **微服务集成**: 与 Redis 微服务通信

## 📋 模块组成

### 控制器
- **ProxyController**: 处理所有代理请求的主控制器

### 服务
- **ProxyService**: 核心代理逻辑实现
- **RouteMatcherService**: 路由匹配服务
- **RouteManagerService**: 路由管理服务
- **LoadBalancerService**: 负载均衡服务
- **CircuitBreakerService**: 熔断器服务

### 守卫和拦截器
- **AuthGuard**: 认证守卫
- **RateLimitGuard**: 限流守卫
- **ProxyInterceptor**: 代理拦截器

## 🔧 配置说明

### 微服务客户端配置
```typescript
// proxy.module.ts
ClientsModule.register([
  {
    name: 'AUTH_SERVICE',
    transport: Transport.REDIS,
    options: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      retryAttempts: 5,
      retryDelay: 3000,
    },
  },
  // 其他服务配置...
])
```

### 支持的游戏服务
1. **AUTH_SERVICE**: 认证服务
2. **CHARACTER_SERVICE**: 用户管理服务
3. **GAME_SERVICE**: 游戏核心服务
4. **CLUB_SERVICE**: 俱乐部管理服务
5. **MATCH_SERVICE**: 比赛服务
6. **CARD_SERVICE**: 卡牌服务

## 🚀 使用示例

### 基础代理请求
```typescript
// 客户端请求
GET /api/users/me
Authorization: Bearer <token>

// 网关处理流程:
// 1. AuthGuard 验证令牌
// 2. RouteMatcherService 匹配路由
// 3. ProxyService 转发到 CHARACTER_SERVICE
// 4. 返回用户信息
```

### 路由配置示例
```typescript
const userRoutes: Route[] = [
  {
    id: 'user-profile',
    name: '用户资料',
    path: '/api/users/me',
    method: RequestMethod.GET,
    target: {
      service: 'CHARACTER_SERVICE',
      path: '/users/profile',
      protocol: 'http',
      loadBalancer: 'round-robin'
    },
    config: {
      enabled: true,
      version: '1.0.0',
      auth: { required: true },
      rateLimit: { 
        enabled: true,
        windowMs: 60000,
        max: 100
      },
      cache: { 
        enabled: true,
        ttl: 300
      },
      proxy: {
        timeout: 5000,
        retries: 3,
        retryDelay: 1000,
        stripPath: false,
        preserveHost: false
      },
      validation: { enabled: true },
      transform: {
        request: { enabled: false },
        response: { enabled: true }
      },
      monitoring: {
        enabled: true,
        logRequest: true,
        logResponse: false,
        metrics: true,
        tracing: true
      }
    },
    middleware: ['auth', 'rate-limit'],
    enabled: true,
    priority: 100,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];
```

## 🎮 游戏场景应用

### 用户管理场景
```typescript
// 用户注册
POST /api/users/register
{
  "username": "player123",
  "email": "<EMAIL>",
  "password": "securePassword"
}

// 路由到 CHARACTER_SERVICE
// 目标: POST /users/register
```

### 比赛数据场景
```typescript
// 获取实时比赛数据
GET /api/matches/123/live

// 路由配置:
{
  path: '/api/matches/:id/live',
  target: {
    service: 'MATCH_SERVICE',
    path: '/matches/:id/live'
  },
  config: {
    cache: { enabled: false },  // 实时数据不缓存
    rateLimit: { max: 50, windowMs: 60000 }
  }
}
```

### 卡牌交易场景
```typescript
// 卡牌交易
POST /api/cards/trade
{
  "fromUserId": "user123",
  "toUserId": "user456",
  "cardIds": ["card1", "card2"]
}

// 路由配置:
{
  path: '/api/cards/trade',
  target: {
    service: 'CARD_SERVICE',
    path: '/cards/trade'
  },
  config: {
    auth: { required: true },
    rateLimit: { max: 10, windowMs: 60000 },  // 严格限流
    validation: { enabled: true }
  }
}
```

## 🔄 请求处理流程

### 1. 请求接收
```
客户端请求 → ProxyController.handleRequest()
```

### 2. 路由匹配
```typescript
// RouteMatcherService 匹配路由
const route = await this.routeMatcherService.matchRoute(
  method,
  path,
  availableRoutes
);
```

### 3. 中间件处理
```
AuthGuard → RateLimitGuard → ProxyInterceptor
```

### 4. 服务转发
```typescript
// ProxyService 转发请求
const response = await this.proxyService.forwardRequest(
  route,
  request,
  context
);
```

### 5. 响应处理
```
响应转换 → 缓存处理 → 返回客户端
```

## ⚙️ 高级配置

### 负载均衡配置
```typescript
// 轮询算法
{
  loadBalancer: 'round-robin'
}

// 加权轮询
{
  loadBalancer: 'weighted-round-robin',
  weights: { server1: 3, server2: 1 }
}

// 最少连接
{
  loadBalancer: 'least-connections'
}

// 一致性哈希
{
  loadBalancer: 'consistent-hash',
  hashKey: 'user-id'
}

// IP 哈希
{
  loadBalancer: 'ip-hash'
}
```

### 熔断器配置
```typescript
{
  circuitBreaker: {
    enabled: true,
    failureThreshold: 5,      // 失败阈值
    recoveryTimeout: 30000,   // 恢复超时
    monitoringPeriod: 10000   // 监控周期
  }
}
```

### 请求转换配置
```typescript
{
  transform: {
    request: {
      enabled: true,
      addHeaders: {
        'X-Gateway-Version': '1.0.0',
        'X-Request-ID': '${requestId}'
      },
      removeHeaders: ['X-Internal-Token'],
      renameFields: {
        'userId': 'user_id',
        'matchId': 'match_id'
      }
    },
    response: {
      enabled: true,
      addHeaders: {
        'X-Response-Time': '${responseTime}ms'
      },
      removeFields: ['internalData'],
      renameFields: {
        'user_id': 'userId'
      }
    }
  }
}
```

## 📊 监控和指标

### 性能指标
- 请求总数
- 响应时间分布
- 错误率统计
- 服务可用性

### 业务指标
- 用户活跃度
- API 使用频率
- 功能模块访问量

### 日志输出
```
[ProxyController] Handling request: GET /api/users/me
[RouteMatcherService] Route matched: user-profile
[ProxyService] Forwarding to CHARACTER_SERVICE: /users/profile
[ProxyInterceptor] Request completed: 150ms
```

## 🚨 错误处理

### 常见错误类型
```typescript
// 路由未找到
{
  "statusCode": 404,
  "message": "Route not found",
  "path": "/api/unknown"
}

// 服务不可用
{
  "statusCode": 503,
  "message": "Service unavailable",
  "service": "CHARACTER_SERVICE"
}

// 请求超时
{
  "statusCode": 504,
  "message": "Gateway timeout",
  "timeout": 5000
}
```

### 重试机制
```typescript
{
  proxy: {
    retries: 3,           // 重试次数
    retryDelay: 1000,     // 重试延迟
    retryCondition: (error) => {
      // 自定义重试条件
      return error.status >= 500;
    }
  }
}
```

代理模块为网关提供了完整的HTTP代理能力，支持复杂的游戏业务场景和高并发访问需求。
