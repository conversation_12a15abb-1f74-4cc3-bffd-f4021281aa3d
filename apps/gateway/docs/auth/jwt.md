# JWT Authentication

The Football Manager Gateway provides comprehensive JWT (JSON Web Token) authentication with advanced features like token refresh, blacklisting, and multi-device support.

## Overview

JWT authentication in the gateway provides:
- Stateless authentication
- Token refresh mechanism
- Token blacklisting for security
- Multi-device session management
- Role and permission-based access control

## Token Structure

### Access Token Payload

```json
{
  "sub": "user-12345",           // User ID
  "username": "john_doe",        // Username
  "email": "<EMAIL>",   // Email address
  "roles": ["user", "player"],   // User roles
  "permissions": [               // User permissions
    "game:play",
    "profile:read",
    "profile:write"
  ],
  "level": 5,                    // User level
  "iat": 1640995200,            // Issued at
  "exp": 1641081600,            // Expires at
  "jti": "token-uuid"           // JWT ID for blacklisting
}
```

### Refresh Token Payload

```json
{
  "sub": "user-12345",          // User ID
  "type": "refresh",            // Token type
  "sessionId": "session-uuid",  // Session ID
  "iat": 1640995200,           // Issued at
  "exp": 1643587200,           // Expires at (longer than access token)
  "jti": "refresh-token-uuid"  // JWT ID
}
```

## Configuration

### Environment Variables

```env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=football-manager-gateway
JWT_AUDIENCE=football-manager-app

# Security Settings
JWT_ENABLE_BLACKLIST=true
JWT_BLACKLIST_TTL=86400
JWT_MAX_SESSIONS_PER_USER=5
```

### Service Configuration

```typescript
// gateway.config.ts
export default () => ({
  gateway: {
    security: {
      jwtSecret: process.env.JWT_SECRET,
      jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
      jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      jwtIssuer: process.env.JWT_ISSUER || 'football-manager-gateway',
      jwtAudience: process.env.JWT_AUDIENCE || 'football-manager-app',
      enableBlacklist: process.env.JWT_ENABLE_BLACKLIST === 'true',
      blacklistTtl: parseInt(process.env.JWT_BLACKLIST_TTL || '86400'),
      maxSessionsPerUser: parseInt(process.env.JWT_MAX_SESSIONS_PER_USER || '5'),
    },
  },
});
```

## Usage Examples

### 1. Token Generation

```typescript
import { AuthService } from '../core/auth/auth.service';

const authService = app.get(AuthService);

// User data
const user = {
  id: 'user-12345',
  username: 'john_doe',
  email: '<EMAIL>',
  roles: ['user', 'player'],
  permissions: ['game:play', 'profile:read'],
  level: 5,
  status: 'active',
  metadata: {},
  createdAt: new Date(),
};

// Generate JWT tokens
const tokenResult = await authService.generateJwtToken(user);

console.log('Access Token:', tokenResult.accessToken);
console.log('Refresh Token:', tokenResult.refreshToken);
console.log('Expires At:', tokenResult.expiresAt);
```

### 2. Token Validation

```typescript
// Validate access token
try {
  const authContext = await authService.validateJwtToken(accessToken);
  
  if (authContext.authenticated) {
    console.log('User:', authContext.user);
    console.log('Roles:', authContext.roles);
    console.log('Permissions:', authContext.permissions);
  }
} catch (error) {
  console.error('Token validation failed:', error.message);
}
```

### 3. Token Refresh

```typescript
// Refresh expired access token
try {
  const newTokenResult = await authService.refreshJwtToken(refreshToken);
  
  console.log('New Access Token:', newTokenResult.accessToken);
  console.log('New Refresh Token:', newTokenResult.refreshToken);
} catch (error) {
  console.error('Token refresh failed:', error.message);
}
```

### 4. Token Blacklisting (Logout)

```typescript
// Blacklist token on logout
try {
  await authService.blacklistToken(accessToken);
  console.log('Token blacklisted successfully');
} catch (error) {
  console.error('Token blacklist failed:', error.message);
}
```

## HTTP Headers

### Authorization Header

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Custom Headers

The gateway also supports custom authentication headers:

```http
X-Auth-Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-API-Key: your-api-key-here
```

## Middleware Integration

### Authentication Middleware

```typescript
import { AuthMiddleware } from '../core/middleware/auth.middleware';

// Apply to all routes
app.use(AuthMiddleware);

// Or apply to specific routes
app.use('/api/protected', AuthMiddleware);
```

### Route-level Authentication

```typescript
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '../core/guards/auth.guard';

@Controller('users')
@UseGuards(AuthGuard)
export class UsersController {
  @Get('profile')
  getProfile(@Req() req: AuthenticatedRequest) {
    return req.user; // User from JWT token
  }
}
```

## Permission Checking

### Basic Permission Check

```typescript
// Check if user has specific permission
const hasPermission = await authService.checkPermission(
  authContext,
  'game',
  'play'
);

if (hasPermission) {
  // Allow action
} else {
  // Deny access
}
```

### Role-based Access

```typescript
// Check if user has required role
const hasRole = authService.checkRole(authContext, ['admin', 'moderator']);

if (hasRole) {
  // Allow admin action
}
```

### Decorator-based Authorization

```typescript
import { RequirePermissions, RequireRoles } from '../core/decorators/auth.decorators';

@Controller('admin')
export class AdminController {
  @Get('users')
  @RequireRoles(['admin'])
  @RequirePermissions(['user:read'])
  getUsers() {
    // Only accessible by admin with user:read permission
  }
}
```

## Security Features

### Token Blacklisting

Tokens are blacklisted in Redis when:
- User logs out
- Token is compromised
- User account is suspended
- Manual blacklisting via admin API

```typescript
// Check if token is blacklisted
const isBlacklisted = await authService.isTokenBlacklisted(tokenId);
```

### Multi-device Session Management

```typescript
// Get user sessions
const sessions = await authService.getUserSessions(userId);

// Revoke specific session
await authService.revokeSession(sessionId);

// Revoke all sessions except current
await authService.revokeOtherSessions(userId, currentSessionId);
```

### Session Limits

```typescript
// Enforce session limits
const sessionCount = await authService.getSessionCount(userId);
const maxSessions = configService.get('gateway.security.maxSessionsPerUser');

if (sessionCount >= maxSessions) {
  // Remove oldest session
  await authService.removeOldestSession(userId);
}
```

## Error Handling

### Common JWT Errors

```typescript
try {
  const authContext = await authService.validateJwtToken(token);
} catch (error) {
  switch (error.name) {
    case 'TokenExpiredError':
      // Token has expired
      return { error: 'Token expired', code: 'TOKEN_EXPIRED' };
      
    case 'JsonWebTokenError':
      // Invalid token
      return { error: 'Invalid token', code: 'INVALID_TOKEN' };
      
    case 'NotBeforeError':
      // Token not active yet
      return { error: 'Token not active', code: 'TOKEN_NOT_ACTIVE' };
      
    case 'UnauthorizedException':
      // Custom authentication error
      return { error: error.message, code: 'UNAUTHORIZED' };
      
    default:
      // Unknown error
      return { error: 'Authentication failed', code: 'AUTH_FAILED' };
  }
}
```

### Error Response Format

```json
{
  "statusCode": 401,
  "message": "Token expired",
  "error": "Unauthorized",
  "code": "TOKEN_EXPIRED",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "path": "/api/users/profile"
}
```

## Best Practices

### 1. Token Security

- Use strong, random JWT secrets
- Rotate JWT secrets regularly
- Set appropriate token expiration times
- Implement token blacklisting
- Use HTTPS in production

### 2. Session Management

- Limit concurrent sessions per user
- Implement session timeout
- Track session activity
- Provide session management UI

### 3. Permission Design

- Use principle of least privilege
- Implement hierarchical permissions
- Regular permission audits
- Document permission requirements

### 4. Monitoring

- Log authentication events
- Monitor failed login attempts
- Track token usage patterns
- Set up security alerts

## Testing

### Unit Tests

```typescript
describe('AuthService', () => {
  it('should generate valid JWT token', async () => {
    const tokenResult = await authService.generateJwtToken(mockUser);
    
    expect(tokenResult.accessToken).toBeDefined();
    expect(tokenResult.refreshToken).toBeDefined();
    expect(tokenResult.expiresAt).toBeInstanceOf(Date);
  });
  
  it('should validate JWT token', async () => {
    const token = await authService.generateJwtToken(mockUser);
    const authContext = await authService.validateJwtToken(token.accessToken);
    
    expect(authContext.authenticated).toBe(true);
    expect(authContext.user.id).toBe(mockUser.id);
  });
});
```

### Integration Tests

```typescript
describe('JWT Authentication E2E', () => {
  it('should authenticate with valid token', async () => {
    const response = await request(app.getHttpServer())
      .get('/api/users/profile')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);
      
    expect(response.body.id).toBe(userId);
  });
  
  it('should reject invalid token', async () => {
    await request(app.getHttpServer())
      .get('/api/users/profile')
      .set('Authorization', 'Bearer invalid-token')
      .expect(401);
  });
});
```

## Troubleshooting

### Common Issues

1. **Token Validation Fails**
   - Check JWT secret configuration
   - Verify token format
   - Check token expiration
   - Ensure Redis connectivity

2. **Permission Denied**
   - Verify user roles and permissions
   - Check permission configuration
   - Review access control logic

3. **Session Issues**
   - Check Redis session storage
   - Verify session configuration
   - Review session timeout settings

### Debug Mode

Enable debug logging for authentication:

```env
LOG_LEVEL=debug
DEBUG_AUTH=true
```

This will log detailed authentication information for troubleshooting.
