import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@libs/redis';

// 接口定义
import {
  BaseGlobalMessage,
  MessageDeliveryRecord,
  GlobalMessageType,
  MessagePriority,
} from '../interfaces/global-message.interface';

/**
 * 消息持久化服务
 * 负责全服消息的持久化存储、历史记录管理和数据恢复
 * 
 * 核心功能：
 * 1. 消息存储：将消息持久化到Redis和数据库
 * 2. 历史记录：维护消息的完整历史记录
 * 3. 投递记录：记录消息的投递状态和结果
 * 4. 数据恢复：支持消息数据的恢复和重放
 * 5. 存储优化：定期清理过期数据，优化存储空间
 */
@Injectable()
export class MessagePersistenceService {
  private readonly logger = new Logger(MessagePersistenceService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 持久化消息
   */
  async persistMessage(message: BaseGlobalMessage): Promise<void> {
    this.logger.debug(`💾 Persisting message: ${message.id}`);

    try {
      // 存储到Redis（主存储）
      await this.storeToRedis(message);
      
      // 存储到历史记录
      await this.storeToHistory(message);
      
      // 创建索引
      await this.createMessageIndex(message);

      this.logger.debug(`✅ Message persisted successfully: ${message.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to persist message: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 获取消息
   */
  async getMessage(messageId: string): Promise<BaseGlobalMessage | null> {
    try {
      const messageKey = `global_message:${messageId}`;
      const messageData = await this.redisService.get(messageKey, 'global');
      
      if (!messageData) {
        this.logger.debug(`📭 Message not found: ${messageId}`);
        return null;
      }

      return JSON.parse(messageData as string);

    } catch (error) {
      this.logger.error(`❌ Failed to get message: ${messageId}`, error);
      return null;
    }
  }

  /**
   * 更新消息状态
   */
  async updateMessageStatus(messageId: string, status: string, metadata?: any): Promise<void> {
    this.logger.debug(`🔄 Updating message status: ${messageId} -> ${status}`);

    try {
      const message = await this.getMessage(messageId);
      if (!message) {
        throw new Error(`Message not found: ${messageId}`);
      }

      // 更新消息元数据
      message.metadata = {
        ...message.metadata,
        status,
        lastUpdated: new Date(),
        ...metadata,
      };

      // 重新存储
      await this.storeToRedis(message);

      // 记录状态变更历史
      await this.recordStatusChange(messageId, status, metadata);

      this.logger.debug(`✅ Message status updated: ${messageId}`);

    } catch (error) {
      this.logger.error(`❌ Failed to update message status: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 记录消息投递
   */
  async recordDelivery(delivery: MessageDeliveryRecord): Promise<void> {
    this.logger.debug(`📝 Recording delivery: ${delivery.messageId} -> ${delivery.userId}`);

    try {
      const deliveryKey = `message_delivery:${delivery.messageId}:${delivery.userId}`;
      await this.redisService.set(
        deliveryKey,
        JSON.stringify(delivery),
        3600 * 24 * 30, // 保存30天
        'global'
      );

      // 更新投递统计
      await this.updateDeliveryStats(delivery);

      this.logger.debug(`✅ Delivery recorded: ${delivery.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to record delivery: ${delivery.id}`, error);
      throw error;
    }
  }

  /**
   * 获取消息投递记录
   */
  async getDeliveryRecords(messageId: string): Promise<MessageDeliveryRecord[]> {
    try {
      const pattern = `message_delivery:${messageId}:*`;
      const keys = await this.redisService.keys(pattern, 'global');
      
      const records: MessageDeliveryRecord[] = [];
      for (const key of keys) {
        const recordData = await this.redisService.get(key, 'global');
        if (recordData) {
          records.push(JSON.parse(recordData as string));
        }
      }

      return records;

    } catch (error) {
      this.logger.error(`❌ Failed to get delivery records: ${messageId}`, error);
      return [];
    }
  }

  /**
   * 查询消息历史
   */
  async getMessageHistory(filters: {
    type?: GlobalMessageType;
    priority?: MessagePriority;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<BaseGlobalMessage[]> {
    this.logger.debug(`🔍 Querying message history with filters:`, filters);

    try {
      const historyKey = 'global_messages:history';
      const limit = filters.limit || 100;
      
      // 从历史记录中获取消息ID列表
      const messageIds = await this.redisService.lrange(historyKey, 0, limit - 1, 'global');
      
      const messages: BaseGlobalMessage[] = [];
      for (const messageId of messageIds) {
        const message = await this.getMessage(messageId as string);
        if (message && this.matchesFilters(message, filters)) {
          messages.push(message);
        }
      }

      return messages;

    } catch (error) {
      this.logger.error('❌ Failed to get message history', error);
      return [];
    }
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: string): Promise<boolean> {
    this.logger.log(`🗑️ Deleting message: ${messageId}`);

    try {
      // 删除主消息记录
      const messageKey = `global_message:${messageId}`;
      const deleted = await this.redisService.del(messageKey, 'global');

      if (deleted > 0) {
        // 删除相关的投递记录
        await this.deleteDeliveryRecords(messageId);
        
        // 从历史记录中移除
        await this.removeFromHistory(messageId);
        
        // 删除索引
        await this.deleteMessageIndex(messageId);

        this.logger.log(`✅ Message deleted successfully: ${messageId}`);
        return true;
      }

      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to delete message: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 清理过期消息
   */
  async cleanupExpiredMessages(): Promise<number> {
    this.logger.log('🧹 Starting cleanup of expired messages');

    try {
      let cleanedCount = 0;
      const now = new Date();

      // 获取所有消息ID
      const historyKey = 'global_messages:history';
      const messageIds = await this.redisService.lrange(historyKey, 0, -1, 'global');

      for (const messageId of messageIds) {
        const message = await this.getMessage(messageId as string);
        
        if (message && message.expireAt && new Date(message.expireAt) < now) {
          await this.deleteMessage(message.id);
          cleanedCount++;
        }
      }

      this.logger.log(`🧹 Cleanup completed: ${cleanedCount} expired messages removed`);
      return cleanedCount;

    } catch (error) {
      this.logger.error('❌ Error during expired messages cleanup', error);
      return 0;
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<any> {
    try {
      const stats = {
        totalMessages: 0,
        totalDeliveries: 0,
        storageSize: 0,
        oldestMessage: null,
        newestMessage: null,
      };

      // 获取消息总数
      const historyKey = 'global_messages:history';
      stats.totalMessages = await this.redisService.llen(historyKey, 'global');

      // 获取投递记录总数
      const deliveryPattern = 'message_delivery:*';
      const deliveryKeys = await this.redisService.keys(deliveryPattern, 'global');
      stats.totalDeliveries = deliveryKeys.length;

      // 获取最新和最旧的消息
      if (stats.totalMessages > 0) {
        const newestId = await this.redisService.lindex(historyKey, 0, 'global');
        const oldestId = await this.redisService.lindex(historyKey, -1, 'global');
        
        if (newestId) {
          const newestMessage = await this.getMessage(newestId as string);
          stats.newestMessage = newestMessage?.createdAt;
        }
        
        if (oldestId) {
          const oldestMessage = await this.getMessage(oldestId as string);
          stats.oldestMessage = oldestMessage?.createdAt;
        }
      }

      return stats;

    } catch (error) {
      this.logger.error('❌ Failed to get storage stats', error);
      return null;
    }
  }

  /**
   * 存储到Redis
   */
  private async storeToRedis(message: BaseGlobalMessage): Promise<void> {
    const messageKey = `global_message:${message.id}`;
    const ttl = this.calculateTTL(message);
    
    await this.redisService.set(
      messageKey,
      JSON.stringify(message),
      ttl,
      'global'
    );
  }

  /**
   * 存储到历史记录
   */
  private async storeToHistory(message: BaseGlobalMessage): Promise<void> {
    const historyKey = 'global_messages:history';
    
    // 添加到历史列表头部
    await this.redisService.lpush(historyKey, message.id, 'global');
    
    // 限制历史记录数量（保留最近10000条）
    await this.redisService.ltrim(historyKey, 0, 9999, 'global');
  }

  /**
   * 创建消息索引
   */
  private async createMessageIndex(message: BaseGlobalMessage): Promise<void> {
    // 按类型索引
    const typeIndexKey = `message_index:type:${message.type}`;
    await this.redisService.sadd(typeIndexKey, message.id, 'global');
    
    // 按优先级索引
    const priorityIndexKey = `message_index:priority:${message.priority}`;
    await this.redisService.sadd(priorityIndexKey, message.id, 'global');
    
    // 按日期索引
    const dateKey = message.createdAt.toISOString().split('T')[0];
    const dateIndexKey = `message_index:date:${dateKey}`;
    await this.redisService.sadd(dateIndexKey, message.id, 'global');
  }

  /**
   * 记录状态变更
   */
  private async recordStatusChange(messageId: string, status: string, metadata?: any): Promise<void> {
    const statusHistoryKey = `message_status_history:${messageId}`;
    const statusRecord = {
      status,
      timestamp: new Date(),
      metadata,
    };
    
    await this.redisService.lpush(statusHistoryKey, JSON.stringify(statusRecord), 'global');
    await this.redisService.ltrim(statusHistoryKey, 0, 99, 'global'); // 保留最近100条状态变更
  }

  /**
   * 更新投递统计
   */
  private async updateDeliveryStats(delivery: MessageDeliveryRecord): Promise<void> {
    const statsKey = `delivery_stats:${delivery.messageId}`;
    const stats = {
      totalDeliveries: 0,
      successfulDeliveries: 0,
      failedDeliveries: 0,
      pendingDeliveries: 0,
      lastUpdated: new Date(),
    };

    // 获取现有统计
    const existingStatsData = await this.redisService.get(statsKey, 'global');
    if (existingStatsData) {
      Object.assign(stats, JSON.parse(existingStatsData as string));
    }

    // 更新统计
    stats.totalDeliveries += 1;
    switch (delivery.status) {
      case 'delivered':
      case 'acknowledged':
        stats.successfulDeliveries += 1;
        break;
      case 'failed':
        stats.failedDeliveries += 1;
        break;
      case 'pending':
        stats.pendingDeliveries += 1;
        break;
    }

    await this.redisService.set(statsKey, JSON.stringify(stats), 3600 * 24 * 30, 'global');
  }

  /**
   * 删除投递记录
   */
  private async deleteDeliveryRecords(messageId: string): Promise<void> {
    const pattern = `message_delivery:${messageId}:*`;
    const keys = await this.redisService.keys(pattern, 'global');
    
    for (const key of keys) {
      await this.redisService.del(key, 'global');
    }
  }

  /**
   * 从历史记录中移除
   */
  private async removeFromHistory(messageId: string): Promise<void> {
    const historyKey = 'global_messages:history';
    await this.redisService.lrem(historyKey, 0, messageId, 'global');
  }

  /**
   * 删除消息索引
   */
  private async deleteMessageIndex(messageId: string): Promise<void> {
    // 这里需要知道消息的类型和优先级才能删除索引
    // 简化处理，实际实现时需要先获取消息信息
    const indexPatterns = [
      'message_index:type:*',
      'message_index:priority:*',
      'message_index:date:*',
    ];

    for (const pattern of indexPatterns) {
      const keys = await this.redisService.keys(pattern, 'global');
      for (const key of keys) {
        await this.redisService.srem(key, messageId, 'global');
      }
    }
  }

  /**
   * 计算TTL
   */
  private calculateTTL(message: BaseGlobalMessage): number {
    if (message.expireAt) {
      const ttl = Math.floor((message.expireAt.getTime() - Date.now()) / 1000);
      return Math.max(ttl, 3600); // 最少1小时
    }
    
    // 默认保存30天
    return 3600 * 24 * 30;
  }

  /**
   * 检查消息是否匹配过滤条件
   */
  private matchesFilters(message: BaseGlobalMessage, filters: any): boolean {
    if (filters.type && message.type !== filters.type) {
      return false;
    }
    
    if (filters.priority && message.priority !== filters.priority) {
      return false;
    }
    
    if (filters.startDate && message.createdAt < filters.startDate) {
      return false;
    }
    
    if (filters.endDate && message.createdAt > filters.endDate) {
      return false;
    }
    
    return true;
  }
}
