import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@libs/redis';

// 接口定义
import { BaseGlobalMessage, GlobalMessageType, MessagePriority } from '../interfaces/global-message.interface';

// DTO
import { CreateAnnouncementDto, UpdateAnnouncementDto } from '../dto/announcement.dto';

// 其他服务
import { GlobalBroadcastService } from './global-broadcast.service';
import { MessagePersistenceService } from './message-persistence.service';

/**
 * 系统公告接口（严格按照设计文档定义）
 */
export interface SystemAnnouncement extends BaseGlobalMessage {
  type: GlobalMessageType.SYSTEM_ANNOUNCEMENT;
  announcementType: 'system' | 'maintenance' | 'update' | 'event';
  displayType: 'popup' | 'banner' | 'notification';
  targetServers: string[] | 'all';
  startTime: Date;
  endTime: Date;
  autoClose?: boolean;
  closeDelay?: number;
  actionButton?: {
    text: string;
    action: string;
    url?: string;
  };
}

/**
 * 公告管理服务
 * 专门负责系统公告的创建、管理和发布
 * 
 * 核心功能：
 * 1. 公告创建：创建各类系统公告
 * 2. 公告管理：编辑、删除、查询公告
 * 3. 公告发布：发布公告到指定目标
 * 4. 公告调度：定时发布公告
 * 5. 公告统计：公告效果统计分析
 */
@Injectable()
export class AnnouncementService {
  private readonly logger = new Logger(AnnouncementService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly globalBroadcastService: GlobalBroadcastService,
    private readonly messagePersistenceService: MessagePersistenceService,
  ) {}

  /**
   * 创建系统公告
   */
  async createAnnouncement(createDto: CreateAnnouncementDto): Promise<SystemAnnouncement> {
    this.logger.log(`📢 Creating system announcement: ${createDto.title}`);

    try {
      const announcement: SystemAnnouncement = {
        id: `announcement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: GlobalMessageType.SYSTEM_ANNOUNCEMENT,
        priority: createDto.priority || MessagePriority.MEDIUM,
        title: createDto.title,
        content: createDto.content,
        createdAt: new Date(),
        publishAt: createDto.publishAt || new Date(),
        expireAt: createDto.expireAt,
        target: {
          scope: createDto.targetServers === 'all' ? 'global' : 'servers',
          servers: createDto.targetServers === 'all' ? undefined : createDto.targetServers,
        },
        metadata: {
          announcementType: createDto.announcementType,
          displayType: createDto.displayType,
          autoClose: createDto.autoClose,
          closeDelay: createDto.closeDelay,
          actionButton: createDto.actionButton,
        },
        // 系统公告特有字段
        announcementType: createDto.announcementType,
        displayType: createDto.displayType,
        targetServers: createDto.targetServers,
        startTime: createDto.startTime || new Date(),
        endTime: createDto.endTime || new Date(Date.now() + 24 * 60 * 60 * 1000), // 默认24小时后结束
        autoClose: createDto.autoClose,
        closeDelay: createDto.closeDelay,
        actionButton: createDto.actionButton,
      };

      // 持久化公告
      await this.messagePersistenceService.persistMessage(announcement);

      // 如果是立即发布，则发布公告
      if (announcement.publishAt <= new Date()) {
        await this.publishAnnouncement(announcement);
      }

      this.logger.log(`✅ System announcement created: ${announcement.id}`);
      return announcement;

    } catch (error) {
      this.logger.error(`❌ Failed to create announcement: ${createDto.title}`, error);
      throw error;
    }
  }

  /**
   * 更新系统公告
   */
  async updateAnnouncement(announcementId: string, updateDto: UpdateAnnouncementDto): Promise<SystemAnnouncement> {
    this.logger.log(`📝 Updating system announcement: ${announcementId}`);

    try {
      const existingAnnouncement = await this.getAnnouncement(announcementId);
      if (!existingAnnouncement) {
        throw new Error(`Announcement not found: ${announcementId}`);
      }

      // 更新公告字段
      const updatedAnnouncement: SystemAnnouncement = {
        ...existingAnnouncement,
        title: updateDto.title || existingAnnouncement.title,
        content: updateDto.content || existingAnnouncement.content,
        priority: updateDto.priority || existingAnnouncement.priority,
        publishAt: updateDto.publishAt || existingAnnouncement.publishAt,
        expireAt: updateDto.expireAt || existingAnnouncement.expireAt,
        announcementType: updateDto.announcementType || existingAnnouncement.announcementType,
        displayType: updateDto.displayType || existingAnnouncement.displayType,
        targetServers: updateDto.targetServers || existingAnnouncement.targetServers,
        startTime: updateDto.startTime || existingAnnouncement.startTime,
        endTime: updateDto.endTime || existingAnnouncement.endTime,
        autoClose: updateDto.autoClose !== undefined ? updateDto.autoClose : existingAnnouncement.autoClose,
        closeDelay: updateDto.closeDelay || existingAnnouncement.closeDelay,
        actionButton: updateDto.actionButton || existingAnnouncement.actionButton,
      };

      // 更新target字段
      if (updateDto.targetServers) {
        updatedAnnouncement.target = {
          scope: updateDto.targetServers === 'all' ? 'global' : 'servers',
          servers: updateDto.targetServers === 'all' ? undefined : updateDto.targetServers,
        };
      }

      // 更新metadata
      updatedAnnouncement.metadata = {
        ...existingAnnouncement.metadata,
        announcementType: updatedAnnouncement.announcementType,
        displayType: updatedAnnouncement.displayType,
        autoClose: updatedAnnouncement.autoClose,
        closeDelay: updatedAnnouncement.closeDelay,
        actionButton: updatedAnnouncement.actionButton,
      };

      // 持久化更新
      await this.messagePersistenceService.persistMessage(updatedAnnouncement);

      this.logger.log(`✅ System announcement updated: ${announcementId}`);
      return updatedAnnouncement;

    } catch (error) {
      this.logger.error(`❌ Failed to update announcement: ${announcementId}`, error);
      throw error;
    }
  }

  /**
   * 发布系统公告
   */
  async publishAnnouncement(announcement: SystemAnnouncement): Promise<void> {
    this.logger.log(`📤 Publishing system announcement: ${announcement.id}`);

    try {
      // 验证公告是否在有效期内
      const now = new Date();
      if (announcement.startTime > now) {
        throw new Error('Announcement start time is in the future');
      }
      if (announcement.endTime < now) {
        throw new Error('Announcement has expired');
      }

      // 使用全服广播服务发布
      await this.globalBroadcastService.broadcastMessage(announcement);

      // 更新发布状态
      await this.messagePersistenceService.updateMessageStatus(
        announcement.id,
        'published',
        { publishedAt: new Date() }
      );

      this.logger.log(`✅ System announcement published: ${announcement.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to publish announcement: ${announcement.id}`, error);
      throw error;
    }
  }

  /**
   * 获取系统公告
   */
  async getAnnouncement(announcementId: string): Promise<SystemAnnouncement | null> {
    try {
      const message = await this.messagePersistenceService.getMessage(announcementId);
      
      if (!message || message.type !== GlobalMessageType.SYSTEM_ANNOUNCEMENT) {
        return null;
      }

      return message as SystemAnnouncement;

    } catch (error) {
      this.logger.error(`❌ Failed to get announcement: ${announcementId}`, error);
      return null;
    }
  }

  /**
   * 获取公告列表
   */
  async getAnnouncements(filters: {
    announcementType?: string;
    displayType?: string;
    active?: boolean;
    limit?: number;
  } = {}): Promise<SystemAnnouncement[]> {
    try {
      const messages = await this.messagePersistenceService.getMessageHistory({
        type: GlobalMessageType.SYSTEM_ANNOUNCEMENT,
        limit: filters.limit || 50,
      });

      let announcements = messages.filter(msg => msg.type === GlobalMessageType.SYSTEM_ANNOUNCEMENT) as SystemAnnouncement[];

      // 应用过滤条件
      if (filters.announcementType) {
        announcements = announcements.filter(a => a.announcementType === filters.announcementType);
      }

      if (filters.displayType) {
        announcements = announcements.filter(a => a.displayType === filters.displayType);
      }

      if (filters.active !== undefined) {
        const now = new Date();
        announcements = announcements.filter(a => {
          const isActive = a.startTime <= now && a.endTime >= now;
          return filters.active ? isActive : !isActive;
        });
      }

      return announcements;

    } catch (error) {
      this.logger.error('❌ Failed to get announcements', error);
      return [];
    }
  }

  /**
   * 删除系统公告
   */
  async deleteAnnouncement(announcementId: string): Promise<boolean> {
    this.logger.log(`🗑️ Deleting system announcement: ${announcementId}`);

    try {
      const deleted = await this.messagePersistenceService.deleteMessage(announcementId);
      
      if (deleted) {
        this.logger.log(`✅ System announcement deleted: ${announcementId}`);
      } else {
        this.logger.warn(`⚠️ Announcement not found for deletion: ${announcementId}`);
      }

      return deleted;

    } catch (error) {
      this.logger.error(`❌ Failed to delete announcement: ${announcementId}`, error);
      throw error;
    }
  }

  /**
   * 获取公告统计
   */
  async getAnnouncementStats(announcementId: string): Promise<any> {
    try {
      const stats = await this.globalBroadcastService.getMessageStats(announcementId);
      
      // 添加公告特有的统计信息
      const announcement = await this.getAnnouncement(announcementId);
      if (announcement) {
        return {
          ...stats,
          announcementType: announcement.announcementType,
          displayType: announcement.displayType,
          targetServers: announcement.targetServers,
          duration: announcement.endTime.getTime() - announcement.startTime.getTime(),
          isActive: announcement.startTime <= new Date() && announcement.endTime >= new Date(),
        };
      }

      return stats;

    } catch (error) {
      this.logger.error(`❌ Failed to get announcement stats: ${announcementId}`, error);
      return null;
    }
  }
}
