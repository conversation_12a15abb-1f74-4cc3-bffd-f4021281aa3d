import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@libs/redis';

// 接口定义
import {
  BaseGlobalMessage,
  MessageTarget,
  MessageDeliveryRecord,
  MessageStats,
  GlobalMessageType,
  MessagePriority,
} from '../interfaces/global-message.interface';

/**
 * 全服广播服务
 * 负责全服消息的广播、投递跟踪和统计
 * 
 * 核心功能：
 * 1. 消息广播：向Redis发布消息到各个频道
 * 2. 目标解析：根据MessageTarget解析具体的目标用户
 * 3. 投递跟踪：跟踪消息的投递状态和确认情况
 * 4. 统计分析：提供消息投递的统计数据
 */
@Injectable()
export class GlobalBroadcastService {
  private readonly logger = new Logger(GlobalBroadcastService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 广播全服消息
   * 根据消息类型选择合适的广播频道
   */
  async broadcastMessage(message: BaseGlobalMessage): Promise<void> {
    this.logger.log(`📢 Broadcasting global message: ${message.type} - ${message.title}`);

    try {
      // 根据消息类型选择频道
      const channel = this.getChannelByMessageType(message.type);
      
      // 发布到Redis频道
      await this.redisService.publish(channel, JSON.stringify(message));
      
      // 记录广播日志
      await this.recordBroadcastLog(message);
      
      this.logger.log(`✅ Message broadcasted successfully: ${message.id}`);
    } catch (error) {
      this.logger.error(`❌ Failed to broadcast message: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 批量广播消息
   */
  async broadcastMessages(messages: BaseGlobalMessage[]): Promise<void> {
    this.logger.log(`📢 Broadcasting ${messages.length} global messages`);

    const results = await Promise.allSettled(
      messages.map(message => this.broadcastMessage(message))
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    this.logger.log(`📊 Batch broadcast completed: ${successful} successful, ${failed} failed`);
  }

  /**
   * 解析消息目标用户
   * 根据MessageTarget配置解析出具体的用户ID列表
   */
  async resolveMessageTargets(target: MessageTarget): Promise<string[]> {
    this.logger.debug(`🎯 Resolving message targets: ${target.scope}`);

    switch (target.scope) {
      case 'global':
        return await this.getAllOnlineUsers();

      case 'servers':
        return await this.getUsersByServers(target.servers || []);

      case 'users':
        return target.users || [];

      case 'conditions':
        return await this.getUsersByConditions(target.conditions || []);

      default:
        this.logger.warn(`Unknown target scope: ${target.scope}`);
        return [];
    }
  }

  /**
   * 记录消息投递状态
   */
  async recordDelivery(record: Omit<MessageDeliveryRecord, 'id'>): Promise<void> {
    const deliveryRecord: MessageDeliveryRecord = {
      id: `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...record,
    };

    const key = `message_delivery:${record.messageId}:${record.userId}`;
    await this.redisService.set(key, JSON.stringify(deliveryRecord), 3600 * 24 * 7, 'global');
  }

  /**
   * 获取消息统计信息
   */
  async getMessageStats(messageId: string): Promise<MessageStats> {
    this.logger.debug(`📊 Getting message stats: ${messageId}`);

    try {
      // 从Redis获取所有投递记录
      const pattern = `message_delivery:${messageId}:*`;
      const keys = await this.redisService.keys(pattern, 'global');
      
      const deliveryRecords: MessageDeliveryRecord[] = [];
      for (const key of keys) {
        const recordData = await this.redisService.get(key, 'global');
        if (recordData) {
          deliveryRecords.push(JSON.parse(recordData as string));
        }
      }

      // 计算统计数据
      const totalTargets = deliveryRecords.length;
      const delivered = deliveryRecords.filter(r => r.status === 'delivered' || r.status === 'acknowledged').length;
      const failed = deliveryRecords.filter(r => r.status === 'failed').length;
      const pending = deliveryRecords.filter(r => r.status === 'pending').length;
      const acknowledged = deliveryRecords.filter(r => r.status === 'acknowledged').length;

      const deliveryRate = totalTargets > 0 ? delivered / totalTargets : 0;
      const acknowledgmentRate = delivered > 0 ? acknowledged / delivered : 0;

      // 计算平均投递时间
      const deliveredRecords = deliveryRecords.filter(r => r.deliveredAt);
      const avgDeliveryTime = deliveredRecords.length > 0
        ? deliveredRecords.reduce((sum, r) => {
            const deliveryTime = (r.deliveredAt!.getTime() - new Date(r.messageId).getTime()) / 1000;
            return sum + deliveryTime;
          }, 0) / deliveredRecords.length
        : 0;

      return {
        messageId,
        totalTargets,
        delivered,
        failed,
        pending,
        acknowledged,
        deliveryRate,
        acknowledgmentRate,
        avgDeliveryTime,
      };

    } catch (error) {
      this.logger.error(`❌ Failed to get message stats: ${messageId}`, error);
      throw error;
    }
  }

  /**
   * 根据消息类型获取广播频道
   */
  private getChannelByMessageType(type: GlobalMessageType): string {
    switch (type) {
      case GlobalMessageType.SYSTEM_ANNOUNCEMENT:
        return 'global_messages:announcement';
      case GlobalMessageType.EMERGENCY_ALERT:
        return 'global_messages:emergency';
      case GlobalMessageType.MAINTENANCE_NOTICE:
        return 'global_messages:maintenance';
      case GlobalMessageType.EVENT_NOTIFICATION:
        return 'global_messages:event';
      case GlobalMessageType.CROSS_SERVER_EVENT:
        return 'global_messages:cross_server';
      case GlobalMessageType.MARKETING_MESSAGE:
        return 'global_messages:marketing';
      default:
        return 'global_messages:broadcast';
    }
  }

  /**
   * 记录广播日志
   */
  private async recordBroadcastLog(message: BaseGlobalMessage): Promise<void> {
    const logKey = `broadcast_log:${message.id}`;
    const logData = {
      messageId: message.id,
      type: message.type,
      priority: message.priority,
      broadcastAt: new Date(),
      channel: this.getChannelByMessageType(message.type),
    };

    await this.redisService.set(logKey, JSON.stringify(logData), 3600 * 24 * 30, 'global');
  }

  /**
   * 获取所有在线用户
   */
  private async getAllOnlineUsers(): Promise<string[]> {
    // 这里应该从实际的在线用户缓存中获取
    // 暂时返回空数组，实际实现时需要集成WebSocket Gateway的用户管理
    return [];
  }

  /**
   * 根据区服获取用户列表
   */
  private async getUsersByServers(serverIds: string[]): Promise<string[]> {
    // 这里应该根据区服ID查询在线用户
    // 暂时返回空数组，实际实现时需要集成WebSocket Gateway的用户管理
    return [];
  }

  /**
   * 根据条件筛选用户
   */
  private async getUsersByConditions(conditions: any[]): Promise<string[]> {
    // 这里应该实现复杂的用户筛选逻辑
    // 例如根据等级、VIP状态、最后登录时间等条件筛选
    // 暂时返回空数组，实际实现时需要集成用户数据查询
    return [];
  }
}
