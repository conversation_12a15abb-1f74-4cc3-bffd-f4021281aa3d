import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface TraceSpan {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tags: Record<string, any>;
  logs: TraceLog[];
  status: 'active' | 'finished' | 'error';
}

export interface TraceLog {
  timestamp: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  fields?: Record<string, any>;
}

export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
}

/**
 * 基础设施层 - 链路追踪服务
 * 
 * 提供分布式链路追踪功能：
 * - 请求链路追踪
 * - 性能分析
 * - 错误追踪
 * - 调用链可视化
 */
@Injectable()
export class TracingService {
  private readonly logger = new Logger(TracingService.name);
  private readonly activeSpans = new Map<string, TraceSpan>();
  private readonly completedTraces = new Map<string, TraceSpan[]>();
  private readonly maxTraceHistory = 1000;

  constructor(private readonly configService: ConfigService) {
    this.logger.log('Tracing service initialized');
  }

  /**
   * 开始新的追踪
   */
  startTrace(operationName: string, parentContext?: TraceContext): TraceContext {
    const traceId = parentContext?.traceId || this.generateTraceId();
    const spanId = this.generateSpanId();
    
    const span: TraceSpan = {
      traceId,
      spanId,
      parentSpanId: parentContext?.spanId,
      operationName,
      startTime: Date.now(),
      tags: {},
      logs: [],
      status: 'active',
    };

    this.activeSpans.set(spanId, span);
    
    this.logger.debug(`Started trace: ${operationName} (${traceId}:${spanId})`);
    
    return { traceId, spanId, parentSpanId: parentContext?.spanId };
  }

  /**
   * 结束追踪
   */
  finishTrace(context: TraceContext, error?: Error) {
    const span = this.activeSpans.get(context.spanId);
    if (!span) {
      this.logger.warn(`Span not found: ${context.spanId}`);
      return;
    }

    span.endTime = Date.now();
    span.duration = span.endTime - span.startTime;
    span.status = error ? 'error' : 'finished';

    if (error) {
      this.addLog(context, 'error', error.message, { 
        stack: error.stack,
        name: error.name 
      });
      this.setTag(context, 'error', true);
      this.setTag(context, 'error.message', error.message);
    }

    this.activeSpans.delete(context.spanId);
    this.storeCompletedTrace(span);

    this.logger.debug(`Finished trace: ${span.operationName} (${span.duration}ms)`);
  }

  /**
   * 添加标签
   */
  setTag(context: TraceContext, key: string, value: any) {
    const span = this.activeSpans.get(context.spanId);
    if (span) {
      span.tags[key] = value;
    }
  }

  /**
   * 添加日志
   */
  addLog(context: TraceContext, level: TraceLog['level'], message: string, fields?: Record<string, any>) {
    const span = this.activeSpans.get(context.spanId);
    if (span) {
      span.logs.push({
        timestamp: Date.now(),
        level,
        message,
        fields,
      });
    }
  }

  /**
   * 获取当前活跃的追踪
   */
  getActiveTraces(): TraceSpan[] {
    return Array.from(this.activeSpans.values());
  }

  /**
   * 获取完成的追踪
   */
  getCompletedTraces(traceId?: string): TraceSpan[] {
    if (traceId) {
      return this.completedTraces.get(traceId) || [];
    }
    
    const allTraces: TraceSpan[] = [];
    for (const traces of this.completedTraces.values()) {
      allTraces.push(...traces);
    }
    
    return allTraces
      .sort((a, b) => (b.startTime || 0) - (a.startTime || 0))
      .slice(0, 100);
  }

  /**
   * 获取追踪统计信息
   */
  getTracingStats() {
    const activeCount = this.activeSpans.size;
    const completedCount = Array.from(this.completedTraces.values())
      .reduce((sum, traces) => sum + traces.length, 0);
    
    const recentTraces = this.getCompletedTraces().slice(0, 100);
    const avgDuration = recentTraces.length > 0
      ? recentTraces.reduce((sum, trace) => sum + (trace.duration || 0), 0) / recentTraces.length
      : 0;
    
    const errorCount = recentTraces.filter(trace => trace.status === 'error').length;
    const errorRate = recentTraces.length > 0 ? errorCount / recentTraces.length : 0;

    return {
      activeTraces: activeCount,
      completedTraces: completedCount,
      averageDuration: Math.round(avgDuration),
      errorRate: Math.round(errorRate * 100) / 100,
      recentErrorCount: errorCount,
    };
  }

  /**
   * 清理过期的追踪数据
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理过期的活跃追踪
    for (const [spanId, span] of this.activeSpans) {
      if (now - span.startTime > maxAge) {
        this.logger.warn(`Cleaning up stale active trace: ${spanId}`);
        this.activeSpans.delete(spanId);
      }
    }

    // 限制完成追踪的数量
    if (this.completedTraces.size > this.maxTraceHistory) {
      const traceIds = Array.from(this.completedTraces.keys());
      const oldestTraces = traceIds
        .map(id => ({
          id,
          oldestTime: Math.min(...(this.completedTraces.get(id) || []).map(t => t.startTime))
        }))
        .sort((a, b) => a.oldestTime - b.oldestTime);

      const toDelete = oldestTraces.slice(0, oldestTraces.length - this.maxTraceHistory);
      toDelete.forEach(({ id }) => {
        this.completedTraces.delete(id);
      });

      if (toDelete.length > 0) {
        this.logger.log(`Cleaned up ${toDelete.length} old trace histories`);
      }
    }
  }

  /**
   * 存储完成的追踪
   */
  private storeCompletedTrace(span: TraceSpan) {
    const traces = this.completedTraces.get(span.traceId) || [];
    traces.push(span);
    this.completedTraces.set(span.traceId, traces);
  }

  /**
   * 生成追踪ID
   */
  private generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成Span ID
   */
  private generateSpanId(): string {
    return `span_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建子追踪
   */
  createChildTrace(parentContext: TraceContext, operationName: string): TraceContext {
    return this.startTrace(operationName, parentContext);
  }

  /**
   * 包装异步操作进行追踪
   */
  async traceAsync<T>(
    operationName: string,
    operation: (context: TraceContext) => Promise<T>,
    parentContext?: TraceContext
  ): Promise<T> {
    const context = this.startTrace(operationName, parentContext);
    
    try {
      const result = await operation(context);
      this.finishTrace(context);
      return result;
    } catch (error) {
      this.finishTrace(context, error as Error);
      throw error;
    }
  }

  /**
   * 包装同步操作进行追踪
   */
  traceSync<T>(
    operationName: string,
    operation: (context: TraceContext) => T,
    parentContext?: TraceContext
  ): T {
    const context = this.startTrace(operationName, parentContext);
    
    try {
      const result = operation(context);
      this.finishTrace(context);
      return result;
    } catch (error) {
      this.finishTrace(context, error as Error);
      throw error;
    }
  }
}
