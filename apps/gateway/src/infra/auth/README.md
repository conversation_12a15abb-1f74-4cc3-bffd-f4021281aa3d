# Gateway认证基础设施模块

## 📋 概述

这个模块提供了Gateway服务的统一认证基础设施，专注于Token验证、权限检查和认证守卫功能。

**重要架构决策**：
- ✅ **Token验证和权限检查**：通过微服务调用Auth服务实现
- ✅ **用户CRUD操作**：通过HTTP路由转发直接代理到Auth服务（不在Gateway层处理）
- ✅ **认证守卫**：统一的HTTP和WebSocket认证守卫

## 🏗️ 架构设计

### 核心原则
- **统一认证**：所有认证请求都通过AuthService代理到Auth微服务
- **缓存优化**：Token验证结果缓存5分钟，提升性能
- **守卫名称保持**：保持原有的AuthGuard、WsAuthGuard等命名
- **功能完整**：保留所有现有的认证功能特性

### 模块结构
```
apps/gateway/src/infra/auth/
├── services/           # 认证服务
│   ├── auth.service.ts           # 核心认证服务（Token验证、权限检查、缓存）
│   └── character-auth.service.ts # 角色认证服务（角色Token生成和验证）
├── guards/             # 认证守卫
│   ├── auth.guard.ts             # HTTP认证守卫
│   ├── ws-auth.guard.ts          # WebSocket认证守卫
│   └── rate-limit.guard.ts       # 限流守卫
├── decorators/         # 装饰器（Public、Roles、TokenScope等）
└── auth.module.ts      # 认证模块

注意：用户CRUD相关的DTO和服务已移除，这些操作通过HTTP路由转发处理
```

## 🚀 使用方法

### 1. 在其他模块中导入
```typescript
import { AuthModule } from '../../infra/auth';

@Module({
  imports: [AuthModule],
  // ...
})
export class YourModule {}
```

### 2. 使用认证守卫
```typescript
import { AuthGuard, WsAuthGuard } from '../../infra/auth';

// HTTP接口认证
@UseGuards(AuthGuard)
@Get('protected')
async protectedRoute(@Request() req) {
  // req.user 包含用户信息
}

// WebSocket事件认证
@UseGuards(WsAuthGuard)
@SubscribeMessage('protected-event')
async protectedEvent(@ConnectedSocket() client: Socket) {
  // client.data.user 包含用户信息
}
```

### 3. 使用认证服务
```typescript
import { AuthService, CharacterAuthService } from '../../infra/auth';

@Injectable()
export class YourService {
  constructor(
    private readonly authService: AuthService,
    private readonly characterAuthService: CharacterAuthService,
  ) {}

  // Token验证和权限检查
  async validateToken(token: string) {
    return await this.authService.validateToken(token);
  }

  async checkPermission(userId: string, resource: string, action: string) {
    return await this.authService.checkPermission(userId, resource, action);
  }

  // 角色认证
  async generateCharacterToken(request: any) {
    return await this.characterAuthService.generateCharacterToken(request);
  }

  // 注意：用户登录、注册等操作通过HTTP路由转发处理
  // 例如：POST /api/auth/login -> 直接转发到Auth服务
}
```

### 4. 使用装饰器
```typescript
import { Public, Roles, TokenScope } from '../../infra/auth';

// 公开接口（不需要认证）
@Public()
@Post('login')
async login() {}

// 需要特定角色
@Roles('admin', 'moderator')
@Post('admin-only')
async adminOnly() {}

// WebSocket Token作用域
@TokenScope('character')
@SubscribeMessage('character-event')
async characterEvent() {}
```

## 🔧 核心功能

### AuthService
- `validateToken(token)` - Token验证（带缓存）
- `checkPermission(userId, resource, action)` - 权限检查
- `validateApiKey(apiKey)` - API Key验证
- `clearTokenCache(token)` - 清除Token缓存（用于登出）
- `injectUserContext(request, result)` - 用户信息注入
- `extractTokenFromRequest(request)` - 从请求提取Token

### CharacterAuthService
- `generateCharacterToken(request)` - 生成角色Token
- `validateCharacterToken(token)` - 验证角色Token
- `logoutCharacter(userId, characterId, serverId)` - 角色登出

## 🔒 安全特性

### Token验证缓存
- 验证结果缓存5分钟
- 使用Token哈希作为缓存键
- 登出时自动清除缓存

### 权限控制
- 支持角色权限检查
- 支持Token作用域验证
- 支持API Key认证

### 限流保护
- 基于用户ID或IP的限流
- 可配置的限流规则
- 自动添加限流响应头

## 📊 性能优化

### 缓存策略
- Token验证结果缓存（TTL: 5分钟）
- 使用Redis作为缓存存储
- 缓存键使用Token哈希值

### 微服务调用
- 统一的微服务客户端
- 错误处理和重试机制
- 调用链路追踪

## 🔄 迁移说明

这个模块是从原有的gateway-auth和websocket模块中提取的认证相关代码：

### 迁移的内容
- ✅ AuthService（Token验证、权限检查、缓存管理）
- ✅ CharacterAuthService（角色认证）
- ✅ AuthGuard（HTTP认证守卫）
- ✅ WsAuthGuard（WebSocket认证守卫，使用统一的AuthService）
- ✅ RateLimitGuard（限流守卫）
- ✅ 认证相关的装饰器（Public、Roles、TokenScope）

### 移除的内容（按架构决策）
- ❌ UserService的CRUD功能（登录、注册、密码修改）
- ❌ 用户CRUD相关的DTO（LoginDto、ChangePasswordDto等）
- ❌ 这些操作现在通过HTTP路由转发直接代理到Auth服务

### 保持不变的内容
- ✅ 守卫名称（AuthGuard、WsAuthGuard等）
- ✅ 接口签名和返回格式
- ✅ 所有现有功能特性
- ✅ 缓存机制和性能优化

## 🎯 下一步计划

阶段一完成后，可以：
1. 配置HTTP路由转发规则，将用户CRUD操作直接代理到Auth服务
2. 更新character模块使用CharacterAuthService
3. 更新websocket模块使用WsAuthGuard
4. 逐步移除gateway-auth模块中重复的认证代码

## 📋 HTTP路由转发配置示例

用户CRUD操作应该通过以下路由转发配置：
```typescript
// 路由转发配置示例
const authRoutes = [
  {
    path: '/api/auth/login',
    target: 'http://auth-service:3001/api/auth/login',
    method: 'POST',
    public: true
  },
  {
    path: '/api/auth/register',
    target: 'http://auth-service:3001/api/auth/register',
    method: 'POST',
    public: true
  },
  {
    path: '/api/auth/change-password',
    target: 'http://auth-service:3001/api/auth/change-password',
    method: 'POST',
    requireAuth: true
  }
];
```
