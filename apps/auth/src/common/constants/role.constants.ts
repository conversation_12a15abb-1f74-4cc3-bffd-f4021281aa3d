/**
 * 角色和权限相关常量
 */

// 系统预定义角色
export const SYSTEM_ROLES = {
  // 超级管理员
  SUPER_ADMIN: 'super_admin',
  
  // 系统管理员
  SYSTEM_ADMIN: 'system_admin',
  
  // 用户管理员
  USER_ADMIN: 'user_admin',
  
  // 安全管理员
  SECURITY_ADMIN: 'security_admin',
  
  // 审计员
  AUDITOR: 'auditor',
  
  // 普通用户
  USER: 'user',
  
  // 游客
  GUEST: 'guest',
} as const;

// 游戏角色
export const GAME_ROLES = {
  // 球队所有者
  TEAM_OWNER: 'team_owner',
  
  // 总经理
  GENERAL_MANAGER: 'general_manager',
  
  // 主教练
  HEAD_COACH: 'head_coach',
  
  // 助理教练
  ASSISTANT_COACH: 'assistant_coach',
  
  // 球探
  SCOUT: 'scout',
  
  // 青训教练
  YOUTH_COACH: 'youth_coach',
  
  // 体能教练
  FITNESS_COACH: 'fitness_coach',
  
  // 门将教练
  GOALKEEPER_COACH: 'goalkeeper_coach',
  
  // 医疗团队
  MEDICAL_STAFF: 'medical_staff',
  
  // 数据分析师
  DATA_ANALYST: 'data_analyst',
  
  // 媒体经理
  MEDIA_MANAGER: 'media_manager',
  
  // 球迷
  FAN: 'fan',
} as const;

// 角色层级
export const ROLE_HIERARCHY = {
  [SYSTEM_ROLES.SUPER_ADMIN]: 100,
  [SYSTEM_ROLES.SYSTEM_ADMIN]: 90,
  [SYSTEM_ROLES.SECURITY_ADMIN]: 80,
  [SYSTEM_ROLES.USER_ADMIN]: 70,
  [SYSTEM_ROLES.AUDITOR]: 60,
  [GAME_ROLES.TEAM_OWNER]: 50,
  [GAME_ROLES.GENERAL_MANAGER]: 45,
  [GAME_ROLES.HEAD_COACH]: 40,
  [GAME_ROLES.ASSISTANT_COACH]: 35,
  [GAME_ROLES.SCOUT]: 30,
  [GAME_ROLES.YOUTH_COACH]: 30,
  [GAME_ROLES.FITNESS_COACH]: 30,
  [GAME_ROLES.GOALKEEPER_COACH]: 30,
  [GAME_ROLES.MEDICAL_STAFF]: 25,
  [GAME_ROLES.DATA_ANALYST]: 25,
  [GAME_ROLES.MEDIA_MANAGER]: 25,
  [SYSTEM_ROLES.USER]: 20,
  [GAME_ROLES.FAN]: 15,
  [SYSTEM_ROLES.GUEST]: 10,
} as const;

// 系统权限
export const SYSTEM_PERMISSIONS = {
  // 用户管理权限
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',
  USER_IMPERSONATE: 'user:impersonate',
  
  // 角色管理权限
  ROLE_CREATE: 'role:create',
  ROLE_READ: 'role:read',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  ROLE_ASSIGN: 'role:assign',
  
  // 权限管理权限
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_READ: 'permission:read',
  PERMISSION_UPDATE: 'permission:update',
  PERMISSION_DELETE: 'permission:delete',
  PERMISSION_ASSIGN: 'permission:assign',
  
  // 系统管理权限
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_MONITOR: 'system:monitor',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_RESTORE: 'system:restore',
  SYSTEM_MAINTENANCE: 'system:maintenance',
  
  // 安全管理权限
  SECURITY_AUDIT: 'security:audit',
  SECURITY_MONITOR: 'security:monitor',
  SECURITY_BLOCK_IP: 'security:block_ip',
  SECURITY_MANAGE_THREATS: 'security:manage_threats',
  
  // 审计权限
  AUDIT_READ: 'audit:read',
  AUDIT_EXPORT: 'audit:export',
  AUDIT_DELETE: 'audit:delete',
  
  // 会话管理权限
  SESSION_READ: 'session:read',
  SESSION_TERMINATE: 'session:terminate',
  SESSION_MANAGE: 'session:manage',
} as const;

// 游戏权限
export const GAME_PERMISSIONS = {
  // 球队管理权限
  TEAM_CREATE: 'team:create',
  TEAM_READ: 'team:read',
  TEAM_UPDATE: 'team:update',
  TEAM_DELETE: 'team:delete',
  TEAM_MANAGE: 'team:manage',
  TEAM_TRANSFER: 'team:transfer',
  
  // 球员管理权限
  PLAYER_CREATE: 'player:create',
  PLAYER_READ: 'player:read',
  PLAYER_UPDATE: 'player:update',
  PLAYER_DELETE: 'player:delete',
  PLAYER_SCOUT: 'player:scout',
  PLAYER_TRAIN: 'player:train',
  
  // 比赛管理权限
  MATCH_CREATE: 'match:create',
  MATCH_READ: 'match:read',
  MATCH_UPDATE: 'match:update',
  MATCH_MANAGE: 'match:manage',
  MATCH_TACTICS: 'match:tactics',
  
  // 财务管理权限
  FINANCE_READ: 'finance:read',
  FINANCE_MANAGE: 'finance:manage',
  FINANCE_TRANSFER: 'finance:transfer',
  
  // 设施管理权限
  FACILITY_READ: 'facility:read',
  FACILITY_MANAGE: 'facility:manage',
  FACILITY_UPGRADE: 'facility:upgrade',
  
  // 青训权限
  YOUTH_READ: 'youth:read',
  YOUTH_MANAGE: 'youth:manage',
  YOUTH_PROMOTE: 'youth:promote',
  
  // 数据分析权限
  DATA_READ: 'data:read',
  DATA_ANALYZE: 'data:analyze',
  DATA_EXPORT: 'data:export',
  
  // 媒体权限
  MEDIA_READ: 'media:read',
  MEDIA_MANAGE: 'media:manage',
  MEDIA_PUBLISH: 'media:publish',
} as const;

// 角色权限映射
export const ROLE_PERMISSIONS = {
  [SYSTEM_ROLES.SUPER_ADMIN]: [
    ...Object.values(SYSTEM_PERMISSIONS),
    ...Object.values(GAME_PERMISSIONS),
  ],
  
  [SYSTEM_ROLES.SYSTEM_ADMIN]: [
    SYSTEM_PERMISSIONS.USER_CREATE,
    SYSTEM_PERMISSIONS.USER_READ,
    SYSTEM_PERMISSIONS.USER_UPDATE,
    SYSTEM_PERMISSIONS.USER_DELETE,
    SYSTEM_PERMISSIONS.ROLE_READ,
    SYSTEM_PERMISSIONS.ROLE_ASSIGN,
    SYSTEM_PERMISSIONS.SYSTEM_CONFIG,
    SYSTEM_PERMISSIONS.SYSTEM_MONITOR,
    SYSTEM_PERMISSIONS.SYSTEM_BACKUP,
    SYSTEM_PERMISSIONS.SYSTEM_MAINTENANCE,
    SYSTEM_PERMISSIONS.SESSION_READ,
    SYSTEM_PERMISSIONS.SESSION_TERMINATE,
  ],
  
  [SYSTEM_ROLES.USER_ADMIN]: [
    SYSTEM_PERMISSIONS.USER_CREATE,
    SYSTEM_PERMISSIONS.USER_READ,
    SYSTEM_PERMISSIONS.USER_UPDATE,
    SYSTEM_PERMISSIONS.ROLE_READ,
    SYSTEM_PERMISSIONS.ROLE_ASSIGN,
    SYSTEM_PERMISSIONS.SESSION_READ,
  ],
  
  [SYSTEM_ROLES.SECURITY_ADMIN]: [
    SYSTEM_PERMISSIONS.SECURITY_AUDIT,
    SYSTEM_PERMISSIONS.SECURITY_MONITOR,
    SYSTEM_PERMISSIONS.SECURITY_BLOCK_IP,
    SYSTEM_PERMISSIONS.SECURITY_MANAGE_THREATS,
    SYSTEM_PERMISSIONS.AUDIT_READ,
    SYSTEM_PERMISSIONS.SESSION_READ,
    SYSTEM_PERMISSIONS.SESSION_TERMINATE,
  ],
  
  [SYSTEM_ROLES.AUDITOR]: [
    SYSTEM_PERMISSIONS.AUDIT_READ,
    SYSTEM_PERMISSIONS.AUDIT_EXPORT,
    SYSTEM_PERMISSIONS.SECURITY_AUDIT,
    SYSTEM_PERMISSIONS.USER_READ,
    SYSTEM_PERMISSIONS.SESSION_READ,
  ],
  
  [GAME_ROLES.TEAM_OWNER]: [
    ...Object.values(GAME_PERMISSIONS),
  ],
  
  [GAME_ROLES.GENERAL_MANAGER]: [
    GAME_PERMISSIONS.TEAM_READ,
    GAME_PERMISSIONS.TEAM_UPDATE,
    GAME_PERMISSIONS.TEAM_MANAGE,
    GAME_PERMISSIONS.PLAYER_CREATE,
    GAME_PERMISSIONS.PLAYER_READ,
    GAME_PERMISSIONS.PLAYER_UPDATE,
    GAME_PERMISSIONS.PLAYER_SCOUT,
    GAME_PERMISSIONS.MATCH_READ,
    GAME_PERMISSIONS.MATCH_MANAGE,
    GAME_PERMISSIONS.FINANCE_READ,
    GAME_PERMISSIONS.FINANCE_MANAGE,
    GAME_PERMISSIONS.FACILITY_READ,
    GAME_PERMISSIONS.FACILITY_MANAGE,
    GAME_PERMISSIONS.DATA_READ,
    GAME_PERMISSIONS.DATA_ANALYZE,
  ],
  
  [GAME_ROLES.HEAD_COACH]: [
    GAME_PERMISSIONS.TEAM_READ,
    GAME_PERMISSIONS.PLAYER_READ,
    GAME_PERMISSIONS.PLAYER_TRAIN,
    GAME_PERMISSIONS.MATCH_READ,
    GAME_PERMISSIONS.MATCH_TACTICS,
    GAME_PERMISSIONS.YOUTH_READ,
    GAME_PERMISSIONS.DATA_READ,
  ],
  
  [GAME_ROLES.SCOUT]: [
    GAME_PERMISSIONS.PLAYER_READ,
    GAME_PERMISSIONS.PLAYER_SCOUT,
    GAME_PERMISSIONS.DATA_READ,
  ],
  
  [SYSTEM_ROLES.USER]: [
    // 基础用户权限
  ],
  
  [GAME_ROLES.FAN]: [
    // 球迷权限
  ],
  
  [SYSTEM_ROLES.GUEST]: [
    // 游客权限
  ],
} as const;

// 角色描述
export const ROLE_DESCRIPTIONS = {
  [SYSTEM_ROLES.SUPER_ADMIN]: '超级管理员，拥有系统所有权限',
  [SYSTEM_ROLES.SYSTEM_ADMIN]: '系统管理员，负责系统配置和维护',
  [SYSTEM_ROLES.USER_ADMIN]: '用户管理员，负责用户管理',
  [SYSTEM_ROLES.SECURITY_ADMIN]: '安全管理员，负责系统安全',
  [SYSTEM_ROLES.AUDITOR]: '审计员，负责审计和监控',
  [SYSTEM_ROLES.USER]: '普通用户',
  [SYSTEM_ROLES.GUEST]: '游客用户',
  
  [GAME_ROLES.TEAM_OWNER]: '球队所有者，拥有球队完全控制权',
  [GAME_ROLES.GENERAL_MANAGER]: '总经理，负责球队运营管理',
  [GAME_ROLES.HEAD_COACH]: '主教练，负责球队训练和比赛',
  [GAME_ROLES.ASSISTANT_COACH]: '助理教练，协助主教练工作',
  [GAME_ROLES.SCOUT]: '球探，负责球员发现和评估',
  [GAME_ROLES.YOUTH_COACH]: '青训教练，负责青年球员培养',
  [GAME_ROLES.FITNESS_COACH]: '体能教练，负责球员体能训练',
  [GAME_ROLES.GOALKEEPER_COACH]: '门将教练，专门训练门将',
  [GAME_ROLES.MEDICAL_STAFF]: '医疗团队，负责球员健康管理',
  [GAME_ROLES.DATA_ANALYST]: '数据分析师，负责数据分析',
  [GAME_ROLES.MEDIA_MANAGER]: '媒体经理，负责媒体关系',
  [GAME_ROLES.FAN]: '球迷，关注和支持球队',
} as const;

// 角色分类
export const ROLE_CATEGORIES = {
  SYSTEM: 'system',
  GAME: 'game',
  CUSTOM: 'custom',
} as const;

// 权限分类
export const PERMISSION_CATEGORIES = {
  SYSTEM: 'system',
  USER: 'user',
  ROLE: 'role',
  SECURITY: 'security',
  AUDIT: 'audit',
  GAME: 'game',
  TEAM: 'team',
  PLAYER: 'player',
  MATCH: 'match',
  FINANCE: 'finance',
  FACILITY: 'facility',
  YOUTH: 'youth',
  DATA: 'data',
  MEDIA: 'media',
} as const;

// 导出类型
export type SystemRole = typeof SYSTEM_ROLES[keyof typeof SYSTEM_ROLES];
export type GameRole = typeof GAME_ROLES[keyof typeof GAME_ROLES];
export type SystemPermission = typeof SYSTEM_PERMISSIONS[keyof typeof SYSTEM_PERMISSIONS];
export type GamePermission = typeof GAME_PERMISSIONS[keyof typeof GAME_PERMISSIONS];
export type RoleCategory = typeof ROLE_CATEGORIES[keyof typeof ROLE_CATEGORIES];
export type PermissionCategory = typeof PERMISSION_CATEGORIES[keyof typeof PERMISSION_CATEGORIES];

// 所有角色联合类型
export type AllRoles = SystemRole | GameRole;

// 所有权限联合类型
export type AllPermissions = SystemPermission | GamePermission;
