/**
 * Auth服务类型定义统一导出
 * 
 * 提供通用的类型定义，包括枚举、联合类型等
 */

// 认证相关类型
export type AuthStrategy = 'jwt' | 'api-key' | 'refresh-token';
export type TokenType = 'access' | 'refresh' | 'api-key';
export type AuthScope = 'read' | 'write' | 'admin';

// 用户相关类型
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';
export type UserRole = 'admin' | 'user' | 'guest';
export type Gender = 'male' | 'female' | 'other';

// 权限相关类型
export type Permission = 
  | 'user:read' 
  | 'user:write' 
  | 'user:delete'
  | 'admin:read'
  | 'admin:write'
  | 'admin:delete'
  | 'system:read'
  | 'system:write'
  | 'system:delete';

// 响应相关类型
export type ResponseStatus = 'success' | 'error' | 'warning';
export type ErrorCode = 
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'INVALID_TOKEN'
  | 'TOKEN_EXPIRED'
  | 'INVALID_CREDENTIALS'
  | 'USER_NOT_FOUND'
  | 'USER_ALREADY_EXISTS'
  | 'VALIDATION_ERROR'
  | 'INTERNAL_ERROR';

// 环境相关类型
export type Environment = 'development' | 'production' | 'test';

// 日志相关类型
export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'verbose';

// 数据库相关类型
export type SortOrder = 'asc' | 'desc';
export type FilterOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like';

// 工具类型
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// ID类型
export type ObjectId = string;
export type UUID = string;
