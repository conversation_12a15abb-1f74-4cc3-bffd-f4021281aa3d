/**
 * 响应相关接口定义
 */

import { ErrorCode } from '../constants/error.constants';

// 基础API响应接口
export interface ApiResponse<T = any> {
  success: boolean;               // 是否成功
  data?: T;                       // 响应数据
  message?: string;               // 响应消息
  timestamp: string;              // 时间戳
  requestId: string;              // 请求ID
  version: string;                // API版本
}

// 成功响应接口
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true;
  data: T;
  message?: string;
}

// 错误响应接口
export interface ErrorResponse extends ApiResponse {
  success: false;
  error: {
    code: ErrorCode;              // 错误代码
    message: string;              // 错误消息
    details?: any;                // 错误详情
    stack?: string;               // 错误堆栈（仅开发环境）
    path?: string;                // 错误路径
    timestamp: string;            // 错误时间
  };
  data?: null;
}

// 分页响应接口
export interface PaginatedResponse<T = any> {
  items: T[];                     // 数据项
  pagination: {
    page: number;                 // 当前页
    limit: number;                // 每页数量
    total: number;                // 总数
    totalPages: number;           // 总页数
    hasNext: boolean;             // 是否有下一页
    hasPrev: boolean;             // 是否有上一页
    nextPage?: number;            // 下一页
    prevPage?: number;            // 上一页
  };
  meta?: {
    sortBy?: string;              // 排序字段
    sortOrder?: 'asc' | 'desc';   // 排序方向
    filters?: any;                // 过滤条件
    search?: string;              // 搜索关键词
  };
}

// 列表响应接口
export interface ListResponse<T = any> {
  items: T[];                     // 数据项
  total: number;                  // 总数
  meta?: {
    filters?: any;                // 过滤条件
    search?: string;              // 搜索关键词
    sortBy?: string;              // 排序字段
    sortOrder?: 'asc' | 'desc';   // 排序方向
  };
}

// 批量操作响应接口
export interface BulkResponse<T = any> {
  total: number;                  // 总数
  successful: number;             // 成功数
  failed: number;                 // 失败数
  results: {
    id: string;                   // 项目ID
    success: boolean;             // 是否成功
    data?: T;                     // 成功数据
    error?: {
      code: ErrorCode;            // 错误代码
      message: string;            // 错误消息
    };
  }[];
  errors?: {
    code: ErrorCode;              // 错误代码
    message: string;              // 错误消息
    count: number;                // 错误数量
  }[];
}

// 验证响应接口
export interface ValidationResponse {
  valid: boolean;                 // 是否有效
  errors?: {
    field: string;                // 字段名
    code: string;                 // 错误代码
    message: string;              // 错误消息
    value?: any;                  // 字段值
  }[];
  warnings?: {
    field: string;                // 字段名
    code: string;                 // 警告代码
    message: string;              // 警告消息
    value?: any;                  // 字段值
  }[];
}

// 健康检查响应接口
export interface HealthResponse {
  status: 'ok' | 'error' | 'shutting_down'; // 状态
  info?: {
    [key: string]: {
      status: 'up' | 'down';      // 组件状态
      message?: string;           // 状态消息
      details?: any;              // 详细信息
    };
  };
  error?: {
    [key: string]: {
      status: 'up' | 'down';      // 组件状态
      message?: string;           // 错误消息
      details?: any;              // 错误详情
    };
  };
  details?: {
    [key: string]: {
      status: 'up' | 'down';      // 组件状态
      message?: string;           // 详细消息
      details?: any;              // 详细信息
    };
  };
}

// 统计响应接口
export interface StatisticsResponse {
  period: {
    start: string;                // 开始时间
    end: string;                  // 结束时间
    duration: string;             // 时间段
  };
  metrics: {
    [key: string]: {
      value: number;              // 指标值
      change?: number;            // 变化量
      changePercent?: number;     // 变化百分比
      trend?: 'up' | 'down' | 'stable'; // 趋势
      unit?: string;              // 单位
      description?: string;       // 描述
    };
  };
  charts?: {
    [key: string]: {
      type: 'line' | 'bar' | 'pie' | 'area'; // 图表类型
      data: any[];                // 图表数据
      labels?: string[];          // 标签
      colors?: string[];          // 颜色
    };
  };
}

// 文件上传响应接口
export interface FileUploadResponse {
  id: string;                     // 文件ID
  filename: string;               // 文件名
  originalName: string;           // 原始文件名
  mimetype: string;               // MIME类型
  size: number;                   // 文件大小
  url: string;                    // 访问URL
  path: string;                   // 文件路径
  uploadedAt: string;             // 上传时间
  metadata?: {
    width?: number;               // 图片宽度
    height?: number;              // 图片高度
    duration?: number;            // 视频时长
    [key: string]: any;           // 其他元数据
  };
}

// 导出响应接口
export interface ExportResponse {
  id: string;                     // 导出ID
  type: 'csv' | 'xlsx' | 'pdf' | 'json'; // 导出类型
  status: 'pending' | 'processing' | 'completed' | 'failed'; // 状态
  progress?: number;              // 进度百分比
  url?: string;                   // 下载URL
  filename?: string;              // 文件名
  size?: number;                  // 文件大小
  createdAt: string;              // 创建时间
  completedAt?: string;           // 完成时间
  expiresAt?: string;             // 过期时间
  error?: string;                 // 错误信息
}

// 搜索响应接口
export interface SearchResponse<T = any> {
  query: string;                  // 搜索查询
  results: T[];                   // 搜索结果
  total: number;                  // 总结果数
  took: number;                   // 搜索耗时（毫秒）
  suggestions?: string[];         // 搜索建议
  facets?: {
    [key: string]: {
      name: string;               // 分面名称
      values: {
        value: string;            // 分面值
        count: number;            // 数量
      }[];
    };
  };
  pagination?: {
    page: number;                 // 当前页
    limit: number;                // 每页数量
    totalPages: number;           // 总页数
    hasNext: boolean;             // 是否有下一页
    hasPrev: boolean;             // 是否有上一页
  };
}

// 缓存响应接口
export interface CacheResponse<T = any> {
  hit: boolean;                   // 是否命中缓存
  data: T;                        // 数据
  ttl?: number;                   // 剩余生存时间
  key: string;                    // 缓存键
  createdAt?: string;             // 缓存创建时间
  updatedAt?: string;             // 缓存更新时间
}

// 任务响应接口
export interface TaskResponse {
  id: string;                     // 任务ID
  type: string;                   // 任务类型
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'; // 状态
  progress?: number;              // 进度百分比
  result?: any;                   // 任务结果
  error?: string;                 // 错误信息
  createdAt: string;              // 创建时间
  startedAt?: string;             // 开始时间
  completedAt?: string;           // 完成时间
  estimatedDuration?: number;     // 预计耗时（秒）
  metadata?: any;                 // 任务元数据
}

// 通知响应接口
export interface NotificationResponse {
  id: string;                     // 通知ID
  type: string;                   // 通知类型
  title: string;                  // 标题
  message: string;                // 消息
  data?: any;                     // 附加数据
  read: boolean;                  // 是否已读
  priority: 'low' | 'normal' | 'high' | 'urgent'; // 优先级
  createdAt: string;              // 创建时间
  readAt?: string;                // 阅读时间
  expiresAt?: string;             // 过期时间
}

// 配置响应接口
export interface ConfigResponse {
  [key: string]: {
    value: any;                   // 配置值
    type: 'string' | 'number' | 'boolean' | 'object' | 'array'; // 类型
    description?: string;         // 描述
    default?: any;                // 默认值
    required?: boolean;           // 是否必需
    sensitive?: boolean;          // 是否敏感
    updatedAt?: string;           // 更新时间
    updatedBy?: string;           // 更新者
  };
}

// 权限响应接口
export interface PermissionResponse {
  resource: string;               // 资源
  action: string;                 // 操作
  allowed: boolean;               // 是否允许
  reason?: string;                // 原因
  conditions?: any;               // 条件
  context?: any;                  // 上下文
}

// 会话响应接口
export interface SessionResponse {
  sessionId: string;              // 会话ID
  userId: string;                 // 用户ID
  active: boolean;                // 是否活跃
  createdAt: string;              // 创建时间
  lastActivity: string;           // 最后活动时间
  expiresAt: string;              // 过期时间
  deviceInfo?: any;               // 设备信息
  ipAddress?: string;             // IP地址
  location?: any;                 // 位置信息
}
