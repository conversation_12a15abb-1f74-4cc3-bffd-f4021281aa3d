import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
  UnauthorizedException
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRepository, PaginationResult } from '../repositories/user.repository';
import { PasswordService } from '../../security/services/password.service';
import { User, UserDocument, UserStatus } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto, ChangePasswordDto, SearchUsersDto, AdminUpdateUserDto } from '../dto/update-user.dto';
import { Cacheable, CacheEvict, CachePut, CacheManagerService } from '@libs/redis';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly passwordService: PasswordService,
    private readonly configService: ConfigService,
    private readonly cacheManager: CacheManagerService,
  ) {}

  /**
   * 创建用户
   */
  async create(createUserDto: CreateUserDto, registrationIp?: string): Promise<UserDocument> {
    this.logger.log(`创建用户: ${createUserDto.username}`);

    // 验证密码确认
    if (createUserDto.password !== createUserDto.confirmPassword) {
      throw new BadRequestException('密码确认不匹配');
    }

    // 检查用户名是否已存在
    const existingUsername = await this.userRepository.existsByUsername(createUserDto.username);
    if (existingUsername) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    const existingEmail = await this.userRepository.existsByEmail(createUserDto.email);
    if (existingEmail) {
      throw new ConflictException('邮箱已存在');
    }

    // 检查手机号是否已存在（如果提供）
    if (createUserDto.phone) {
      const existingPhone = await this.userRepository.existsByPhone(createUserDto.phone);
      if (existingPhone) {
        throw new ConflictException('手机号已存在');
      }
    }

    // 验证密码强度
    const passwordValidation = this.passwordService.validatePassword(
      createUserDto.password,
      {
        username: createUserDto.username,
        email: createUserDto.email,
        firstName: createUserDto.profile.firstName,
        lastName: createUserDto.profile.lastName
      }
    );

    if (!passwordValidation.isValid) {
      throw new BadRequestException({
        message: '密码不符合安全要求',
        errors: passwordValidation.errors
      });
    }

    // 哈希密码
    const { hash, salt } = await this.passwordService.hashPassword(createUserDto.password);

    // 准备用户数据
    const userData = {
      ...createUserDto,
      passwordHash: hash,
      salt,
      registrationIp,
      gameProfile: {
        level: 1,
        experience: 0,
        coins: 1000, // 新用户赠送1000游戏币
        achievements: [],
        preferences: createUserDto.gamePreferences || {
          notifications: true,
          autoSave: true,
          theme: 'light',
          gameLanguage: 'zh',
          soundEnabled: true,
          musicEnabled: true
        }
      },
      security: {
        mfaEnabled: false,
        trustedDevices: [],
        lastPasswordChange: new Date(),
        passwordHistory: [hash],
        loginAttempts: 0,
        backupCodes: []
      }
    };

    // 移除不需要的字段
    delete userData.password;
    delete userData.confirmPassword;
    delete userData.gamePreferences;

    try {
      const user = await this.userRepository.create(userData);
      this.logger.log(`用户创建成功: ${user.username} (${user.id})`);
      
      // TODO: 发送欢迎邮件和邮箱验证
      
      return user;
    } catch (error) {
      this.logger.error('创建用户失败', error);
      throw new BadRequestException('创建用户失败');
    }
  }

  /**
   * 根据ID查找用户（带缓存优化）
   * 🔧 返回纯对象，已删除敏感信息
   */
  async findById(id: string): Promise<any> {
    const startTime = Date.now();

    try {
      // 获取用户缓存仓库（缓存纯对象）
      const repository = this.cacheManager.getRepository<UserDocument>('users');

      // 使用 Cache-Aside 模式：先查缓存，缓存未命中时查数据库
      const user = await repository.getOrLoad(
        `user:id:${id}`,
        async () => {
          this.logger.log(`Cache miss: Loading user ${id} from database`);
          const dbUser = await this.userRepository.findById(id);

          // 验证用户存在性和状态
          if (!dbUser || dbUser.status === UserStatus.DELETED) {
            throw new NotFoundException('用户不存在');
          }

          // 🔧 缓存前转换为纯对象，自动删除敏感信息
          return dbUser.toJSON();
        },
        {
          ttl: 300, // 5分钟缓存
          enableProtection: true, // 启用缓存保护
          enableAvalancheProtection: true, // 防止缓存雪崩
          enableBreakdownProtection: true, // 防止缓存击穿
        }
      );

      // 记录性能日志
      const duration = Date.now() - startTime;
      this.logger.log(`User ${id} loaded in ${duration}ms`);

      return user;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to load user ${id} in ${duration}ms: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据用户名查找用户（带缓存优化）
   * 🔧 返回纯对象，已删除敏感信息
   */
  async findByUsername(username: string): Promise<any> {
    const startTime = Date.now();

    try {
      // 获取用户缓存仓库（缓存纯对象）
      const repository = this.cacheManager.getRepository<UserDocument>('users');

      // 使用 Cache-Aside 模式
      const user = await repository.getOrLoad(
        `user:username:${username}`,
        async () => {
          this.logger.log(`Cache miss: Loading user by username ${username} from database`);
          const dbUser = await this.userRepository.findByUsername(username);

          // 验证用户存在性和状态
          if (!dbUser || dbUser.status === UserStatus.DELETED) {
            throw new NotFoundException('用户不存在');
          }

          // 🔧 缓存前转换为纯对象，自动删除敏感信息
          return dbUser.toJSON();
        },
        {
          ttl: 300, // 5分钟缓存
          enableProtection: true,
          enableAvalancheProtection: true,
          enableBreakdownProtection: true,
        }
      );

      // 记录性能日志
      const duration = Date.now() - startTime;
      this.logger.log(`User by username ${username} loaded in ${duration}ms`);

      return user;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to load user by username ${username} in ${duration}ms: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据邮箱查找用户（带缓存优化）
   * 🔧 返回纯对象，已删除敏感信息
   */
  async findByEmail(email: string): Promise<any> {
    const startTime = Date.now();

    try {
      // 获取用户缓存仓库（缓存纯对象）
      const repository = this.cacheManager.getRepository<UserDocument>('users');

      // 使用 Cache-Aside 模式
      const user = await repository.getOrLoad(
        `user:email:${email}`,
        async () => {
          this.logger.log(`Cache miss: Loading user by email ${email} from database`);
          const dbUser = await this.userRepository.findByEmail(email);

          // 验证用户存在性和状态
          if (!dbUser || dbUser.status === UserStatus.DELETED) {
            throw new NotFoundException('用户不存在');
          }

          // 🔧 缓存前转换为纯对象，自动删除敏感信息
          return dbUser.toJSON();
        },
        {
          ttl: 300, // 5分钟缓存
          enableProtection: true,
          enableAvalancheProtection: true,
          enableBreakdownProtection: true,
        }
      );

      // 记录性能日志
      const duration = Date.now() - startTime;
      this.logger.log(`User by email ${email} loaded in ${duration}ms`);

      return user;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to load user by email ${email} in ${duration}ms: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据用户名或邮箱查找用户（用于登录）
   */
  async findByUsernameOrEmail(username: string, includePassword = false): Promise<UserDocument> {
    const user = await this.userRepository.findByUsernameOrEmail(username, includePassword);
    if (!user || user.status === UserStatus.DELETED) {
      throw new NotFoundException('用户不存在');
    }
    return user;
  }

  /**
   * 更新用户信息
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserDocument> {
    this.logger.log(`更新用户信息: ${id}`);

    const user = await this.findById(id);

    // 检查邮箱是否被其他用户使用
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await this.userRepository.findByEmail(updateUserDto.email);
      if (existingEmail && existingEmail.id !== id) {
        throw new ConflictException('邮箱已被其他用户使用');
      }
      // 如果更改了邮箱，需要重新验证
      updateUserDto.emailVerified = false;
    }

    // 检查手机号是否被其他用户使用
    if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
      const existingPhone = await this.userRepository.findByPhone(updateUserDto.phone);
      if (existingPhone && existingPhone.id !== id) {
        throw new ConflictException('手机号已被其他用户使用');
      }
      // 如果更改了手机号，需要重新验证
      updateUserDto.phoneVerified = false;
    }

    const updatedUser = await this.userRepository.update(id, updateUserDto);
    if (!updatedUser) {
      throw new NotFoundException('用户不存在');
    }

    // 更新缓存：使用 Write-Through 模式
    await this.updateUserCache(updatedUser);

    // 如果用户名或邮箱发生变化，需要清除旧的缓存
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const repository = this.cacheManager.getRepository<UserDocument>('users');
      await repository.delete(`user:email:${user.email}`);
    }

    this.logger.log(`用户信息更新成功: ${updatedUser.username} (${updatedUser.id})`);
    return updatedUser;
  }

  /**
   * 更改密码
   */
  async changePassword(id: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    this.logger.log(`更新用户密码: ${id}`);

    // 验证新密码确认
    if (changePasswordDto.newPassword !== changePasswordDto.confirmNewPassword) {
      throw new BadRequestException('新密码确认不匹配');
    }

    const user = await this.userRepository.findById(id, true);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await this.passwordService.verifyPassword(
      changePasswordDto.currentPassword,
      user.passwordHash
    );

    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('当前密码错误');
    }

    // 验证新密码强度
    const passwordValidation = this.passwordService.validatePassword(
      changePasswordDto.newPassword,
      {
        username: user.username,
        email: user.email,
        firstName: user.profile.firstName,
        lastName: user.profile.lastName
      }
    );

    if (!passwordValidation.isValid) {
      throw new BadRequestException({
        message: '新密码不符合安全要求',
        errors: passwordValidation.errors
      });
    }

    // 检查新密码是否在历史记录中
    const isInHistory = await this.passwordService.isPasswordInHistory(
      changePasswordDto.newPassword,
      user.security.passwordHistory
    );

    if (isInHistory) {
      throw new BadRequestException('新密码不能与最近使用的密码相同');
    }

    // 哈希新密码
    const { hash, salt } = await this.passwordService.hashPassword(changePasswordDto.newPassword);

    // 更新密码历史
    const updatedHistory = this.passwordService.updatePasswordHistory(
      user.security.passwordHistory,
      hash
    );

    // 更新用户密码
    await this.userRepository.updatePartial(
      { _id: id },
      {
        $set: {
          passwordHash: hash,
          salt,
          'security.lastPasswordChange': new Date(),
          'security.passwordHistory': updatedHistory,
          'security.loginAttempts': 0
        },
        $unset: {
          'security.lockedUntil': 1
        }
      }
    );

    // 清除用户缓存
    await this.evictUserCache(user);

    this.logger.log(`用户密码更新成功: ${user.username} (${user.id})`);

    // TODO: 发送密码更改通知邮件
    // TODO: 撤销所有现有的JWT令牌
  }

  /**
   * 搜索用户
   */
  async search(searchDto: SearchUsersDto): Promise<PaginationResult<UserDocument>> {
    return await this.userRepository.search(searchDto);
  }

  /**
   * 软删除用户
   */
  async remove(id: string): Promise<void> {
    this.logger.log(`删除用户: ${id}`);

    const user = await this.findById(id);
    await this.userRepository.softDelete(id);

    // 清除用户缓存
    await this.evictUserCache(user);

    this.logger.log(`用户删除成功: ${user.username} (${user.id})`);
  }

  /**
   * 管理员更新用户
   */
  async adminUpdate(id: string, adminUpdateDto: AdminUpdateUserDto): Promise<UserDocument> {
    this.logger.log(`管理员更新用户: ${id}`);

    const user = await this.findById(id);

    // 处理特殊操作
    const updates: any = { ...adminUpdateDto };

    if (adminUpdateDto.resetLoginAttempts) {
      updates['security.loginAttempts'] = 0;
      updates.$unset = { 'security.lockedUntil': 1 };
      delete updates.resetLoginAttempts;
    }

    if (adminUpdateDto.unlockAccount) {
      updates.$unset = { 'security.lockedUntil': 1 };
      updates['security.loginAttempts'] = 0;
      delete updates.unlockAccount;
    }

    const updatedUser = await this.userRepository.updatePartial(
      { _id: id },
      { $set: updates, ...updates.$unset ? { $unset: updates.$unset } : {} }
    );

    if (!updatedUser) {
      throw new NotFoundException('用户不存在');
    }

    this.logger.log(`管理员更新用户成功: ${updatedUser.username} (${updatedUser.id})`);
    return updatedUser;
  }

  /**
   * 批量更新用户
   */
  async bulkUpdate(userIds: string[], updates: Partial<AdminUpdateUserDto>): Promise<number> {
    this.logger.log(`批量更新用户: ${userIds.length}个用户`);

    const modifiedCount = await this.userRepository.bulkUpdate(userIds, updates);

    this.logger.log(`批量更新完成: ${modifiedCount}个用户被更新`);
    return modifiedCount;
  }

  /**
   * 获取用户统计信息
   */
  async getStatistics(): Promise<any> {
    return await this.userRepository.getStatistics();
  }

  /**
   * 验证用户状态
   */
  validateUserStatus(user: UserDocument): void {
    if (user.status === UserStatus.SUSPENDED) {
      throw new UnauthorizedException('账户已被暂停');
    }

    if (user.status === UserStatus.INACTIVE) {
      throw new UnauthorizedException('账户未激活');
    }

    if (user.status === UserStatus.DELETED) {
      throw new UnauthorizedException('账户不存在');
    }

    if (user.isLocked) {
      const lockTime = Math.ceil((user.security.lockedUntil.getTime() - Date.now()) / 60000);
      throw new UnauthorizedException(`账户已被锁定，请在${lockTime}分钟后重试`);
    }
  }

  /**
   * 更新最后登录信息
   */
  async updateLastLogin(id: string, ip?: string): Promise<void> {
    await this.userRepository.updatePartial(
      { _id: id },
      {
        $set: {
          lastLoginAt: new Date(),
          lastActiveAt: new Date(),
          ...(ip && { lastLoginIp: ip })
        }
      }
    );
  }

  /**
   * 增加登录尝试次数
   */
  async incrementLoginAttempts(user: UserDocument): Promise<void> {
    await user.incrementLoginAttempts();
  }

  /**
   * 重置登录尝试次数
   */
  async resetLoginAttempts(user: UserDocument): Promise<void> {
    await user.resetLoginAttempts();
  }

  /**
   * 验证用户密码
   */
  async validatePassword(user: UserDocument, password: string): Promise<boolean> {
    return await this.passwordService.verifyPassword(password, user.passwordHash);
  }

  /**
   * 根据重置令牌查找用户
   */
  async findByResetToken(token: string): Promise<UserDocument | null> {
    // TODO: 实现根据重置令牌查找用户的方法
    // 需要在用户仓储中添加相应的方法
    throw new Error('findByResetToken method not implemented');
  }

  // ==================== 缓存管理辅助方法 ====================

  /**
   * 更新用户缓存（Write-Through模式）
   */
  private async updateUserCache(user: UserDocument): Promise<void> {
    try {
      const repository = this.cacheManager.getRepository<UserDocument>('users');

      // 同时更新多个缓存键
      const promises = [
        // 按ID缓存
        repository.setThrough(
          `user:id:${user.id}`,
          user,
          { ttl: 300, enableProtection: true }
        ),
        // 按用户名缓存
        repository.setThrough(
          `user:username:${user.username}`,
          user,
          { ttl: 300, enableProtection: true }
        ),
        // 按邮箱缓存
        repository.setThrough(
          `user:email:${user.email}`,
          user,
          { ttl: 300, enableProtection: true }
        ),
      ];

      await Promise.all(promises);
      this.logger.log(`User cache updated for user ${user.id}`);
    } catch (error) {
      this.logger.error(`Failed to update user cache for user ${user.id}: ${error.message}`);
    }
  }

  /**
   * 清除用户缓存
   */
  private async evictUserCache(user: UserDocument): Promise<void> {
    try {
      const repository = this.cacheManager.getRepository<UserDocument>('users');

      // 清除所有相关的缓存键
      const promises = [
        repository.delete(`user:id:${user.id}`),
        repository.delete(`user:username:${user.username}`),
        repository.delete(`user:email:${user.email}`),
      ];

      await Promise.all(promises);
      this.logger.log(`User cache evicted for user ${user.id}`);
    } catch (error) {
      this.logger.error(`Failed to evict user cache for user ${user.id}: ${error.message}`);
    }
  }
}
