import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CharacterSession, CharacterSessionDocument } from '../entities/character-session.entity';

export interface CreateCharacterSessionData {
  userId: string;
  characterId: string;
  serverId: string;
  serverName: string;
  deviceInfo?: any;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  lastActivity?: Date;
  active?: boolean;
}

export interface UpdateCharacterSessionData {
  lastActivity?: Date;
  expiresAt?: Date;
  active?: boolean;
  terminatedAt?: Date;
}

/**
 * 角色会话仓储接口
 */
export interface ICharacterSessionRepository {
  create(sessionData: CreateCharacterSessionData): Promise<CharacterSessionDocument>;
  findById(id: string): Promise<CharacterSessionDocument | null>;
  findByUserId(userId: string): Promise<CharacterSessionDocument[]>;
  findByCharacterId(characterId: string): Promise<CharacterSessionDocument[]>;
  findActiveSession(userId: string, serverId: string): Promise<CharacterSessionDocument | null>;
  findActiveByUserId(userId: string): Promise<CharacterSessionDocument[]>;
  update(id: string, updateData: UpdateCharacterSessionData): Promise<CharacterSessionDocument | null>;
  terminate(id: string): Promise<void>;
  terminateByUserId(userId: string): Promise<number>;
  terminateByCharacterId(characterId: string): Promise<number>;
  updateLastActivity(id: string): Promise<void>;
  findExpiredSessions(): Promise<CharacterSessionDocument[]>;
  deleteExpiredSessions(): Promise<number>;
  countActiveByUserId(userId: string): Promise<number>;
  countActiveByServerId(serverId: string): Promise<number>;
}

/**
 * 角色会话仓储实现
 * 
 * 负责角色会话数据的持久化操作，包括：
 * - 角色会话的CRUD操作
 * - 会话状态管理和验证
 * - 跨服务器的会话控制
 * - 过期会话清理
 */
@Injectable()
export class CharacterSessionRepository implements ICharacterSessionRepository {
  private readonly logger = new Logger(CharacterSessionRepository.name);

  constructor(
    @InjectModel(CharacterSession.name) private characterSessionModel: Model<CharacterSessionDocument>,
  ) {}

  /**
   * 创建角色会话
   */
  async create(sessionData: CreateCharacterSessionData): Promise<CharacterSessionDocument> {
    const session = new this.characterSessionModel({
      ...sessionData,
      lastActivity: sessionData.lastActivity || new Date(),
      active: sessionData.active !== undefined ? sessionData.active : true,
    });
    return await session.save();
  }

  /**
   * 根据ID查找角色会话
   */
  async findById(id: string): Promise<CharacterSessionDocument | null> {
    return await this.characterSessionModel.findById(id).exec();
  }

  /**
   * 根据用户ID查找所有角色会话
   */
  async findByUserId(userId: string): Promise<CharacterSessionDocument[]> {
    return await this.characterSessionModel
      .find({ userId })
      .sort({ lastActivity: -1 })
      .exec();
  }

  /**
   * 根据角色ID查找所有会话
   */
  async findByCharacterId(characterId: string): Promise<CharacterSessionDocument[]> {
    return await this.characterSessionModel
      .find({ characterId })
      .sort({ lastActivity: -1 })
      .exec();
  }

  /**
   * 查找用户在指定服务器的活跃会话
   */
  async findActiveSession(userId: string, serverId: string): Promise<CharacterSessionDocument | null> {
    return await this.characterSessionModel.findOne({
      userId,
      serverId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).exec();
  }

  /**
   * 查找用户所有活跃会话
   */
  async findActiveByUserId(userId: string): Promise<CharacterSessionDocument[]> {
    return await this.characterSessionModel.find({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    }).sort({ lastActivity: -1 }).exec();
  }

  /**
   * 更新角色会话
   */
  async update(id: string, updateData: UpdateCharacterSessionData): Promise<CharacterSessionDocument | null> {
    return await this.characterSessionModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    ).exec();
  }

  /**
   * 终止角色会话
   */
  async terminate(id: string): Promise<void> {
    await this.characterSessionModel.findByIdAndUpdate(
      id,
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();
  }

  /**
   * 终止用户所有角色会话
   */
  async terminateByUserId(userId: string): Promise<number> {
    const result = await this.characterSessionModel.updateMany(
      { userId, active: true },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();
    return result.modifiedCount;
  }

  /**
   * 终止角色所有会话
   */
  async terminateByCharacterId(characterId: string): Promise<number> {
    const result = await this.characterSessionModel.updateMany(
      { characterId, active: true },
      {
        $set: {
          active: false,
          terminatedAt: new Date(),
        }
      }
    ).exec();
    return result.modifiedCount;
  }

  /**
   * 更新最后活动时间
   */
  async updateLastActivity(id: string): Promise<void> {
    await this.characterSessionModel.findByIdAndUpdate(
      id,
      { $set: { lastActivity: new Date() } }
    ).exec();
  }

  /**
   * 查找过期会话
   */
  async findExpiredSessions(): Promise<CharacterSessionDocument[]> {
    return await this.characterSessionModel.find({
      expiresAt: { $lt: new Date() }
    }).exec();
  }

  /**
   * 删除过期会话
   */
  async deleteExpiredSessions(): Promise<number> {
    const result = await this.characterSessionModel.deleteMany({
      expiresAt: { $lt: new Date() }
    }).exec();
    return result.deletedCount;
  }

  /**
   * 统计用户活跃角色会话数
   */
  async countActiveByUserId(userId: string): Promise<number> {
    return await this.characterSessionModel.countDocuments({
      userId,
      active: true,
      expiresAt: { $gt: new Date() }
    });
  }

  /**
   * 统计服务器活跃会话数
   */
  async countActiveByServerId(serverId: string): Promise<number> {
    return await this.characterSessionModel.countDocuments({
      serverId,
      active: true,
      expiresAt: { $gt: new Date() }
    });
  }
}
