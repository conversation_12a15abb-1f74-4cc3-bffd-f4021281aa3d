import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../../rbac/guards/roles.guard';
import { Roles } from '@auth/common/decorators/roles.decorator';
import { AdminPermissions } from '@auth/common/decorators/permissions.decorator';
import {ApiResponseDto} from "@auth/common";

@ApiTags('系统管理')
@Controller('admin/system')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class SystemController {
  @Get('info')
  @Roles('admin', 'super_admin')
  @AdminPermissions.systemAdmin()
  @ApiOperation({ summary: '获取系统信息' })
  getSystemInfo() :ApiResponseDto<any> {
    return {
      success: true,
      data: {},
      timestamp: new Date().toISOString(),
      message: '系统控制器已创建，功能开发中...',
    };
  }
}
