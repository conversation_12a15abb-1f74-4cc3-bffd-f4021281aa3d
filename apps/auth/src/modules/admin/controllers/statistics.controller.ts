import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { RolesGuard } from '../../rbac/guards/roles.guard';
import { Roles } from '@auth/common/decorators/roles.decorator';
import { AdminPermissions } from '@auth/common/decorators/permissions.decorator';
import {ApiResponseDto} from "@auth/common";

@ApiTags('统计分析')
@Controller('admin/statistics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class StatisticsController {
  @Get('overview')
  @Roles('admin', 'super_admin')
  @AdminPermissions.statisticsView()
  @ApiOperation({ summary: '获取统计概览' })
  getStatisticsOverview() :ApiResponseDto<any> {
    return {
      success: true,
      data: {},
      timestamp: new Date().toISOString(),
      message: '统计控制器已创建，功能开发中...',
    };
  }
}
