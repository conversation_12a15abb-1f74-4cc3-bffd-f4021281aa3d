/**
 * 环境切换限流测试工具
 * 测试不同环境下限流配置的正确性
 */

const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

class EnvironmentRateLimitTester {
  constructor() {
    this.testResults = {};
  }

  async runEnvironmentTests() {
    console.log('🌍 环境切换限流测试');
    console.log('测试不同环境下限流配置的正确性');
    console.log('=' .repeat(60));

    // 1. 测试开发环境
    console.log('\n📋 测试1: 开发环境限流禁用');
    await this.testDevelopmentEnvironment();

    // 2. 提示手动切换到生产环境测试
    console.log('\n📋 测试2: 生产环境限流启用');
    await this.promptProductionTest();

    // 3. 总结所有测试结果
    this.printFinalSummary();
  }

  async testDevelopmentEnvironment() {
    console.log('🔍 检查开发环境配置...');
    
    // 检查当前环境变量
    const currentEnv = await this.checkCurrentEnvironment();
    
    if (currentEnv !== 'development') {
      console.log('⚠️ 当前不是开发环境，跳过开发环境测试');
      this.testResults.development = { skipped: true, reason: '环境不匹配' };
      return;
    }

    console.log('✅ 确认开发环境，开始限流禁用测试');
    
    // 运行开发环境测试
    const devTestPassed = await this.runDevelopmentRateLimitTest();
    
    this.testResults.development = {
      passed: devTestPassed,
      environment: 'development',
      expectedBehavior: '限流禁用',
      actualBehavior: devTestPassed ? '限流已禁用' : '限流仍启用'
    };
  }

  async runDevelopmentRateLimitTest() {
    console.log('🚀 发送快速并发请求测试限流禁用...');
    
    let rateLimitedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // 发送15个并发请求
    const promises = [];
    for (let i = 0; i < 15; i++) {
      promises.push(this.sendTestRequest(i + 1));
    }

    const results = await Promise.all(promises);
    
    results.forEach(result => {
      if (result.rateLimited) {
        rateLimitedCount++;
      } else if (result.success) {
        successCount++;
      } else {
        errorCount++;
      }
    });

    console.log(`📊 开发环境测试结果:`);
    console.log(`  成功处理: ${successCount}`);
    console.log(`  被限流: ${rateLimitedCount}`);
    console.log(`  错误: ${errorCount}`);

    const passed = rateLimitedCount === 0;
    
    if (passed) {
      console.log('✅ 开发环境测试通过 - 限流已正确禁用');
    } else {
      console.log('❌ 开发环境测试失败 - 仍有请求被限流');
    }

    return passed;
  }

  async promptProductionTest() {
    console.log('🏭 生产环境限流测试说明');
    console.log('');
    console.log('要测试生产环境限流，请按以下步骤操作：');
    console.log('');
    console.log('1. 停止当前Auth服务 (Ctrl+C)');
    console.log('2. 设置生产环境变量:');
    console.log('   export NODE_ENV=production');
    console.log('   # 或在Windows: set NODE_ENV=production');
    console.log('3. 重新启动Auth服务:');
    console.log('   npm run start:auth');
    console.log('4. 运行生产环境测试:');
    console.log('   node test-rate-limit-enabled.js');
    console.log('');
    console.log('⚠️ 注意: 生产环境测试包含等待时间，大约需要2-3分钟');
    
    // 检查是否可以进行生产环境测试
    const canTestProduction = await this.checkProductionReadiness();
    
    if (canTestProduction) {
      console.log('\n🎯 检测到生产环境配置，开始生产环境测试...');
      await this.runProductionRateLimitTest();
    } else {
      console.log('\n📝 生产环境测试需要手动执行');
      this.testResults.production = { 
        skipped: true, 
        reason: '需要手动切换环境' 
      };
    }
  }

  async checkProductionReadiness() {
    const nodeEnv = process.env.NODE_ENV;
    const rateLimitEnabled = process.env.RATE_LIMIT_ENABLED;
    
    return nodeEnv === 'production' || rateLimitEnabled === 'true';
  }

  async runProductionRateLimitTest() {
    console.log('🔍 生产环境限流测试开始...');
    
    // 1. 测试正常频率
    console.log('\n📊 测试正常请求频率');
    const normalResult = await this.testNormalFrequency();
    
    // 2. 测试超限频率
    console.log('\n🚨 测试超限请求频率');
    const overLimitResult = await this.testOverLimitFrequency();
    
    const productionPassed = normalResult.passed && overLimitResult.triggered;
    
    this.testResults.production = {
      passed: productionPassed,
      environment: 'production',
      expectedBehavior: '限流启用',
      actualBehavior: productionPassed ? '限流正常工作' : '限流配置异常',
      details: {
        normalFrequency: normalResult,
        overLimit: overLimitResult
      }
    };
    
    if (productionPassed) {
      console.log('✅ 生产环境测试通过 - 限流正常工作');
    } else {
      console.log('❌ 生产环境测试失败 - 限流配置异常');
    }
  }

  async testNormalFrequency() {
    console.log('发送3个请求，间隔2秒');
    
    let successCount = 0;
    let rateLimitedCount = 0;
    
    for (let i = 0; i < 3; i++) {
      const result = await this.sendTestRequest(i + 1);
      if (result.rateLimited) {
        rateLimitedCount++;
      } else if (result.success) {
        successCount++;
      }
      
      if (i < 2) {
        console.log('  等待2秒...');
        await this.sleep(2000);
      }
    }
    
    const passed = rateLimitedCount === 0;
    console.log(`结果: ${successCount}成功, ${rateLimitedCount}限流`);
    
    return { passed, successCount, rateLimitedCount };
  }

  async testOverLimitFrequency() {
    console.log('快速发送10个请求');
    
    let successCount = 0;
    let rateLimitedCount = 0;
    
    const promises = [];
    for (let i = 0; i < 10; i++) {
      promises.push(this.sendTestRequest(i + 1));
    }
    
    const results = await Promise.all(promises);
    
    results.forEach(result => {
      if (result.rateLimited) {
        rateLimitedCount++;
      } else if (result.success) {
        successCount++;
      }
    });
    
    const triggered = rateLimitedCount > 0;
    console.log(`结果: ${successCount}成功, ${rateLimitedCount}限流`);
    
    return { triggered, successCount, rateLimitedCount };
  }

  async sendTestRequest(num) {
    try {
      const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
        username 'test',
        password: 'wrong'
      }, {
        validateStatus: () => true,
        timeout: 5000
      });
      
      const isRateLimited = response.status === 429;
      const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
      
      const status = isRateLimited ? '❌ 限流' : (isSuccess ? '✅ 正常' : '⚠️ 错误');
      console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ${status}`);
      
      return { 
        rateLimited: isRateLimited, 
        success: isSuccess, 
        status: response.status 
      };
    } catch (error) {
      console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误`);
      return { rateLimited: false, success: false, error: error.message };
    }
  }

  async checkCurrentEnvironment() {
    try {
      // 尝试获取服务健康状态
      const response = await axios.get(`${AUTH_BASE_URL}/health`, {
        timeout: 3000,
        validateStatus: () => true
      });
      
      if (response.status === 200) {
        console.log('✅ Auth服务连接正常');
        
        // 从环境变量检查
        const nodeEnv = process.env.NODE_ENV || 'development';
        console.log(`📋 NODE_ENV: ${nodeEnv}`);
        
        return nodeEnv;
      } else {
        console.log('⚠️ Auth服务状态异常');
        return null;
      }
    } catch (error) {
      console.log('❌ 无法连接到Auth服务，请确保服务已启动');
      return null;
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printFinalSummary() {
    console.log('\n🎯 环境限流测试总结');
    console.log('=' .repeat(60));
    
    Object.entries(this.testResults).forEach(([env, result]) => {
      console.log(`\n${env.toUpperCase()}环境:`);
      
      if (result.skipped) {
        console.log(`  ⏭️ 跳过: ${result.reason}`);
      } else {
        const status = result.passed ? '✅ 通过' : '❌ 失败';
        console.log(`  状态: ${status}`);
        console.log(`  期望行为: ${result.expectedBehavior}`);
        console.log(`  实际行为: ${result.actualBehavior}`);
      }
    });
    
    const testedEnvironments = Object.values(this.testResults).filter(r => !r.skipped);
    const passedTests = testedEnvironments.filter(r => r.passed);
    
    console.log(`\n📊 测试统计:`);
    console.log(`  已测试环境: ${testedEnvironments.length}`);
    console.log(`  通过测试: ${passedTests.length}`);
    console.log(`  失败测试: ${testedEnvironments.length - passedTests.length}`);
    
    if (passedTests.length === testedEnvironments.length && testedEnvironments.length > 0) {
      console.log('\n🎉 所有环境限流配置正确！');
    } else {
      console.log('\n⚠️ 部分环境配置需要检查');
    }
  }
}

// 运行测试
async function runEnvironmentTests() {
  const tester = new EnvironmentRateLimitTester();
  await tester.runEnvironmentTests();
}

if (require.main === module) {
  runEnvironmentTests().catch(console.error);
}

module.exports = EnvironmentRateLimitTester;
