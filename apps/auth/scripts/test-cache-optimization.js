/**
 * 缓存优化测试脚本
 * 测试findById函数的缓存功能
 */

const axios = require('axios');
const Redis = require('ioredis');

// 配置
const AUTH_BASE_URL = 'http://127.0.0.1:3001';
const REDIS_CONFIG = {
  host: '***************',
  port: 6379,
  password: '123456',
  db: 1, // Auth服务使用数据库1
};

class CacheOptimizationTest {
  constructor() {
    this.redis = new Redis(REDIS_CONFIG);
    this.authToken = null;
    this.testUserId = null;
  }

  async init() {
    console.log('🚀 初始化缓存优化测试...');
    
    // 测试Redis连接
    try {
      await this.redis.ping();
      console.log('✅ Redis连接成功');
    } catch (error) {
      console.error('❌ Redis连接失败:', error.message);
      process.exit(1);
    }

    // 获取认证令牌（使用测试用户）
    await this.getAuthToken();
  }

  async getAuthToken() {
    try {
      console.log('🔐 获取认证令牌...');

      // 尝试登录测试用户
      const loginResponse = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
        username 'cachetest',
        password: 'CacheTest123!'
      });

      if (loginResponse.data.success) {
        this.authToken = loginResponse.data.data.accessToken;
        this.testUserId = loginResponse.data.data.user.id;
        console.log('✅ 认证令牌获取成功');
        console.log(`✅ 测试用户ID: ${this.testUserId}`);
      } else {
        console.log('⚠️ 登录失败，将使用无认证测试');
      }
    } catch (error) {
      console.log('⚠️ 登录失败，将使用无认证测试:', error.response?.data?.message || error.message);
    }
  }

  async testCachePerformance() {
    console.log('\n📊 测试缓存性能...');

    // 使用实际的测试用户ID
    const testUserId = this.testUserId || '60f1b2b3c4d5e6f7a8b9c0d1';
    console.log(`🎯 测试用户ID: ${testUserId}`);

    // 清除可能存在的缓存
    await this.clearUserCache(testUserId);
    
    console.log('\n1️⃣ 第一次查询（缓存未命中）:');
    const firstQueryTime = await this.queryUserById(testUserId);
    
    console.log('\n2️⃣ 第二次查询（缓存命中）:');
    const secondQueryTime = await this.queryUserById(testUserId);
    
    console.log('\n3️⃣ 第三次查询（缓存命中）:');
    const thirdQueryTime = await this.queryUserById(testUserId);
    
    // 性能分析
    console.log('\n📈 性能分析:');
    console.log(`第一次查询: ${firstQueryTime}ms（缓存未命中）`);
    console.log(`第二次查询: ${secondQueryTime}ms（缓存命中）`);
    console.log(`第三次查询: ${thirdQueryTime}ms（缓存命中）`);
    
    if (secondQueryTime < firstQueryTime) {
      const improvement = ((firstQueryTime - secondQueryTime) / firstQueryTime * 100).toFixed(1);
      console.log(`✅ 缓存优化效果: 性能提升 ${improvement}%`);
    }
  }

  async queryUserById(userId) {
    const startTime = Date.now();
    
    try {
      const headers = {};
      if (this.authToken) {
        headers.Authorization = `Bearer ${this.authToken}`;
      }

      const response = await axios.get(`${AUTH_BASE_URL}/users/${userId}`, {
        headers,
        validateStatus: () => true // 接受所有状态码
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`   状态码: ${response.status}`);
      console.log(`   响应时间: ${duration}ms`);
      
      if (response.status === 200) {
        console.log(`   用户名: ${response.data.data?.username || 'N/A'}`);
      } else if (response.status === 401) {
        console.log(`   需要认证: ${response.data.message}`);
      } else if (response.status === 404) {
        console.log(`   用户不存在: ${response.data.message}`);
      } else {
        console.log(`   响应: ${response.data.message || 'Unknown'}`);
      }

      return duration;
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`   错误: ${error.message}`);
      console.log(`   响应时间: ${duration}ms`);
      
      return duration;
    }
  }

  async clearUserCache(userId) {
    try {
      console.log('🧹 清除用户缓存...');
      
      // 根据Redis前缀架构v2.0，实际的键格式是: development:fm:auth:user:123
      const keyPatterns = [
        `development:fm:auth:user:${userId}`,
        `development:fm:auth:user:username:*`,
        `development:fm:auth:user:email:*`,
      ];

      let deletedCount = 0;
      for (const pattern of keyPatterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
          deletedCount += keys.length;
        }
      }

      console.log(`✅ 清除了 ${deletedCount} 个缓存键`);
    } catch (error) {
      console.log(`⚠️ 清除缓存失败: ${error.message}`);
    }
  }

  async testCacheKeys() {
    console.log('\n🔍 检查Redis缓存键...');
    
    try {
      // 查看Auth服务的所有缓存键
      const authKeys = await this.redis.keys('development:fm:auth:*');
      
      console.log(`📊 Auth服务缓存键总数: ${authKeys.length}`);
      
      if (authKeys.length > 0) {
        console.log('🔑 缓存键示例:');
        authKeys.slice(0, 10).forEach((key, index) => {
          console.log(`   ${index + 1}. ${key}`);
        });
        
        if (authKeys.length > 10) {
          console.log(`   ... 还有 ${authKeys.length - 10} 个键`);
        }
      } else {
        console.log('📭 暂无缓存键');
      }
    } catch (error) {
      console.log(`❌ 检查缓存键失败: ${error.message}`);
    }
  }

  async testVerifyTokenCache() {
    console.log('\n🔐 测试verify-token缓存...');
    
    if (!this.authToken) {
      console.log('⚠️ 无认证令牌，跳过verify-token测试');
      return;
    }

    console.log('\n1️⃣ 第一次验证令牌:');
    const firstVerifyTime = await this.verifyToken();
    
    console.log('\n2️⃣ 第二次验证令牌:');
    const secondVerifyTime = await this.verifyToken();
    
    console.log('\n📈 verify-token性能分析:');
    console.log(`第一次验证: ${firstVerifyTime}ms`);
    console.log(`第二次验证: ${secondVerifyTime}ms`);
    
    if (secondVerifyTime < firstVerifyTime) {
      const improvement = ((firstVerifyTime - secondVerifyTime) / firstVerifyTime * 100).toFixed(1);
      console.log(`✅ 缓存优化效果: 性能提升 ${improvement}%`);
    }
  }

  async verifyToken() {
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${AUTH_BASE_URL}/auth/verify-token`, {
        token: this.authToken
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`   状态码: ${response.status}`);
      console.log(`   响应时间: ${duration}ms`);
      console.log(`   令牌有效: ${response.data.data?.valid || false}`);

      return duration;
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`   错误: ${error.message}`);
      console.log(`   响应时间: ${duration}ms`);
      
      return duration;
    }
  }

  async cleanup() {
    console.log('\n🧹 清理资源...');
    await this.redis.quit();
    console.log('✅ 测试完成');
  }
}

// 运行测试
async function runTest() {
  const test = new CacheOptimizationTest();
  
  try {
    await test.init();
    await test.testCacheKeys();
    await test.testCachePerformance();
    await test.testVerifyTokenCache();
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await test.cleanup();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = CacheOptimizationTest;
