# ===== 认证服务安全开发环境配置 =====
# 开发环境特定的安全配置，覆盖基础安全配置

# 开发环境IP白名单（禁用）
ENABLE_IP_WHITELIST=false
ALLOWED_IPS=
TRUSTED_PROXIES=

# 开发环境服务间认证（简化）
ENABLE_SERVICE_AUTH=false
SERVICE_SECRET=development-secret-key
SERVICE_TOKEN_EXPIRY=86400         # 24小时，便于开发

# 开发环境监控和日志（详细）
SECURITY_LOG_LEVEL=debug
LOG_SUCCESSFUL_ACCESS=true
LOG_FAILED_ACCESS=true
ENABLE_SECURITY_METRICS=true

# 开发环境频率限制（禁用）
RATE_LIMIT_ENABLED=false
RATE_LIMIT_WINDOW_MS=1000
RATE_LIMIT_MAX_REQUESTS=999999
RATE_LIMIT_SKIP_SUCCESS=true

# 开发环境CORS配置（宽松）
CORS_ENABLED=true
CORS_ORIGIN=*
CORS_CREDENTIALS=true
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS
CORS_HEADERS=*

# 开发环境JWT配置（宽松）
JWT_SECRET=development-jwt-secret-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=auth-service-dev

# 开发环境密码策略（简化）
PASSWORD_MIN_LENGTH=6
PASSWORD_REQUIRE_UPPERCASE=false
PASSWORD_REQUIRE_LOWERCASE=false
PASSWORD_REQUIRE_NUMBERS=false
PASSWORD_REQUIRE_SPECIAL_CHARS=false
PASSWORD_SALT_ROUNDS=10
PASSWORD_MAX_AGE=999999
PASSWORD_HISTORY_COUNT=0

# 开发环境会话配置（宽松）
SESSION_SECRET=development-session-secret
SESSION_MAX_AGE=86400000           # 24小时
SESSION_SECURE=false               # HTTP允许
SESSION_SAME_SITE=lax

# 开发环境调试配置
SECURITY_DEBUG=true
ENABLE_SECURITY_HEADERS=false
ENABLE_HELMET=false
