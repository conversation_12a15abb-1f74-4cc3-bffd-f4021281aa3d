# 认证服务共享模块使用指南

## 📋 概述

认证服务共享模块提供了一套完整的认证、授权、验证、日志记录和安全防护功能。本指南将详细介绍每个组件的功能和使用方法。

## 🏗️ 架构概览

```
shared/
├── constants/          # 常量定义
├── interfaces/         # 接口定义
├── dto/               # 数据传输对象
├── services/          # 核心服务
├── guards/            # 路由守卫
├── interceptors/      # 拦截器
├── pipes/             # 管道
├── filters/           # 异常过滤器
└── decorators/        # 装饰器
```

## 🔧 核心服务

### CryptoService - 加密服务

提供全面的加密、解密、哈希和签名功能。

#### 主要功能
- 数据加密/解密（AES-256-GCM）
- 密码哈希/验证（bcrypt）
- 数字签名和验证
- 随机数据生成
- 密码强度评估

#### 使用示例

```typescript
import { CryptoService } from '@shared/services/crypto.service';

@Injectable()
export class AuthService {
  constructor(private readonly cryptoService: CryptoService) {}

  // 密码哈希
  async hashPassword(password: string): Promise<string> {
    return this.cryptoService.hashPassword(password);
  }

  // 密码验证
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return this.cryptoService.verifyPassword(password, hash);
  }

  // 数据加密
  encryptSensitiveData(data: string): EncryptionResult {
    return this.cryptoService.encrypt(data);
  }

  // 生成安全令牌
  generateSecureToken(): string {
    return this.cryptoService.generateToken(32);
  }

  // 密码强度检查
  checkPasswordStrength(password: string) {
    return this.cryptoService.calculatePasswordStrength(password);
  }
}
```

### ValidationService - 验证服务

提供各种数据验证功能，包括邮箱、手机、用户名等。

#### 主要功能
- DTO对象验证
- 邮箱地址验证
- 手机号码验证
- 用户名验证
- 密码强度验证
- URL和IP地址验证

#### 使用示例

```typescript
import { ValidationService } from '@shared/services/validation.service';

@Injectable()
export class UserService {
  constructor(private readonly validationService: ValidationService) {}

  async validateUserData(userData: any): Promise<ValidationResult> {
    // 验证邮箱
    const emailResult = this.validationService.validateEmail(userData.email);
    if (!emailResult.valid) {
      throw new BadRequestException('邮箱格式无效');
    }

    // 验证用户名
    const usernameResult = this.validationService.validateUsername(userData.username);
    if (!usernameResult.valid) {
      throw new BadRequestException('用户名格式无效');
    }

    // 验证密码强度
    const passwordResult = this.validationService.validatePassword(userData.password);
    if (!passwordResult.valid) {
      throw new BadRequestException(`密码不符合要求: ${passwordResult.errors.join(', ')}`);
    }

    return { valid: true, errors: [], warnings: [] };
  }
}
```

### UtilsService - 工具服务

提供各种通用工具函数。

#### 主要功能
- 设备指纹生成
- IP地理位置解析
- User-Agent解析
- 时间格式化
- 对象操作（深度克隆、合并）
- 数组操作（分块、去重）
- 敏感信息掩码

#### 使用示例

```typescript
import { UtilsService } from '@shared/services/utils.service';

@Injectable()
export class SessionService {
  constructor(private readonly utilsService: UtilsService) {}

  createSession(request: Request): SessionInfo {
    const ip = this.getClientIp(request);
    const userAgent = request.get('User-Agent');

    return {
      sessionId: this.utilsService.generateRandomString(32),
      deviceFingerprint: this.utilsService.generateDeviceFingerprint(userAgent, ip),
      deviceInfo: this.utilsService.parseUserAgent(userAgent),
      location: this.utilsService.getLocationFromIp(ip),
      createdAt: new Date(),
    };
  }

  maskSensitiveData(email: string): string {
    return this.utilsService.maskSensitiveData(email, 'email');
  }
}
```

## 🛡️ 守卫组件

### ThrottlerBehindProxyGuard - 代理后限流守卫

为在代理服务器后面的应用提供智能限流保护。

#### 特性
- 真实IP地址识别
- Redis存储支持
- 路径特定配置
- 认证端点特殊处理

#### 使用示例

```typescript
import { ThrottlerBehindProxyGuard, Throttle } from '@shared/guards/throttler-behind-proxy.guard';

@Controller('auth')
@UseGuards(ThrottlerBehindProxyGuard)
export class AuthController {
  // 登录限流：15分钟内最多5次
  @Post('login')
  @Throttle({ limit: 5, ttl: 900 })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  // 注册限流：1小时内最多3次
  @Post('register')
  @Throttle({ limit: 3, ttl: 3600 })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }
}
```

## 🔄 拦截器组件

### ResponseInterceptor - 响应拦截器

统一处理API响应格式，提供标准化的成功响应。

#### 使用示例

```typescript
import { ResponseInterceptor } from '@shared/interceptors/response.interceptor';

@Controller('users')
@UseInterceptors(ResponseInterceptor)
export class UsersController {
  @Get()
  async getUsers(): Promise<User[]> {
    // 返回的数据会被自动包装成标准响应格式
    return this.usersService.findAll();
  }
}

// 响应格式：
// {
//   "success": true,
//   "data": [...],
//   "message": "查询成功",
//   "timestamp": "2023-12-01T10:00:00.000Z"
// }
```

### LoggingInterceptor - 日志拦截器

记录详细的请求和响应日志，支持安全日志记录。

#### 使用示例

```typescript
import { LoggingInterceptor, SecurityLoggingInterceptor } from '@shared/interceptors/logging.interceptor';

@Controller('auth')
@UseInterceptors(LoggingInterceptor, SecurityLoggingInterceptor)
export class AuthController {
  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    // 会自动记录请求和响应日志
    // 安全相关的操作会记录到安全日志
    return this.authService.login(loginDto);
  }
}
```

### TimeoutInterceptor - 超时拦截器

为请求设置智能超时时间，支持动态调整。

#### 使用示例

```typescript
import { TimeoutInterceptor, Timeout } from '@shared/interceptors/timeout.interceptor';

@Controller('files')
@UseInterceptors(TimeoutInterceptor)
export class FilesController {
  // 文件上传：5分钟超时
  @Post('upload')
  @Timeout(300000)
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.filesService.upload(file);
  }

  // 普通查询：30秒超时（默认）
  @Get()
  async getFiles() {
    return this.filesService.findAll();
  }
}
```

## 🔧 管道组件

### ValidationPipe - 增强验证管道

提供增强的数据验证功能，包括业务规则和安全验证。

#### 使用示例

```typescript
import { ValidationPipe, CustomValidationPipe } from '@shared/pipes/validation.pipe';

@Controller('users')
export class UsersController {
  @Post()
  @UsePipes(new ValidationPipe(configService))
  async createUser(@Body() createUserDto: CreateUserDto) {
    // 会自动验证DTO并进行安全检查
    return this.usersService.create(createUserDto);
  }

  @Post('register')
  @UsePipes(new CustomValidationPipe(configService))
  async register(@Body() registerDto: RegisterDto) {
    // 会进行额外的业务规则验证
    return this.authService.register(registerDto);
  }
}
```

### ParseObjectIdPipe - ObjectId解析管道

验证和转换MongoDB ObjectId。

#### 使用示例

```typescript
import { 
  ParseObjectIdPipe, 
  ParseOptionalObjectIdPipe,
  ParseObjectIdArrayPipe 
} from '@shared/pipes/parse-object-id.pipe';

@Controller('users')
export class UsersController {
  @Get(':id')
  async getUser(@Param('id', ParseObjectIdPipe) id: string) {
    return this.usersService.findById(id);
  }

  @Get()
  async getUsers(
    @Query('roleId', ParseOptionalObjectIdPipe) roleId?: string,
    @Query('ids', ParseObjectIdArrayPipe) ids?: string[]
  ) {
    return this.usersService.findByFilters({ roleId, ids });
  }
}
```

## 🚫 过滤器组件

### AllExceptionsFilter - 全局异常过滤器

捕获和处理所有未处理的异常，提供统一的错误响应。

#### 使用示例

```typescript
import { AllExceptionsFilter } from '@shared/filters/all-exceptions.filter';

// 在 main.ts 中全局注册
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  app.useGlobalFilters(new AllExceptionsFilter(app.get(ConfigService)));
  
  await app.listen(3000);
}
```

### ValidationExceptionFilter - 验证异常过滤器

专门处理数据验证相关的异常。

#### 使用示例

```typescript
import { ValidationExceptionFilter } from '@shared/filters/validation-exception.filter';

@Controller('users')
@UseFilters(ValidationExceptionFilter)
export class UsersController {
  @Post()
  async createUser(@Body() createUserDto: CreateUserDto) {
    // 验证错误会被格式化为标准错误响应
    return this.usersService.create(createUserDto);
  }
}
```

## 🏷️ 装饰器组件

### 认证装饰器

提供丰富的认证和授权装饰器。

#### 使用示例

```typescript
import { 
  Public, 
  Auth, 
  RequireRoles, 
  RequirePermissions,
  RequireMfa,
  RequireEmailVerified,
  SecureEndpoint,
  RateLimit
} from '@shared/decorators/auth.decorator';

@Controller('users')
export class UsersController {
  // 公开端点
  @Get('public')
  @Public()
  async getPublicInfo() {
    return { message: '公开信息' };
  }

  // 需要认证
  @Get('me')
  @Auth()
  async getProfile(@CurrentUser() user: User) {
    return user;
  }

  // 需要特定角色
  @Get('admin')
  @RequireRoles('admin', 'super_admin')
  async getAdminData() {
    return this.usersService.getAdminData();
  }

  // 需要特定权限（使用现有的权限装饰器）
  @Post('admin/users')
  @RequirePermissions([
    { resource: 'user', action: 'create' },
    { resource: 'user', action: 'manage' }
  ])
  async createUser(@Body() dto: CreateUserDto) {
    return this.usersService.create(dto);
  }

  // 或者使用新的字符串权限装饰器
  @Post('admin/roles')
  @RequireStringPermissions('user:create', 'user:manage')
  async createRole(@Body() dto: CreateRoleDto) {
    return this.rolesService.create(dto);
  }

  // 需要MFA验证
  @Delete(':id')
  @RequireMfa()
  @RequirePermissions('user:delete')
  async deleteUser(@Param('id') id: string) {
    return this.usersService.delete(id);
  }

  // 组合安全要求（使用字符串权限）
  @Post('sensitive-operation')
  @SecureEndpoint({
    roles: ['admin'],
    permissions: ['sensitive:access', 'admin:operate'],
    requireMfa: true,
    requireEmailVerified: true,
    rateLimit: { limit: 3, ttl: 3600 }
  })
  async sensitiveOperation() {
    return this.usersService.performSensitiveOperation();
  }

  // 限流保护
  @Post('bulk-operation')
  @RateLimit(10, 60) // 1分钟10次
  async bulkOperation(@Body() data: any[]) {
    return this.usersService.bulkProcess(data);
  }
}
```

### API响应装饰器

提供标准化的API文档装饰器。

#### 使用示例

```typescript
import { 
  ApiSuccessResponse,
  ApiCreateResponse,
  ApiPaginatedResponse,
  ApiStandardResponses,
  ApiValidationErrorResponse,
  ApiAuthErrorResponse
} from '@shared/decorators/api-response.decorator';

@Controller('users')
export class UsersController {
  @Get()
  @ApiPaginatedResponse({ itemType: UserDto })
  @ApiStandardResponses({ includeAuth: true })
  async getUsers(@Query() pagination: PaginationDto) {
    return this.usersService.findAll(pagination);
  }

  @Post()
  @ApiCreateResponse({ type: UserDto })
  @ApiValidationErrorResponse()
  @ApiAuthErrorResponse()
  async createUser(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get(':id')
  @ApiSuccessResponse({ type: UserDto })
  @ApiNotFoundErrorResponse('用户')
  async getUser(@Param('id') id: string) {
    return this.usersService.findById(id);
  }
}
```

## 📊 分页功能

### PaginationDto - 分页查询

提供标准化的分页查询功能。

#### 使用示例

```typescript
import { PaginationDto, PaginationHelper } from '@shared/dto/pagination.dto';

@Controller('users')
export class UsersController {
  @Get()
  async getUsers(@Query() pagination: PaginationDto) {
    const { items, total } = await this.usersService.findAll(pagination);
    
    return PaginationHelper.createResponse(
      items,
      total,
      pagination.page,
      pagination.limit,
      {
        sortBy: pagination.sortBy,
        sortOrder: pagination.sortOrder,
        search: pagination.search
      }
    );
  }
}

// 查询示例：
// GET /users?page=1&limit=10&sortBy=createdAt&sortOrder=desc&search=john
```

## 🔐 常量和接口

### 错误代码

```typescript
import { ERROR_CODES, ERROR_MESSAGES } from '@shared/constants/error.constants';

// 使用错误代码
throw new BadRequestException({
  code: ERROR_CODES.VALIDATION_FAILED,
  message: ERROR_MESSAGES[ERROR_CODES.VALIDATION_FAILED]
});
```

### 角色和权限

```typescript
import { SYSTEM_ROLES, GAME_ROLES, SYSTEM_PERMISSIONS } from '@shared/constants/role.constants';

// 检查角色
if (user.roles.includes(SYSTEM_ROLES.SUPER_ADMIN)) {
  // 超级管理员逻辑
}

// 检查权限
if (user.permissions.includes(SYSTEM_PERMISSIONS.USER_MANAGE)) {
  // 用户管理权限逻辑
}
```

## 🚀 最佳实践

### 1. 错误处理

```typescript
@Controller('users')
@UseFilters(AllExceptionsFilter, ValidationExceptionFilter)
export class UsersController {
  @Post()
  async createUser(@Body() dto: CreateUserDto) {
    try {
      return await this.usersService.create(dto);
    } catch (error) {
      // 错误会被过滤器自动处理和格式化
      throw error;
    }
  }
}
```

### 2. 安全防护

```typescript
@Controller('auth')
@UseGuards(ThrottlerBehindProxyGuard)
@UseInterceptors(SecurityLoggingInterceptor)
export class AuthController {
  @Post('login')
  @RateLimit(5, 900) // 15分钟5次
  @UsePipes(CustomValidationPipe) // 安全验证
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }
}
```

### 3. 性能优化

```typescript
@Controller('data')
@UseInterceptors(CacheResponseInterceptor, TimeoutInterceptor)
export class DataController {
  @Get('heavy-computation')
  @Timeout(60000) // 1分钟超时
  async getHeavyData() {
    return this.dataService.performHeavyComputation();
  }
}
```

## 📝 配置说明

在 `.env` 文件中添加以下配置：

```env
# 加密配置
ENCRYPTION_MASTER_KEY=your-32-char-master-key-here
ENCRYPTION_ALGORITHM=aes-256-gcm

# 日志配置
LOGGING_ENABLED=true
LOGGING_LEVEL=info
LOGGING_INCLUDE_BODY=false

# 超时配置
TIMEOUT_DEFAULT=30000
TIMEOUT_UPLOAD=300000

# 限流配置
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# API配置
API_VERSION=v1
```

## 🧪 测试

运行兼容性验证：

```bash
npx ts-node apps/auth/scripts/verify-compatibility.ts
```

运行单元测试：

```bash
npm test -- --testPathPattern="shared"
```

## 📚 更多资源

- [API文档](./API_DOCUMENTATION.md)
- [安全指南](./SECURITY_GUIDE.md)
- [性能优化](./PERFORMANCE_GUIDE.md)
- [故障排除](./TROUBLESHOOTING.md)
