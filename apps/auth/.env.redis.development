# ===== 认证服务Redis开发环境配置 =====
# 开发环境特定的Redis配置，覆盖基础Redis配置

# 开发环境Redis连接
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=0

# 开发环境TTL配置（更短，便于测试）
REDIS_AUTH_SESSION_TTL=3600        # 1小时（生产环境2小时）
REDIS_AUTH_TOKEN_TTL=1800          # 30分钟（生产环境1小时）
REDIS_AUTH_BLACKLIST_TTL=43200     # 12小时（生产环境24小时）

# 开发环境限流配置（更宽松）
REDIS_AUTH_RATE_LIMIT_WINDOW=60
REDIS_AUTH_RATE_LIMIT_MAX_REQUESTS=1000  # 开发环境放宽限制

# 开发环境缓存配置
REDIS_AUTH_CACHE_TTL=300           # 5分钟缓存
REDIS_AUTH_CACHE_MAX_SIZE=1000     # 最大缓存条目

# 开发环境连接池配置
REDIS_AUTH_POOL_MIN=1
REDIS_AUTH_POOL_MAX=5

# 开发环境调试配置
REDIS_AUTH_DEBUG=true
REDIS_AUTH_LOG_COMMANDS=true
