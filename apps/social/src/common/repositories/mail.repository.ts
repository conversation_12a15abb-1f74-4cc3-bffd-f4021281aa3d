/**
 * 邮件数据访问层
 * 基于Repository模式实现数据库访问
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Mail, MailDocument, MailStatus } from '../schemas/mail.schema';
import { CreateMailDto } from '../dto/mail.dto';

@Injectable()
export class MailRepository {
  private readonly logger = new Logger(MailRepository.name);

  constructor(
    @InjectModel(Mail.name) private mailModel: Model<MailDocument>,
  ) {}

  /**
   * 创建邮件
   */
  async create(createData: CreateMailDto & { uid: string }): Promise<MailDocument> {
    try {
      const mail = new this.mailModel(createData);
      const savedMail = await mail.save();
      this.logger.log(`邮件创建成功: ${savedMail.uid}`);
      return savedMail;
    } catch (error) {
      this.logger.error('创建邮件失败', error);
      throw error;
    }
  }

  /**
   * 根据UID查找邮件
   */
  async findByUid(uid: string): Promise<MailDocument | null> {
    try {
      return await this.mailModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`查找邮件失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 根据接收者ID查找邮件列表
   */
  async findByReceiver(
    receiverUid: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<{ mails: MailDocument[]; total: number }> {
    try {
      const skip = (page - 1) * limit;
      
      const [mails, total] = await Promise.all([
        this.mailModel
          .find({ receiverUid })
          .sort({ sendTime: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.mailModel.countDocuments({ receiverUid })
      ]);

      return { mails, total };
    } catch (error) {
      this.logger.error(`查找邮件列表失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 查找未读邮件
   */
  async findUnreadByReceiver(receiverUid: string): Promise<MailDocument[]> {
    try {
      return await this.mailModel
        .find({ 
          receiverUid, 
          status: MailStatus.UNREAD 
        })
        .sort({ sendTime: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`查找未读邮件失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 统计未读邮件数量
   */
  async countUnreadByReceiver(receiverUid: string): Promise<number> {
    try {
      return await this.mailModel.countDocuments({ 
        receiverUid, 
        status: MailStatus.UNREAD 
      });
    } catch (error) {
      this.logger.error(`统计未读邮件失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 更新邮件
   */
  async update(
    uid: string, 
    updateData: UpdateQuery<MailDocument>
  ): Promise<MailDocument | null> {
    try {
      const updatedMail = await this.mailModel.findOneAndUpdate(
        { uid },
        updateData,
        { new: true }
      ).exec();

      if (updatedMail) {
        this.logger.log(`邮件更新成功: ${uid}`);
      }

      return updatedMail;
    } catch (error) {
      this.logger.error(`更新邮件失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 标记邮件为已读
   */
  async markAsRead(uid: string): Promise<MailDocument | null> {
    try {
      return await this.update(uid, {
        status: MailStatus.READ,
        readTime: Date.now()
      });
    } catch (error) {
      this.logger.error(`标记邮件已读失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 标记邮件附件为已领取
   */
  async markAttachmentClaimed(uid: string): Promise<MailDocument | null> {
    try {
      return await this.update(uid, {
        status: MailStatus.CLAIMED,
        claimTime: Date.now()
      });
    } catch (error) {
      this.logger.error(`标记附件已领取失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 删除邮件
   */
  async delete(uid: string): Promise<boolean> {
    try {
      const result = await this.mailModel.deleteOne({ uid }).exec();
      if (result.deletedCount > 0) {
        this.logger.log(`邮件删除成功: ${uid}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`删除邮件失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 批量删除过期邮件
   */
  async deleteExpiredMails(): Promise<number> {
    try {
      const now = Date.now();
      const result = await this.mailModel.deleteMany({
        expireTime: { $gt: 0, $lt: now }
      });

      this.logger.log(`删除过期邮件: ${result.deletedCount} 封`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error('删除过期邮件失败', error);
      throw error;
    }
  }

  /**
   * 查找玩家最老的邮件
   */
  async findOldestByReceiver(receiverUid: string): Promise<MailDocument | null> {
    try {
      return await this.mailModel
        .findOne({ receiverUid })
        .sort({ sendTime: 1 })
        .exec();
    } catch (error) {
      this.logger.error(`查找最老邮件失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 统计玩家邮件数量
   */
  async countByReceiver(receiverUid: string): Promise<number> {
    try {
      return await this.mailModel.countDocuments({ receiverUid });
    } catch (error) {
      this.logger.error(`统计邮件数量失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 查找有附件的邮件
   */
  async findWithAttachmentsByReceiver(receiverUid: string): Promise<MailDocument[]> {
    try {
      return await this.mailModel
        .find({ 
          receiverUid,
          'attachList.0': { $exists: true },
          status: { $ne: MailStatus.CLAIMED }
        })
        .sort({ sendTime: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`查找有附件邮件失败: ${receiverUid}`, error);
      throw error;
    }
  }

  /**
   * 根据发送者查找邮件
   */
  async findBySender(senderUid: string): Promise<MailDocument[]> {
    try {
      return await this.mailModel
        .find({ senderUid })
        .sort({ sendTime: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`查找发送邮件失败: ${senderUid}`, error);
      throw error;
    }
  }

  /**
   * 根据邮件类型查找邮件
   */
  async findByType(receiverUid: string, mailType: number): Promise<MailDocument[]> {
    try {
      return await this.mailModel
        .find({ receiverUid, mailType })
        .sort({ sendTime: -1 })
        .exec();
    } catch (error) {
      this.logger.error(`查找指定类型邮件失败: ${receiverUid}, 类型: ${mailType}`, error);
      throw error;
    }
  }

  /**
   * 批量更新邮件状态
   */
  async batchUpdateStatus(
    filter: FilterQuery<MailDocument>, 
    status: MailStatus
  ): Promise<number> {
    try {
      const result = await this.mailModel.updateMany(
        filter,
        { status, readTime: Date.now() }
      );

      this.logger.log(`批量更新邮件状态: ${result.modifiedCount} 封`);
      return result.modifiedCount;
    } catch (error) {
      this.logger.error('批量更新邮件状态失败', error);
      throw error;
    }
  }
}
