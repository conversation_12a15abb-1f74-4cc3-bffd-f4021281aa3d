/**
 * 公会系统DTO定义
 * 基于old项目association.js实体迁移
 */

import { IsString, IsNumber, IsArray, IsOptional, IsEnum, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { GuildPosition } from '../schemas/guild.schema';

// 公会成员DTO
export class GuildMemberDto {
  @IsString()
  characterId: string;     // 玩家UID

  @IsString()
  characterName: string;   // 玩家名字

  @IsNumber()
  @IsOptional()
  isOnLine?: number;    // 是否在线

  @IsNumber()
  @IsOptional()
  leaveTime?: number;   // 离线时间

  @IsNumber()
  @IsOptional()
  exp?: number;         // 玩家活跃度

  @IsNumber()
  @IsOptional()
  contribute?: number;  // 玩家贡献

  @IsString()
  @IsOptional()
  faceUrl?: string;     // 玩家头像

  @IsNumber()
  @IsOptional()
  strength?: number;    // 玩家实力

  @IsEnum(GuildPosition)
  pos: GuildPosition;   // 玩家职位

  @IsString()
  gid: string;          // 玩家gid

  @IsString()
  @IsOptional()
  frontendId?: string;  // 前端ID

  @IsString()
  @IsOptional()
  sessionId?: string;   // 会话ID

  @IsNumber()
  @IsOptional()
  joinTime?: number;    // 加入时间
}

// 公会申请DTO
export class GuildApplicationDto {
  @IsString()
  characterId: string;     // 申请者UID

  @IsString()
  characterName: string;   // 申请者名字

  @IsNumber()
  askTime: number;      // 申请时间

  @IsString()
  gid: string;          // 玩家gid

  @IsString()
  @IsOptional()
  faceUrl?: string;     // 玩家头像

  @IsNumber()
  @IsOptional()
  strength?: number;    // 玩家实力

  @IsString()
  @IsOptional()
  frontendId?: string;  // 前端ID

  @IsString()
  @IsOptional()
  sessionId?: string;   // 会话ID
}

// 创建公会DTO
export class CreateGuildDto {
  @IsString()
  characterId: string;     // 创建者ID

  @IsString()
  characterName: string;   // 创建者名字

  @IsString()
  guildName: string;    // 公会名字

  @IsNumber()
  @IsOptional()
  faceId?: number;      // 公会头像

  @IsNumber()
  @IsOptional()
  strength?: number;    // 玩家实力

  @IsString()
  gid: string;          // 玩家gid

  @IsString()
  @IsOptional()
  faceUrl?: string;     // 玩家头像

  @IsString()
  @IsOptional()
  frontendId?: string;  // 前端ID

  @IsString()
  @IsOptional()
  sessionId?: string;   // 会话ID
}

// 申请加入公会DTO
export class ApplyJoinGuildDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  characterId: string;     // 申请者ID

  @IsString()
  characterName: string;   // 申请者名字

  @IsString()
  gid: string;          // 玩家gid

  @IsString()
  @IsOptional()
  faceUrl?: string;     // 玩家头像

  @IsNumber()
  @IsOptional()
  strength?: number;    // 玩家实力

  @IsString()
  @IsOptional()
  frontendId?: string;  // 前端ID

  @IsString()
  @IsOptional()
  sessionId?: string;   // 会话ID
}

// 处理申请DTO
export class ProcessApplicationDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  approverId: string;   // 处理者ID

  @IsString()
  applicantId: string;  // 申请者ID

  @IsBoolean()
  approved: boolean;    // 是否同意
}

// 退出公会DTO
export class LeaveGuildDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  @IsOptional()
  exitType?: number;    // 退出类型 1主动退出 2被踢出
}

// 更新公会信息DTO
export class UpdateGuildDto {
  @IsString()
  @IsOptional()
  notice?: string;      // 公会公告

  @IsNumber()
  @IsOptional()
  faceId?: number;      // 公会头像
}

// 公会信息DTO（返回给客户端）
export class GuildInfoDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  guildName: string;    // 公会名字

  @IsString()
  creator: string;      // 创建者

  @IsNumber()
  createTime: number;   // 创建时间

  @IsString()
  guildNotice: string;  // 公会公告

  @IsNumber()
  faceId: number;       // 公会头像

  @IsNumber()
  guildLevel: number;   // 公会等级

  @IsNumber()
  exp: number;          // 公会经验

  @IsNumber()
  contribute: number;   // 公会贡献

  @IsNumber()
  memberCount: number;  // 成员数量

  @IsNumber()
  maxMembers: number;   // 最大成员数

  @IsNumber()
  onlineMemberCount: number; // 在线成员数

  @IsNumber()
  vicePresidentCount: number; // 副会长数量

  @IsNumber()
  maxVicePresidents: number;  // 最大副会长数

  @IsNumber()
  applicationCount: number;   // 申请数量

  @IsBoolean()
  isFull: boolean;      // 是否已满员
}

// 公会成员列表DTO
export class GuildMemberListDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GuildMemberDto)
  members: GuildMemberDto[]; // 成员列表

  @IsNumber()
  total: number;        // 总数

  @IsNumber()
  maxMembers: number;   // 最大成员数
}

// 公会申请列表DTO
export class GuildApplicationListDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GuildApplicationDto)
  applications: GuildApplicationDto[]; // 申请列表

  @IsNumber()
  total: number;        // 总数
}

// 公会操作结果DTO
export class GuildOperationResultDto {
  @IsBoolean()
  success: boolean;     // 操作是否成功

  @IsString()
  @IsOptional()
  guildId?: string;     // 公会ID

  @IsString()
  @IsOptional()
  message?: string;     // 操作消息

  @IsNumber()
  @IsOptional()
  operationTime?: number; // 操作时间

  @IsOptional()
  data?: any;           // 额外数据

  @IsOptional()
  newMember?: any;      // 新成员信息

  @IsOptional()
  newPosition?: number; // 新职位

  @IsOptional()
  newPresident?: string; // 新会长ID
}

// 搜索公会DTO
export class SearchGuildDto {
  @IsString()
  @IsOptional()
  keyword?: string;     // 搜索关键词

  @IsNumber()
  @IsOptional()
  minLevel?: number;    // 最小等级

  @IsNumber()
  @IsOptional()
  maxLevel?: number;    // 最大等级

  @IsNumber()
  @IsOptional()
  page?: number;        // 页码

  @IsNumber()
  @IsOptional()
  limit?: number;       // 每页数量
}

// 公会列表DTO
export class GuildListDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GuildInfoDto)
  guilds: GuildInfoDto[]; // 公会列表

  @IsNumber()
  total: number;        // 总数

  @IsNumber()
  page: number;         // 当前页

  @IsNumber()
  limit: number;        // 每页数量
}

// 职位变更DTO
export class ChangePositionDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  operatorId: string;   // 操作者ID

  @IsString()
  targetCharacterId: string; // 目标玩家ID

  @IsEnum(GuildPosition)
  newPosition: GuildPosition; // 新职位
}

// 转让会长DTO
export class TransferPresidencyDto {
  @IsString()
  guildId: string;      // 公会ID

  @IsString()
  currentPresidentId: string; // 当前会长ID

  @IsString()
  newPresidentId: string;     // 新会长ID
}
