/**
 * 聊天相关的数据传输对象
 */

import { IsString, IsOptional, IsNumber, IsEnum, IsObject, Length, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ChatMessageType } from '../schemas/chat.schema';

// 发送聊天消息DTO
export class SendChatMessageDto {
  @ApiProperty({ description: '发送者角色ID' })
  @IsString()
  senderId: string;

  @ApiProperty({ description: '发送者名称' })
  @IsString()
  senderName: string;

  @ApiProperty({ description: '频道ID' })
  @IsString()
  channelId: string;

  @ApiProperty({ description: '消息类型', enum: ChatMessageType })
  @IsEnum(ChatMessageType)
  messageType: ChatMessageType;

  @ApiProperty({ description: '消息内容', minLength: 1, maxLength: 500 })
  @IsString()
  @Length(1, 500)
  content: string;

  @ApiPropertyOptional({ description: '接收者角色ID（私聊时必填）' })
  @IsOptional()
  @IsString()
  receiverId?: string;

  @ApiPropertyOptional({ description: '接收者名称（私聊时必填）' })
  @IsOptional()
  @IsString()
  receiverName?: string;

  @ApiPropertyOptional({ description: '额外数据（表情、图片等）' })
  @IsOptional()
  @IsObject()
  extraData?: any;

  @ApiProperty({ description: '区服ID' })
  @IsString()
  serverId: string;
}

// 获取聊天历史DTO
export class GetChatHistoryDto {
  @ApiProperty({ description: '频道ID' })
  @IsString()
  channelId: string;

  @ApiPropertyOptional({ description: '消息数量限制', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({ description: '获取此时间之前的消息' })
  @IsOptional()
  @IsNumber()
  before?: number;
}

// 获取私聊历史DTO
export class GetPrivateChatDto {
  @ApiProperty({ description: '发送者角色ID' })
  @IsString()
  senderId: string;

  @ApiProperty({ description: '接收者角色ID' })
  @IsString()
  receiverId: string;

  @ApiPropertyOptional({ description: '消息数量限制', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({ description: '获取此时间之前的消息' })
  @IsOptional()
  @IsNumber()
  before?: number;
}

// 加入频道DTO
export class JoinChannelDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '频道ID' })
  @IsString()
  channelId: string;

  @ApiProperty({ description: '区服ID' })
  @IsString()
  serverId: string;
}

// 离开频道DTO
export class LeaveChannelDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '频道ID' })
  @IsString()
  channelId: string;
}

// 聊天消息响应DTO
export class ChatMessageResponseDto {
  @ApiProperty({ description: '消息ID' })
  messageId: string;

  @ApiProperty({ description: '发送者角色ID' })
  senderId: string;

  @ApiProperty({ description: '发送者名称' })
  senderName: string;

  @ApiProperty({ description: '频道ID' })
  channelId: string;

  @ApiProperty({ description: '消息类型' })
  messageType: ChatMessageType;

  @ApiProperty({ description: '消息内容' })
  content: string;

  @ApiPropertyOptional({ description: '接收者角色ID' })
  receiverId?: string;

  @ApiPropertyOptional({ description: '接收者名称' })
  receiverName?: string;

  @ApiPropertyOptional({ description: '额外数据' })
  extraData?: any;

  @ApiProperty({ description: '消息状态' })
  status: string;

  @ApiProperty({ description: '发送时间' })
  sendTime: number;

  @ApiProperty({ description: '读取时间' })
  readTime: number;
}

// 聊天历史响应DTO
export class ChatHistoryResponseDto {
  @ApiProperty({ description: '消息列表', type: [ChatMessageResponseDto] })
  messages: ChatMessageResponseDto[];

  @ApiProperty({ description: '消息总数' })
  total: number;

  @ApiProperty({ description: '频道ID' })
  channelId: string;

  @ApiProperty({ description: '是否还有更多消息' })
  hasMore: boolean;
}

// 频道信息响应DTO
export class ChatChannelResponseDto {
  @ApiProperty({ description: '频道ID' })
  channelId: string;

  @ApiProperty({ description: '频道名称' })
  channelName: string;

  @ApiProperty({ description: '频道类型' })
  channelType: ChatMessageType;

  @ApiProperty({ description: '成员数量' })
  memberCount: number;

  @ApiProperty({ description: '最大成员数' })
  maxMembers: number;

  @ApiProperty({ description: '是否活跃' })
  isActive: boolean;

  @ApiProperty({ description: '最后消息时间' })
  lastMessageTime: number;

  @ApiPropertyOptional({ description: '最后消息内容' })
  lastMessageContent?: string;
}
