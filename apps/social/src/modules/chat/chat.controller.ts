import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ChatService } from './chat.service';
import { SendChatMessageDto, GetChatHistoryDto, GetPrivateChatDto } from '../../common/dto/chat.dto';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class ChatController {
  private readonly logger = new Logger(ChatController.name);

  constructor(private readonly chatService: ChatService) {}

  /**
   * 发送聊天消息
   */
  @MessagePattern('chat.sendMessage')
  async sendMessage(@Payload() payload: { characterId: string; serverId: string; messageData: SendChatMessageDto; injectedContext?: InjectedContext }) {
    this.logger.log(`发送聊天消息: ${payload.characterId}`);

    // 设置发送者信息
    const messageData = {
      ...payload.messageData,
      senderId: payload.characterId,
      serverId: payload.serverId,
    };

    const result = await this.chatService.sendMessage(messageData);
    return {
      code: 0,
      message: '消息发送成功',
      data: result,
    };
  }

  /**
   * 获取聊天历史
   */
  @MessagePattern('chat.getHistory')
  async getChatHistory(@Payload() payload: { query: GetChatHistoryDto; injectedContext?: InjectedContext }) {
    this.logger.log(`获取聊天历史: ${payload.query.channelId}`);
    const result = await this.chatService.getChatHistory(payload.query);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 获取私聊历史
   */
  @MessagePattern('chat.getPrivateHistory')
  async getPrivateHistory(@Payload() payload: { query: GetPrivateChatDto; injectedContext?: InjectedContext }) {
    this.logger.log(`获取私聊历史: ${payload.query.senderId} <-> ${payload.query.receiverId}`);
    const result = await this.chatService.getPrivateChatHistory(payload.query);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 加入聊天频道
   */
  @MessagePattern('chat.joinChannel')
  async joinChannel(@Payload() payload: { characterId: string; channelId: string; serverId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`加入聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.joinChannel(
      payload.characterId,
      payload.channelId,
      payload.serverId
    );
    return {
      code: 0,
      message: '加入频道成功',
      data: result,
    };
  }

  /**
   * 离开聊天频道
   */
  @MessagePattern('chat.leaveChannel')
  async leaveChannel(@Payload() payload: { characterId: string; channelId: string; injectedContext?: InjectedContext }) {
    this.logger.log(`离开聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.leaveChannel(payload.characterId, payload.channelId);
    return {
      code: 0,
      message: '离开频道成功',
      data: result,
    };
  }
}
