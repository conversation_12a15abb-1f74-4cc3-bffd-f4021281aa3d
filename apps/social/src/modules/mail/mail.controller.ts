import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MailService } from './mail.service';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class MailController {
  private readonly logger = new Logger(MailController.name);

  constructor(private readonly mailService: MailService) {}

  /**
   * 发送邮件
   */
  @MessagePattern('mail.send')
  async sendMail(@Payload() payload: { receiverUid: string; senderUid: string; mailId: number; mailType: number; attachList: any[]; specialAttachInfo?: any; param1?: any; param2?: any; param3?: any; param4?: any; injectedContext?: InjectedContext }) {
    this.logger.log('发送邮件');
    const result = await this.mailService.sendMailReward(
      payload.receiverUid,
      payload.senderUid,
      payload.mailId,
      payload.mailType,
      payload.attachList,
      payload.specialAttachInfo,
      payload.param1,
      payload.param2,
      payload.param3,
      payload.param4
    );
    return {
      code: 0,
      message: '邮件发送成功',
      data: result,
    };
  }

  /**
   * 获取邮件列表
   */
  @MessagePattern('mail.getList')
  async getMailList(@Payload() payload: { characterId: string; page?: number; limit?: number; injectedContext?: InjectedContext }) {
    this.logger.log(`获取邮件列表: ${payload.characterId}`);
    const result = await this.mailService.getMailList({
      characterId: payload.characterId,
      page: payload.page,
      limit: payload.limit,
    });
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 读取邮件
   */
  @MessagePattern('mail.read')
  async readMail(@Payload() payload: { characterId: string; mailUid: string; injectedContext?: InjectedContext }) {
    this.logger.log(`读取邮件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.readMail({
      characterId: payload.characterId,
      mailUid: payload.mailUid,
    });
    return {
      code: 0,
      message: '邮件已读',
      data: result,
    };
  }

  /**
   * 删除邮件
   */
  @MessagePattern('mail.delete')
  async deleteMail(@Payload() payload: { characterId: string; mailUid: string; injectedContext?: InjectedContext }) {
    this.logger.log(`删除邮件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.deleteMail(payload.characterId, payload.mailUid);
    return {
      code: 0,
      message: '邮件已删除',
      data: result,
    };
  }

  /**
   * 领取邮件附件
   */
  @MessagePattern('mail.claimAttachment')
  async claimMailAttachment(@Payload() payload: { characterId: string; mailUid: string; injectedContext?: InjectedContext }) {
    this.logger.log(`领取邮件附件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.claimMailAttachment(payload.characterId, payload.mailUid);
    return {
      code: 0,
      message: '附件领取成功',
      data: result,
    };
  }

  /**
   * 批量删除邮件
   */
  @MessagePattern('mail.batchDelete')
  async batchDeleteMails(@Payload() payload: { characterId: string; mailUids: string[]; injectedContext?: InjectedContext }) {
    this.logger.log(`批量删除邮件: ${payload.characterId}, 数量: ${payload.mailUids.length}`);
    const result = await this.mailService.batchDeleteMails(payload.characterId, payload.mailUids);
    return {
      code: 0,
      message: '批量删除成功',
      data: result,
    };
  }

  /**
   * 批量领取附件
   */
  @MessagePattern('mail.batchClaim')
  async batchClaimAttachments(@Payload() payload: { characterId: string; mailUids: string[]; injectedContext?: InjectedContext }) {
    this.logger.log(`批量领取附件: ${payload.characterId}, 数量: ${payload.mailUids.length}`);
    const result = await this.mailService.batchClaimAttachments(payload.characterId, payload.mailUids);
    return {
      code: 0,
      message: '批量领取成功',
      data: result,
    };
  }
}
