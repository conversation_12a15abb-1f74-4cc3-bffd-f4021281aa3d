/**
 * 社交服务健康检查服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { RedisService } from '@libs/redis';

export interface HealthStatus {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  service: string;
  version: string;
  checks: {
    database: {
      status: 'ok' | 'error';
      responseTime?: number;
      error?: string;
    };
    redis: {
      status: 'ok' | 'error';
      responseTime?: number;
      error?: string;
    };
    memory: {
      status: 'ok' | 'warning' | 'error';
      usage: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
      };
      percentage: number;
    };
  };
}

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly serviceName = 'social-service';
  private readonly version = process.env.npm_package_version || '1.0.0';

  constructor(
    @InjectConnection() private readonly mongoConnection: Connection,
    private readonly redisService: RedisService,
  ) {}

  async check(): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      const [databaseCheck, redisCheck, memoryCheck] = await Promise.all([
        this.checkDatabase(),
        this.checkRedis(),
        this.checkMemory(),
      ]);

      const status: HealthStatus = {
        status: this.getOverallStatus([databaseCheck, redisCheck, memoryCheck]),
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: this.serviceName,
        version: this.version,
        checks: {
          database: databaseCheck,
          redis: redisCheck,
          memory: memoryCheck,
        },
      };

      this.logger.debug(`Health check completed in ${Date.now() - startTime}ms`);
      return status;
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        service: this.serviceName,
        version: this.version,
        checks: {
          database: { status: 'error', error: 'Health check failed' },
          redis: { status: 'error', error: 'Health check failed' },
          memory: { 
            status: 'error', 
            usage: { rss: 0, heapTotal: 0, heapUsed: 0, external: 0 },
            percentage: 0
          },
        },
      };
    }
  }

  async readiness(): Promise<{ status: string; ready: boolean }> {
    try {
      const [dbReady, redisReady] = await Promise.all([
        this.isDatabaseReady(),
        this.isRedisReady(),
      ]);

      const ready = dbReady && redisReady;
      return {
        status: ready ? 'ready' : 'not ready',
        ready,
      };
    } catch (error) {
      this.logger.error('Readiness check failed', error);
      return {
        status: 'not ready',
        ready: false,
      };
    }
  }

  async liveness(): Promise<{ status: string; alive: boolean }> {
    try {
      const memoryUsage = process.memoryUsage();
      const alive = memoryUsage.heapUsed > 0;
      
      return {
        status: alive ? 'alive' : 'dead',
        alive,
      };
    } catch (error) {
      this.logger.error('Liveness check failed', error);
      return {
        status: 'dead',
        alive: false,
      };
    }
  }

  private async checkDatabase(): Promise<HealthStatus['checks']['database']> {
    const startTime = Date.now();
    
    try {
      if (this.mongoConnection.readyState !== 1) {
        return {
          status: 'error',
          error: 'Database not connected',
        };
      }

      await this.mongoConnection.db.admin().ping();
      
      return {
        status: 'ok',
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return {
        status: 'error',
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async checkRedis(): Promise<HealthStatus['checks']['redis']> {
    const startTime = Date.now();
    
    try {
      const testKey = `health:${this.serviceName}:${Date.now()}`;
      const testValue = 'ping';
      
      await this.redisService.set(testKey, testValue);
      const result = await this.redisService.get(testKey);
      await this.redisService.del(testKey);
      
      if (result !== testValue) {
        return {
          status: 'error',
          error: 'Redis ping test failed',
        };
      }

      return {
        status: 'ok',
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      this.logger.error('Redis health check failed', error);
      return {
        status: 'error',
        responseTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private checkMemory(): HealthStatus['checks']['memory'] {
    const usage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const percentage = (usage.rss / totalMemory) * 100;

    let status: 'ok' | 'warning' | 'error' = 'ok';
    if (percentage > 90) {
      status = 'error';
    } else if (percentage > 80) {
      status = 'warning';
    }

    return {
      status,
      usage: {
        rss: Math.round(usage.rss / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024),
      },
      percentage: Math.round(percentage * 100) / 100,
    };
  }

  private async isDatabaseReady(): Promise<boolean> {
    try {
      return this.mongoConnection.readyState === 1;
    } catch {
      return false;
    }
  }

  private async isRedisReady(): Promise<boolean> {
    try {
      const testKey = `ready:${this.serviceName}`;
      await this.redisService.set(testKey, '1');
      await this.redisService.del(testKey);
      return true;
    } catch {
      return false;
    }
  }

  private getOverallStatus(checks: Array<{ status: string }>): 'ok' | 'error' {
    return checks.every(check => check.status === 'ok') ? 'ok' : 'error';
  }
}
