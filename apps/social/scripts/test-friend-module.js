/**
 * 好友系统测试模块
 * 基于Hero服务球探系统的成功测试模式
 * 
 * 测试内容：
 * 1. 获取好友列表
 * 2. 搜索用户功能
 * 3. 发送好友请求
 * 4. 处理好友请求（接受/拒绝）
 * 5. 删除好友功能
 * 6. 好友在线状态
 */

const chalk = require('chalk');

class FriendModuleTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
    this.friendData = null;
  }

  /**
   * WebSocket调用封装 - 使用Hero服务的成功模式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取好友列表
   */
  async testGetFriendList() {
    console.log(chalk.yellow('📋 测试获取好友列表...'));

    try {
      const response = await this.callWebSocket('social.friend.getList', {
        characterId: this.testData.characterId,
        serverId: this.testData.serverId
      });

      // 使用标准的响应格式解析
      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取好友列表成功'));
        console.log(chalk.gray(`   好友数量: ${data.friends ? data.friends.length : 0}`));
        
        this.friendData = data;
        this.testResults.push({
          test: '获取好友列表',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 获取好友列表失败: ${errorMsg}`));
        this.testResults.push({
          test: '获取好友列表',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取好友列表异常: ${error.message}`));
      this.testResults.push({
        test: '获取好友列表',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试搜索用户功能
   */
  async testSearchUser() {
    console.log(chalk.yellow('🔍 测试搜索用户功能...'));

    try {
      // 搜索一个测试用户名
      const searchKeyword = 'test';
      const response = await this.callWebSocket('social.friend.search', {
        characterId: this.testData.characterId,
        searchDto: {
          keyword: searchKeyword
        }
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 搜索用户成功'));
        console.log(chalk.gray(`   搜索关键词: ${searchKeyword}`));
        console.log(chalk.gray(`   搜索结果数量: ${data.users ? data.users.length : 0}`));
        
        this.testResults.push({
          test: '搜索用户',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 搜索用户失败: ${errorMsg}`));
        this.testResults.push({
          test: '搜索用户',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 搜索用户异常: ${error.message}`));
      this.testResults.push({
        test: '搜索用户',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试发送好友请求
   */
  async testSendFriendRequest() {
    console.log(chalk.yellow('📤 测试发送好友请求...'));

    try {
      // 使用一个测试目标用户ID
      const targetPlayerId = 'test-target-player-123';
      const response = await this.callWebSocket('social.friend.add', {
        characterId: this.testData.characterId,
        serverId: this.testData.serverId,
        addDto: {
          targetCharacterId: targetPlayerId,
          message: '你好，我想加你为好友！'
        }
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 发送好友请求成功'));
        console.log(chalk.gray(`   目标玩家ID: ${targetPlayerId}`));
        
        this.testResults.push({
          test: '发送好友请求',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 发送好友请求失败: ${errorMsg}`));
        this.testResults.push({
          test: '发送好友请求',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 发送好友请求异常: ${error.message}`));
      this.testResults.push({
        test: '发送好友请求',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试获取好友请求列表
   */
  async testGetFriendRequests() {
    console.log(chalk.yellow('📥 测试获取好友请求列表...'));

    try {
      const response = await this.callWebSocket('social.friend.getApplies', {
        characterId: this.testData.characterId,
        serverId: this.testData.serverId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取好友请求列表成功'));
        console.log(chalk.gray(`   待处理请求数量: ${data.requests ? data.requests.length : 0}`));
        
        this.testResults.push({
          test: '获取好友请求列表',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 获取好友请求列表失败: ${errorMsg}`));
        this.testResults.push({
          test: '获取好友请求列表',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取好友请求列表异常: ${error.message}`));
      this.testResults.push({
        test: '获取好友请求列表',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试处理好友请求（接受）
   */
  async testAcceptFriendRequest() {
    console.log(chalk.yellow('✅ 测试接受好友请求...'));

    try {
      // 使用一个测试申请者ID
      const applicantPlayerId = 'test-applicant-123';
      const response = await this.callWebSocket('social.friend.handleApply', {
        characterId: this.testData.characterId,
        serverId: this.testData.serverId,
        handleDto: {
          applicantCharacterId: applicantPlayerId,
          accept: true
        }
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 接受好友请求成功'));
        console.log(chalk.gray(`   申请者ID: ${applicantPlayerId}`));
        
        this.testResults.push({
          test: '接受好友请求',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 接受好友请求失败: ${errorMsg}`));
        this.testResults.push({
          test: '接受好友请求',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 接受好友请求异常: ${error.message}`));
      this.testResults.push({
        test: '接受好友请求',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试删除好友
   */
  async testRemoveFriend() {
    console.log(chalk.yellow('🗑️ 测试删除好友...'));

    try {
      // 使用一个测试好友ID
      const friendPlayerId = 'test-friend-123';
      const response = await this.callWebSocket('social.friend.remove', {
        characterId: this.testData.characterId,
        serverId: this.testData.serverId,
        removeDto: {
          friendCharacterId: friendPlayerId
        }
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 删除好友成功'));
        console.log(chalk.gray(`   好友玩家ID: ${friendPlayerId}`));
        
        this.testResults.push({
          test: '删除好友',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 删除好友失败: ${errorMsg}`));
        this.testResults.push({
          test: '删除好友',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 删除好友异常: ${error.message}`));
      this.testResults.push({
        test: '删除好友',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 运行所有测试
   */
  async runTests() {
    console.log(chalk.cyan('🔧 开始好友系统测试'));

    // 按顺序执行测试
    await this.testGetFriendList();
    await this.testSearchUser();
    await this.testSendFriendRequest();
    await this.testGetFriendRequests();
    await this.testAcceptFriendRequest();
    await this.testRemoveFriend();

    // 返回测试结果
    return this.testResults;
  }
}

module.exports = FriendModuleTester;
