import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Character相关组件
import { CharacterController } from './character.controller';
import { CharacterService } from './character.service';
import { Character, CharacterSchema } from '@character/common/schemas/character.schema';
import { CharacterRepository } from '@character/common/repositories/character.repository';

// 导入Formation模块（使用forwardRef解决循环依赖）
import { FormationModule } from '../formation/formation.module';

/**
 * 角色模块
 */
@Module({
  imports: [
    // 注册Character Schema
    MongooseModule.forFeature([
      { name: Character.name, schema: CharacterSchema },
    ]),
    // 使用forwardRef解决循环依赖问题
    forwardRef(() => FormationModule),
  ],

  controllers: [CharacterController],

  providers: [
    CharacterService,
    CharacterRepository,
  ],

  exports: [
    CharacterService,
    CharacterRepository,
  ],
})
export class CharacterModule {}
