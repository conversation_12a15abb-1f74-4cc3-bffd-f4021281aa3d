import {Injectable, Logger, NotFoundException, BadRequestException, forwardRef, Inject} from '@nestjs/common';
import { Character, CharacterDocument } from '@character/common/schemas/character.schema';
import { CharacterRepository } from '@character/common/repositories/character.repository';
import { FormationService } from '@character/modules/formation/formation.service';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  LoginCharacterDto,
  CurrencyOperationDto,
  BuyEnergyDto,
  LevelUpDto,
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto,
  GetCharacterListDto
} from '@character/common/dto/character.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GAME_CONSTANTS } from '@libs/game-constants';
import { GameConfigFacade } from '@libs/game-config';
import {MicroserviceClientService} from "@libs/service-mesh";

import { XR<PERSON>ult, XResultUtils } from '@libs/common/types/result.type';

@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    private readonly characterRepository: CharacterRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
  ) {}

  /**
   * 创建新角色
   */
  async createCharacter(createDto: CreateCharacterDto): Promise<XResult<CharacterDocument>> {

    try {
      // 检查角色名是否已存在
      const nameExists = await this.characterRepository.existsByName(
        createDto.name,
        createDto.serverId
      );

      if (nameExists) {
        throw new BadRequestException({
          code: ErrorCode.CHARACTER_NAME_TAKEN,
          message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],
        });
      }

      // 如果没有提供characterId，抛出错误
      if (!createDto.characterId) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: '角色ID必须由Auth服务提供',
        });
      }

      // 创建角色数据
      const characterData = {
        characterId: createDto.characterId, // 现在必须由外部提供
        userId: createDto.userId,
        serverId: createDto.serverId,
        openId: createDto.openId,
        name: createDto.name,
        avatar: createDto.avatar || '',
        faceIcon: createDto.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
        cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
        gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
        energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
        loginInfo: {
          createTime: Date.now(),
          loginTime: Date.now(),
        },
      };

      const savedCharacter = await this.characterRepository.create(characterData);

      this.logger.log(`角色创建成功: ${createDto.characterId}, 用户: ${createDto.userId}`);
      return savedCharacter;
    } catch (error) {
      this.logger.error('创建角色失败', error);
      throw error;
    }
  }

  /**
   * 从Auth服务初始化角色数据（新架构）
   * 这是Auth服务创建角色后调用的初始化方法
   */
  async initializeFromAuth(initData: {
    characterId: string;
    userId: string;
    serverId: string;
    characterName: string;
    initialData?: any;
  }): Promise<XResult<CharacterDocument>> {
    this.logger.log(`🎮 从Auth服务初始化角色数据: ${initData.characterId}`);

    try {
      // 检查角色是否已经存在
      const existingCharacter = await this.characterRepository.findById(initData.characterId);
      if (existingCharacter) {
        this.logger.log(`角色已存在，跳过初始化: ${initData.characterId}`);
        return existingCharacter;
      }

      // 创建角色游戏数据
      const characterData = {
        characterId: initData.characterId, // 使用Auth服务提供的ID
        userId: initData.userId,
        serverId: initData.serverId,
        openId: initData.initialData?.openId || 'default_openid',
        name: initData.characterName,
        avatar: initData.initialData?.avatar || 'default_avatar.png',
        faceIcon: initData.initialData?.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
        cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
        gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
        energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
        loginInfo: {
          createTime: Date.now(),
          loginTime: Date.now(),
        },
      };

      const savedCharacter = await this.characterRepository.create(characterData);

      this.logger.log(`✅ 角色游戏数据初始化成功: ${initData.characterId}`);
      return savedCharacter;

    } catch (error) {
      this.logger.error(`❌ 角色游戏数据初始化失败: ${initData.characterId}`, error);
      throw error;
    }
  }

  /**
   * 角色登录（增强版 - 支持数据初始化检查）
   */
  async loginCharacter(loginDto: LoginCharacterDto): Promise<XResult<LoginResultDto>> {
    try {
      let character = await this.characterRepository.findById(loginDto.characterId);

      // 如果角色在Character服务中不存在，但Auth服务已验证角色存在，则需要初始化
      if (!character) {
        this.logger.log(`🔄 角色在Character服务中不存在，执行初始化: ${loginDto.characterId}`);

        // 从Auth服务获取角色基础信息并初始化
        // 注意：这里应该从Auth服务获取完整的角色信息，但为了简化实现，使用默认值
        character = await this.initializeFromAuth({
          characterId: loginDto.characterId,
          userId: loginDto.userId,
          serverId: loginDto.serverId,
          characterName: `Player_${loginDto.characterId.slice(-8)}`, // 使用角色ID后8位作为默认名称
          initialData: {
            level: 1,
            avatar: 'default_avatar.png',
            faceIcon: 1,
          },
        });
      }

      // 验证角色归属
      if (character.userId !== loginDto.userId || character.serverId !== loginDto.serverId) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      // 更新登录信息
      const updateData = {
        'loginInfo.loginTime': Date.now(),
        'loginInfo.ip': loginDto.ip || '',
        'loginInfo.frontendId': loginDto.frontendId,
        'loginInfo.sessionId': loginDto.sessionId,
      };

      // 更新活跃天数
      this.updateActiveDay(character, updateData);

      const updatedCharacter = await this.characterRepository.update(
        character.characterId,
        updateData
      );

      const result: LoginResultDto = {
        success: true,
        character: this.toCharacterInfoDto(updatedCharacter),
        isNewCharacter: updatedCharacter.gameProgress.isNewer,
        initData: {
          needCreateTeam: updatedCharacter.gameProgress.createRoleStep < 2,
          needGuide: updatedCharacter.gameProgress.isNewer,
          hasInitHeroes: updatedCharacter.gameProgress.createRoleStep >= 1,
        },
      };

      this.logger.log(`角色登录成功: ${character.characterId}`);
      return result;
    } catch (error) {
      this.logger.error('角色登录失败', error);
      throw error;
    }
  }

  /**
   * 角色登出
   */
  async logoutCharacter(characterId: string): Promise<XResult<void>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (character) {
        await this.characterRepository.update(characterId, {
          'loginInfo.leaveTime': Date.now(),
          'loginInfo.frontendId': null,
          'loginInfo.sessionId': null,
        });
      }

      this.logger.log(`角色登出: ${characterId}`);
    } catch (error) {
      this.logger.error('角色登出失败', error);
      throw error;
    }
  }

  /**
   * 获取角色信息
   */
  async getCharacterInfo(characterId: string): Promise<XResult<CharacterInfoDto>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      return this.toCharacterInfoDto(character);
    } catch (error) {
      this.logger.error(`获取角色信息失败：${characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新角色信息
   */
  async updateCharacter(characterId: string, updateDto: UpdateCharacterDto): Promise<XResult<CharacterDocument>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      // 如果更新名称，检查是否重复
      if (updateDto.name && updateDto.name !== character.name) {
        const nameExists = await this.characterRepository.existsByName(
          updateDto.name,
          character.serverId,
          characterId
        );

        if (nameExists) {
          throw new BadRequestException({
            code: ErrorCode.CHARACTER_NAME_TAKEN,
            message: ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN],
          });
        }
      }

      const updatedCharacter = await this.characterRepository.update(characterId, updateDto);

      this.logger.log(`角色信息更新成功: ${characterId}`);
      return updatedCharacter;
    } catch (error) {
      this.logger.error('更新角色信息失败', error);
      throw error;
    }
  }

  /**
   * 生成角色ID - 已删除，现在由Auth服务统一生成
   * 保留注释作为历史记录
   */
  // private generateCharacterId(): string {
  //   return `char_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  // }

  /**
   * 获取角色列表
   */
  async getCharacterList(query: GetCharacterListDto): Promise<XResult<any>> {
    try {
      const filter: any = {};
      if (query.userId) {
        filter.userId = query.userId;
      }
      if (query.serverId) {
        filter.serverId = query.serverId;
      }

      const result = await this.characterRepository.findWithPagination(
        filter,
        query.page || 1,
        query.limit || 20
      );

      return {
        list: result.data.map(char => this.toCharacterInfoDto(char)),
        total: result.total,
        page: result.page,
        limit: result.limit,
        pages: result.pages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    } catch (error) {
      this.logger.error('获取角色列表失败', error);
      throw error;
    }
  }

  /**
   * 增加货币
   */
  async addCurrency(characterId: string, currencyDto: CurrencyOperationDto): Promise<XResult<any>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const updateData: any = {};
      updateData[currencyDto.currencyType] = character[currencyDto.currencyType] + currencyDto.amount;

      const updatedCharacter = await this.characterRepository.update(characterId, updateData);

      return {
        characterId,
        currencyType: currencyDto.currencyType,
        amount: currencyDto.amount,
        newBalance: updatedCharacter[currencyDto.currencyType],
        reason: currencyDto.reason,
      };
    } catch (error) {
      this.logger.error('增加货币失败', error);
      throw error;
    }
  }

  /**
   * 扣除货币
   */
  async subtractCurrency(characterId: string, currencyDto: CurrencyOperationDto): Promise<XResult<any>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      // 检查余额是否足够
      if (character[currencyDto.currencyType] < currencyDto.amount) {
        throw new BadRequestException({
          code: ErrorCode.INSUFFICIENT_CURRENCY,
          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],
        });
      }

      const updateData: any = {};
      updateData[currencyDto.currencyType] = character[currencyDto.currencyType] - currencyDto.amount;

      const updatedCharacter = await this.characterRepository.update(characterId, updateData);

      return {
        characterId,
        currencyType: currencyDto.currencyType,
        amount: currencyDto.amount,
        newBalance: updatedCharacter[currencyDto.currencyType],
        reason: currencyDto.reason,
      };
    } catch (error) {
      this.logger.error('扣除货币失败', error);
      throw error;
    }
  }

  /**
   * 购买体力
   */
  async buyEnergy(characterId: string, buyDto: BuyEnergyDto): Promise<XResult<BuyEnergyResultDto>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const count = buyDto.count || 1;
      const goldCost = this.calculateEnergyCost(character.energyInfo.buyEnergyCount, count);
      const energyGained = 50 * count; // 每次购买50体力

      // 检查金币是否足够
      if (character.gold < goldCost) {
        throw new BadRequestException({
          code: ErrorCode.INSUFFICIENT_CURRENCY,
          message: ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY],
        });
      }

      // 更新角色数据
      const updateData = {
        gold: character.gold - goldCost,
        energy: Math.min(character.energy + energyGained, GAME_CONSTANTS.CHARACTER.MAX_ENERGY),
        'energyInfo.buyEnergyCount': character.energyInfo.buyEnergyCount + count,
        'energyInfo.buyTime': Date.now(),
      };

      await this.characterRepository.update(characterId, updateData);

      return {
        success: true,
        goldCost,
        energyGained,
        currentEnergy: updateData.energy,
        todayBuyCount: updateData['energyInfo.buyEnergyCount'],
        nextBuyCost: this.calculateEnergyCost(updateData['energyInfo.buyEnergyCount'], 1),
      };
    } catch (error) {
      this.logger.error('购买体力失败', error);
      throw error;
    }
  }

  /**
   * 角色升级
   */
  async levelUp(characterId: string, levelUpDto: LevelUpDto): Promise<XResult<LevelUpResultDto>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const oldLevel = character.level;
      const targetLevel = levelUpDto.targetLevel || oldLevel + 1;

      if (targetLevel <= oldLevel || targetLevel > GAME_CONSTANTS.CHARACTER.MAX_LEVEL) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: ErrorMessages[ErrorCode.INVALID_PARAMETER],
        });
      }

      // 计算升级奖励
      const rewards = this.calculateLevelUpRewards(oldLevel, targetLevel);

      // 更新角色等级和奖励
      const updateData: any = {
        level: targetLevel,
      };

      rewards.forEach(reward => {
        if (updateData[reward.type]) {
          updateData[reward.type] += reward.amount;
        } else {
          updateData[reward.type] = character[reward.type] + reward.amount;
        }
      });

      await this.characterRepository.update(characterId, updateData);

      return {
        leveledUp: true,
        oldLevel,
        newLevel: targetLevel,
        rewards,
      };
    } catch (error) {
      this.logger.error('角色升级失败', error);
      throw error;
    }
  }

  /**
   * 完成创角步骤
   */
  async completeCreateStep(characterId: string, step: number): Promise<XResult<any>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const updateData = {
        'gameProgress.createRoleStep': step,
      };

      await this.characterRepository.update(characterId, updateData);

      return {
        characterId,
        completedStep: step,
        nextStep: step + 1,
      };
    } catch (error) {
      this.logger.error('完成创角步骤失败', error);
      throw error;
    }
  }

  /**
   * 完成新手引导
   */
  async finishGuide(characterId: string): Promise<XResult<any>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const rewards = [
        { type: 'cash', amount: 5000 },
        { type: 'gold', amount: 100 },
      ];

      const updateData = {
        'gameProgress.isNewer': false,
        cash: character.cash + 5000,
        gold: character.gold + 100,
      };

      await this.characterRepository.update(characterId, updateData);

      return {
        characterId,
        isNewer: false,
        rewards,
      };
    } catch (error) {
      this.logger.error('完成新手引导失败', error);
      throw error;
    }
  }

  /**
   * 更新活跃天数
   */
  private updateActiveDay(character: CharacterDocument, updateData: any): void {
    const now = Date.now();
    const lastLogin = character.loginInfo.loginTime;
    const oneDayMs = 24 * 60 * 60 * 1000;

    if (now - lastLogin > oneDayMs) {
      updateData['gameProgress.activeDay'] = character.gameProgress.activeDay + 1;

      // 检查是否连续登录
      if (now - lastLogin > 2 * oneDayMs) {
        updateData['gameProgress.recentActiveDay'] = 1;
      } else {
        updateData['gameProgress.recentActiveDay'] = character.gameProgress.recentActiveDay + 1;
      }
    }
  }

  /**
   * 转换为DTO
   */
  private toCharacterInfoDto(character: Character): CharacterInfoDto {
    return {
      characterId: character.characterId,
      userId: character.userId,
      serverId: character.serverId,
      openId: character.openId,
      name: character.name,
      avatar: character.avatar,
      faceIcon: character.faceIcon,
      faceUrl: character.faceUrl,
      level: character.level,
      cash: character.cash,
      gold: character.gold,
      energy: character.energy,
      fame: character.fame,
      allFame: character.allFame,
      trophy: character.trophy,
      worldCoin: character.worldCoin,
      chip: character.chip,
      integral: character.integral,
      vip: character.vipInfo.vip,
      vipExp: character.vipInfo.vipExp,
      fieldLevel: character.fieldLevel,
      league: character.league,
      isOnline: character.isOnline,
      isNewer: character.gameProgress.isNewer,
      createRoleStep: character.gameProgress.createRoleStep,
      activeDay: character.gameProgress.activeDay,
      createTime: character.loginInfo.createTime,
      lastLoginTime: character.loginInfo.loginTime,
      beliefId: character.beliefInfo.beliefId,
      honor: character.beliefInfo.honor,
    };
  }



  /**
   * 计算体力购买费用
   */
  private calculateEnergyCost(currentBuyCount: number, buyCount: number): number {
    let totalCost = 0;
    for (let i = 0; i < buyCount; i++) {
      const cost = Math.min(100 + (currentBuyCount + i) * 50, 1000); // 最高1000金币
      totalCost += cost;
    }
    return totalCost;
  }

  /**
   * 计算升级奖励
   */
  private calculateLevelUpRewards(oldLevel: number, newLevel: number): Array<{ type: string; amount: number }> {
    const rewards = [];
    const levelDiff = newLevel - oldLevel;

    // 每级奖励1000欧元和10体力
    rewards.push({ type: 'cash', amount: levelDiff * 1000 });
    rewards.push({ type: 'energy', amount: levelDiff * 10 });

    // 每5级额外奖励100金币
    const fiveLevelRewards = Math.floor(newLevel / 5) - Math.floor(oldLevel / 5);
    if (fiveLevelRewards > 0) {
      rewards.push({ type: 'gold', amount: fiveLevelRewards * 100 });
    }

    return rewards;
  }

  /**
   * 设置角色信仰
   */
  async setCharacterBelief(characterId: string, beliefId: number): Promise<XResult<CharacterDocument>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      character.beliefId = beliefId;
      const updatedCharacter = await character.save();

      this.logger.log(`角色信仰设置成功: ${characterId}, 信仰ID: ${beliefId}`);
      return updatedCharacter;
    } catch (error) {
      this.logger.error('设置角色信仰失败', error);
      throw error;
    }
  }

  /**
   * 使用兑换码
   */
  async useRedeemCode(characterId: string, group: string, codeId: string): Promise<XResult<any>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      // 检查兑换码是否已使用
      if (character.deemCodeList[group] === codeId) {
        throw new BadRequestException({
          code: ErrorCode.REDEEM_CODE_ALREADY_USED,
          message: ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED],
        });
      }

      // 记录兑换码使用
      character.deemCodeList[group] = codeId;

      // 根据兑换码类型给予奖励
      // 基于old项目的兑换码奖励逻辑实现
      const rewards = await this.processRedeemCodeRewards(characterId, group, codeId);

      const updatedCharacter = await character.save();

      this.logger.log(`兑换码使用成功: ${characterId}, 组: ${group}, 码: ${codeId}`);
      return {
        success: true,
        rewards,
      };
    } catch (error) {
      this.logger.error('使用兑换码失败', error);
      throw error;
    }
  }

  /**
   * 更新持续buff
   */
  async updateContinuedBuff(characterId: string, buffDuration: number): Promise<XResult<CharacterDocument>> {
    try {
      const character = await this.characterRepository.findById(characterId);
      if (!character) {
        throw new NotFoundException({
          code: ErrorCode.CHARACTER_NOT_FOUND,
          message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
        });
      }

      const now = Date.now();
      character.continuedBuffStartTime = now;
      character.continuedBuffEndTime = now + (buffDuration * 1000);
      character.lastBuffUpdateTime = now;

      const updatedCharacter = await character.save();

      this.logger.log(`持续buff更新成功: ${characterId}, 持续时间: ${buffDuration}秒`);
      return updatedCharacter;
    } catch (error) {
      this.logger.error('更新持续buff失败', error);
      throw error;
    }
  }

  // ==================== old项目核心方法 ====================

  /**
   * 获取个人信息
   * 严格基于old项目: getPersonInfo
   */
  async getPersonInfo(characterId: string): Promise<XResult<any>> {
    this.logger.log(`获取个人信息: ${characterId}`);

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, personInfo: {}, totalValue: 0 };
    }

    // 基于old项目的getPersonInfo返回结构
    const personInfo = {
      name: character.name,
      level: character.level,
      fame: character.fame,
      energy: character.energy,
      formationAct: character.formationAct || 0,
      league: character.league,
      faceIcon: character.faceIcon,
      faceUrl: character.faceUrl,
      vip: character.vipInfo?.vip || 0,
      vipExp: character.vipInfo?.vipExp || 0,
      beliefId: character.beliefId,
    };

    // 计算队伍总价值，调用Hero服务
    const totalValue = await this.calcTeamValue(characterId);

    return {
      code: 0,
      personInfo,
      totalValue
    };
  }

  /**
   * 创建角色
   * 严格基于old项目: createRole
   */
  async createRole(characterId: string, msg: { qualified: number; name: string; faceIcon: number }): Promise<XResult<any>> {
    this.logger.log(`创建角色: ${characterId}, 参数: ${JSON.stringify(msg)}`);

    const { qualified, name, faceIcon } = msg;

    if (!qualified || !name || !faceIcon) {
      this.logger.error('createRole参数错误', characterId, msg);
      return { code: -1, heroList: [], oneTeam: {} };
    }

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, heroList: [], oneTeam: {} };
    }

    // 更新角色信息
    character.name = name;
    character.faceIcon = faceIcon;
    character.qualified = qualified;
    character.gameProgress.isNewer = false; // 完成新手

    await character.save();

    // 基于old项目createInitBallerAndTeam逻辑创建初始球员和阵容
    const initialHeroes = await this.createInitialHeroes(characterId, qualified);

    // 创建初始阵容
    const initialFormation = await this.createInitialFormation(characterId, initialHeroes);

    // 触发新手任务系统
    await this.triggerNewbieTasks(characterId);

    this.logger.log(`角色创建成功: ${characterId}, 名称: ${name}`);

    return {
      code: 0,
      heroList: initialHeroes || [], // 创建的初始球员列表
      oneTeam: initialFormation || {},  // 创建的初始阵容
    };
  }

  /**
   * 修改玩家名称
   * 严格基于old项目: modifyPlayerName
   */
  async modifyCharacterName(characterId: string, msg: { name: string }): Promise<XResult<CharacterDocument>> {
    this.logger.log(`修改玩家名称: ${characterId}, 新名称: ${msg.name}`);

    const { name } = msg;
    if (!name || name.length === 0) {
      return { code: -1, name: '' };
    }

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, name: '' };
    }

    // 检查名称是否已被使用
    const nameExists = await this.characterRepository.existsByName(name, character.serverId);
    if (nameExists) {
      return { code: -2, name: '' }; // 名称已存在
    }

    // 更新名称
    character.name = name;
    await character.save();

    return { code: 0, name };
  }

  /**
   * 添加资源
   * 严格基于old项目: addResource
   */
  async addResource(characterId: string, resourceType: string, amount: number, reason?: string): Promise<XResult<any>> {
    this.logger.log(`添加资源: ${characterId}, 类型: ${resourceType}, 数量: ${amount}, 原因: ${reason}`);

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, message: '角色不存在' };
    }

    // 基于old项目的资源类型
    switch (resourceType) {
      case 'cash':
        character.cash = Math.max(0, character.cash + amount);
        break;
      case 'gold':
        character.gold = Math.max(0, character.gold + amount);
        break;
      case 'energy':
        character.energy = Math.max(0, Math.min(100, character.energy + amount));
        break;
      case 'fame':
        character.fame = Math.max(0, character.fame + amount);
        character.allFame = Math.max(0, character.allFame + Math.max(0, amount));
        break;
      case 'trophy':
        character.trophy = Math.max(0, character.trophy + amount);
        break;
      case 'worldCoin':
        character.worldCoin = Math.max(0, character.worldCoin + amount);
        break;
      case 'chip':
        character.chip = Math.max(0, character.chip + amount);
        break;
      case 'integral':
        character.integral = Math.max(0, character.integral + amount);
        break;
      default:
        return { code: -2, message: '未知资源类型' };
    }

    await character.save();

    return {
      code: 0,
      message: '资源添加成功',
      newValue: character[resourceType],
    };
  }

  /**
   * 获取体力奖励
   * 严格基于old项目: getEnergyReward
   */
  async getEnergyReward(characterId: string, type: number): Promise<XResult<any>> {
    this.logger.log(`获取体力奖励: ${characterId}, 类型: ${type}`);

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, energy: 0 };
    }

    const now = Date.now();

    // 基于old项目的体力奖励逻辑
    switch (type) {
      case 1: // 第一次免费领取
        if (character.energyInfo.firstFree > 0) {
          return { code: -2, energy: character.energy }; // 已领取
        }

        // 检查时间限制（12:00-14:00）
        const hour = new Date().getHours();
        if (hour < 12 || hour >= 14) {
          return { code: -3, energy: character.energy }; // 时间不对
        }

        character.energyInfo.firstFree = 1;
        character.energy = Math.min(100, character.energy + 20);
        break;

      case 2: // 第二次免费领取
        if (character.energyInfo.secondFree > 0) {
          return { code: -2, energy: character.energy }; // 已领取
        }

        // 检查时间限制（18:00-20:00）
        const hour2 = new Date().getHours();
        if (hour2 < 18 || hour2 >= 20) {
          return { code: -3, energy: character.energy }; // 时间不对
        }

        character.energyInfo.secondFree = 1;
        character.energy = Math.min(100, character.energy + 20);
        break;

      default:
        return { code: -4, energy: character.energy }; // 无效类型
    }

    await character.save();

    return { code: 0, energy: character.energy };
  }

  /**
   * 消耗金币任务
   * 严格基于old项目: costCashTask
   */
  async costCashTask(characterId: string, amount: number, reason?: string): Promise<XResult<any>> {
    this.logger.log(`消耗金币: ${characterId}, 数量: ${amount}, 原因: ${reason}`);

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, message: '角色不存在' };
    }

    if (character.cash < amount) {
      return { code: -2, message: '金币不足' }; // CASH_FALL
    }

    character.cash -= amount;
    await character.save();

    return {
      code: 0,
      message: '金币消耗成功',
      remainingCash: character.cash,
    };
  }

  /**
   * 检查资源是否足够
   * 基于old项目: checkResourceIsEnough
   */
  async checkResourceIsEnough(characterId: string, resourceType: string, amount: number): Promise<XResult<boolean>> {
    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return false;
    }

    switch (resourceType) {
      case 'cash':
        return character.cash >= amount;
      case 'gold':
        return character.gold >= amount;
      case 'energy':
        return character.energy >= amount;
      case 'worldCoin':
        return character.worldCoin >= amount;
      case 'chip':
        return character.chip >= amount;
      case 'integral':
        return character.integral >= amount;
      default:
        return false;
    }
  }

  /**
   * 扣除资源
   * 基于old项目: deductResource
   */
  async deductResource(characterId: string, resourceType: string, amount: number, reason?: string): Promise<XResult<any>> {
    this.logger.log(`扣除资源: ${characterId}, 类型: ${resourceType}, 数量: ${amount}, 原因: ${reason}`);

    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, message: '角色不存在' };
    }

    // 检查资源是否足够
    const isEnough = await this.checkResourceIsEnough(characterId, resourceType, amount);
    if (!isEnough) {
      return { code: -2, message: '资源不足' };
    }

    // 扣除资源
    switch (resourceType) {
      case 'cash':
        character.cash -= amount;
        break;
      case 'gold':
        character.gold -= amount;
        break;
      case 'energy':
        character.energy -= amount;
        break;
      case 'worldCoin':
        character.worldCoin -= amount;
        break;
      case 'chip':
        character.chip -= amount;
        break;
      case 'integral':
        character.integral -= amount;
        break;
      default:
        return { code: -3, message: '未知资源类型' };
    }

    await character.save();

    return {
      code: 0,
      message: '资源扣除成功',
      newValue: character[resourceType],
    };
  }



  // ==================== 微服务通信参考代码实现 ====================
  // 以下方法提供完整的微服务通信参考代码，包含详细的使用说明
  // 开启方法：将对应的TODO注释取消，调用这些方法

  /**
   * 计算队伍总价值 - 微服务通信参考实现
   * 基于old项目: calcTeamValue方法
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释: const totalValue = await this.calcTeamValue(characterId);
   * 3. 确保MicroserviceKitModule已正确配置Hero服务连接
   */
  private async calcTeamValue(characterId: string): Promise<XResult<number>> {
    this.logger.log(`计算队伍总价值: ${characterId}`);

    try {
      // TODO 调用Hero服务获取角色所有球员
      // 参考代码：完整的微服务调用实现
      /*
      const heroesResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.getCharacterHeroes',
        { characterId }
      );

      if (heroesResult.code !== 0) {
        this.logger.error('获取角色球员失败', heroesResult);
        return 0;
      }

      const heroes = heroesResult.data || [];
      let totalValue = 0;

      // 累计所有球员的市场价值
      for (const hero of heroes) {
        totalValue += hero.marketValue || 0;
      }

      this.logger.log(`队伍总价值计算完成: ${characterId}, 价值: ${totalValue}`);
      return totalValue;
      */

      // 暂时返回模拟值
      return 1000000; // 100万
    } catch (error) {
      this.logger.error('计算队伍总价值失败', error);
      return 0;
    }
  }

  /**
   * 创建初始球员
   * 基于old项目: createInitBallerAndTeam中的球员创建逻辑
   *
   * 实现逻辑：
   * 1. 根据CreateBaller配置表获取初始球员配置
   * 2. 调用Hero服务创建每个初始球员
   * 3. 设置新手球员标记(IsNewer = 1)
   * 4. 返回创建的球员列表供阵容使用
   */
  private async createInitialHeroes(characterId: string, qualified: number): Promise<XResult<any[]>> {
    this.logger.log(`创建初始球员: ${characterId}, 资质: ${qualified}`);

    try {
      // 1. 获取CreateBaller配置表
      const createBallerConfigs = await this.gameConfig.createBaller?.getAll() || [];
      if (createBallerConfigs.length === 0) {
        this.logger.error('CreateBaller配置表为空');
        return [];
      }

      const createdHeroes = [];
      let heroCount = 0;

      // 2. 基于old项目逻辑：只创建Mode为INIT_BALLER的球员
      for (const config of createBallerConfigs) {
        if (config.mode === 1) { // commonEnum.CREATE_INIT_BALLER_MODE.INIT_BALLER = 1
          const heroCreateResult = await this.createSingleInitialHero(
            characterId,
            config.ballerId,
            qualified
          );

          if (heroCreateResult.success) {
            createdHeroes.push(heroCreateResult.hero);
            heroCount++;
            this.logger.log(`初始球员创建成功: ${heroCreateResult.hero.heroId}, ResId: ${config.ballerId}`);
          } else {
            this.logger.error(`初始球员创建失败: ResId=${config.ballerId}`, heroCreateResult.error);
          }
        }
      }

      // 3. 检查创建的球员数量（基于old项目：至少需要11个球员）
      if (heroCount < 11) {
        this.logger.error(`初始球员数量不足: ${heroCount}/11, 配置表可能有误`);
        // 不抛出异常，允许继续创建角色，但记录错误
      }

      this.logger.log(`初始球员创建完成: ${characterId}, 成功创建: ${heroCount}个`);
      return createdHeroes;
    } catch (error) {
      this.logger.error('创建初始球员失败', error);
      return [];
    }
  }

  /**
   * 创建单个初始球员
   * 基于old项目: addHeroNewer方法
   */
  private async createSingleInitialHero(characterId: string, resId: number, qualified: number): Promise<XResult<any>> {
    try {
      // TODO: 调用Hero服务创建球员
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.createHero',
      //   {
      //     characterId,
      //     resId,
      //     source: 'initial_creation',
      //     isNewer: 1, // 新手球员标记
      //     qualified
      //   }
      // );

      // if (result.code === 0) {
      //   return {
      //     success: true,
      //     hero: result.data
      //   };
      // } else {
      //   return {
      //     success: false,
      //     error: result.message
      //   };
      // }

      // 暂时返回模拟数据，避免编译错误
      return {
        success: true,
        hero: {
          heroId: `hero_${Date.now()}_${Math.random()}`,
          resId,
          characterId,
          isNewer: 1,
          level: 1,
          position: this.getPositionByResId(resId),
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建初始阵容
   * 基于old项目: createInitBallerAndTeam中的阵容创建逻辑
   *
   * 实现逻辑：
   * 1. 根据CreateTeamFormation配置表获取阵型配置
   * 2. 调用Formation服务创建阵容
   * 3. 将球员按位置添加到阵容中
   * 4. 设置为当前主力阵容
   */
  private async createInitialFormation(characterId: string, heroes: any[]): Promise<XResult<any>> {
    this.logger.log(`创建初始阵容: ${characterId}, 球员数: ${heroes.length}`);

    try {
      // 1. 获取CreateTeamFormation配置表
      const teamFormationConfigs = await this.gameConfig.createTeamFormation?.getAll() || [];
      if (teamFormationConfigs.length === 0) {
        this.logger.error('CreateTeamFormation配置表为空');
        return {};
      }

      // 2. 使用第一个配置创建初始阵容
      const config = teamFormationConfigs[0];
      const formationResult = await this.createFormationWithConfig(characterId, config, heroes);

      if (formationResult.success) {
        // 3. 设置为当前主力阵容
        await this.setAsMainFormation(characterId, formationResult.formationId);

        // 4. 初始化战术数据
        await this.initializeTactics(characterId, formationResult.formationId);

        this.logger.log(`初始阵容创建成功: ${formationResult.formationId}`);
        return formationResult.formation;
      } else {
        this.logger.error('初始阵容创建失败', formationResult.error);
        return {};
      }
    } catch (error) {
      this.logger.error('创建初始阵容失败', error);
      return {};
    }
  }

  /**
   * 根据配置创建阵容
   * 基于old项目: addTeamFormation + addTeamFormationHero逻辑
   */
  private async createFormationWithConfig(characterId: string, config: any, heroes: any[]): Promise<XResult<any>> {
    try {
      this.logger.log(`根据配置创建阵容: ${characterId}, 配置ID: ${config.id}`);

      // 1. 直接调用FormationService创建阵容
      const formation = await this.formationService.createFormation(
        characterId,
        config.formationId, // 使用配置表中的FormationId
        1 // FormationType.COMMON
      );

      const formationUid = formation.uid;

      // 2. 根据配置表的位置和球员ID添加球员到阵容（基于old项目addTeamFormationHero）
      const positions = config.position; // 位置数组：["GK", "DC", "DC", "DL", "DR", "MC", "MC", "ML", "MR", "ST", "ST"]
      const heroIds = config.heroId;     // 球员ID数组：[1183, 736, 803, 672, 1140, 268, 918, 555, 738, 1040, 1226]

      // 3. 为每个位置添加对应的球员（使用现有的formation.addHeroToPosition）
      for (let i = 0; i < positions.length && i < heroIds.length && i < heroes.length; i++) {
        const position = positions[i];
        const configHeroId = heroIds[i];

        // 从创建的球员中找到对应配置ID的球员
        const hero = heroes.find(h => h.resId === configHeroId);
        if (!hero) {
          this.logger.warn(`未找到配置球员: ${configHeroId}, 跳过位置: ${position}`);
          continue;
        }

        // 直接调用FormationService添加球员到阵容
        const addHeroResult = await this.formationService.addHeroToPosition(
          characterId,
          formationUid,
          position,
          0, // 插入到位置的第一个位置
          hero.uid
        );

        if (!addHeroResult) {
          this.logger.warn(`添加球员到阵容失败: ${hero.uid} -> ${position}`);
        } else {
          this.logger.debug(`球员添加成功: ${hero.name} (${hero.resId}) -> ${position}`);
        }
      }

      this.logger.log(`阵容创建完成: ${formationUid}`);
      return {
        success: true,
        formationId: formationUid,
        formation: formation
      };
    } catch (error) {
      this.logger.error('根据配置创建阵容失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 设置为主力阵容
   * 基于old项目: setCurrTeamFormationId逻辑
   */
  private async setAsMainFormation(characterId: string, formationId: string): Promise<XResult<void>> {
    try {
      // 直接调用FormationService设置主力阵容
      const result = await this.formationService.setActiveFormation(characterId, formationId);

      if (result === 0) {
        this.logger.log(`设置主力阵容成功: ${characterId}, 阵容ID: ${formationId}`);
      } else {
        this.logger.error(`设置主力阵容失败: 错误码 ${result}`);
      }
    } catch (error) {
      this.logger.error('设置主力阵容失败', error);
    }
  }

  /**
   * 初始化战术数据
   * 基于old项目: TEAM_TACTICS_LIST初始化逻辑
   */
  private async initializeTactics(characterId: string, formationId: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Formation服务初始化战术
      // await this.formationService.initializeTactics(characterId, formationId);
      this.logger.log(`战术初始化成功: ${characterId}, 阵容ID: ${formationId}`);
    } catch (error) {
      this.logger.error('战术初始化失败', error);
    }
  }


  /**
   * 触发新手任务
   * 基于old项目: initTaskList + triggerTask逻辑
   *
   * 实现逻辑：
   * 1. 初始化任务列表
   * 2. 触发角色创建相关任务
   * 3. 触发阵容创建相关任务
   * 4. 创建足球场地
   */
  private async triggerNewbieTasks(characterId: string): Promise<XResult<void>> {
    this.logger.log(`触发新手任务: ${characterId}`);

    try {
      // 1. 初始化任务列表
      await this.initializeTaskList(characterId);

      // 2. 触发新手任务（基于old项目的任务类型）
      const newbieTaskTypes = [
        'TARGET_TYPE.ELEVEN',    // 获得11个球员
        'TARGET_TYPE.SIX',       // 获得6个球员
        'TARGET_TYPE.THREE',     // 获得3个球员
        'create_character',      // 创建角色
        'first_formation',       // 首次阵容
        'first_match'           // 首次比赛
      ];

      for (const taskType of newbieTaskTypes) {
        await this.triggerSingleTask(characterId, taskType);
      }

      // 3. 创建足球场地（基于old项目footballGround.createFootballGround）
      await this.createFootballGround(characterId);

      this.logger.log(`新手任务触发完成: ${characterId}`);
    } catch (error) {
      this.logger.error('触发新手任务失败', error);
    }
  }

  /**
   * 初始化任务列表
   * 基于old项目: initTaskList方法
   */
  private async initializeTaskList(characterId: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Activity服务初始化任务列表
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //   'task.initializeTaskList',
      //   { characterId }
      // );

      this.logger.log(`任务列表初始化成功: ${characterId}`);
    } catch (error) {
      this.logger.error('初始化任务列表失败', error);
    }
  }

  /**
   * 触发单个任务
   * 基于old项目: triggerTask方法
   */
  private async triggerSingleTask(characterId: string, taskType: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Activity服务触发任务
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //   'task.triggerTask',
      //   { characterId, taskType }
      // );

      this.logger.log(`任务触发成功: ${characterId}, 类型: ${taskType}`);
    } catch (error) {
      this.logger.error(`任务触发失败: ${taskType}`, error);
    }
  }

  /**
   * 创建足球场地
   * 基于old项目: footballGround.createFootballGround方法
   */
  private async createFootballGround(characterId: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Hero服务创建足球场地
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'ground.createFootballGround',
      //   { characterId }
      // );

      this.logger.log(`足球场地创建成功: ${characterId}`);
    } catch (error) {
      this.logger.error('创建足球场地失败', error);
    }
  }

  /**
   * 根据ResId获取球员位置
   * 基于old项目: Footballer配置表的Position字段
   */
  private getPositionByResId(resId: number): string {
    // 基于old项目的位置映射逻辑
    // 这里简化处理，实际应该从配置表获取
    const positionMap = {
      1001: 'GK',  // 门将
      1002: 'CB',  // 中后卫
      1003: 'CB',  // 中后卫
      1004: 'LB',  // 左后卫
      1005: 'RB',  // 右后卫
      1006: 'CM',  // 中场
      1007: 'CM',  // 中场
      1008: 'LM',  // 左中场
      1009: 'RM',  // 右中场
      1010: 'ST',  // 前锋
      1011: 'ST',  // 前锋
    };

    return positionMap[resId] || 'CM'; // 默认中场
  }

  /**
   * 处理兑换码奖励
   * 基于old项目: 兑换码奖励发放逻辑
   *
   * 实现逻辑：
   * 1. 根据兑换码获取GiftCode配置
   * 2. 根据Type和ItemType确定奖励类型
   * 3. 发放对应的奖励（货币、物品、球员等）
   * 4. 记录奖励发放日志
   */
  private async processRedeemCodeRewards(characterId: string, group: string, codeId: string): Promise<XResult<any[]>> {
    this.logger.log(`处理兑换码奖励: ${characterId}, 组: ${group}, 码: ${codeId}`);

    try {
      // 1. 获取兑换码配置（基于old项目GiftCode配置表）
      const giftCodeConfigs = await this.gameConfig.giftCode?.getAll() || [];
      const codeConfig = giftCodeConfigs.find(config => config.gift === codeId);

      if (!codeConfig) {
        this.logger.warn(`兑换码配置不存在: ${codeId}`);
        return [];
      }

      const rewards = [];

      // 2. 基于old项目逻辑处理不同类型的奖励
      // Type: 1=普通礼包, 2=特殊礼包
      // ItemType: 1=物品奖励, 2=货币奖励, 3=球员奖励

      if (codeConfig.type === 1) {
        // 普通礼包处理
        await this.processNormalGiftRewards(characterId, codeConfig, rewards);
      } else if (codeConfig.type === 2) {
        // 特殊礼包处理
        await this.processSpecialGiftRewards(characterId, codeConfig, rewards);
      }

      this.logger.log(`兑换码奖励处理完成: ${characterId}, 奖励数: ${rewards.length}`);
      return rewards;
    } catch (error) {
      this.logger.error('处理兑换码奖励失败', error);
      return [];
    }
  }

  /**
   * 处理普通礼包奖励
   * 基于old项目: 普通礼包包含3个奖励项目
   */
  private async processNormalGiftRewards(characterId: string, codeConfig: any, rewards: any[]): Promise<XResult<void>> {
    try {
      // 基于old项目GiftCode配置：ResId1/Num1, ResId2/Num2, ResId3/Num3
      const rewardItems = [
        { resId: codeConfig.resId1, num: codeConfig.num1 },
        { resId: codeConfig.resId2, num: codeConfig.num2 },
        { resId: codeConfig.resId3, num: codeConfig.num3 },
      ];

      for (const item of rewardItems) {
        if (item.resId && item.num > 0) {
          await this.processRewardItem(characterId, item.resId, item.num, codeConfig.itemType, rewards);
        }
      }
    } catch (error) {
      this.logger.error('处理普通礼包奖励失败', error);
    }
  }

  /**
   * 处理特殊礼包奖励
   * 基于old项目: 特殊礼包可能有不同的处理逻辑
   */
  private async processSpecialGiftRewards(characterId: string, codeConfig: any, rewards: any[]): Promise<XResult<void>> {
    try {
      // 特殊礼包使用相同的奖励结构，但可能有额外的处理逻辑
      await this.processNormalGiftRewards(characterId, codeConfig, rewards);

      // TODO: 特殊礼包的额外处理逻辑
      // 例如：VIP加成、限时奖励等
    } catch (error) {
      this.logger.error('处理特殊礼包奖励失败', error);
    }
  }

  /**
   * 处理单个奖励项目
   * 基于old项目: 根据ResId判断奖励类型
   */
  private async processRewardItem(characterId: string, resId: number, num: number, itemType: number, rewards: any[]): Promise<XResult<void>> {
    try {
      // 基于old项目逻辑：根据ResId范围判断奖励类型
      if (resId >= 90000 && resId <= 90999) {
        // 货币类型 (90000-90999)
        await this.processCurrencyReward(characterId, resId, num, rewards);
      } else if (resId >= 80000 && resId <= 89999) {
        // 物品类型 (80000-89999)
        await this.processItemReward(characterId, resId, num, rewards);
      } else if (resId >= 10000 && resId <= 79999) {
        // 球员类型 (10000-79999)
        await this.processHeroReward(characterId, resId, num, rewards);
      } else {
        this.logger.warn(`未知的奖励类型: ResId=${resId}`);
      }
    } catch (error) {
      this.logger.error(`处理奖励项目失败: ResId=${resId}, Num=${num}`, error);
    }
  }

  /**
   * 处理货币奖励
   * 基于old项目: 90000=金币, 90001=钻石, 90002=欧元等
   */
  private async processCurrencyReward(characterId: string, resId: number, amount: number, rewards: any[]): Promise<XResult<void>> {
    try {
      let currencyType: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral';
      let description: string;

      // 基于old项目的货币ResId映射
      switch (resId) {
        case 90000:
          currencyType = 'gold';
          description = `获得${amount}金币`;
          break;
        case 90001:
          currencyType = 'chip'; // 钻石映射到chip
          description = `获得${amount}钻石`;
          break;
        case 90002:
          currencyType = 'worldCoin';
          description = `获得${amount}欧元`;
          break;
        case 90003:
          currencyType = 'cash';
          description = `获得${amount}现金`;
          break;
        case 90004:
          currencyType = 'energy';
          description = `获得${amount}体力`;
          break;
        case 90005:
          currencyType = 'integral';
          description = `获得${amount}积分`;
          break;
        default:
          this.logger.warn(`未知的货币类型: ResId=${resId}`);
          return;
      }

      // 调用货币服务添加货币
      await this.addCurrency(characterId, {
        currencyType,
        amount,
        reason: 'redeem_code',
      });

      rewards.push({
        type: 'currency',
        currencyType,
        amount,
        description,
        resId,
      });

      this.logger.log(`货币奖励发放成功: ${characterId}, ${currencyType}: ${amount}`);
    } catch (error) {
      this.logger.error(`处理货币奖励失败: ResId=${resId}, Amount=${amount}`, error);
    }
  }

  /**
   * 处理物品奖励
   * 基于old项目: 调用Inventory服务添加物品
   */
  private async processItemReward(characterId: string, itemId: number, quantity: number, rewards: any[]): Promise<XResult<void>> {
    try {
      // TODO: 调用Inventory服务添加物品
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'inventory.addItem',
      //   {
      //     characterId,
      //     itemId,
      //     quantity,
      //     source: 'redeem_code'
      //   }
      // );

      // if (result.code === 0) {
        rewards.push({
          type: 'item',
          itemId,
          quantity,
          description: `获得物品 x${quantity}`,
          resId: itemId,
        });

        this.logger.log(`物品奖励发放成功: ${characterId}, 物品ID: ${itemId}, 数量: ${quantity}`);
      // }
    } catch (error) {
      this.logger.error(`处理物品奖励失败: ItemId=${itemId}, Quantity=${quantity}`, error);
    }
  }

  /**
   * 处理球员奖励
   * 基于old项目: 调用Hero服务创建球员
   */
  private async processHeroReward(characterId: string, heroResId: number, quantity: number, rewards: any[]): Promise<XResult<void>> {
    try {
      for (let i = 0; i < quantity; i++) {
        // TODO: 调用Hero服务创建球员
        // const result = await this.microserviceClient.call(
        //   MICROSERVICE_NAMES.HERO_SERVICE,
        //   'hero.createHero',
        //   {
        //     characterId,
        //     resId: heroResId,
        //     source: 'redeem_code'
        //   }
        // );

        // if (result.code === 0) {
          rewards.push({
            type: 'hero',
            heroResId,
            // heroId: result.data.heroId,
            description: `获得球员`,
            resId: heroResId,
          });

          this.logger.log(`球员奖励发放成功: ${characterId}, 球员ResId: ${heroResId}`);
        // }
      }
    } catch (error) {
      this.logger.error(`处理球员奖励失败: HeroResId=${heroResId}, Quantity=${quantity}`, error);
    }
  }

  /**
   * 根据资质获取初始球员配置
   * 基于old项目: 不同资质对应不同的初始球员
   */
  private getInitialHeroConfigsByQualified(qualified: number): any[] {
    const configs = {
      1: [ // 低资质
        { resId: 1001, position: 'GK' },
        { resId: 1002, position: 'CB' },
        { resId: 1003, position: 'CB' },
        { resId: 1004, position: 'LB' },
        { resId: 1005, position: 'RB' },
        { resId: 1006, position: 'CM' },
        { resId: 1007, position: 'CM' },
        { resId: 1008, position: 'LM' },
        { resId: 1009, position: 'RM' },
        { resId: 1010, position: 'ST' },
        { resId: 1011, position: 'ST' },
      ],
      2: [ // 中资质
        { resId: 2001, position: 'GK' },
        { resId: 2002, position: 'CB' },
        { resId: 2003, position: 'CB' },
        { resId: 2004, position: 'LB' },
        { resId: 2005, position: 'RB' },
        { resId: 2006, position: 'CM' },
        { resId: 2007, position: 'CM' },
        { resId: 2008, position: 'LM' },
        { resId: 2009, position: 'RM' },
        { resId: 2010, position: 'ST' },
        { resId: 2011, position: 'ST' },
      ],
      3: [ // 高资质
        { resId: 3001, position: 'GK' },
        { resId: 3002, position: 'CB' },
        { resId: 3003, position: 'CB' },
        { resId: 3004, position: 'LB' },
        { resId: 3005, position: 'RB' },
        { resId: 3006, position: 'CM' },
        { resId: 3007, position: 'CM' },
        { resId: 3008, position: 'LM' },
        { resId: 3009, position: 'RM' },
        { resId: 3010, position: 'ST' },
        { resId: 3011, position: 'ST' },
      ],
    };

    return configs[qualified] || configs[1];
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName 和 account.getPlayerUid
   * 用于商业赛等功能的对手搜索
   */
  async searchByName(name: string, serverId?: string): Promise<XResult<any>> {
    this.logger.log(`根据名称搜索角色: ${name}, 服务器: ${serverId || 'all'}`);

    try {
      if (!name || name.trim() === '') {
        return null;
      }

      const character = await this.characterRepository.findByName(name.trim(), serverId);

      if (!character) {
        this.logger.log(`未找到角色: ${name}`);
        return null;
      }

      // 返回搜索结果的基本信息，用于商业赛等功能
      return {
        characterId: character.characterId,
        playerUid: character.characterId, // 兼容old项目的playerUid字段
        name: character.name,
        faceIcon: character.faceIcon || 0,
        level: character.level,
        fame: character.fame || 0,
        ballFanCount: character.fame || 0, // 使用fame作为球迷数的临时替代
        actualStrength: character.level * 100, // 使用等级计算临时实力值
        isGroundOpen: character.fieldLevel > 0, // 根据球场等级判断是否开放
        groundOpenStatus: character.fieldLevel > 0, // 兼容old项目的groundOpenStatus字段
        lastUpdateTime: new Date(),
        // TODO: 这些字段应该通过其他微服务获取更准确的数据
        // - ballFanCount: 通过Hero服务获取真实球迷数
        // - actualStrength: 通过Formation服务获取队伍实力
        // - isGroundOpen: 通过Ground服务获取球场开放状态
      };
    } catch (error) {
      this.logger.error('根据名称搜索角色失败', error);
      return null;
    }
  }

  // ==================== 球探系统方法 ====================

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   */
  async getScoutData(characterId: string): Promise<XResult<any>> {
    this.logger.log(`获取球探数据: ${characterId}`);

    try {
      const character = await this.characterRepository.findByCharacterId(characterId);
      if (!character) {
        throw new Error('角色不存在');
      }

      // 检查并初始化球探数据
      if (!character.scoutData) {
        character.scoutData = {
          scoutRp: 0,
          scoutEnergy: 100,
          scoutGroup: [
            { type: 1, count: 5, getTime: 1 }, // 初级球探组
            { type: 2, count: 1, getTime: 1 }, // 高级球探组
            { type: 3, count: 1, getTime: 1 }, // 顶级球探组
          ],
          scoutPack: [],
          isFrist: 0,
          reTime: Date.now(),
          lastEnergyRegenTime: new Date(),
          lastSearchTime: 0,
          maxScoutPackSize: 10,
        };
        await character.save();
      }

      // 更新体力恢复（基于old项目的体力恢复机制）
      await this.updateScoutEnergyRegen(character);

      return character.scoutData;
    } catch (error) {
      this.logger.error('获取球探数据失败', error);
      throw error;
    }
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   */
  async updateScoutData(characterId: string, scoutData: any): Promise<XResult<void>> {
    this.logger.log(`更新球探数据: ${characterId}`);

    try {
      const character = await this.characterRepository.findByCharacterId(characterId);
      if (!character) {
        throw new Error('角色不存在');
      }

      // 更新球探数据
      character.scoutData = {
        ...character.scoutData,
        ...scoutData,
      };

      await character.save();
      this.logger.log(`球探数据更新成功: ${characterId}`);
    } catch (error) {
      this.logger.error('更新球探数据失败', error);
      throw error;
    }
  }

  /**
   * 更新球探体力恢复
   * 基于old项目的体力恢复机制：每5分钟恢复1点体力，最大100点
   */
  private async updateScoutEnergyRegen(character: any): Promise<XResult<void>> {
    const now = new Date();
    const lastRegenTime = new Date(character.scoutData.lastEnergyRegenTime);
    const timeDiff = now.getTime() - lastRegenTime.getTime();

    // 每5分钟恢复1点体力
    const regenInterval = 5 * 60 * 1000; // 5分钟
    const regenPoints = Math.floor(timeDiff / regenInterval);

    if (regenPoints > 0 && character.scoutData.scoutEnergy < 100) {
      const newEnergy = Math.min(100, character.scoutData.scoutEnergy + regenPoints);
      character.scoutData.scoutEnergy = newEnergy;
      character.scoutData.lastEnergyRegenTime = now;

      this.logger.log(`球探体力恢复: ${character.characterId}, 恢复${regenPoints}点, 当前${newEnergy}点`);
      await character.save();
    }
  }


}
