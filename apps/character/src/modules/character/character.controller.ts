import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MessagePatternInternal } from '@libs/common/decorators';
import {BaseController, BasePayload, PaginationPayload, XResultUtils} from '@libs/common/controller';
import { CharacterService } from './character.service';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  LoginCharacterDto,
  CurrencyOperationDto,
  BuyEnergyDto,
  LevelUpDto,
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto,
  GetCharacterListDto
} from '@character/common/dto/character.dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';
import { CharacterDocument } from '@character/common/schemas/character.schema';
import { XResponse, PaginationResult } from '@libs/common/types/result.type';
import {InjectedContext} from "@libs/common";

/**
 * 角色控制器
 * 继承BaseController，提供统一的微服务接口处理和Result模式集成
 *
 * 🎯 核心功能：
 * - 角色创建和管理
 * - 角色登录和认证
 * - 角色属性和资源管理
 * - 角色升级和能量购买
 * - 角色列表查询和分页
 *
 * 🚀 优化特性：
 * - 统一的请求处理和响应格式
 * - 完整的Result模式错误处理
 * - 自动的性能监控和日志记录
 * - 标准化的缓存装饰器集成
 * - 参数验证和提取框架
 */
@Controller()
export class CharacterController extends BaseController {
  constructor(private readonly characterService: CharacterService) {
    super('CharacterController', {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      autoHandleExceptions: true
    });
  }

  // ========== 角色管理接口 ==========

  /**
   * 创建新角色
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.create')
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(@Payload() payload: BasePayload & { createDto: CreateCharacterDto }): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.createCharacter(payload.createDto);
      return this.fromResult(result, '角色创建成功');
    }, payload);
  }

  /**
   * 角色登录
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.login')
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: BasePayload & { loginDto: LoginCharacterDto }): Promise<XResponse<LoginResultDto>> {
    return this.handleRequest(async () => {
      const result = await this.characterService.loginCharacter(payload.loginDto);
      return this.fromResult(result, '登录成功');
    }, payload);
  }

  /**
   * 角色登出
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.logout')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server'
  })
  async logoutCharacter(@Payload() payload: BasePayload & { characterId: string; serverId?: string }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId']);

      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.logoutCharacter(payload.characterId);
      return this.fromResult(result, '登出成功');
    }, payload);
  }

  /**
   * 获取角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: BasePayload & { characterId: string; serverId?: string }): Promise<XResponse<CharacterInfoDto>> {
    // 🔍 详细日志：验证payload注入机制
    this.logger.log(`=== Payload注入验证 - getCharacterInfo ===`);
    this.logger.log(`📋 原始字段:`);
    this.logger.log(`  - characterId: ${payload.characterId}`);
    this.logger.log(`  - serverId: ${payload.serverId}`);

    this.logger.log(`🔧 注入字段:`);
    this.logger.log(`  - userId: ${(payload as any).userId || 'N/A'}`);
    this.logger.log(`  - wsContext: ${JSON.stringify((payload as any).wsContext || 'N/A')}`);
    this.logger.log(`  - serverContext: ${JSON.stringify((payload as any).serverContext || 'N/A')}`);

    this.logger.log(`📊 完整payload结构:`);
    this.logger.log(JSON.stringify(payload, null, 2));
    this.logger.log(`=== Payload注入验证结束 ===`);
    
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.getCharacterInfo(payload.characterId);
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  /**
   * 更新角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.update')
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateCharacter(@Payload() payload: BasePayload & { characterId: string; updateDto: UpdateCharacterDto; serverId?: string }): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'updateDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.updateCharacter(payload.characterId, payload.updateDto);
      return this.fromResult(result, '更新成功');
    }, payload);
  }

  /**
   * 获取角色列表
   * 使用BaseController的统一请求处理框架和分页支持
   */
  @MessagePattern('character.getList')
  @Cacheable({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getCharacterList(@Payload() payload: PaginationPayload & { getCharacterListDto: GetCharacterListDto }): Promise<XResponse<PaginationResult<any>>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['getCharacterListDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.getCharacterList(payload.getCharacterListDto);
      return this.fromResult(result, '获取成功');
    }, payload);
  }

  // ========== 货币操作接口（内部服务调用）==========

  /**
   * 增加货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.add')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addCurrency(@Payload() payload: BasePayload & {
    characterId: string;
    currencyDto: CurrencyOperationDto;
    serverId?: string
  }) {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'currencyDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.addCurrency(payload.characterId, payload.currencyDto);
      return this.fromResult(result, '货币增加成功');
    }, payload);
  }

  /**
   * 扣除货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.subtract')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async subtractCurrency(@Payload() payload: BasePayload & {
    characterId: string;
    currencyDto: CurrencyOperationDto;
    serverId?: string
  }) {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'currencyDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.subtractCurrency(payload.characterId, payload.currencyDto);
      return this.fromResult(result, '货币扣除成功');
    }, payload);
  }

  // ========== 角色功能接口 ==========

  /**
   * 购买体力
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.energy.buy')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyEnergy(@Payload() payload: BasePayload & { characterId: string; buyDto: BuyEnergyDto; serverId?: string }): Promise<XResponse<BuyEnergyResultDto>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'buyDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.buyEnergy(payload.characterId, payload.buyDto);
      return this.fromResult(result, '购买体力成功');
    }, payload);
  }

  /**
   * 角色升级
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.levelup')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUp(@Payload() payload: BasePayload & { characterId: string; levelUpDto: LevelUpDto; serverId?: string }): Promise<XResponse<LevelUpResultDto>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'levelUpDto']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.levelUp(payload.characterId, payload.levelUpDto);
      return this.fromResult(result, '升级成功');
    }, payload);
  }

  /**
   * 完成创角步骤
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.completeStep')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async completeCreateStep(@Payload() payload: BasePayload & { characterId: string; step: number; serverId?: string }): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'step']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.completeCreateStep(payload.characterId, payload.step);
      return this.fromResult(result, '步骤完成成功');
    }, payload);
  }

  /**
   * 完成新手引导
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.finishGuide')
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async finishGuide(@Payload() payload: BasePayload & { characterId: string; serverId?: string }): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.finishGuide(payload.characterId);
      return this.fromResult(result, '新手引导完成');
    }, payload);
  }

  /**
   * 设置角色信仰
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.setBelief')
  async setCharacterBelief(@Payload() payload: BasePayload & { characterId: string; beliefId: number }): Promise<XResponse<CharacterDocument>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'beliefId']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.setCharacterBelief(payload.characterId, payload.beliefId);
      return this.fromResult(result, '设置成功');
    }, payload);
  }

  /**
   * 使用兑换码
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.useRedeemCode')
  async useRedeemCode(@Payload() payload: BasePayload & { characterId: string; group: string; codeId: string }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      // 验证必需参数
      const validation = this.validateRequiredFields(payload, ['characterId', 'group', 'codeId']);
      if (XResultUtils.isFailure(validation)) {
        return this.fromResult(validation);
      }

      const result = await this.characterService.useRedeemCode(payload.characterId, payload.group, payload.codeId);
      return this.fromResult(result, '兑换成功');
    }, payload);
  }

  /**
   * 更新持续buff
   */
  @MessagePattern('character.updateBuff')
  async updateContinuedBuff(@Payload() payload: { characterId: string; buffDuration: number; injectedContext?: InjectedContext }): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`更新持续buff: ${payload.characterId}, 持续时间: ${payload.buffDuration}秒`);
    const character = await this.characterService.updateContinuedBuff(payload.characterId, payload.buffDuration);
    return {
      code: 0,
      message: '更新成功',
      data: character,
    };
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName
   * 用于商业赛等功能的对手搜索
   */
  @MessagePattern('character.searchByName')
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: { name: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<CharacterDocument[]>> {
    this.logger.log(`根据名称搜索角色: ${payload.name}`);
    const character = await this.characterService.searchByName(payload.name, payload.serverId);
    return {
      code: 0,
      message: '搜索成功',
      data: character,
    };
  }

  // ==================== old项目核心API ====================

  /**
   * 获取个人信息
   * 对应old项目: game.player.getPersonInfo
   */
  @MessagePattern('character.getPersonInfo')
  @Cacheable({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getPersonInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<CharacterInfoDto>> {
    this.logger.log(`获取个人信息: ${payload.characterId}`);
    const result = await this.characterService.getPersonInfo(payload.characterId);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 创建角色
   * 对应old项目: game.player.createRole
   */
  @MessagePattern('character.createRole')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createRole(@Payload() payload: { characterId: string; qualified: number; name: string; faceIcon: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`创建角色: ${payload.characterId}, 参数: ${JSON.stringify(payload)}`);
    const result = await this.characterService.createRole(payload.characterId, {
      qualified: payload.qualified,
      name: payload.name,
      faceIcon: payload.faceIcon,
    });
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 修改玩家名称
   * 对应old项目: game.player.modifyPlayerName
   */
  @MessagePattern('character.modifyCharacterName')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async modifyCharacterName(@Payload() payload: { characterId: string; name: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`修改玩家名称: ${payload.characterId}, 新名称: ${payload.name}`);
    const result = await this.characterService.modifyCharacterName(payload.characterId, {
      name: payload.name,
    });
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 添加资源 - 仅内部服务调用
   * 对应old项目: game.player.addResource
   */
  @MessagePatternInternal('character.addResource')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addResource(@Payload() payload: {
    characterId: string;
    resourceType: string;
    amount: number;
    reason?: string;
    serverId?: string;
  }) {
    this.logger.log(`添加资源: ${payload.characterId}, 类型: ${payload.resourceType}, 数量: ${payload.amount}`);
    const result = await this.characterService.addResource(
      payload.characterId,
      payload.resourceType,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取体力奖励
   * 对应old项目: game.player.getEnergyReward
   */
  @MessagePattern('character.getEnergyReward')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getEnergyReward(@Payload() payload: { characterId: string; type: number; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取体力奖励: ${payload.characterId}, 类型: ${payload.type}`);
    const result = await this.characterService.getEnergyReward(payload.characterId, payload.type);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 消耗金币任务
   * 对应old项目: game.player.costCashTask
   */
  @MessagePattern('character.costCashTask')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async costCashTask(@Payload() payload: { characterId: string; amount: number; reason?: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`消耗金币: ${payload.characterId}, 数量: ${payload.amount}`);
    const result = await this.characterService.costCashTask(
      payload.characterId,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 扣除资源 - 仅内部服务调用
   * 对应old项目: game.player.deductResource
   */
  @MessagePatternInternal('character.deductResource')
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deductResource(@Payload() payload: {
    characterId: string;
    resourceType: string;
    amount: number;
    reason?: string;
    serverId?: string;
  }) {
    this.logger.log(`扣除资源: ${payload.characterId}, 类型: ${payload.resourceType}, 数量: ${payload.amount}`);
    const result = await this.characterService.deductResource(
      payload.characterId,
      payload.resourceType,
      payload.amount,
      payload.reason
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  // ==================== 球探系统API ====================

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   */
  @MessagePattern('character.getScoutData')
  @Cacheable({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutData(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`获取球探数据: ${payload.characterId}`);

    try {
      const scoutData = await this.characterService.getScoutData(payload.characterId);
      return {
        code: 0,
        message: '获取球探数据成功',
        data: scoutData,
      };
    } catch (error) {
      this.logger.error('获取球探数据失败', error);
      return {
        code: -1,
        message: error.message || '获取球探数据失败',
        data: null,
      };
    }
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   */
  @MessagePattern('character.updateScoutData')
  @CacheEvict({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateScoutData(@Payload() payload: { characterId: string; scoutData: any; serverId?: string; injectedContext?: InjectedContext }): Promise<XResponse<any>> {
    this.logger.log(`更新球探数据: ${payload.characterId}`);

    try {
      await this.characterService.updateScoutData(payload.characterId, payload.scoutData);
      return {
        code: 0,
        message: '更新球探数据成功',
        data: null,
      };
    } catch (error) {
      this.logger.error('更新球探数据失败', error);
      return {
        code: -1,
        message: error.message || '更新球探数据失败',
        data: null,
      };
    }
  }

  /**
   * 接收Auth服务的角色初始化通知
   */
  @MessagePattern('character.initializeFromAuth')
  async initializeFromAuth(@Payload() payload: { characterId: string; userId: string; serverId: string; characterName: string; initialData?: any; injectedContext?: InjectedContext }): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`📨 收到Auth服务角色初始化通知: ${payload.characterId}`);

    try {
      const character = await this.characterService.initializeFromAuth(payload);

      return {
        code: 0,
        message: '角色游戏数据初始化成功',
        data: {
          characterId: character.characterId,
          success: true,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 角色游戏数据初始化失败: ${payload.characterId}`, error);
      return {
        code: -1,
        message: '角色游戏数据初始化失败',
        error: error.message,
      };
    }
  }
}
