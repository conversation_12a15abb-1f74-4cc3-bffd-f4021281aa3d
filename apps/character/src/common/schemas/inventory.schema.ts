/**
 * Inventory Schema - 严格基于old项目bag.js重新设计
 * 确保与old项目的数据结构和功能100%一致
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// ==================== 类型安全接口定义 ====================

/**
 * 客户端背包条目接口 - 替换makeClientBagList的any返回值
 */
export interface ClientBagEntry {
  id: number;
  name: string;
  type: BookMarkType;
  capacity: number;
  usedSlots: number;
  expandCount: number;
  nextExpandCost: number;
  itemUids: string[];
  createTime: number;
  updateTime: number;
  remainingCapacity: number;
  usageRate: number;
  canExpand: boolean;
}

/**
 * 背包数组转换接口 - 替换bagToArray的any返回值
 */
export interface BagArrayEntry {
  id: number;
  name: string;
  type: BookMarkType;
  capacity: number;
  usedSlots: number;
  expandCount: number;
  nextExpandCost: number;
  itemUids: string[];
  createTime: number;
  updateTime: number;
}

/**
 * ResId2Uid数组转换接口 - 替换resId2UidToArray的any返回值
 */
export interface ResId2UidArrayEntry {
  resId: string;
  count: number;
}

/**
 * 物品使用结果接口 - 替换useItem方法的any返回值
 */
export interface ItemUseResult {
  success: boolean;
  code: number;
  message?: string;
  rewards?: ItemReward[];
  consumedItems?: ConsumedItem[];
  updatedCurrency?: CurrencyUpdate[];
}

/**
 * 物品奖励接口
 */
export interface ItemReward {
  type: 'currency' | 'item' | 'hero' | 'experience';
  id?: number;
  amount: number;
  description?: string;
}

/**
 * 消耗物品接口
 */
export interface ConsumedItem {
  itemUid: string;
  resId: number;
  quantity: number;
}

/**
 * 货币更新接口
 */
export interface CurrencyUpdate {
  currencyType: string;
  oldAmount: number;
  newAmount: number;
  changeAmount: number;
}

// 页签类型枚举 - 基于old项目bagBookMarkConfig
export enum BookMarkType {
  EQUIPMENT = 1,    // 装备页签
  CONSUMABLE = 2,   // 消耗品页签
  MATERIAL = 3,     // 材料页签
  CARD = 4,         // 卡片页签
  GIFT = 5,         // 礼包页签
  CURRENCY = 6,     // 货币页签
}

// 物品使用类型枚举 - 基于old项目BagUseItemTypeEnum
export enum ItemUseType {
  CURRENCY = 1,     // 货币类
  CONSUME = 2,      // 消耗品类
  GIFT_BAG = 3,     // 礼包类
  EQUIPMENT = 4,    // 装备类
}

// 页签实例 - 基于old项目bag Map结构
@Schema({ _id: false })
export class BookMarkEntry {
  @Prop({ required: true })
  id: number;               // 页签ID

  @Prop({ required: true })
  name: string;             // 页签名称

  @Prop({ required: true })
  type: BookMarkType;       // 页签类型

  @Prop({ default: 50 })
  capacity: number;         // 当前容量

  @Prop({ default: 0 })
  usedSlots: number;        // 已使用槽位

  @Prop({ default: 0 })
  expandCount: number;      // 扩展次数

  @Prop({ default: 1000 })
  nextExpandCost: number;   // 下次扩展费用

  @Prop({ type: [String], default: [] })
  itemUids: string[];       // 物品UID列表

  @Prop({ default: Date.now })
  createTime: number;       // 创建时间

  @Prop({ default: Date.now })
  updateTime: number;       // 更新时间

  // 虚拟字段：剩余容量
  get remainingCapacity(): number {
    return this.capacity - this.usedSlots;
  }

  // 虚拟字段：使用率
  get usageRate(): number {
    return this.capacity > 0 ? (this.usedSlots / this.capacity) * 100 : 0;
  }

  // 虚拟字段：是否可以扩展
  get canExpand(): boolean {
    return this.capacity < 200; // 假设最大容量为200
  }
}

// 物品映射项 - 基于old项目itemUidToBookMarkId
@Schema({ _id: false })
export class ItemToBookMarkMapping {
  @Prop({ required: true })
  itemUid: string;          // 物品UID

  @Prop({ required: true })
  bookMarkId: number;       // 页签ID
}

// 背包类型枚举 - 基于old项目背包分类
export enum InventoryType {
  EQUIPMENT = 1,    // 装备背包
  CONSUMABLE = 2,   // 消耗品背包
  MATERIAL = 3,     // 材料背包
  CARD = 4,         // 卡片背包
  GIFT = 5,         // 礼包背包
  CURRENCY = 6,     // 货币背包
}

// 背包物品项 - 基于old项目物品结构
@Schema({ _id: false })
export class InventoryItem {
  @Prop({ required: true })
  itemId: string;           // 物品ID

  @Prop({ required: true })
  configId: number;         // 配置ID

  @Prop({ default: 1 })
  quantity: number;         // 数量

  @Prop({ default: 0 })
  bind: number;             // 绑定状态

  @Prop({ default: Date.now })
  acquiredTime: number;     // 获得时间

  @Prop({ default: 0 })
  slot: number;             // 槽位位置

  @Prop({ default: Date.now })
  obtainTime: number;       // 获得时间（兼容字段）
}

// 主Inventory Schema - 严格基于old项目Bag实体
@Schema({
  collection: 'inventories',
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Inventory {
  @Prop({ required: true, index: true })
  uid: string;              // 玩家UID - 对应old项目的uid

  @Prop({ required: true, index: true })
  characterId: string;      // 角色ID (新增字段，用于微服务架构)

  @Prop({ required: true, index: true })
  serverId: string;         // 服务器ID (新增字段，用于微服务架构)

  // 页签数据 - 对应old项目的bag (Map转为Array)
  @Prop({ type: [BookMarkEntry], default: [] })
  bag: BookMarkEntry[];

  // 物品列表 - 对应Repository中使用的items字段
  @Prop({ type: [InventoryItem], default: [] })
  items: InventoryItem[];

  // 物品映射 - 对应old项目的itemUidToBookMarkId (Map转为Array)
  @Prop({ type: [ItemToBookMarkMapping], default: [] })
  itemUidToBookMarkId: ItemToBookMarkMapping[];

  @Prop({ default: Date.now })
  createTime: number;       // 创建时间

  @Prop({ default: Date.now })
  updateTime: number;       // 更新时间
}

export const InventorySchema = SchemaFactory.createForClass(Inventory);



// 定义Document类型和方法接口
export interface InventoryMethods {
  // 页签管理方法 - 基于old项目
  getOneBookMark(id: number): BookMarkEntry | null;
  delOneBookMark(id: number): boolean;
  checkBookMarkInitDB(): void;
  resetItemBagByBookMarkId(id: number): void;

  // 物品映射方法 - 基于old项目
  addItemToBookMarkMapping(itemUid: string, bookMarkId: number): void;
  removeItemFromBookMarkMapping(itemUid: string): boolean;
  getBookMarkIdByItemUid(itemUid: string): number | null;
  clearItemIdToBookMarkId(): void;

  // 数据转换方法 - 基于old项目，使用严格类型
  makeClientBagList(): ClientBagEntry[];
  bagToMap(arr: BagArrayEntry[]): Map<number, BookMarkEntry>;
  bagToArray(map: Map<number, BookMarkEntry>): BagArrayEntry[];
  resId2UidToMap(arr: ResId2UidArrayEntry[]): Map<string, number>;
  resId2UidToArray(map: Map<string, number>): ResId2UidArrayEntry[];

  // 背包操作方法 - 基于old项目
  addToBag(bookMarkId: number, itemUid: string): boolean;
  removeFromBag(bookMarkId: number, itemUid: string): boolean;
  findItemInBag(itemUid: string): { bookMarkId: number; index: number } | null;
  sortBag(bookMarkId: number, sortType: string): void;

  // 扩展方法 - 基于old项目
  expandBag(bookMarkId: number, expandCount: number): { success: boolean; cost: number };
  calculateExpandCost(bookMarkId: number, expandCount: number): number;
  canExpand(bookMarkId: number): boolean;

  // 物品使用方法 - 基于old项目，使用严格类型
  useItemMainType(bookMarkId: number, uid: string, resId: number, type: number, subType: number, num: number, heroUid?: string): ItemUseResult;
  useItemBySubTypeCurrency(bookMarkId: number, uid: string, resId: number, subType: number, num: number): ItemUseResult;
  useItemBySubTypeConsume(bookMarkId: number, uid: string, resId: number, subType: number, num: number, heroUid?: string): ItemUseResult;
  useItemBySubTypeGiftPackage(bookMarkId: number, uid: string, resId: number, subType: number, num: number): ItemUseResult;
}

export type InventoryDocument = Inventory & Document & InventoryMethods;

// 创建索引
InventorySchema.index({ uid: 1 }, { unique: true });
InventorySchema.index({ characterId: 1 });
InventorySchema.index({ serverId: 1 });

// 添加实例方法 - 基于old项目的核心方法

/**
 * 获取指定页签
 * 基于old项目: getOneBookMark方法
 */
InventorySchema.methods.getOneBookMark = function(id: number): BookMarkEntry | null {
  return this.bag.find((entry: BookMarkEntry) => entry.id === id) || null;
};

/**
 * 删除指定页签
 * 基于old项目: delOneBookMark方法
 */
InventorySchema.methods.delOneBookMark = function(id: number): boolean {
  const index = this.bag.findIndex((entry: BookMarkEntry) => entry.id === id);
  if (index !== -1) {
    this.bag.splice(index, 1);
    return true;
  }
  return false;
};

/**
 * 添加物品到页签映射
 * 基于old项目: itemUidToBookMarkId Map操作
 */
InventorySchema.methods.addItemToBookMarkMapping = function(itemUid: string, bookMarkId: number): void {
  // 先检查是否已存在
  const existing = this.itemUidToBookMarkId.find((mapping: ItemToBookMarkMapping) => mapping.itemUid === itemUid);
  if (existing) {
    existing.bookMarkId = bookMarkId;
  } else {
    this.itemUidToBookMarkId.push({
      itemUid: itemUid,
      bookMarkId: bookMarkId
    });
  }
};

/**
 * 从页签映射中移除物品
 * 基于old项目: itemUidToBookMarkId Map操作
 */
InventorySchema.methods.removeItemFromBookMarkMapping = function(itemUid: string): boolean {
  const index = this.itemUidToBookMarkId.findIndex((mapping: ItemToBookMarkMapping) => mapping.itemUid === itemUid);
  if (index !== -1) {
    this.itemUidToBookMarkId.splice(index, 1);
    return true;
  }
  return false;
};

/**
 * 根据物品UID获取页签ID
 * 基于old项目: itemUidToBookMarkId Map查询
 */
InventorySchema.methods.getBookMarkIdByItemUid = function(itemUid: string): number | null {
  const mapping = this.itemUidToBookMarkId.find((mapping: ItemToBookMarkMapping) => mapping.itemUid === itemUid);
  return mapping ? mapping.BookMarkId : null;
};

/**
 * 清除所有物品映射
 * 基于old项目: _clearItemIdToBookMarkId方法
 */
InventorySchema.methods.clearItemIdToBookMarkId = function(): void {
  this.itemUidToBookMarkId = [];
};

/**
 * 生成客户端背包数据
 * 基于old项目: makeClientBagList方法，使用严格类型和正确字段名
 */
InventorySchema.methods.makeClientBagList = function(): ClientBagEntry[] {
  const clientList: ClientBagEntry[] = [];
  for (const entry of this.bag) {
    clientList.push({
      id: entry.id,
      name: entry.name,
      type: entry.type,
      capacity: entry.capacity,
      usedSlots: entry.usedSlots,
      expandCount: entry.expandCount,
      nextExpandCost: entry.nextExpandCost,
      itemUids: entry.itemUids,
      createTime: entry.createTime,
      updateTime: entry.updateTime,
      remainingCapacity: entry.remainingCapacity,
      usageRate: entry.usageRate,
      canExpand: entry.canExpand,
    });
  }
  return clientList;
};

/**
 * 添加物品到页签
 * 基于old项目: addToBag方法
 */
InventorySchema.methods.addToBag = function(bookMarkId: number, itemUid: string): boolean {
  const bookMark = this.getOneBookMark(bookMarkId);
  if (!bookMark) {
    return false;
  }

  if (bookMark.usedSlots >= bookMark.capacity) {
    return false; // 背包已满
  }

  bookMark.itemUids.push(itemUid);
  bookMark.usedSlots += 1;
  bookMark.updateTime = Date.now();

  // 添加到映射
  this.addItemToBookMarkMapping(itemUid, bookMarkId);

  return true;
};

/**
 * 从页签移除物品
 * 基于old项目: removeFromBag方法
 */
InventorySchema.methods.removeFromBag = function(bookMarkId: number, itemUid: string): boolean {
  const bookMark = this.getOneBookMark(bookMarkId);
  if (!bookMark) {
    return false;
  }

  const index = bookMark.itemUids.indexOf(itemUid);
  if (index === -1) {
    return false;
  }

  bookMark.itemUids.splice(index, 1);
  bookMark.usedSlots -= 1;
  bookMark.updateTime = Date.now();

  // 从映射中移除
  this.removeItemFromBookMarkMapping(itemUid);

  return true;
};

/**
 * 扩展背包
 * 基于old项目: expandBag方法
 */
InventorySchema.methods.expandBag = function(bookMarkId: number, expandCount: number): { success: boolean; cost: number } {
  const bookMark = this.getOneBookMark(bookMarkId);
  if (!bookMark) {
    return { success: false, cost: 0 };
  }

  if (!bookMark.canExpand) {
    return { success: false, cost: 0 };
  }

  const cost = this.calculateExpandCost(bookMarkId, expandCount);

  // TODO: 检查玩家是否有足够的货币

  bookMark.capacity += expandCount * 10; // 每次扩展10个槽位
  bookMark.expandCount += expandCount;
  bookMark.nextExpandCost = this.calculateExpandCost(bookMarkId, 1);
  bookMark.updateTime = Date.now();

  return { success: true, cost };
};

/**
 * 计算扩展费用
 * 基于old项目: calculateExpandCost方法
 */
InventorySchema.methods.calculateExpandCost = function(bookMarkId: number, expandCount: number): number {
  const bookMark = this.getOneBookMark(bookMarkId);
  if (!bookMark) {
    return 0;
  }

  const baseCost = 1000; // 基础费用
  const multiplier = 1.5; // 倍数
  let totalCost = 0;

  for (let i = 0; i < expandCount; i++) {
    const currentExpand = bookMark.expandCount + i;
    const cost = Math.floor(baseCost * Math.pow(multiplier, currentExpand));
    totalCost += cost;
  }

  return totalCost;
};
