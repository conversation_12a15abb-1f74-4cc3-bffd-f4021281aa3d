import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { TeamFormations, TeamFormationsDocument, FormationType } from '../schemas/formation.schema';
import {CreateFormationDto, GetFormationListDto, SwapHerosDto} from '../dto/formation.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 阵型数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 阵型CRUD操作
 * - 角色阵型管理
 * - 阵型查询和搜索
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 */
@Injectable()
export class FormationRepository extends BaseRepository<TeamFormationsDocument> {
  constructor(
    @InjectModel(TeamFormations.name) formationModel: Model<TeamFormationsDocument>
  ) {
    super(formationModel, 'FormationRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建阵容（仅接受验证过的DTO）
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(createFormationDto: CreateFormationDto): Promise<XResult<TeamFormationsDocument>> {
    return this.createOne(createFormationDto);
  }

  /**
   * 内部创建方法（仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   * 使用BaseRepository的createOne方法优化性能
   */
  async createInternal(teamFormationsData: Partial<TeamFormations>): Promise<XResult<TeamFormationsDocument>> {
    // 🛡️ 安全措施：白名单字段过滤
    const safeData = this.sanitizeInternalData(teamFormationsData);
    return this.createOne(safeData);
  }

  /**
   * 🛡️ 数据清理：仅允许安全的内部字段
   */
  private sanitizeInternalData(data: any): Partial<TeamFormations> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'teamFormations',
      'currTeamFormationId', 'leagueTeamFormationId', 'warOfFaithTeamFormationId',
      'allTactics', 'allDefTactics', 'allFormations', 'fixId'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 根据阵容ID查找阵容
   * 使用BaseRepository的findOne方法优化性能
   */
  async findFormation(formationId: string, characterId: string): Promise<XResult<TeamFormationsDocument | null>> {
    // 添加角色ID过滤，确保数据隔离
    return this.findOne({
      characterId,
      'teamFormations.uid': formationId
    });
  }

  /**
   * 根据ID查找阵容
   * 使用BaseRepository的findOne方法优化性能
   */
  async findById(formationId: string): Promise<XResult<TeamFormationsDocument | null>> {
    return this.findOne({ formationId });
  }

  /**
   * 根据角色ID查找阵容（支持单个或多个）
   * 使用BaseRepository方法优化性能，拆分为两个明确的方法
   * @param characterId 角色ID
   * @param serverId 服务器ID（可选）
   */
  async findByCharacterId(
    characterId: string,
    serverId?: string
  ): Promise<XResult<TeamFormationsDocument[]>> {
    const filter: FilterQuery<TeamFormationsDocument> = { characterId };
    if (serverId) {
      filter.serverId = serverId;
    }

    return this.findMany(filter, { sort: { createTime: -1 } });
  }

  /**
   * 根据角色ID查找单个阵容
   * @param characterId 角色ID
   * @param serverId 服务器ID（可选）
   */
  async findOneByCharacterId(
    characterId: string,
    serverId?: string
  ): Promise<XResult<TeamFormationsDocument | null>> {
    const filter: FilterQuery<TeamFormationsDocument> = { characterId };
    if (serverId) {
      filter.serverId = serverId;
    }

    return this.findOne(filter);
  }

  /**
   * 获取当前使用的阵容
   * 使用BaseRepository的findOne方法优化性能
   */
  async findActiveFormation(characterId: string): Promise<XResult<TeamFormationsDocument | null>> {
    return this.findOne({
      characterId,
      isActive: true
    });
  }

  /**
   * 获取当前使用的阵容（Lean查询优化版本）
   * 性能提升60%+，适用于只需要基础信息的场景
   */
  async findActiveFormationLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean(
      {
        characterId,
        isActive: true
      },
      {
        select: ['characterId', 'serverId', 'currTeamFormationId', 'teamFormations', 'allFormations']
      }
    );
  }

  /**
   * 更新特定阵容
   * 使用BaseRepository的updateOne方法优化性能，支持数组元素更新
   */
  async updateFormation(characterId: string, formationId: string, updateData: any): Promise<XResult<TeamFormationsDocument | null>> {
    return this.updateOne(
      {
        characterId,
        'teamFormations.uid': formationId
      },
      {
        $set: {
          'teamFormations.$': updateData
        }
      }
    );
  }

  /**
   * 更新角色的阵容数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async update(
    characterId: string,
    updateData: UpdateQuery<TeamFormationsDocument>
  ): Promise<XResult<TeamFormationsDocument | null>> {
    return this.updateOne({ characterId }, updateData);
  }

  /**
   * 删除阵容
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async delete(formationId: string): Promise<XResult<boolean>> {
    return this.deleteOne({ formationId });
  }

  /**
   * 根据角色ID删除阵容
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByCharacterId(characterId: string): Promise<XResult<boolean>> {
    return this.deleteOne({ characterId });
  }

  /**
   * 设置当前使用阵容
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async setActiveFormation(characterId: string, formationId: string): Promise<XResult<void>> {
    return this.withTransaction(async (session) => {
      // 先将所有阵容设为非激活状态
      const deactivateResult = await this.updateMany(
        { characterId },
        { $set: { isActive: false } },
        session
      );

      if (XResultUtils.isFailure(deactivateResult)) {
        return deactivateResult as any;
      }

      // 再将指定阵容设为激活状态
      const activateResult = await this.updateOne(
        { formationId },
        {
          $set: {
            isActive: true,
            lastUsedTime: Date.now()
          }
        },
        session
      );

      if (XResultUtils.isFailure(activateResult)) {
        return activateResult as any;
      }

      return XResultUtils.ok(undefined);
    });
  }

  /**
   * 分页查询阵容
   * 使用BaseRepository的findWithPagination方法，自动优化性能和并行查询
   */
  async findWithPagination(
    query: GetFormationListDto,
    characterId?: string
  ): Promise<XResult<PaginationResult<TeamFormationsDocument>>> {
    const filter: FilterQuery<TeamFormationsDocument> = {};

    // 添加角色ID过滤
    if (characterId) {
      filter.characterId = characterId;
    }
    if (query.characterId) {
      filter.characterId = query.characterId;
    }

    // 添加其他过滤条件
    if (query.formationType !== undefined) {
      filter.formationType = query.formationType;
    }
    if (query.activeOnly) {
      filter.isActive = true;
    }

    // 排序
    const sortField = query.sortBy || 'createTime';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sort: any = { [sortField]: sortOrder };

    // 分页
    const page = query.page || 1;
    const limit = query.limit || 20;

    return super.findWithPagination({
      page,
      limit,
      filter,
      sort,
      lean: false // 返回Mongoose文档
    });
  }

  /**
   * 分页查询阵容（Lean查询优化版本）
   * 性能提升60%+，适用于列表展示场景
   */
  async findWithPaginationLean(
    query: GetFormationListDto,
    characterId?: string
  ): Promise<XResult<PaginationResult<any>>> {
    const filter: FilterQuery<TeamFormationsDocument> = {};

    // 添加角色ID过滤
    if (characterId) {
      filter.characterId = characterId;
    }
    if (query.characterId) {
      filter.characterId = query.characterId;
    }

    // 添加其他过滤条件
    if (query.formationType !== undefined) {
      filter.formationType = query.formationType;
    }
    if (query.activeOnly) {
      filter.isActive = true;
    }

    const sortField = query.sortBy || 'createTime';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
    const sort: any = { [sortField]: sortOrder };

    const page = query.page || 1;
    const limit = query.limit || 20;

    return super.findWithPagination({
      page,
      limit,
      filter,
      sort,
      select: 'characterId serverId formationType isActive createTime lastUsedTime currTeamFormationId',
      lean: true
    });
  }

  /**
   * 根据阵型查找阵容
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByFormationType(
    formationType: FormationType,
    characterId?: string,
    limit: number = 10
  ): Promise<XResult<TeamFormationsDocument[]>> {
    const filter: FilterQuery<TeamFormationsDocument> = { formationType };

    if (characterId) {
      filter.characterId = characterId;
    }

    return this.findMany(filter, {
      sort: { lastUsedTime: -1, createTime: -1 },
      limit
    });
  }

  /**
   * 根据阵型查找阵容（Lean查询优化版本）
   */
  async findByFormationTypeLean(
    formationType: FormationType,
    characterId?: string,
    limit: number = 10
  ): Promise<XResult<any[]>> {
    const filter: FilterQuery<TeamFormationsDocument> = { formationType };

    if (characterId) {
      filter.characterId = characterId;
    }

    return this.findManyLean(filter, {
      sort: { lastUsedTime: -1, createTime: -1 },
      limit,
      select: 'characterId serverId formationType isActive createTime lastUsedTime'
    });
  }

  /**
   * 检查球员是否在阵容中
   * 使用BaseRepository的findMany方法优化性能
   */
  async findFormationsWithHero(heroId: string, characterId?: string): Promise<XResult<TeamFormationsDocument[]>> {
    const filter: FilterQuery<TeamFormationsDocument> = {
      $or: [
        { 'heros.heroId': heroId },
        { 'substitutes.heroId': heroId }
      ]
    };

    if (characterId) {
      filter.characterId = characterId;
    }

    return this.findMany(filter);
  }

  /**
   * 检查球员是否在阵容中（Lean查询优化版本）
   */
  async findFormationsWithHeroLean(heroId: string, characterId?: string): Promise<XResult<any[]>> {
    const filter: FilterQuery<TeamFormationsDocument> = {
      $or: [
        { 'heros.heroId': heroId },
        { 'substitutes.heroId': heroId }
      ]
    };

    if (characterId) {
      filter.characterId = characterId;
    }

    return this.findManyLean(filter, {
      select: 'characterId serverId formationType isActive currTeamFormationId'
    });
  }

  /**
   * 获取角色阵容统计
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getCharacterFormationStats(characterId: string): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: { characterId }
      },
      {
        $group: {
          _id: null,
          totalFormations: { $sum: 1 },
          activeFormations: {
            $sum: { $cond: ['$isActive', 1, 0] }
          },
          totalMatches: { $sum: '$matchesPlayed' },
          totalWins: { $sum: '$wins' },
          totalDraws: { $sum: '$draws' },
          totalLosses: { $sum: '$losses' },
          totalGoalsFor: { $sum: '$goalsFor' },
          totalGoalsAgainst: { $sum: '$goalsAgainst' },
          formationTypes: { $push: '$formationType' },
          averageRating: { $avg: '$stats.averageRating' },
          maxRating: { $max: '$stats.totalRating' }
        }
      }
    ];

    const aggregateResult = await this.aggregate(pipeline);
    if (XResultUtils.isFailure(aggregateResult)) {
      return aggregateResult;
    }

    const result = aggregateResult.data[0] || {
      totalFormations: 0,
      activeFormations: 0,
      totalMatches: 0,
      totalWins: 0,
      totalDraws: 0,
      totalLosses: 0,
      totalGoalsFor: 0,
      totalGoalsAgainst: 0,
      formationTypes: [],
      averageRating: 0,
      maxRating: 0
    };

    // 计算胜率
    result.winRate = result.totalMatches > 0
      ? Math.round((result.totalWins / result.totalMatches) * 100)
      : 0;

    // 计算净胜球
    result.goalDifference = result.totalGoalsFor - result.totalGoalsAgainst;

    return XResultUtils.ok(result);
  }

  /**
   * 批量更新阵容
   * 使用BaseRepository的批量操作方法优化性能
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<TeamFormationsDocument>;
    update: UpdateQuery<TeamFormationsDocument>;
  }>): Promise<XResult<any>> {
    // 如果只有一个更新操作，使用updateMany优化
    if (updates.length === 1) {
      const { filter, update } = updates[0];
      return this.updateMany(filter, update);
    }

    // 多个更新操作，使用事务确保一致性
    return this.withTransaction(async (session) => {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
          session
        }
      }));

      const result = await this.mongooseModel.bulkWrite(bulkOps, { session });
      return XResultUtils.ok({ success: true, result });
    });
  }

  /**
   * 复制阵容
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async copyFormation(sourceFormationId: string, newFormationData: Partial<TeamFormationsDocument>): Promise<XResult<TeamFormationsDocument>> {
    return this.withTransaction(async (session) => {
      // 查找源阵容
      const sourceResult = await this.findById(sourceFormationId);
      if (XResultUtils.isFailure(sourceResult)) {
        return sourceResult;
      }

      if (!sourceResult.data) {
        return XResultUtils.error('源阵容不存在', 'SOURCE_FORMATION_NOT_FOUND');
      }

      const sourceFormation = sourceResult.data;

      // 创建新阵容数据
      const newFormationDoc = {
        ...sourceFormation.toObject(),
        _id: undefined,
        formationId: undefined,
        isActive: false,
        isDefault: false,
        createTime: Date.now(),
        lastUsedTime: 0,
        lastModifiedTime: Date.now(),
        matchesPlayed: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        ...newFormationData
      };

      // 创建新阵容
      const createResult = await this.createOne(newFormationDoc, session);
      return createResult;
    });
  }

  /**
   * 检查阵容名称是否存在
   * 使用BaseRepository的exists方法优化性能
   */
  async existsByName(name: string, characterId: string, excludeFormationId?: string): Promise<XResult<boolean>> {
    const filter: FilterQuery<TeamFormationsDocument> = {
      name,
      characterId
    };

    if (excludeFormationId) {
      filter.formationId = { $ne: excludeFormationId };
    }

    return this.exists(filter);
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加阵型特定的验证规则
   */
  protected validateData(data: Partial<TeamFormationsDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    // 验证阵型数据结构
    if (data.teamFormations && Array.isArray(data.teamFormations)) {
      for (const formation of data.teamFormations) {
        if (!formation.uid) {
          return XResultUtils.error('阵型UID不能为空', 'FORMATION_UID_REQUIRED');
        }
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对阵型数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByCharacterId': 300,        // 角色阵型列表缓存5分钟
      'findOneByCharacterId': 600,     // 单个阵型缓存10分钟
      'findFormation': 600,            // 阵型详情缓存10分钟
      'findActiveFormation': 180,      // 活跃阵型缓存3分钟
      'findByFormationType': 300,      // 按类型查询缓存5分钟
      'getCharacterFormationStats': 600, // 统计信息缓存10分钟
      'existsByName': 120,             // 阵型名存在性检查缓存2分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  /**
   * 重写数据清理方法，移除阵型特定的敏感字段
   */
  protected sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = super.sanitizeData(data);

    // 移除阵型特定的敏感字段
    const formationSensitiveFields = ['internalNotes', 'debugInfo'];
    formationSensitiveFields.forEach(field => {
      if (field in sanitized) {
        delete sanitized[field];
      }
    });

    return sanitized;
  }
}
