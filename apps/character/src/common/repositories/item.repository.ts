import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Item, ItemDocument } from '../schemas/item.schema';
import { ItemType, ItemQuality } from '../dto/item.dto';
import { GetItemListDto } from '../dto/item.dto';

@Injectable()
export class ItemRepository {
  private readonly logger = new Logger(ItemRepository.name);

  constructor(
    // 配置表通过GameConfigFacade访问，不需要直接注入
    @InjectModel(Item.name) private itemModel: Model<ItemDocument>,
  ) {}

  // ==================== 物品配置相关 ====================

  /**
   * 根据配置ID查找物品定义
   */
  async findItemDefinitionById(itemId: number): Promise<any | null> {
    try {
      // TODO: 通过GameConfigFacade获取配置
      return null;
    } catch (error) {
      this.logger.error(`根据配置ID查找物品定义失败: ${itemId}`, error);
      throw error;
    }
  }

  /**
   * 搜索物品定义
   */
  async searchItemDefinitions(
    keyword: string,
    type?: number,
    quality?: number,
    limit: number = 20
  ): Promise<any[]> {
    try {
      const filter: any = {
        $or: [
          { name: { $regex: keyword, $options: 'i' } },
          { description: { $regex: keyword, $options: 'i' } }
        ],
        isActive: true
      };

      if (type !== undefined) {
        filter.type = type;
      }
      if (quality !== undefined) {
        filter.quality = quality;
      }

      // TODO: 通过GameConfigFacade获取配置
      return [];
    } catch (error) {
      this.logger.error('搜索物品定义失败', error);
      throw error;
    }
  }

  // ==================== 物品实例相关 ====================

  /**
   * 创建物品（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   */
  async create(itemData: Partial<Item>): Promise<ItemDocument> {
    try {
      // 🛡️ 安全措施：白名单字段过滤
      const safeData = this.sanitizeItemData(itemData);
      const item = new this.itemModel({
        ...safeData,
        obtainTime: (safeData as any).obtainTime || Date.now(),
      });
      return await item.save();
    } catch (error) {
      this.logger.error('创建物品失败', error);
      throw error;
    }
  }

  /**
   * 🛡️ 数据清理：仅允许安全的物品字段
   */
  private sanitizeItemData(data: any): Partial<Item> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'item', 'resId2Uid', 'obtainTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 根据ID查找物品
   */
  async findItemById(itemId: string): Promise<ItemDocument | null> {
    try {
      return await this.itemModel.findOne({ itemId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找物品失败: ${itemId}`, error);
      throw error;
    }
  }

  /**
   * 更新物品
   */
  async updateItem(
    itemId: string, 
    updateData: UpdateQuery<ItemDocument>,
    options?: QueryOptions
  ): Promise<ItemDocument | null> {
    try {
      return await this.itemModel.findOneAndUpdate(
        { itemId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新物品失败: ${itemId}`, error);
      throw error;
    }
  }

  /**
   * 删除物品
   */
  async deleteItem(itemId: string): Promise<ItemDocument | null> {
    try {
      const result = await this.itemModel.findOneAndDelete({ itemId }).exec();
      return result as unknown as ItemDocument | null;
    } catch (error) {
      this.logger.error(`删除物品失败: ${itemId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找物品（支持单个或列表查询）
   * @param characterId 角色ID
   * @param query 查询条件（可选，用于列表查询）
   */
  async findByCharacterId(
    characterId: string,
    query?: GetItemListDto
  ): Promise<ItemDocument | ItemDocument[] | null> {
    try {
      // 如果有查询条件，返回列表
      if (query) {
        return await this.findItemsByCharacterId(query);
      }
      // 否则返回单个文档
      return await this.itemModel.findOne({ characterId }).exec();
    } catch (error) {
      this.logger.error(`根据角色ID查找物品失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新物品
   */
  async update(itemId: string, updateData: any): Promise<ItemDocument | null> {
    try {
      return await this.itemModel.findByIdAndUpdate(itemId, updateData, { new: true }).exec();
    } catch (error) {
      this.logger.error(`更新物品失败: ${itemId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找物品列表
   */
  async findItemsByCharacterId(query: GetItemListDto): Promise<ItemDocument[]> {
    try {
      const filter: FilterQuery<ItemDocument> = { characterId: query.characterId };

      if (query.equippedOnly) {
        filter.slot = { $gt: 0 };
      }

      if (query.usableOnly) {
        // 简化处理，实际需要关联查询
      }

      const sortField = query.sortBy || 'obtainTime';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortField]: sortOrder };

      return await this.itemModel
        .find(filter)
        .sort(sort)
        .exec();
    } catch (error) {
      this.logger.error(`根据角色ID查找物品列表失败: ${query.characterId}`, error);
      throw error;
    }
  }

  /**
   * 根据配置ID查找物品实例
   */
  async findItemsByConfigId(configId: number): Promise<ItemDocument[]> {
    try {
      return await this.itemModel.find({ configId }).exec();
    } catch (error) {
      this.logger.error(`根据配置ID查找物品实例失败: ${configId}`, error);
      throw error;
    }
  }

  /**
   * 批量更新物品
   */
  async bulkUpdateItems(updates: Array<{
    filter: FilterQuery<ItemDocument>;
    update: UpdateQuery<ItemDocument>;
  }>): Promise<any> {
    try {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
        }
      }));

      return await this.itemModel.bulkWrite(bulkOps);
    } catch (error) {
      this.logger.error('批量更新物品失败', error);
      throw error;
    }
  }

  /**
   * 获取角色物品统计
   */
  async getCharacterItemStats(characterId: string): Promise<any> {
    try {
      const stats = await this.itemModel.aggregate([
        {
          $match: { characterId }
        },
        {
          $group: {
            _id: null,
            totalItems: { $sum: 1 },
            totalQuantity: { $sum: '$quantity' },
            equippedItems: {
              $sum: {
                $cond: [{ $gt: ['$slot', 0] }, 1, 0]
              }
            },
            expiredItems: {
              $sum: {
                $cond: [
                  { 
                    $and: [
                      { $gt: ['$expireTime', 0] },
                      { $lt: ['$expireTime', Date.now()] }
                    ]
                  }, 
                  1, 
                  0
                ]
              }
            }
          }
        }
      ]);

      return stats[0] || {
        totalItems: 0,
        totalQuantity: 0,
        equippedItems: 0,
        expiredItems: 0
      };
    } catch (error) {
      this.logger.error('获取角色物品统计失败', error);
      throw error;
    }
  }
}
