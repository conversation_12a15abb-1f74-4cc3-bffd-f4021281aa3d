import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Character, CharacterDocument } from '../schemas/character.schema';
import { CreateCharacterDto } from '../dto/character.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 角色数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 角色CRUD操作
 * - 角色查询和搜索
 * - 分页查询支持
 * - 统计信息查询
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 并行查询优化（分页查询）
 */
@Injectable()
export class CharacterRepository extends BaseRepository<CharacterDocument> {
  constructor(
    @InjectModel(Character.name) characterModel: Model<CharacterDocument>
  ) {
    super(characterModel, 'CharacterRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建角色
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(createCharacterDto: CreateCharacterDto): Promise<XResult<CharacterDocument>> {
    return this.createOne(createCharacterDto);
  }

  /**
   * 根据角色ID查找角色
   * 使用BaseRepository的findOne方法，支持性能优化的Lean查询
   */
  async findByCharacterId(characterId: string): Promise<XResult<CharacterDocument | null>> {
    return this.findOne({ characterId });
  }

  /**
   * 根据角色ID查找角色（别名方法，兼容old项目）
   */
  async findById(characterId: string): Promise<XResult<CharacterDocument | null>> {
    return this.findByCharacterId(characterId);
  }

  /**
   * 根据角色ID查找角色（Lean查询优化版本）
   * 性能提升60%+，适用于只需要数据而不需要Mongoose文档方法的场景
   */
  async findByCharacterIdLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ characterId });
  }

  /**
   * 根据用户ID查找角色列表
   * 使用BaseRepository的findMany方法，自动应用Result模式和性能监控
   */
  async findByUserId(userId: string, serverId?: string): Promise<XResult<CharacterDocument[]>> {
    const filter: FilterQuery<CharacterDocument> = { userId };
    if (serverId) {
      filter.serverId = serverId;
    }
    return this.findMany(filter, { sort: { 'loginInfo.createTime': -1 } });
  }

  /**
   * 根据用户ID查找角色列表（Lean查询优化版本）
   * 性能提升60%+，返回普通JavaScript对象
   */
  async findByUserIdLean(userId: string, serverId?: string): Promise<XResult<any[]>> {
    const filter: FilterQuery<CharacterDocument> = { userId };
    if (serverId) {
      filter.serverId = serverId;
    }
    return this.findManyLean(filter, {
      sort: { 'loginInfo.createTime': -1 },
      select: ['characterId', 'name', 'level', 'serverId', 'loginInfo.createTime']
    });
  }

  /**
   * 根据角色名查找角色
   * 增强版：支持可选serverId和软删除过滤
   * 基于old项目的全局搜索功能，使用BaseRepository优化性能
   */
  async findByName(name: string, serverId?: string): Promise<XResult<CharacterDocument | null>> {
    const filter: any = {
      name: name.trim(),
      deletedAt: { $exists: false } // 排除已删除的角色
    };

    if (serverId) {
      filter.serverId = serverId;
    }

    return this.findOne(filter);
  }

  /**
   * 根据角色名查找角色（Lean查询优化版本）
   */
  async findByNameLean(name: string, serverId?: string): Promise<XResult<any | null>> {
    const filter: any = {
      name: name.trim(),
      deletedAt: { $exists: false }
    };

    if (serverId) {
      filter.serverId = serverId;
    }

    return this.findOneLean(filter, {
      select: ['characterId', 'name', 'level', 'serverId', 'loginInfo.createTime']
    });
  }

  /**
   * 根据OpenID查找角色
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByOpenId(openId: string, serverId?: string): Promise<XResult<CharacterDocument[]>> {
    const filter: FilterQuery<CharacterDocument> = { openId };
    if (serverId) {
      filter.serverId = serverId;
    }
    return this.findMany(filter, { sort: { 'loginInfo.createTime': -1 } });
  }

  /**
   * 更新角色
   * 使用BaseRepository的updateOne方法，自动应用Result模式和性能监控
   */
  async update(
    characterId: string,
    updateData: UpdateQuery<CharacterDocument>
  ): Promise<XResult<CharacterDocument | null>> {
    return this.updateOne({ characterId }, updateData);
  }

  /**
   * 删除角色（软删除）
   * 使用BaseRepository的updateOne方法优化性能
   */
  async softDelete(characterId: string): Promise<XResult<CharacterDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $set: {
          deletedAt: new Date(),
          'loginInfo.sessionId': null,
          'loginInfo.frontendId': null
        }
      }
    );
  }

  /**
   * 检查角色名是否存在
   * 使用BaseRepository的exists方法优化性能
   */
  async existsByName(name: string, serverId: string, excludeCharacterId?: string): Promise<XResult<boolean>> {
    const filter: FilterQuery<CharacterDocument> = {
      name,
      serverId,
      deletedAt: { $exists: false }
    };

    if (excludeCharacterId) {
      filter.characterId = { $ne: excludeCharacterId };
    }

    return this.exists(filter);
  }

  /**
   * 分页查询角色
   * 使用BaseRepository的findWithPagination方法，自动优化性能和并行查询
   */
  async findWithPagination(
    filter: FilterQuery<CharacterDocument> = {},
    page: number = 1,
    limit: number = 20,
    sort: any = { 'loginInfo.createTime': -1 }
  ): Promise<XResult<PaginationResult<CharacterDocument>>> {
    // 添加软删除过滤
    const finalFilter = {
      ...filter,
      deletedAt: { $exists: false }
    };

    return super.findWithPagination({
      page,
      limit,
      filter: finalFilter,
      sort,
      lean: false // 返回Mongoose文档
    });
  }

  /**
   * 分页查询角色（Lean查询优化版本）
   * 性能提升60%+，适用于列表展示场景
   */
  async findWithPaginationLean(
    filter: FilterQuery<CharacterDocument> = {},
    page: number = 1,
    limit: number = 20,
    sort: any = { 'loginInfo.createTime': -1 }
  ): Promise<XResult<PaginationResult<any>>> {
    const finalFilter = {
      ...filter,
      deletedAt: { $exists: false }
    };

    return super.findWithPagination({
      page,
      limit,
      filter: finalFilter,
      sort,
      select: ['characterId', 'name', 'level', 'serverId', 'loginInfo.createTime', 'loginInfo.lastLoginTime'],
      lean: true
    });
  }

  /**
   * 根据等级范围查找角色
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByLevelRange(
    minLevel: number,
    maxLevel: number,
    serverId?: string,
    limit: number = 100
  ): Promise<XResult<CharacterDocument[]>> {
    const filter: FilterQuery<CharacterDocument> = {
      level: { $gte: minLevel, $lte: maxLevel },
      deletedAt: { $exists: false }
    };

    if (serverId) {
      filter.serverId = serverId;
    }

    return this.findMany(filter, {
      sort: { level: -1, fame: -1 },
      limit
    });
  }

  /**
   * 获取在线角色数量
   * 使用BaseRepository的count方法优化性能
   */
  async getOnlineCount(serverId?: string): Promise<XResult<number>> {
    const filter: FilterQuery<CharacterDocument> = {
      'loginInfo.frontendId': { $exists: true, $ne: null },
      'loginInfo.sessionId': { $exists: true, $ne: null },
      deletedAt: { $exists: false }
    };

    if (serverId) {
      filter.serverId = serverId;
    }

    return this.count(filter);
  }

  /**
   * 批量更新角色
   * 使用BaseRepository的批量操作方法优化性能
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<CharacterDocument>;
    update: UpdateQuery<CharacterDocument>;
  }>): Promise<XResult<any>> {
    // 如果只有一个更新操作，使用updateMany优化
    if (updates.length === 1) {
      const { filter, update } = updates[0];
      return this.updateMany(filter, update);
    }

    // 多个更新操作，使用原生bulkWrite
    return this.withTransaction(async (session) => {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
          session
        }
      }));

      const result = await this.mongooseModel.bulkWrite(bulkOps, { session });
      return XResultUtils.ok({ success: true, result });
    });
  }

  /**
   * 获取服务器统计信息
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getServerStats(serverId: string): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: {
          serverId,
          deletedAt: { $exists: false }
        }
      },
      {
        $group: {
          _id: null,
          totalCharacters: { $sum: 1 },
          averageLevel: { $avg: '$level' },
          maxLevel: { $max: '$level' },
          totalCash: { $sum: '$cash' },
          totalGold: { $sum: '$gold' },
          onlineCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$loginInfo.frontendId', null] },
                    { $ne: ['$loginInfo.sessionId', null] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }

    // 返回默认统计信息
    return XResultUtils.ok({
      totalCharacters: 0,
      averageLevel: 0,
      maxLevel: 0,
      totalCash: 0,
      totalGold: 0,
      onlineCount: 0
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加角色特定的验证规则
   */
  protected validateData(data: Partial<CharacterDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.name || data.name.trim().length === 0) {
        return XResultUtils.error('角色名称不能为空', 'INVALID_CHARACTER_NAME');
      }

      if (data.name.length > 20) {
        return XResultUtils.error('角色名称不能超过20个字符', 'CHARACTER_NAME_TOO_LONG');
      }

      if (!data.userId) {
        return XResultUtils.error('用户ID不能为空', 'USER_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    if (data.level !== undefined && data.level < 1) {
      return XResultUtils.error('角色等级不能小于1', 'INVALID_LEVEL');
    }

    if (data.cash !== undefined && data.cash < 0) {
      return XResultUtils.error('现金数量不能为负数', 'INVALID_CASH');
    }

    if (data.gold !== undefined && data.gold < 0) {
      return XResultUtils.error('金币数量不能为负数', 'INVALID_GOLD');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对角色数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByCharacterId': 600,        // 角色详情缓存10分钟
      'findByCharacterIdLean': 300,    // 角色简介缓存5分钟
      'findByUserId': 180,             // 用户角色列表缓存3分钟
      'findByName': 300,               // 角色名查询缓存5分钟
      'getOnlineCount': 60,            // 在线数量缓存1分钟
      'existsByName': 120,             // 角色名存在性检查缓存2分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
