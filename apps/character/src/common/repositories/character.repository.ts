import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Character, CharacterDocument } from '../schemas/character.schema';
import { CreateCharacterDto } from '../dto/character.dto';

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class CharacterRepository {
  private readonly logger = new Logger(CharacterRepository.name);

  constructor(
    @InjectModel(Character.name) private characterModel: Model<CharacterDocument>,
  ) {}

  /**
   * 创建角色
   */
  async create(createCharacterDto: CreateCharacterDto): Promise<CharacterDocument> {
    try {
      const character = new this.characterModel(createCharacterDto);
      return await character.save();
    } catch (error) {
      this.logger.error('创建角色失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找角色
   */
  async findById(characterId: string): Promise<CharacterDocument | null> {
    try {
      return await this.characterModel.findOne({ characterId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找角色失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找角色（别名方法，兼容old项目）
   */
  async findByCharacterId(characterId: string): Promise<CharacterDocument | null> {
    return this.findById(characterId);
  }

  /**
   * 根据用户ID查找角色列表
   */
  async findByUserId(userId: string, serverId?: string): Promise<CharacterDocument[]> {
    try {
      const filter: FilterQuery<CharacterDocument> = { userId };
      if (serverId) {
        filter.serverId = serverId;
      }
      return await this.characterModel.find(filter).exec();
    } catch (error) {
      this.logger.error(`根据用户ID查找角色失败: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色名查找角色
   * 增强版：支持可选serverId和软删除过滤
   * 基于old项目的全局搜索功能
   */
  async findByName(name: string, serverId?: string): Promise<CharacterDocument | null> {
    try {
      const filter: any = {
        name: name.trim(),
        deletedAt: { $exists: false } // 排除已删除的角色
      };

      if (serverId) {
        filter.serverId = serverId;
      }

      return await this.characterModel.findOne(filter).exec();
    } catch (error) {
      this.logger.error(`根据角色名查找角色失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 根据OpenID查找角色
   */
  async findByOpenId(openId: string, serverId?: string): Promise<CharacterDocument[]> {
    try {
      const filter: FilterQuery<CharacterDocument> = { openId };
      if (serverId) {
        filter.serverId = serverId;
      }
      return await this.characterModel.find(filter).exec();
    } catch (error) {
      this.logger.error(`根据OpenID查找角色失败: ${openId}`, error);
      throw error;
    }
  }

  /**
   * 更新角色
   */
  async update(
    characterId: string, 
    updateData: UpdateQuery<CharacterDocument>,
    options?: QueryOptions
  ): Promise<CharacterDocument | null> {
    try {
      return await this.characterModel.findOneAndUpdate(
        { characterId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新角色失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 删除角色（软删除）
   */
  async softDelete(characterId: string): Promise<CharacterDocument | null> {
    try {
      return await this.characterModel.findOneAndUpdate(
        { characterId },
        { 
          $set: { 
            deletedAt: new Date(),
            'loginInfo.sessionId': null,
            'loginInfo.frontendId': null
          }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error(`软删除角色失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 检查角色名是否存在
   */
  async existsByName(name: string, serverId: string, excludeCharacterId?: string): Promise<boolean> {
    try {
      const filter: FilterQuery<CharacterDocument> = { 
        name, 
        serverId,
        deletedAt: { $exists: false }
      };
      
      if (excludeCharacterId) {
        filter.characterId = { $ne: excludeCharacterId };
      }

      const count = await this.characterModel.countDocuments(filter);
      return count > 0;
    } catch (error) {
      this.logger.error(`检查角色名是否存在失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 分页查询角色
   */
  async findWithPagination(
    filter: FilterQuery<CharacterDocument> = {},
    page: number = 1,
    limit: number = 20,
    sort: any = { 'loginInfo.createTime': -1 }
  ): Promise<PaginationResult<CharacterDocument>> {
    try {
      const skip = (page - 1) * limit;
      
      // 添加软删除过滤
      const finalFilter = {
        ...filter,
        deletedAt: { $exists: false }
      };

      const [data, total] = await Promise.all([
        this.characterModel
          .find(finalFilter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.characterModel.countDocuments(finalFilter)
      ]);

      const pages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error('分页查询角色失败', error);
      throw error;
    }
  }

  /**
   * 根据等级范围查找角色
   */
  async findByLevelRange(
    minLevel: number, 
    maxLevel: number, 
    serverId?: string,
    limit: number = 100
  ): Promise<CharacterDocument[]> {
    try {
      const filter: FilterQuery<CharacterDocument> = {
        level: { $gte: minLevel, $lte: maxLevel },
        deletedAt: { $exists: false }
      };
      
      if (serverId) {
        filter.serverId = serverId;
      }

      return await this.characterModel
        .find(filter)
        .sort({ level: -1, fame: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(`根据等级范围查找角色失败: ${minLevel}-${maxLevel}`, error);
      throw error;
    }
  }

  /**
   * 获取在线角色数量
   */
  async getOnlineCount(serverId?: string): Promise<number> {
    try {
      const filter: FilterQuery<CharacterDocument> = {
        'loginInfo.frontendId': { $exists: true, $ne: null },
        'loginInfo.sessionId': { $exists: true, $ne: null },
        deletedAt: { $exists: false }
      };
      
      if (serverId) {
        filter.serverId = serverId;
      }

      return await this.characterModel.countDocuments(filter);
    } catch (error) {
      this.logger.error('获取在线角色数量失败', error);
      throw error;
    }
  }

  /**
   * 批量更新角色
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<CharacterDocument>;
    update: UpdateQuery<CharacterDocument>;
  }>): Promise<any> {
    try {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any, // 类型转换
        }
      }));

      return await this.characterModel.bulkWrite(bulkOps);
    } catch (error) {
      this.logger.error('批量更新角色失败', error);
      throw error;
    }
  }

  /**
   * 获取服务器统计信息
   */
  async getServerStats(serverId: string): Promise<any> {
    try {
      const stats = await this.characterModel.aggregate([
        {
          $match: {
            serverId,
            deletedAt: { $exists: false }
          }
        },
        {
          $group: {
            _id: null,
            totalCharacters: { $sum: 1 },
            averageLevel: { $avg: '$level' },
            maxLevel: { $max: '$level' },
            totalCash: { $sum: '$cash' },
            totalGold: { $sum: '$gold' },
            onlineCount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $ne: ['$loginInfo.frontendId', null] },
                      { $ne: ['$loginInfo.sessionId', null] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      return stats[0] || {
        totalCharacters: 0,
        averageLevel: 0,
        maxLevel: 0,
        totalCash: 0,
        totalGold: 0,
        onlineCount: 0
      };
    } catch (error) {
      this.logger.error(`获取服务器统计信息失败: ${serverId}`, error);
      throw error;
    }
  }
}
