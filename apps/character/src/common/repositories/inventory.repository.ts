import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Inventory, InventoryDocument, BookMarkType, BookMarkEntry, InventoryItem } from '../schemas/inventory.schema';
import { AddItemToInventoryDto, GetInventoryListDto } from '../dto/inventory.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 背包数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 背包CRUD操作
 * - 物品管理（添加、移除、更新）
 * - 书签管理
 * - 背包查询和搜索
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 事务支持确保数据一致性
 */
@Injectable()
export class InventoryRepository extends BaseRepository<InventoryDocument> {
  constructor(
    @InjectModel(Inventory.name) inventoryModel: Model<InventoryDocument>
  ) {
    super(inventoryModel, 'InventoryRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建背包（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(inventoryData: Partial<Inventory>): Promise<XResult<InventoryDocument>> {
    // 🛡️ 安全措施：白名单字段过滤
    const safeData = this.sanitizeInventoryData(inventoryData);
    return this.createOne(safeData);
  }

  /**
   * 根据背包ID查找背包
   * 使用BaseRepository的findOne方法优化性能
   */
  async findById(inventoryId: string): Promise<XResult<InventoryDocument | null>> {
    return this.findOne({ inventoryId });
  }

  /**
   * 根据角色ID查找背包（背包是单一文档，不按type分类）
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByCharacterId(characterId: string): Promise<XResult<InventoryDocument | null>> {
    return this.findOne({ characterId });
  }

  /**
   * 根据角色ID查找背包（Lean查询优化版本）
   * 性能提升60%+，适用于只需要基础信息的场景
   */
  async findByCharacterIdLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean(
      { characterId },
      { select: 'characterId serverId bag.items bag.capacity bag.usedSlots createTime updateTime' }
    );
  }

  /**
   * 更新背包
   * 使用BaseRepository的updateOne方法优化性能
   */
  async update(
    characterId: string,
    updateData: UpdateQuery<InventoryDocument>
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne({ characterId }, updateData);
  }

  /**
   * 根据角色ID删除背包
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByCharacterId(characterId: string): Promise<XResult<boolean>> {
    return this.deleteOne({ characterId });
  }

  // ========== 物品操作方法 ==========

  /**
   * 添加物品到背包
   * 使用BaseRepository的updateOne方法优化性能
   */
  async addItem(
    characterId: string,
    item: InventoryItem
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $push: { 'bag.items': item },
        $inc: { 'bag.usedSlots': 1 }
      }
    );
  }

  /**
   * 批量添加物品到背包
   * 使用BaseRepository的updateOne方法优化性能
   */
  async addItems(
    characterId: string,
    items: InventoryItem[]
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $push: { 'bag.items': { $each: items } },
        $inc: { 'bag.usedSlots': items.length }
      }
    );
  }

  /**
   * 从背包移除物品
   * 使用BaseRepository的updateOne方法优化性能
   */
  async removeItem(
    characterId: string,
    itemId: string
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $pull: { 'bag.items': { itemId } },
        $inc: { 'bag.usedSlots': -1 }
      }
    );
  }

  /**
   * 批量移除物品
   * 使用BaseRepository的updateOne方法优化性能
   */
  async removeItems(
    characterId: string,
    itemIds: string[]
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $pull: { 'bag.items': { itemId: { $in: itemIds } } },
        $inc: { 'bag.usedSlots': -itemIds.length }
      }
    );
  }

  /**
   * 更新背包中的物品
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateItem(
    characterId: string,
    itemId: string,
    updateData: Partial<InventoryItem>
  ): Promise<XResult<InventoryDocument | null>> {
    const updateFields: any = {};
    Object.keys(updateData).forEach(key => {
      updateFields[`bag.items.$.${key}`] = updateData[key];
    });

    return this.updateOne(
      { characterId, 'bag.items.itemId': itemId },
      { $set: updateFields }
    );
  }

  /**
   * 查找背包中的物品
   * 使用BaseRepository的findOne方法优化性能
   */
  async findItemInInventory(
    characterId: string,
    itemId: string
  ): Promise<XResult<InventoryItem | null>> {
    const result = await this.findOne(
      { characterId, 'bag.items.itemId': itemId },
      { select: { 'bag.items.$': 1 } }
    );

    if (XResultUtils.isSuccess(result) && result.data?.bag?.items?.[0]) {
      return XResultUtils.ok(result.data.bag.items[0]);
    }

    return XResultUtils.ok(null);
  }

  /**
   * 查找背包中的物品（Lean查询优化版本）
   */
  async findItemInInventoryLean(
    characterId: string,
    itemId: string
  ): Promise<XResult<any | null>> {
    const result = await this.findOneLean(
      { characterId, 'bag.items.itemId': itemId },
      { select: { 'bag.items.$': 1 } }
    );

    if (XResultUtils.isSuccess(result) && result.data?.bag?.items?.[0]) {
      return XResultUtils.ok(result.data.bag.items[0]);
    }

    return XResultUtils.ok(null);
  }

  /**
   * 查找背包中指定配置ID的物品
   * 使用BaseRepository的findOne方法优化性能
   */
  async findItemsByConfigId(
    characterId: string,
    configId: number
  ): Promise<XResult<InventoryItem[]>> {
    const result = await this.findOne(
      { characterId },
      { select: { 'bag.items': { $elemMatch: { configId } } } }
    );

    if (XResultUtils.isSuccess(result) && result.data?.bag?.items) {
      return XResultUtils.ok(result.data.bag.items);
    }

    return XResultUtils.ok([]);
  }

  /**
   * 扩展背包容量
   * 使用BaseRepository的updateOne方法优化性能
   */
  async expandCapacity(
    characterId: string,
    additionalSlots: number
  ): Promise<XResult<InventoryDocument | null>> {
    return this.updateOne(
      { characterId },
      {
        $inc: {
          'bag.capacity': additionalSlots,
          'bag.expandCount': 1
        }
      }
    );
  }

  /**
   * 检查背包容量是否足够
   * 使用BaseRepository的findOne方法优化性能
   */
  async checkCapacity(
    characterId: string,
    requiredSlots: number = 1
  ): Promise<XResult<boolean>> {
    const result = await this.findOneLean(
      { characterId },
      { select: 'bag.capacity bag.usedSlots' }
    );

    if (XResultUtils.isFailure(result) || !result.data) {
      return XResultUtils.error('背包不存在', 'INVENTORY_NOT_FOUND');
    }

    const availableSlots = result.data.bag.capacity - result.data.bag.usedSlots;
    return XResultUtils.ok(availableSlots >= requiredSlots);
  }

  // ========== 背包管理方法 ==========

  /**
   * 整理背包
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async sortInventory(
    characterId: string,
    sortBy: string = 'slot',
    sortOrder: string = 'asc'
  ): Promise<XResult<InventoryDocument | null>> {
    return this.withTransaction(async (session) => {
      // 查找背包
      const inventoryResult = await this.findByCharacterId(characterId);
      if (XResultUtils.isFailure(inventoryResult)) {
        return inventoryResult;
      }

      if (!inventoryResult.data) {
        return XResultUtils.error('背包不存在', 'INVENTORY_NOT_FOUND');
      }

      const inventory = inventoryResult.data;

      // 对物品进行排序
      const sortedItems = inventory.bag.items.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (sortBy) {
          case 'configId':
            aValue = a.configId;
            bValue = b.configId;
            break;
          case 'quantity':
            aValue = a.quantity;
            bValue = b.quantity;
            break;
          case 'obtainTime':
            aValue = a.obtainTime;
            bValue = b.obtainTime;
            break;
          default:
            aValue = a.slot;
            bValue = b.slot;
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        }
        return aValue - bValue;
      });

      // 重新分配槽位
      sortedItems.forEach((item, index) => {
        item.slot = index + 1;
      });

      // 更新背包
      const updateResult = await this.updateOne(
        { characterId },
        {
          'bag.items': sortedItems,
          sortBy,
          sortOrder,
          lastSortTime: Date.now()
        },
        session
      );

      return updateResult;
    });
  }

  /**
   * 清理过期物品
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async cleanExpiredItems(characterId: string): Promise<XResult<number>> {
    return this.withTransaction(async (session) => {
      const now = Date.now();
      const filter: FilterQuery<InventoryDocument> = { characterId };

      // 清理过期物品
      const updateResult = await this.updateOne(
        filter,
        {
          $pull: {
            'bag.items': {
              expireTime: { $gt: 0, $lt: now }
            }
          },
          $set: {
            lastCleanTime: now
          }
        },
        session
      );

      if (XResultUtils.isFailure(updateResult)) {
        return updateResult as any;
      }

      // 重新计算已使用槽位
      const inventoryResult = await this.findOne(filter, { session });
      if (XResultUtils.isSuccess(inventoryResult) && inventoryResult.data) {
        await this.updateOne(
          { _id: inventoryResult.data._id },
          { 'bag.usedSlots': inventoryResult.data.bag.items.length },
          session
        );
      }

      return XResultUtils.ok(1); // 返回处理的背包数量
    });
  }

  // ========== 统计和聚合方法 ==========

  /**
   * 获取背包统计信息
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getInventoryStats(characterId: string): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: { characterId }
      },
      {
        $project: {
          capacity: '$bag.capacity',
          usedSlots: '$bag.usedSlots',
          totalItems: { $size: '$bag.items' },
          expiredItems: {
            $size: {
              $filter: {
                input: '$bag.items',
                cond: {
                  $and: [
                    { $gt: ['$$this.expireTime', 0] },
                    { $lt: ['$$this.expireTime', Date.now()] }
                  ]
                }
              }
            }
          }
        }
      }
    ];

    const aggregateResult = await this.aggregate(pipeline);
    if (XResultUtils.isFailure(aggregateResult)) {
      return aggregateResult;
    }

    const stats = aggregateResult.data[0] || {
      capacity: 0,
      usedSlots: 0,
      totalItems: 0,
      expiredItems: 0
    };

    // 计算使用率
    stats.usageRate = stats.capacity > 0 ? (stats.usedSlots / stats.capacity) * 100 : 0;

    return XResultUtils.ok(stats);
  }

  /**
   * 批量更新背包
   * 使用BaseRepository的批量操作方法优化性能
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<InventoryDocument>;
    update: UpdateQuery<InventoryDocument>;
  }>): Promise<XResult<any>> {
    // 如果只有一个更新操作，使用updateMany优化
    if (updates.length === 1) {
      const { filter, update } = updates[0];
      return this.updateMany(filter, update);
    }

    // 多个更新操作，使用事务确保一致性
    return this.withTransaction(async (session) => {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
          session
        }
      }));

      const result = await this.mongooseModel.bulkWrite(bulkOps, { session });
      return XResultUtils.ok({ success: true, result });
    });
  }

  /**
   * 检查背包是否存在
   * 使用BaseRepository的exists方法优化性能
   */
  async inventoryExists(characterId: string): Promise<XResult<boolean>> {
    return this.exists({ characterId });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加背包特定的验证规则
   */
  protected validateData(data: Partial<InventoryDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    // 验证背包容量
    if (data.bag?.capacity !== undefined && data.bag.capacity < 0) {
      return XResultUtils.error('背包容量不能为负数', 'INVALID_CAPACITY');
    }

    // 验证已使用槽位
    if (data.bag?.usedSlots !== undefined && data.bag.usedSlots < 0) {
      return XResultUtils.error('已使用槽位不能为负数', 'INVALID_USED_SLOTS');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对背包数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByCharacterId': 300,          // 角色背包缓存5分钟
      'findItemInInventory': 600,        // 物品查询缓存10分钟
      'checkCapacity': 120,              // 容量检查缓存2分钟
      'getInventoryStats': 300,          // 统计信息缓存5分钟
      'inventoryExists': 600,            // 存在性检查缓存10分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  /**
   * 重写数据清理方法，移除背包特定的敏感字段
   */
  protected sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = super.sanitizeData(data);

    // 移除背包特定的敏感字段
    const inventorySensitiveFields = ['internalNotes', 'debugInfo', 'adminFlags'];
    inventorySensitiveFields.forEach(field => {
      if (field in sanitized) {
        delete sanitized[field];
      }
    });

    return sanitized;
  }

  // ========== 私有辅助方法 ==========
  /**
   * 🛡️ 数据清理：仅允许安全的背包字段
   */
  private sanitizeInventoryData(data: any): Partial<Inventory> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'bag', 'itemUidToBookMarkId',
      'createTime', 'updateTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }
}
