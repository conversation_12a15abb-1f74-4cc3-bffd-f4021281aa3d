import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Inventory, InventoryDocument, BookMarkType, BookMarkEntry, InventoryType, InventoryItem } from '../schemas/inventory.schema';
import { AddItemToInventoryDto, GetInventoryListDto } from '../dto/inventory.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 背包数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 背包CRUD操作
 * - 物品管理（添加、移除、更新）
 * - 书签管理
 * - 背包查询和搜索
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 事务支持确保数据一致性
 */
@Injectable()
export class InventoryRepository extends BaseRepository<InventoryDocument> {
  constructor(
    @InjectModel(Inventory.name) inventoryModel: Model<InventoryDocument>
  ) {
    super(inventoryModel, 'InventoryRepository');
  }

  /**
   * 创建背包（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   */
  async create(inventoryData: Partial<Inventory>): Promise<XResult<InventoryDocument>> {
    try {
      // 🛡️ 安全措施：白名单字段过滤
      const safeData = this.sanitizeInventoryData(inventoryData);
      const inventory = new this.inventoryModel(safeData);
      return await inventory.save();
    } catch (error) {
      this.logger.error('创建背包失败', error);
      throw error;
    }
  }

  /**
   * 🛡️ 数据清理：仅允许安全的背包字段
   */
  private sanitizeInventoryData(data: any): Partial<Inventory> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'bag', 'itemUidToBookMarkId',
      'createTime', 'updateTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 根据ID查找背包
   */
  async findById(inventoryId: string): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOne({ inventoryId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找背包失败: ${inventoryId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID和类型查找背包
   */
  async findByCharacterAndType(
    characterId: string, 
    type: InventoryType
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOne({ characterId, type }).exec();
    } catch (error) {
      this.logger.error(`根据角色ID和类型查找背包失败: ${characterId}, ${type}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找背包（支持单个或多个）
   * @param characterId 角色ID
   * @param findOne 是否只查找一个（默认false返回数组）
   */
  async findByCharacterId(
    characterId: string,
    findOne: boolean = false
  ): Promise<XResult<InventoryDocument[] | InventoryDocument | null>> {
    try {
      if (findOne) {
        return await this.inventoryModel.findOne({ characterId }).exec();
      } else {
        return await this.inventoryModel.find({ characterId }).sort({ type: 1 }).exec();
      }
    } catch (error) {
      this.logger.error(`根据角色ID查找背包失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新背包
   */
  async update(
    inventoryId: string, 
    updateData: UpdateQuery<InventoryDocument>,
    options?: QueryOptions
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOneAndUpdate(
        { inventoryId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新背包失败: ${inventoryId}`, error);
      throw error;
    }
  }

  /**
   * 删除背包
   */
  async delete(inventoryId: string): Promise<XResult<InventoryDocument | null>> {
    try {
      const result = await this.inventoryModel.findOneAndDelete({ inventoryId }).exec();
      return result as unknown as InventoryDocument | null;
    } catch (error) {
      this.logger.error(`删除背包失败: ${inventoryId}`, error);
      throw error;
    }
  }

  /**
   * 添加物品到背包
   */
  async addItem(
    characterId: string,
    type: InventoryType,
    item: InventoryItem
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOneAndUpdate(
        { characterId, type },
        { 
          $push: { items: item },
          $inc: { usedSlots: 1 }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error('添加物品到背包失败', error);
      throw error;
    }
  }

  /**
   * 从背包移除物品
   */
  async removeItem(
    characterId: string,
    type: InventoryType,
    itemId: string
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOneAndUpdate(
        { characterId, type },
        { 
          $pull: { items: { itemId } },
          $inc: { usedSlots: -1 }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error('从背包移除物品失败', error);
      throw error;
    }
  }

  /**
   * 更新背包中的物品
   */
  async updateItem(
    characterId: string,
    type: InventoryType,
    itemId: string,
    updateData: Partial<InventoryItem>
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      const updateFields: any = {};
      Object.keys(updateData).forEach(key => {
        updateFields[`items.$.${key}`] = updateData[key];
      });

      return await this.inventoryModel.findOneAndUpdate(
        { characterId, type, 'items.itemId': itemId },
        { $set: updateFields },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error('更新背包物品失败', error);
      throw error;
    }
  }

  /**
   * 查找背包中的物品
   */
  async findItemInInventory(
    characterId: string,
    type: InventoryType,
    itemId: string
  ): Promise<XResult<InventoryItem | null>> {
    try {
      const inventory = await this.inventoryModel.findOne(
        { characterId, type, 'items.itemId': itemId },
        { 'items.$': 1 }
      ).exec();

      return inventory?.items?.[0] || null;
    } catch (error) {
      this.logger.error('查找背包物品失败', error);
      throw error;
    }
  }

  /**
   * 查找背包中指定配置ID的物品
   */
  async findItemsByConfigId(
    characterId: string,
    type: InventoryType,
    configId: number
  ): Promise<XResult<InventoryItem[]>> {
    try {
      const inventory = await this.inventoryModel.findOne(
        { characterId, type },
        { items: { $elemMatch: { configId } } }
      ).exec();

      return inventory?.items || [];
    } catch (error) {
      this.logger.error('查找指定配置ID的物品失败', error);
      throw error;
    }
  }

  /**
   * 扩展背包容量
   */
  async expandCapacity(
    characterId: string,
    type: InventoryType,
    additionalSlots: number
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      return await this.inventoryModel.findOneAndUpdate(
        { characterId, type },
        { 
          $inc: { 
            capacity: additionalSlots,
            expandCount: 1
          }
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error('扩展背包容量失败', error);
      throw error;
    }
  }

  /**
   * 整理背包
   */
  async sortInventory(
    characterId: string,
    type: InventoryType,
    sortBy: string = 'slot',
    sortOrder: string = 'asc'
  ): Promise<XResult<InventoryDocument | null>> {
    try {
      const inventory = await this.findByCharacterAndType(characterId, type);
      if (!inventory) return null;

      // 对物品进行排序
      const sortedItems = inventory.items.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (sortBy) {
          case 'configId':
            aValue = a.configId;
            bValue = b.configId;
            break;
          case 'quantity':
            aValue = a.quantity;
            bValue = b.quantity;
            break;
          case 'obtainTime':
            aValue = a.obtainTime;
            bValue = b.obtainTime;
            break;
          default:
            aValue = a.slot;
            bValue = b.slot;
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        }
        return aValue - bValue;
      });

      // 重新分配槽位
      sortedItems.forEach((item, index) => {
        item.slot = index + 1;
      });

      return await this.inventoryModel.findOneAndUpdate(
        { characterId, type },
        { 
          items: sortedItems,
          sortBy,
          sortOrder,
          lastSortTime: Date.now()
        },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error('整理背包失败', error);
      throw error;
    }
  }

  /**
   * 清理过期物品
   */
  async cleanExpiredItems(
    characterId: string,
    type?: InventoryType
  ): Promise<XResult<number>> {
    try {
      const now = Date.now();
      const filter: FilterQuery<InventoryDocument> = { characterId };
      
      if (type !== undefined) {
        filter.type = type;
      }

      const result = await this.inventoryModel.updateMany(
        filter,
        { 
          $pull: { 
            items: { 
              expireTime: { $gt: 0, $lt: now }
            }
          },
          $set: {
            lastCleanTime: now
          }
        }
      );

      // 重新计算已使用槽位
      const inventories = await this.inventoryModel.find(filter);
      for (const inventory of inventories) {
        await this.inventoryModel.updateOne(
          { _id: inventory._id },
          { usedSlots: inventory.items.length }
        );
      }

      return result.modifiedCount;
    } catch (error) {
      this.logger.error('清理过期物品失败', error);
      throw error;
    }
  }

  /**
   * 获取背包统计信息
   */
  async getInventoryStats(characterId: string): Promise<XResult<any>> {
    try {
      const stats = await this.inventoryModel.aggregate([
        {
          $match: { characterId }
        },
        {
          $group: {
            _id: '$type',
            capacity: { $first: '$capacity' },
            usedSlots: { $first: '$usedSlots' },
            totalItems: { $sum: { $size: '$items' } },
            expiredItems: {
              $sum: {
                $size: {
                  $filter: {
                    input: '$items',
                    cond: {
                      $and: [
                        { $gt: ['$$this.expireTime', 0] },
                        { $lt: ['$$this.expireTime', Date.now()] }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      ]);

      return stats.reduce((acc, stat) => {
        acc[stat._id] = {
          capacity: stat.capacity,
          usedSlots: stat.usedSlots,
          totalItems: stat.totalItems,
          expiredItems: stat.expiredItems,
          usageRate: stat.capacity > 0 ? (stat.usedSlots / stat.capacity) * 100 : 0
        };
        return acc;
      }, {});
    } catch (error) {
      this.logger.error('获取背包统计失败', error);
      throw error;
    }
  }

  /**
   * 批量更新背包
   */
  async bulkUpdate(updates: Array<{
    filter: FilterQuery<InventoryDocument>;
    update: UpdateQuery<InventoryDocument>;
  }>): Promise<XResult<any>> {
    try {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
        }
      }));

      return await this.inventoryModel.bulkWrite(bulkOps);
    } catch (error) {
      this.logger.error('批量更新背包失败', error);
      throw error;
    }
  }

  /**
   * 检查背包是否存在
   */
  async exists(characterId: string, type: InventoryType): Promise<XResult<boolean>> {
    try {
      const count = await this.inventoryModel.countDocuments({ characterId, type });
      return count > 0;
    } catch (error) {
      this.logger.error('检查背包是否存在失败', error);
      throw error;
    }
  }
}
