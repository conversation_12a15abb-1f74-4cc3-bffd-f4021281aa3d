import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tactic, TacticDocument } from '../schemas/tactic.schema';

/**
 * 战术数据访问层
 */
@Injectable()
export class TacticRepository {
  private readonly logger = new Logger(TacticRepository.name);

  constructor(
    @InjectModel(Tactic.name)
    private readonly tacticModel: Model<TacticDocument>,
  ) {}

  /**
   * 根据ID查找战术
   */
  async findById(id: string): Promise<TacticDocument | null> {
    try {
      return await this.tacticModel.findById(id).exec();
    } catch (error) {
      this.logger.error(`查找战术失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找战术（支持单个或多个）
   * @param characterId 角色ID
   * @param findOne 是否只查找一个（默认false返回数组）
   */
  async findByCharacterId(
    characterId: string,
    findOne: boolean = false
  ): Promise<TacticDocument[] | TacticDocument | null> {
    try {
      if (findOne) {
        return await this.tacticModel.findOne({ characterId }).exec();
      } else {
        return await this.tacticModel.find({ characterId }).exec();
      }
    } catch (error) {
      this.logger.error(`查找角色战术失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID和战术键名查找战术
   */
  async findByCharacterIdAndTacticKey(characterId: string, tacticKey: string): Promise<TacticDocument | null> {
    try {
      return await this.tacticModel.findOne({ characterId, tacticKey }).exec();
    } catch (error) {
      this.logger.error(`查找角色战术失败: ${characterId} - ${tacticKey}`, error);
      throw error;
    }
  }

  /**
   * 查找角色的激活战术
   */
  async findActiveTacticsByCharacterId(characterId: string): Promise<TacticDocument[]> {
    try {
      return await this.tacticModel.find({ characterId, isActive: true }).exec();
    } catch (error) {
      this.logger.error(`查找激活战术失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 创建战术（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   */
  async create(tacticData: Partial<Tactic>): Promise<TacticDocument> {
    try {
      // 🛡️ 安全措施：白名单字段过滤
      const safeData = this.sanitizeTacticData(tacticData);
      const tactic = new this.tacticModel(safeData);
      return await tactic.save();
    } catch (error) {
      this.logger.error('创建战术失败', error);
      throw error;
    }
  }

  /**
   * 🛡️ 数据清理：仅允许安全的战术字段
   */
  private sanitizeTacticData(data: any): Partial<Tactic> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'tactics', 'formationTactics',
      'combinations', 'heroBonusCache', 'createTime', 'updateTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }

  /**
   * 更新战术
   */
  async update(id: string, updateData: Partial<Tactic>): Promise<TacticDocument | null> {
    try {
      return await this.tacticModel.findByIdAndUpdate(
        id,
        { ...updateData, updateTime: Date.now() },
        { new: true }
      ).exec();
    } catch (error) {
      this.logger.error(`更新战术失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 批量更新战术状态
   */
  async batchUpdateStatus(characterId: string, filter: any, updateData: Partial<Tactic>): Promise<any> {
    try {
      return await this.tacticModel.updateMany(
        { characterId, ...filter },
        { ...updateData, updateTime: Date.now() }
      ).exec();
    } catch (error) {
      this.logger.error(`批量更新战术状态失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 删除战术
   */
  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.tacticModel.findByIdAndDelete(id).exec();
      return !!result;
    } catch (error) {
      this.logger.error(`删除战术失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 统计角色战术数量
   */
  async countByCharacterId(characterId: string): Promise<number> {
    try {
      return await this.tacticModel.countDocuments({ characterId }).exec();
    } catch (error) {
      this.logger.error(`统计战术数量失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 查找需要升级的战术
   */
  async findUpgradeableTactics(characterId: string): Promise<TacticDocument[]> {
    try {
      return await this.tacticModel.find({
        characterId,
        isUnlocked: true,
        level: { $lt: 10 },
        experience: { $gte: 100 } // 假设100经验可以升级
      }).exec();
    } catch (error) {
      this.logger.error(`查找可升级战术失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新战术使用统计
   */
  async updateUsageStats(id: string, isVictory: boolean): Promise<TacticDocument | null> {
    try {
      const updateData: any = {
        $inc: {
          usageCount: 1,
          totalMatches: 1,
        },
        lastUsedTime: Date.now(),
        updateTime: Date.now(),
      };

      if (isVictory) {
        updateData.$inc.victories = 1;
      }

      return await this.tacticModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
    } catch (error) {
      this.logger.error(`更新战术使用统计失败: ${id}`, error);
      throw error;
    }
  }
}
