import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Tactic, TacticDocument } from '../schemas/tactic.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 战术数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 战术CRUD操作
 * - 战术配置管理
 * - 战术查询和搜索
 * - 批量操作支持
 * - 战术统计分析
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 事务支持确保数据一致性
 */
@Injectable()
export class TacticRepository extends BaseRepository<TacticDocument> {
  constructor(
    @InjectModel(Tactic.name) tacticModel: Model<TacticDocument>
  ) {
    super(tacticModel, 'TacticRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 根据战术ID查找战术
   * 使用BaseRepository的findById方法优化性能
   */
  async findById(id: string): Promise<XResult<TacticDocument | null>> {
    return super.findById(id);
  }

  /**
   * 根据战术ID查找战术（Lean查询优化版本）
   * 性能提升60%+，适用于只需要基础信息的场景
   */
  async findByIdLean(id: string): Promise<XResult<any | null>> {
    return super.findByIdLean(id, {
      select: ['characterId', 'serverId', 'tactics', 'formationTactics', 'createTime']
    });
  }

  /**
   * 根据角色ID查找战术列表
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByCharacterId(characterId: string): Promise<XResult<TacticDocument[]>> {
    return this.findMany({ characterId }, { sort: { createTime: -1 } });
  }

  /**
   * 根据角色ID查找单个战术
   * 使用BaseRepository的findOne方法优化性能
   */
  async findOneByCharacterId(characterId: string): Promise<XResult<TacticDocument | null>> {
    return this.findOne({ characterId });
  }

  /**
   * 根据角色ID查找战术列表（Lean查询优化版本）
   * 性能提升60%+，适用于列表展示场景
   */
  async findByCharacterIdLean(characterId: string): Promise<XResult<any[]>> {
    return this.findManyLean(
      { characterId },
      {
        sort: { createTime: -1 },
        select: 'characterId serverId tacticName tacticType isActive createTime updateTime'
      }
    );
  }

  /**
   * 根据角色ID和战术键名查找战术
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByCharacterIdAndTacticKey(characterId: string, tacticKey: string): Promise<XResult<TacticDocument | null>> {
    return this.findOne({ characterId, tacticKey });
  }

  /**
   * 根据角色ID和战术键名查找战术（Lean查询优化版本）
   */
  async findByCharacterIdAndTacticKeyLean(characterId: string, tacticKey: string): Promise<XResult<any | null>> {
    return this.findOneLean(
      { characterId, tacticKey },
      { select: 'characterId serverId tacticKey tacticName isActive' }
    );
  }

  /**
   * 查找角色的激活战术
   * 使用BaseRepository的findMany方法优化性能
   */
  async findActiveTacticsByCharacterId(characterId: string): Promise<XResult<TacticDocument[]>> {
    return this.findMany({ characterId, isActive: true }, { sort: { createTime: -1 } });
  }

  /**
   * 查找角色的激活战术（Lean查询优化版本）
   * 性能提升60%+，适用于列表展示场景
   */
  async findActiveTacticsByCharacterIdLean(characterId: string): Promise<XResult<any[]>> {
    return this.findManyLean(
      { characterId, isActive: true },
      {
        sort: { createTime: -1 },
        select: 'characterId serverId tacticKey tacticName tacticType isActive'
      }
    );
  }

  /**
   * 创建战术（内部方法，仅供系统初始化使用）
   * 🔒 安全注意：此方法仅应在受信任的内部流程中使用
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(tacticData: Partial<Tactic>): Promise<XResult<TacticDocument>> {
    // 🛡️ 安全措施：白名单字段过滤
    const safeData = this.sanitizeTacticData(tacticData);
    return this.createOne(safeData);
  }

  /**
   * 批量创建战术
   * 使用BaseRepository的createMany方法优化性能
   */
  async createMany(tacticsData: Partial<Tactic>[]): Promise<XResult<TacticDocument[]>> {
    const sanitizedData = tacticsData.map(data => this.sanitizeTacticData(data));
    return super.createMany(sanitizedData);
  }

  /**
   * 更新战术
   * 使用BaseRepository的updateById方法优化性能
   */
  async update(id: string, updateData: Partial<Tactic>): Promise<XResult<TacticDocument | null>> {
    const finalUpdateData = { ...updateData, updateTime: Date.now() };
    return this.updateById(id, finalUpdateData);
  }

  /**
   * 根据角色ID和战术键名更新战术
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateByCharacterIdAndTacticKey(
    characterId: string,
    tacticKey: string,
    updateData: Partial<Tactic>
  ): Promise<XResult<TacticDocument | null>> {
    const finalUpdateData = { ...updateData, updateTime: Date.now() };
    return this.updateOne({ characterId, tacticKey }, finalUpdateData);
  }

  /**
   * 批量更新战术状态
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchUpdateStatus(characterId: string, filter: any, updateData: Partial<Tactic>): Promise<XResult<any>> {
    const finalUpdateData = { ...updateData, updateTime: Date.now() };
    return this.updateMany({ characterId, ...filter }, finalUpdateData);
  }

  /**
   * 删除战术
   * 使用BaseRepository的deleteById方法优化性能
   */
  async delete(id: string): Promise<XResult<boolean>> {
    return this.deleteById(id);
  }

  /**
   * 根据角色ID删除战术
   * 使用BaseRepository的deleteMany方法优化性能
   */
  async deleteByCharacterId(characterId: string): Promise<XResult<any>> {
    return this.deleteMany({ characterId });
  }

  /**
   * 根据角色ID和战术键名删除战术
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByCharacterIdAndTacticKey(characterId: string, tacticKey: string): Promise<XResult<boolean>> {
    return this.deleteOne({ characterId, tacticKey });
  }

  // ========== 统计和查询方法 ==========

  /**
   * 统计角色战术数量
   * 使用BaseRepository的count方法优化性能
   */
  async countByCharacterId(characterId: string): Promise<XResult<number>> {
    return this.count({ characterId });
  }

  /**
   * 统计角色激活战术数量
   * 使用BaseRepository的count方法优化性能
   */
  async countActiveTacticsByCharacterId(characterId: string): Promise<XResult<number>> {
    return this.count({ characterId, isActive: true });
  }

  /**
   * 查找需要升级的战术
   * 使用BaseRepository的findMany方法优化性能
   */
  async findUpgradeableTactics(characterId: string): Promise<XResult<TacticDocument[]>> {
    return this.findMany({
      characterId,
      isUnlocked: true,
      level: { $lt: 10 },
      experience: { $gte: 100 } // 假设100经验可以升级
    });
  }

  /**
   * 查找需要升级的战术（Lean查询优化版本）
   */
  async findUpgradeableTacticsLean(characterId: string): Promise<XResult<any[]>> {
    return this.findManyLean(
      {
        characterId,
        isUnlocked: true,
        level: { $lt: 10 },
        experience: { $gte: 100 }
      },
      {
        select: 'characterId tacticKey tacticName level experience isUnlocked'
      }
    );
  }

  /**
   * 更新战术使用统计
   * 使用BaseRepository的updateById方法优化性能
   */
  async updateUsageStats(id: string, isVictory: boolean): Promise<XResult<TacticDocument | null>> {
    const updateData: any = {
      $inc: {
        usageCount: 1,
        totalMatches: 1,
      },
      lastUsedTime: Date.now(),
      updateTime: Date.now(),
    };

    if (isVictory) {
      updateData.$inc.victories = 1;
    }

    return this.updateById(id, updateData);
  }

  /**
   * 获取战术统计信息
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getTacticStats(characterId: string): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: { characterId }
      },
      {
        $group: {
          _id: null,
          totalTactics: { $sum: 1 },
          activeTactics: {
            $sum: { $cond: ['$isActive', 1, 0] }
          },
          unlockedTactics: {
            $sum: { $cond: ['$isUnlocked', 1, 0] }
          },
          totalMatches: { $sum: '$totalMatches' },
          totalVictories: { $sum: '$victories' },
          averageLevel: { $avg: '$level' },
          maxLevel: { $max: '$level' }
        }
      }
    ];

    const aggregateResult = await this.aggregate(pipeline);
    if (XResultUtils.isFailure(aggregateResult)) {
      return aggregateResult;
    }

    const result = aggregateResult.data[0] || {
      totalTactics: 0,
      activeTactics: 0,
      unlockedTactics: 0,
      totalMatches: 0,
      totalVictories: 0,
      averageLevel: 0,
      maxLevel: 0
    };

    // 计算胜率
    result.winRate = result.totalMatches > 0
      ? Math.round((result.totalVictories / result.totalMatches) * 100)
      : 0;

    return XResultUtils.ok(result);
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加战术特定的验证规则
   * 基于TacticDocument的实际字段定义进行验证
   */
  protected validateData(data: Partial<TacticDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      // 验证必需字段
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }

      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    // 验证战术数组中的单个战术
    if (data.tactics && Array.isArray(data.tactics)) {
      for (const tactic of data.tactics) {
        // 验证战术等级
        if (tactic.level !== undefined && (tactic.level < 1 || tactic.level > 10)) {
          return XResultUtils.error(`战术${tactic.tacticKey}等级必须在1-10之间`, 'INVALID_TACTIC_LEVEL');
        }

        // 验证经验值
        if (tactic.experience !== undefined && tactic.experience < 0) {
          return XResultUtils.error(`战术${tactic.tacticKey}经验值不能为负数`, 'INVALID_EXPERIENCE');
        }

        // 验证使用次数
        if (tactic.usageCount !== undefined && tactic.usageCount < 0) {
          return XResultUtils.error(`战术${tactic.tacticKey}使用次数不能为负数`, 'INVALID_USAGE_COUNT');
        }

        // 验证胜利次数不能超过总场次
        if (tactic.winCount !== undefined && tactic.totalMatches !== undefined &&
            tactic.winCount > tactic.totalMatches) {
          return XResultUtils.error(`战术${tactic.tacticKey}胜利次数不能超过总场次`, 'INVALID_WIN_COUNT');
        }

        // 验证必需的战术字段
        if (!tactic.tacticId) {
          return XResultUtils.error('战术ID不能为空', 'TACTIC_ID_REQUIRED');
        }

        if (!tactic.tacticKey) {
          return XResultUtils.error('战术键名不能为空', 'TACTIC_KEY_REQUIRED');
        }

        if (!tactic.name) {
          return XResultUtils.error('战术名称不能为空', 'TACTIC_NAME_REQUIRED');
        }
      }
    }

    // 验证阵容战术配置
    if (data.formationTactics && Array.isArray(data.formationTactics)) {
      for (const formationTactic of data.formationTactics) {
        if (!formationTactic.formationId) {
          return XResultUtils.error('阵容ID不能为空', 'FORMATION_ID_REQUIRED');
        }

        if (!formationTactic.attackTacticId) {
          return XResultUtils.error('进攻战术ID不能为空', 'ATTACK_TACTIC_ID_REQUIRED');
        }

        if (!formationTactic.defenseTacticId) {
          return XResultUtils.error('防守战术ID不能为空', 'DEFENSE_TACTIC_ID_REQUIRED');
        }
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对战术数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findById': 600,                           // 战术详情缓存10分钟
      'findByCharacterId': 300,                  // 角色战术列表缓存5分钟
      'findActiveTacticsByCharacterId': 180,     // 激活战术缓存3分钟
      'findByCharacterIdAndTacticKey': 600,      // 特定战术缓存10分钟
      'findUpgradeableTactics': 120,             // 可升级战术缓存2分钟
      'countByCharacterId': 300,                 // 数量统计缓存5分钟
      'getTacticStats': 600,                     // 统计信息缓存10分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  // ========== 私有辅助方法 ==========

  /**
   * 清理战术数据（白名单字段过滤）
   * 基于TacticDocument的实际字段定义
   */
  private sanitizeTacticData(data: any): Partial<Tactic> {
    const allowedFields = [
      'uid', 'characterId', 'serverId', 'tactics', 'formationTactics',
      'combinations', 'heroBonusCache', 'createTime', 'updateTime'
    ];

    const sanitized: any = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        sanitized[field] = data[field];
      }
    }

    return sanitized;
  }
}
