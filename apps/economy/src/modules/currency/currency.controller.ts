import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CurrencyService } from './currency.service';

import { InjectedContext } from '@libs/common/types';

@Controller()
export class CurrencyController {
  private readonly logger = new Logger(CurrencyController.name);

  constructor(private readonly currencyService: CurrencyService) {}

  /**
   * 货币转换
   */
  @MessagePattern('currency.convert')
  async convertCurrency(@Payload() payload: { fromCurrency: string; toCurrency: string; amount: number; injectedContext?: InjectedContext }) {
    this.logger.log(`货币转换: ${payload.amount} ${payload.fromCurrency} -> ${payload.toCurrency}`);
    const result = await this.currencyService.convertCurrency(
      payload.fromCurrency, 
      payload.toCurrency, 
      payload.amount
    );
    return {
      code: 0,
      message: '转换成功',
      data: result,
    };
  }

  /**
   * 获取汇率
   */
  @MessagePattern('currency.getRate')
  async getExchangeRate(@Payload() payload: { fromCurrency: string; toCurrency: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取汇率: ${payload.fromCurrency} -> ${payload.toCurrency}`);
    const rate = await this.currencyService.getExchangeRate(payload.fromCurrency, payload.toCurrency);
    return {
      code: 0,
      message: '获取成功',
      data: { rate },
    };
  }
}
