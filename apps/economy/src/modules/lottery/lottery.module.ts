import { Module } from '@nestjs/common';
import { LotteryController } from './lottery.controller';
import { LotteryService } from './lottery.service';

/**
 * 传统抽奖系统模块
 * 基于old项目Player.prototype.lotteryHero的功能实现
 *
 * 核心功能：
 * - 金币抽奖和代币抽奖
 * - 单抽和十连抽
 * - 权重随机算法
 * - 抽奖历史记录
 * - 成本检查和扣除
 * - 球员奖励发放
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MicroserviceKitModule已在app.module.ts中注册，可直接使用MicroserviceClientService
 */
@Module({
  controllers: [LotteryController],
  providers: [LotteryService],
  exports: [LotteryService],
})
export class LotteryModule {}
