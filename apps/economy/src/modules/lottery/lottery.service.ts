import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { GameConfigFacade } from '@libs/game-config';

/**
 * 传统抽奖系统服务
 * 基于old项目Player.prototype.lotteryHero的完整实现
 * 
 * 核心功能：
 * - 金币抽奖和代币抽奖
 * - 权重随机算法
 * - 单抽和十连抽
 * - 抽奖历史记录
 * - 成本检查和扣除
 */
@Injectable()
export class LotteryService {
  private readonly logger = new Logger(LotteryService.name);

  constructor(
    private readonly microserviceClient: MicroserviceClientService,
    private readonly gameConfig: GameConfigFacade,
  ) {}

  /**
   * 球员抽奖主方法
   * 严格基于old项目: Player.prototype.lotteryHero
   */
  async lotteryHero(characterId: string, type: number, times: number) {
    this.logger.log(`球员抽奖: ${characterId}, 类型: ${type}, 次数: ${times}`);

    // 参数校验（基于old项目的校验逻辑）
    if (type !== 1 && type !== 2) { // 1=金币抽奖, 2=代币抽奖
      return {
        code: -1,
        message: '抽奖类型参数错误',
        data: null,
      };
    }

    if (times !== 1 && times !== 10) { // 1=单抽, 10=十连抽
      return {
        code: -2,
        message: '抽奖次数参数错误',
        data: null,
      };
    }

    try {
      const results = [];
      let totalCost = 0;
      let costType = '';

      // 计算总费用（严格基于old项目逻辑）
      totalCost = await this.getLotteryCost(type, times);
      costType = type === 1 ? 'gold' : 'worldCoin'; // 金币或代币

      // 检查角色资源是否足够
      const hasEnoughResource = await this.checkCharacterResource(
        characterId,
        costType,
        totalCost
      );

      if (!hasEnoughResource.success) {
        return {
          code: -3,
          message: hasEnoughResource.message,
          data: {
            required: totalCost,
            current: hasEnoughResource.currentAmount,
            costType,
          },
        };
      }

      // 扣除费用
      const deductResult = await this.deductCharacterResource(
        characterId,
        costType,
        totalCost
      );

      if (!deductResult.success) {
        return {
          code: -4,
          message: '扣除费用失败',
          data: null,
        };
      }

      // 执行抽奖
      for (let i = 0; i < times; i++) {
        const lotteryResult = await this.lotteryOnce(type);
        if (lotteryResult) {
          results.push(lotteryResult);
        }
      }

      // 发放奖励（创建球员）
      const rewardResults = await this.giveHeroRewards(characterId, results);

      // 记录抽奖历史
      await this.recordLotteryHistory(characterId, {
        type,
        times,
        cost: totalCost,
        costType,
        results: results,
        timestamp: new Date(),
      });

      return {
        code: 0,
        message: '抽奖成功',
        data: {
          results: rewardResults,
          totalCost,
          costType,
          heroCount: results.length,
        },
      };

    } catch (error) {
      this.logger.error('球员抽奖失败', error);
      return {
        code: -5,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 单次抽奖
   * 严格基于old项目: Player.prototype.lotteryOnce
   */
  private async lotteryOnce(type: number): Promise<any> {
    try {
      // 获取抽奖配置（使用现有的配置表）
      // TODO: 需要确认正确的抽奖配置表名称
      // 暂时使用HeroPool配置表作为抽奖池
      const heroPoolConfigs = await this.gameConfig.heroPool.getAll();

      if (!heroPoolConfigs || heroPoolConfigs.length === 0) {
        throw new Error('抽奖配置获取失败');
      }

      // 计算总权重
      let totalWeight = 0;
      for (const config of heroPoolConfigs) {
        totalWeight += config.weight || 0;
      }

      if (totalWeight === 0) {
        throw new Error('抽奖权重配置错误');
      }

      // 生成随机数
      const randNum = Math.floor(Math.random() * totalWeight) + 1;
      let currentWeight = 0;

      // 权重随机算法（基于old项目的实现）
      for (const config of heroPoolConfigs) {
        currentWeight += config.weight || 0;

        if (currentWeight >= randNum) {
          this.logger.debug(`抽奖结果: ${config.resId}, 权重: ${currentWeight}, 随机数: ${randNum}`);

          return {
            heroId: config.resId,
            fameNum: 0, // TODO: 从其他配置表获取声望值
            type: type,
          };
        }
      }

      // 兜底：返回第一个配置
      if (heroPoolConfigs.length > 0) {
        const firstConfig = heroPoolConfigs[0];
        return {
          heroId: firstConfig.resId,
          fameNum: 0,
          type: type,
        };
      }

      return null;
    } catch (error) {
      this.logger.error('单次抽奖失败', error);
      return null;
    }
  }

  /**
   * 获取抽奖费用
   * 严格基于old项目: dataApi.allData.data["Setting"][38]["Value"] 等
   */
  private async getLotteryCost(type: number, times: number = 1): Promise<number> {
    try {
      // 基于old项目的费用获取逻辑
      // Setting[38] = 金币单抽, Setting[39] = 金币十连抽
      // Setting[36] = 代币单抽, Setting[37] = 代币十连抽

      if (type === 1) { // 金币抽奖
        if (times === 1) {
          // 金币单抽费用 - Setting[38]
          const config = await this.gameConfig.systemParam.get(38);
          return config?.parameter || 50000; // 默认5万金币
        } else if (times === 10) {
          // 金币十连抽费用 - Setting[39]
          const config = await this.gameConfig.systemParam.get(39);
          return config?.parameter || 450000; // 默认45万金币
        }
      } else if (type === 2) { // 代币抽奖
        if (times === 1) {
          // 代币单抽费用 - Setting[36]
          const config = await this.gameConfig.systemParam.get(36);
          return config?.parameter || 100; // 默认100代币
        } else if (times === 10) {
          // 代币十连抽费用 - Setting[37]
          const config = await this.gameConfig.systemParam.get(37);
          return config?.parameter || 900; // 默认900代币
        }
      }

      return 0;
    } catch (error) {
      this.logger.error('获取抽奖费用失败', error);
      // 返回默认费用
      if (type === 1) {
        return times === 1 ? 50000 : 450000;
      } else if (type === 2) {
        return times === 1 ? 100 : 900;
      }
      return 0;
    }
  }

  /**
   * 检查角色资源是否足够
   */
  private async checkCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number
  ): Promise<any> {
    try {
      // 调用Character服务获取角色信息
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',  // 修复：使用正确的MessagePattern
        {
          characterId,
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          message: '获取角色信息失败',
          currentAmount: 0,
        };
      }

      const currentAmount = result.data?.[resourceType] || 0;
      const hasEnough = currentAmount >= amount;

      return {
        success: hasEnough,
        currentAmount,
        required: amount,
        message: hasEnough ? '资源充足' : `${resourceType}不足`,
      };
    } catch (error) {
      this.logger.error('检查角色资源失败', error);
      return {
        success: false,
        message: '检查资源失败',
        currentAmount: 0,
      };
    }
  }

  /**
   * 扣除角色资源
   */
  private async deductCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number
  ): Promise<any> {
    try {
      // 调用Character服务扣除资源
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.subtract',
        {
          characterId,
          currencyDto: {
            currencyType: resourceType,
            amount,
            reason: 'lottery_hero',
          },
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          message: '扣除资源失败',
        };
      }

      return {
        success: true,
        newBalance: result.data?.newBalance || 0,
        message: '扣除资源成功',
      };
    } catch (error) {
      this.logger.error('扣除角色资源失败', error);
      return {
        success: false,
        message: '扣除资源失败',
      };
    }
  }

  /**
   * 发放球员奖励
   */
  private async giveHeroRewards(characterId: string, lotteryResults: any[]): Promise<any[]> {
    const rewardResults = [];

    for (const result of lotteryResults) {
      try {
        // 调用Hero服务创建球员
        const heroResult = await this.microserviceClient.call(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'hero.create',  // 修复：使用正确的MessagePattern
          {
            characterId,
            serverId: 'server_001',
            resId: result.heroId,  // 修复：使用正确的参数名
            name: `抽奖球员${result.heroId}`,  // 添加必需的name参数
            position: 'MC',  // 添加必需的position参数，使用正确的HeroPosition枚举值
            quality: 2,  // 添加必需的quality参数，默认品质2（绿色）
            level: 1,  // 添加必需的level参数
            obtainType: 1,  // 添加获得类型：抽奖
          }
        );

        if (heroResult && heroResult.code === 0) {
          rewardResults.push({
            heroId: result.heroId,
            heroUid: heroResult.data?.heroUid || '',
            fameNum: result.fameNum,
            success: true,
          });
        } else {
          rewardResults.push({
            heroId: result.heroId,
            heroUid: '',
            fameNum: result.fameNum,
            success: false,
            error: heroResult?.message || '创建球员失败',
          });
        }
      } catch (error) {
        this.logger.error(`创建球员失败: ${result.heroId}`, error);
        rewardResults.push({
          heroId: result.heroId,
          heroUid: '',
          fameNum: result.fameNum,
          success: false,
          error: error.message,
        });
      }
    }

    return rewardResults;
  }

  /**
   * 记录抽奖历史
   * 基于old项目: 抽奖历史记录存储逻辑
   */
  private async recordLotteryHistory(characterId: string, historyData: any): Promise<void> {
    try {
      // 构建历史记录数据（基于old项目结构）
      const historyRecord = {
        characterId,
        type: historyData.type, // 1=金币抽奖, 2=代币抽奖
        times: historyData.times, // 1=单抽, 10=十连抽
        cost: historyData.cost,
        costType: historyData.costType,
        results: historyData.results.map(result => ({
          heroId: result.heroId,
          fameNum: result.fameNum,
          type: result.type,
        })),
        timestamp: historyData.timestamp,
        serverId: 'server_001', // TODO: 从参数获取
        recordId: this.generateRecordId(),
      };

      // TODO: 调用Character服务存储抽奖历史
      // await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.saveLotteryHistory',
      //   historyRecord
      // );

      this.logger.log(`抽奖历史记录成功: ${characterId}, 记录ID: ${historyRecord.recordId}`);
    } catch (error) {
      this.logger.error('记录抽奖历史失败', error);
    }
  }

  /**
   * 获取抽奖配置
   * 基于old项目: LuckyHeroEx配置表和Setting配置表
   */
  async getLotteryConfig(): Promise<any> {
    try {
      // 基于old项目：获取抽奖池总权重
      const goldAmount = await this.getLotteryPoolAmount(1); // 金币抽奖池
      const tokenAmount = await this.getLotteryPoolAmount(2); // 代币抽奖池

      return {
        goldCostOnce: await this.getLotteryCost(1, 1),
        goldCostTen: await this.getLotteryCost(1, 10),
        tokenCostOnce: await this.getLotteryCost(2, 1),
        tokenCostTen: await this.getLotteryCost(2, 10),
        goldAmount, // 金币抽奖池总权重
        tokenAmount, // 代币抽奖池总权重
        poolInfo: await this.getLotteryPoolInfo(), // 抽奖池详细信息
      };
    } catch (error) {
      this.logger.error('获取抽奖配置失败', error);
      return {};
    }
  }

  /**
   * 获取抽奖历史
   * 基于old项目: 抽奖历史查询逻辑
   */
  async getLotteryHistory(characterId: string, page: number, limit: number): Promise<any> {
    try {
      // TODO: 调用Character服务查询抽奖历史
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getLotteryHistory',
      //   { characterId, page, limit }
      // );
      //
      // if (result.code === 0) {
      //   return this.formatLotteryHistory(result.data);
      // }

      // 暂时返回模拟的历史数据
      const mockRecords = this.generateMockLotteryHistory(characterId, page, limit);

      return {
        page,
        limit,
        total: mockRecords.total,
        records: mockRecords.records,
      };
    } catch (error) {
      this.logger.error('获取抽奖历史失败', error);
      return {
        page,
        limit,
        total: 0,
        records: [],
      };
    }
  }

  /**
   * 生成记录ID
   * 基于old项目: 唯一记录标识生成
   */
  private generateRecordId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `lottery_${timestamp}_${random}`;
  }

  /**
   * 格式化抽奖历史数据
   * 基于old项目: 历史数据格式化
   */
  private formatLotteryHistory(historyData: any): any {
    return {
      page: historyData.page || 1,
      limit: historyData.limit || 10,
      total: historyData.total || 0,
      records: (historyData.records || []).map(record => ({
        recordId: record.recordId,
        type: record.type,
        times: record.times,
        cost: record.cost,
        costType: record.costType,
        results: record.results,
        timestamp: record.timestamp,
        formattedTime: new Date(record.timestamp).toLocaleString('zh-CN'),
      })),
    };
  }

  /**
   * 生成模拟抽奖历史数据
   * 基于old项目: 抽奖历史数据结构
   */
  private generateMockLotteryHistory(characterId: string, page: number, limit: number): any {
    const mockRecords = [];
    const total = 25; // 模拟总记录数

    // 计算当前页的记录范围
    const startIndex = (page - 1) * limit;
    const endIndex = Math.min(startIndex + limit, total);

    for (let i = startIndex; i < endIndex; i++) {
      const isGoldLottery = i % 2 === 0;
      const isTenTimes = i % 3 === 0;

      mockRecords.push({
        recordId: `lottery_${Date.now() - i * 3600000}_${i}`,
        type: isGoldLottery ? 1 : 2, // 1=金币, 2=代币
        times: isTenTimes ? 10 : 1,
        cost: isGoldLottery ? (isTenTimes ? 450000 : 50000) : (isTenTimes ? 900 : 100),
        costType: isGoldLottery ? 'gold' : 'worldCoin',
        results: this.generateMockLotteryResults(isTenTimes ? 10 : 1),
        timestamp: new Date(Date.now() - i * 3600000), // 每小时一条记录
        formattedTime: new Date(Date.now() - i * 3600000).toLocaleString('zh-CN'),
      });
    }

    return {
      total,
      records: mockRecords,
    };
  }

  /**
   * 生成模拟抽奖结果
   * 基于old项目: 抽奖结果数据结构
   */
  private generateMockLotteryResults(count: number): any[] {
    const results = [];
    const heroIds = [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010];

    for (let i = 0; i < count; i++) {
      const randomHeroId = heroIds[Math.floor(Math.random() * heroIds.length)];
      results.push({
        heroId: randomHeroId,
        fameNum: Math.floor(Math.random() * 100) + 50, // 50-150声望
        type: Math.floor(Math.random() * 2) + 1, // 1或2
      });
    }

    return results;
  }

  /**
   * 获取抽奖池总权重
   * 基于old项目: LuckyHeroEx配置表的权重计算
   */
  private async getLotteryPoolAmount(type: number): Promise<number> {
    try {
      // TODO: 从配置表获取抽奖池权重
      // const configs = await this.gameConfig.luckyHeroEx?.getByType(type);
      // return configs.reduce((total, config) => total + config.weight, 0);

      // 暂时返回模拟权重
      return type === 1 ? 10000 : 8000; // 金币池权重更高
    } catch (error) {
      this.logger.error('获取抽奖池总权重失败', error);
      return 0;
    }
  }

  /**
   * 获取抽奖池详细信息
   * 基于old项目: LuckyHero和LuckyHeroEx配置表
   */
  private async getLotteryPoolInfo(): Promise<any> {
    try {
      // TODO: 从配置表获取抽奖池详细信息
      // const goldPool = await this.gameConfig.luckyHeroEx?.getByType(1);
      // const tokenPool = await this.gameConfig.luckyHeroEx?.getByType(2);

      // 暂时返回模拟抽奖池信息
      return {
        goldPool: {
          type: 1,
          totalWeight: 10000,
          items: [
            { heroId: 1001, weight: 1000, probability: '10%' },
            { heroId: 1002, weight: 800, probability: '8%' },
            { heroId: 1003, weight: 500, probability: '5%' },
          ],
        },
        tokenPool: {
          type: 2,
          totalWeight: 8000,
          items: [
            { heroId: 2001, weight: 2000, probability: '25%' },
            { heroId: 2002, weight: 1500, probability: '18.75%' },
            { heroId: 2003, weight: 1000, probability: '12.5%' },
          ],
        },
      };
    } catch (error) {
      this.logger.error('获取抽奖池详细信息失败', error);
      return {};
    }
  }
}
