import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Shop, ShopDocument, ShopType, RefreshCycle } from '../schemas/shop.schema';
import { GetShopListDto, GetPurchaseHistoryDto } from '../dto/shop.dto';

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class ShopRepository {
  private readonly logger = new Logger(ShopRepository.name);

  constructor(
    @InjectModel(Shop.name) private shopModel: Model<ShopDocument>,
  ) {}

  /**
   * 创建商店记录
   */
  async createShop(shopData: Partial<Shop>): Promise<ShopDocument> {
    try {
      const shop = new this.shopModel({
        ...shopData,
        everyDayReTime: Date.now(),
        weeklyReTime: Date.now(),
        monthlyReTime: Date.now(),
        seasonReTime: Date.now(),
      });
      return await shop.save();
    } catch (error) {
      this.logger.error('创建商店记录失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找商店记录
   */
  async findShopById(shopId: string): Promise<ShopDocument | null> {
    try {
      return await this.shopModel.findOne({ shopId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找商店记录失败: ${shopId}`, error);
      throw error;
    }
  }

  /**
   * 根据角色ID和商店类型查找商店记录
   */
  async findShopByCharacterAndType(characterId: string, shopType: ShopType): Promise<ShopDocument | null> {
    try {
      return await this.shopModel.findOne({ characterId, shopType }).exec();
    } catch (error) {
      this.logger.error(`根据角色ID和商店类型查找商店记录失败: ${characterId}, ${shopType}`, error);
      throw error;
    }
  }

  /**
   * 获取或创建商店记录
   */
  async getOrCreateShop(characterId: string, serverId: string, shopType: ShopType): Promise<ShopDocument> {
    try {
      let shop = await this.findShopByCharacterAndType(characterId, shopType);
      
      if (!shop) {
        const shopId = this.generateShopId(characterId, shopType);
        shop = await this.createShop({
          shopId,
          characterId,
          serverId,
          shopType,
        });
        this.logger.log(`创建新商店记录: ${shopId}`);
      }
      
      return shop;
    } catch (error) {
      this.logger.error('获取或创建商店记录失败', error);
      throw error;
    }
  }

  /**
   * 根据角色ID查找商店列表
   */
  async findShopsByCharacterId(query: GetShopListDto): Promise<ShopDocument[]> {
    try {
      const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };

      if (query.shopType !== undefined) {
        filter.shopType = query.shopType;
      }

      if (query.hasRecordsOnly) {
        filter.totalPurchases = { $gt: 0 };
      }

      const sortField = query.sortBy || 'lastPurchaseTime';
      const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortField]: sortOrder };

      return await this.shopModel
        .find(filter)
        .sort(sort)
        .exec();
    } catch (error) {
      this.logger.error(`根据角色ID查找商店列表失败: ${query.characterId}`, error);
      throw error;
    }
  }

  /**
   * 更新商店记录
   */
  async updateShop(
    shopId: string, 
    updateData: UpdateQuery<ShopDocument>,
    options?: QueryOptions
  ): Promise<ShopDocument | null> {
    try {
      return await this.shopModel.findOneAndUpdate(
        { shopId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新商店记录失败: ${shopId}`, error);
      throw error;
    }
  }

  /**
   * 删除商店记录
   */
  async deleteShop(shopId: string): Promise<ShopDocument | null> {
    try {
      const result = await this.shopModel.findOneAndDelete({ shopId }).exec();
      return result as unknown as ShopDocument | null;
    } catch (error) {
      this.logger.error(`删除商店记录失败: ${shopId}`, error);
      throw error;
    }
  }

  /**
   * 获取需要刷新的商店
   */
  async findShopsNeedingRefresh(cycle: RefreshCycle): Promise<ShopDocument[]> {
    try {
      const now = Date.now();
      let timeField: string;
      let intervalMs: number;

      switch (cycle) {
        case RefreshCycle.DAILY:
          timeField = 'everyDayReTime';
          intervalMs = 24 * 60 * 60 * 1000; // 1天
          break;
        case RefreshCycle.WEEKLY:
          timeField = 'weeklyReTime';
          intervalMs = 7 * 24 * 60 * 60 * 1000; // 7天
          break;
        case RefreshCycle.MONTHLY:
          timeField = 'monthlyReTime';
          intervalMs = 30 * 24 * 60 * 60 * 1000; // 30天
          break;
        case RefreshCycle.SEASON:
          timeField = 'seasonReTime';
          intervalMs = 90 * 24 * 60 * 60 * 1000; // 90天
          break;
        default:
          return [];
      }

      const filter: FilterQuery<ShopDocument> = {
        [timeField]: { $lte: now - intervalMs }
      };

      return await this.shopModel.find(filter).exec();
    } catch (error) {
      this.logger.error('获取需要刷新的商店失败', error);
      throw error;
    }
  }

  /**
   * 批量刷新商店限制
   */
  async batchRefreshShops(shopIds: string[], cycle: RefreshCycle): Promise<any> {
    try {
      const now = Date.now();
      let updateData: any = {};

      switch (cycle) {
        case RefreshCycle.DAILY:
          updateData = {
            everyDayBuy: [],
            everyDayReTime: now,
          };
          break;
        case RefreshCycle.WEEKLY:
          updateData = {
            weeklyBuy: [],
            weeklyReTime: now,
          };
          break;
        case RefreshCycle.MONTHLY:
          updateData = {
            monthlyBuy: [],
            monthlyReTime: now,
          };
          break;
        case RefreshCycle.SEASON:
          updateData = {
            seasonBuy: [],
            seasonReTime: now,
          };
          break;
      }

      return await this.shopModel.updateMany(
        { shopId: { $in: shopIds } },
        { $set: updateData }
      );
    } catch (error) {
      this.logger.error('批量刷新商店限制失败', error);
      throw error;
    }
  }

  /**
   * 获取购买历史
   */
  async getPurchaseHistory(query: GetPurchaseHistoryDto): Promise<PaginationResult<any>> {
    try {
      const filter: FilterQuery<ShopDocument> = { characterId: query.characterId };

      if (query.shopType !== undefined) {
        filter.shopType = query.shopType;
      }

      const page = query.page || 1;
      const limit = query.limit || 20;
      const skip = (page - 1) * limit;

      // 使用聚合查询获取购买历史
      const pipeline: any[] = [
        { $match: filter },
        { $unwind: '$purchaseHistory' },
      ];

      if (query.goodsId) {
        pipeline.push({ $match: { 'purchaseHistory.goodsId': query.goodsId } });
      }

      if (query.startTime || query.endTime) {
        const timeFilter: any = {};
        if (query.startTime) timeFilter.$gte = query.startTime;
        if (query.endTime) timeFilter.$lte = query.endTime;
        pipeline.push({ $match: { 'purchaseHistory.purchaseTime': timeFilter } });
      }

      pipeline.push(
        { $sort: { 'purchaseHistory.purchaseTime': -1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            shopId: 1,
            shopType: 1,
            characterId: 1,
            purchase: '$purchaseHistory',
          }
        }
      );

      const [data, totalResult] = await Promise.all([
        this.shopModel.aggregate(pipeline),
        this.shopModel.aggregate([
          ...pipeline.slice(0, -3), // 移除 sort, skip, limit, project
          { $count: 'total' }
        ])
      ]);

      const total = totalResult[0]?.total || 0;
      const pages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error('获取购买历史失败', error);
      throw error;
    }
  }

  /**
   * 获取商店统计
   */
  async getShopStats(characterId: string, days: number = 30, shopType?: ShopType): Promise<any> {
    try {
      const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);
      const filter: FilterQuery<ShopDocument> = { 
        characterId,
        lastPurchaseTime: { $gte: startTime }
      };

      if (shopType !== undefined) {
        filter.shopType = shopType;
      }

      const stats = await this.shopModel.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalShops: { $sum: 1 },
            totalSpent: { $sum: '$totalSpent' },
            totalPurchases: { $sum: '$totalPurchases' },
            avgSpentPerShop: { $avg: '$totalSpent' },
            avgPurchasesPerShop: { $avg: '$totalPurchases' },
            shopTypeDistribution: {
              $push: {
                shopType: '$shopType',
                spent: '$totalSpent',
                purchases: '$totalPurchases'
              }
            }
          }
        }
      ]);

      return stats[0] || {
        totalShops: 0,
        totalSpent: 0,
        totalPurchases: 0,
        avgSpentPerShop: 0,
        avgPurchasesPerShop: 0,
        shopTypeDistribution: []
      };
    } catch (error) {
      this.logger.error('获取商店统计失败', error);
      throw error;
    }
  }

  /**
   * 生成商店ID
   */
  private generateShopId(characterId: string, shopType: ShopType): string {
    return `shop_${characterId}_${shopType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
