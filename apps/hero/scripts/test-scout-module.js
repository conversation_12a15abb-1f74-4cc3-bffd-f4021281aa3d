/**
 * 球探系统测试模块
 * 基于match服务的成功测试模式
 * 
 * 测试内容：
 * 1. 获取球探数据
 * 2. 球探探索功能
 * 3. 球探搜索功能
 * 4. 球探签约功能
 * 5. 球探体力购买功能
 * 6. 球探RP值兑换功能
 */

const chalk = require('chalk');

class ScoutModuleTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
    this.scoutData = null;
  }

  /**
   * WebSocket调用封装 - 使用match服务的成功模式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取球探包信息
   */
  async testGetScoutData() {
    console.log(chalk.yellow('📋 测试获取球探包信息...'));

    try {
      const response = await this.callWebSocket('hero.scout.getPackInfo');

      // 使用标准的响应格式解析
      const success = response?.payload?.success && response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取球探包信息成功'));
        console.log(chalk.gray(`   球探RP: ${data.scoutRp || 0}`));
        console.log(chalk.gray(`   球探体力: ${data.scoutEnergy || 0}`));
        console.log(chalk.gray(`   球探包: ${data.scoutPack?.length || 0}个球员`));

        // 保存球探数据供后续测试使用
        this.scoutData = data;

        this.testResults.push({ test: 'getScoutData', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 获取球探包信息失败'));
        const errorMsg = response?.payload?.data?.message || response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'getScoutData', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取球探包信息异常: ${error.message}`));
      this.testResults.push({ test: 'getScoutData', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试球探探索功能
   */
  async testScoutExplore() {
    console.log(chalk.yellow('⚡ 测试球探探索功能...'));

    try {
      const response = await this.callWebSocket('hero.scout.explore', {
        type: 1 // 初级球探
      });

      // 使用标准的响应格式解析
      const success = response?.payload?.success && response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 球探探索成功'));
        console.log(chalk.gray(`   获得球员: ${data.hero?.configId || '无'}`));
        console.log(chalk.gray(`   剩余体力: ${data.scoutEnergy || 0}`));
        console.log(chalk.gray(`   球探包数量: ${data.scoutPackCount || 0}`));

        this.testResults.push({ test: 'scoutExplore', success: true });
        return true;
      } else {
        console.log(chalk.yellow('⚠️ 球探探索失败或体力不足'));
        const errorMsg = response?.payload?.data?.message || response?.payload?.message || '未知错误';
        console.log(chalk.yellow(`   信息: ${errorMsg}`));
        this.testResults.push({ test: 'scoutExplore', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 球探探索异常: ${error.message}`));
      this.testResults.push({ test: 'scoutExplore', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试球探搜索功能
   */
  async testScoutSearch() {
    console.log(chalk.yellow('🔎 测试球探搜索功能...'));

    try {
      const response = await this.callWebSocket('hero.scout.search', {
        scoutType: '1' // 初级球探
      });

      // 使用标准的响应格式解析
      const success = response?.payload?.success && response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 球探搜索成功'));
        console.log(chalk.gray(`   找到球员: ${data.foundHeroes?.length || data.heroes?.length || 0}个`));
        console.log(chalk.gray(`   消耗金币: ${data.searchCost || data.cost || 0}`));
        console.log(chalk.gray(`   球探类型: ${data.scoutType || 1}`));

        this.testResults.push({ test: 'scoutSearch', success: true });
        return true;
      } else {
        console.log(chalk.yellow('⚠️ 球探搜索失败或金币不足'));
        const errorMsg = response?.payload?.data?.message || response?.payload?.message || '未知错误';
        console.log(chalk.yellow(`   信息: ${errorMsg}`));
        this.testResults.push({ test: 'scoutSearch', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 球探搜索异常: ${error.message}`));
      this.testResults.push({ test: 'scoutSearch', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试球探体力购买功能
   */
  async testBuyScoutEnergy() {
    console.log(chalk.yellow('💎 测试球探体力购买功能...'));

    try {
      const response = await this.callWebSocket('hero.scout.buyEnergy', {
        amount: 50 // 购买50点体力
      });

      // 使用标准的响应格式解析
      const success = response?.payload?.success && response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 球探体力购买成功'));
        console.log(chalk.gray(`   增加体力: ${data.energyAdded || 0}`));
        console.log(chalk.gray(`   当前体力: ${data.scoutEnergy || 0}`));
        console.log(chalk.gray(`   消耗金币: ${data.cost || 0}`));

        this.testResults.push({ test: 'buyScoutEnergy', success: true });
        return true;
      } else {
        console.log(chalk.yellow('⚠️ 球探体力购买失败'));
        const errorMsg = response?.payload?.data?.message || response?.payload?.message || '未知错误';
        console.log(chalk.yellow(`   信息: ${errorMsg}`));
        this.testResults.push({ test: 'buyScoutEnergy', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 球探体力购买异常: ${error.message}`));
      this.testResults.push({ test: 'buyScoutEnergy', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试球探RP值兑换功能
   */
  async testExchangeScout() {
    console.log(chalk.yellow('🎁 测试球探RP值兑换功能...'));

    try {
      const response = await this.callWebSocket('hero.scout.exchangeScout', {
        type: 3 // 兑换类型（默认为3）
      });

      // 使用标准的响应格式解析
      const success = response?.payload?.success && response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 球探RP值兑换成功'));
        console.log(chalk.gray(`   兑换球员: ${data.heroId || data.resId || '无'}`));
        console.log(chalk.gray(`   消耗RP: ${data.rpCost || 0}`));
        console.log(chalk.gray(`   剩余RP: ${data.remainingRp || 0}`));

        this.testResults.push({ test: 'exchangeScout', success: true });
        return true;
      } else {
        console.log(chalk.yellow('⚠️ 球探RP值兑换失败'));
        const errorMsg = response?.payload?.data?.message || response?.payload?.message || '未知错误';
        console.log(chalk.yellow(`   信息: ${errorMsg}`));
        this.testResults.push({ test: 'exchangeScout', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 球探RP值兑换异常: ${error.message}`));
      this.testResults.push({ test: 'exchangeScout', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 运行所有球探系统测试
   */
  async runTests() {
    console.log(chalk.blue('🚀 开始球探系统测试'));

    // 1. 获取球探数据
    await this.testGetScoutData();

    // 2. 测试球探探索
    await this.testScoutExplore();

    // 3. 测试球探搜索
    await this.testScoutSearch();

    // 4. 测试球探体力购买
    await this.testBuyScoutEnergy();

    // 5. 测试球探RP值兑换
    await this.testExchangeScout();

    // 测试结果汇总
    const successCount = this.testResults.filter(r => r.success).length;
    const totalCount = this.testResults.length;

    console.log(chalk.blue('\n📊 球探系统测试结果汇总:'));
    console.log(chalk.white(`总测试项: ${totalCount}`));
    console.log(chalk.green(`成功项: ${successCount}`));
    console.log(chalk.red(`失败项: ${totalCount - successCount}`));

    if (successCount === totalCount) {
      console.log(chalk.green('🎉 球探系统测试全部通过！'));
    } else {
      console.log(chalk.yellow('⚠️ 部分球探系统测试失败，请检查日志'));
    }

    return successCount === totalCount;
  }
}

module.exports = ScoutModuleTester;
