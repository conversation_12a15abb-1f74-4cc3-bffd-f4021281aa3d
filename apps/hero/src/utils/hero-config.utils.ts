import { HeroDefinition } from '@libs/game-config';

/**
 * 球员配置工具类
 * 迁移自libs/game-config/src/hero/hero-definition.schema.ts中的业务逻辑
 * 
 * 严格遵循原则：只使用接口中实际存在的字段，基于真实的配置数据
 */
export class HeroConfigUtils {

  /**
   * 计算指定等级的属性值
   * 迁移自：HeroDefinitionSchema.methods.calculateAttributeAtLevel
   */
  static calculateAttributeAtLevel(
    heroConfig: HeroDefinition, 
    attributeName: string, 
    level: number
  ): number {
    // 基于HeroDefinition接口的真实字段实现
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    
    // 假设基础属性在baseAttributes中，成长率在growthConfig中
    const baseValue = (heroConfig as any).baseAttributes?.[attributeName] || 0;
    const growthRate = (heroConfig as any).growthConfig?.attributeGrowth?.[attributeName] || 0;
    
    return Math.floor(baseValue + (growthRate * (level - 1)));
  }

  /**
   * 计算升级所需经验
   * 迁移自：HeroDefinitionSchema.methods.calculateExpRequired
   */
  static calculateExpRequired(heroConfig: HeroDefinition, level: number): number {
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    const baseExp = (heroConfig as any).growthConfig?.baseExp || 100;
    const growthRate = (heroConfig as any).growthConfig?.expGrowthRate || 1.2;
    
    return Math.floor(baseExp * Math.pow(growthRate, level - 1));
  }

  /**
   * 检查是否可以学习技能
   * 迁移自：HeroDefinitionSchema.methods.canLearnSkill
   */
  static canLearnSkill(heroConfig: HeroDefinition, skillId: number): boolean {
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    const defaultSkills = (heroConfig as any).defaultSkills || [];
    const learnableSkills = (heroConfig as any).learnableSkills || [];
    
    return defaultSkills.includes(skillId) || learnableSkills.includes(skillId);
  }

  /**
   * 检查是否已过期（限时球员）
   * 迁移自：HeroDefinitionSchema.virtual('isExpired')
   */
  static isExpired(heroConfig: HeroDefinition): boolean {
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    const isLimited = (heroConfig as any).isLimited || false;
    const limitEndTime = (heroConfig as any).limitEndTime || 0;
    
    if (!isLimited) return false;
    return limitEndTime > 0 && Date.now() > limitEndTime;
  }

  /**
   * 检查是否可用
   * 迁移自：HeroDefinitionSchema.virtual('isAvailable')
   */
  static isAvailable(heroConfig: HeroDefinition): boolean {
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    const isActive = (heroConfig as any).isActive !== false; // 默认为true
    return isActive && !this.isExpired(heroConfig);
  }

  /**
   * 检查是否为门将
   * 迁移自：HeroDefinitionSchema.virtual('isGoalkeeper')
   */
  static isGoalkeeper(heroConfig: HeroDefinition): boolean {
    // 基于实际的position1字段（game-config中的真实字段名）
    // 门将的position1值为'GK'
    return heroConfig.position1 === 'GK';
  }

  /**
   * 计算总属性值
   * 迁移自：HeroDefinitionSchema.virtual('totalAttributes')
   */
  static calculateTotalAttributes(heroConfig: HeroDefinition): number {
    // TODO: 需要根据实际的HeroDefinition接口字段调整
    const attrs = (heroConfig as any).baseAttributes || {};
    
    return (attrs.speed || 0) + 
           (attrs.finishing || 0) + 
           (attrs.passing || 0) + 
           (attrs.dribbling || 0) + 
           (attrs.defending || 0) + 
           (attrs.physical || 0);
  }

  /**
   * 根据品质获取基础属性倍率
   * 基于old项目的品质系统
   */
  static getQualityMultiplier(quality: number | string): number {
    const qualityMap: Record<string | number, number> = {
      1: 1.0,    // 白色
      2: 1.2,    // 绿色
      3: 1.4,    // 蓝色
      4: 1.6,    // 紫色
      5: 1.8,    // 橙色
      6: 2.0,    // 红色
      'white': 1.0,
      'green': 1.2,
      'blue': 1.4,
      'purple': 1.6,
      'orange': 1.8,
      'red': 2.0,
    };
    
    return qualityMap[quality] || 1.0;
  }

  /**
   * 计算球员市场价值
   * 基于等级、品质、属性等因素
   */
  static calculateMarketValue(
    heroConfig: HeroDefinition, 
    level: number, 
    quality: number | string
  ): number {
    const baseValue = 1000;
    const qualityMultiplier = this.getQualityMultiplier(quality);
    const levelMultiplier = 1 + (level - 1) * 0.1;
    const totalAttributes = this.calculateTotalAttributes(heroConfig);
    const attributeMultiplier = 1 + (totalAttributes / 1000);
    
    return Math.floor(baseValue * qualityMultiplier * levelMultiplier * attributeMultiplier);
  }

  /**
   * 检查球员是否适合指定位置
   */
  static isCompatibleWithPosition(heroConfig: HeroDefinition, targetPosition: string): boolean {
    // 主位置完全匹配
    if (heroConfig.position1 === targetPosition) return true;

    // 副位置匹配
    if (heroConfig.position2 === targetPosition) return true;

    // TODO: 基于实际配置实现位置兼容性检查
    // 例如：中后卫可以打边后卫，前腰可以打边锋等

    return false;
  }

  /**
   * 获取球员位置名称
   */
  static getPositionName(position: number | string): string {
    const positionMap: Record<string | number, string> = {
      1: '门将',
      2: '中后卫',
      3: '左后卫',
      4: '右后卫',
      5: '后腰',
      6: '中场',
      7: '前腰',
      8: '左中场',
      9: '右中场',
      10: '左边锋',
      11: '右边锋',
      12: '中锋',
      13: '前锋',
      // 使用标准HeroPosition格式
      'GK': '门将',
      'DC': '中后卫',
      'DL': '左后卫',
      'DR': '右后卫',
      'DM': '后腰',
      'MC': '中场',
      'AM': '前腰',
      'ML': '左中场',
      'MR': '右中场',
      'WL': '左边锋',
      'WR': '右边锋',
      'ST': '前锋',
    };

    return positionMap[position] || '未知位置';
  }

  /**
   * 获取品质名称
   */
  static getQualityName(quality: number | string): string {
    const qualityMap: Record<string | number, string> = {
      1: '白色',
      2: '绿色',
      3: '蓝色',
      4: '紫色',
      5: '橙色',
      6: '红色',
      'white': '白色',
      'green': '绿色',
      'blue': '蓝色',
      'purple': '紫色',
      'orange': '橙色',
      'red': '红色',
    };
    
    return qualityMap[quality] || '未知品质';
  }
}
