import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { TrainingService } from './training.service';
import { TrainHeroDto } from '@hero/common/dto/hero.dto';

import { InjectedContext } from '@libs/common/types';

/**
 * 球员训练控制器
 * 基于old项目的训练相关功能，从hero模块迁移而来
 */
@Controller()
export class TrainingController {
  private readonly logger = new Logger(TrainingController.name);

  constructor(private readonly trainingService: TrainingService) {}

  /**
   * 获取球员特训信息
   * 对应old项目: getTrainInfo
   */
  @MessagePattern('training.getTrainInfo')
  @Cacheable({
    key: 'hero:train:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTrainInfo(@Payload() payload: { heroId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取球员特训信息: ${payload.heroId}`);
    const trainInfo = await this.trainingService.getTrainInfo(payload.heroId);
    return {
      code: 0,
      message: '获取成功',
      data: { heroTrain: trainInfo },
    };
  }

  /**
   * 训练球员
   * 对应old项目: trainHero
   */
  @MessagePattern('training.train')
  @CacheEvict({
    key: 'hero:info:#{payload.trainDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async trainHero(@Payload() payload: { trainDto: TrainHeroDto; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`训练球员: ${payload.trainDto.heroId}, 类型: ${payload.trainDto.trainingType}`);
    const result = await this.trainingService.trainHero(payload.trainDto);
    return {
      code: 0,
      message: '训练成功',
      data: result,
    };
  }

  /**
   * 替换特训
   * 对应old项目: replaceHeroTrain
   */
  @MessagePattern('training.replaceHeroTrain')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async replaceHeroTrain(@Payload() payload: { heroId: string; index: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`替换特训: ${payload.heroId}, 方式: ${payload.index}`);
    const result = await this.trainingService.replaceHeroTrain(payload.heroId, payload.index);
    return {
      code: 0,
      message: '替换成功',
      data: { index: result },
    };
  }

  /**
   * 设置球员训练状态
   * 从hero模块迁移而来
   */
  @MessagePattern('training.setTrainStatus')
  async setHeroTrainStatus(@Payload() payload: { heroId: string; isTrain: boolean; isLockTrain?: boolean; injectedContext?: InjectedContext }) {
    this.logger.log(`设置球员训练状态: ${payload.heroId}, 训练中: ${payload.isTrain}`);
    const hero = await this.trainingService.setHeroTrainStatus(payload.heroId, payload.isTrain, payload.isLockTrain);
    return {
      code: 0,
      message: '设置成功',
      data: hero,
    };
  }
}
