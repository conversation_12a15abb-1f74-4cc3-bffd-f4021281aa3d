import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Skill相关组件
import { SkillController } from './skill.controller';
import { SkillService } from './skill.service';
import { SkillRepository } from '@hero/common/repositories/skill.repository';
import { Skill, SkillSchema } from '@hero/common/schemas/skill.schema';

import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { Hero, HeroSchema } from '@hero/common/schemas/hero.schema';

/**
 * 技能系统模块
 * 处理球员技能相关功能
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - SkillDefinition Schema已废弃，现在使用GameConfigFacade
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 * - SkillService需要HeroRepository，因此需要注册Hero Schema和HeroRepository
 */
@Module({
  imports: [
    // 注册Skill和Hero Schema（必需）
    MongooseModule.forFeature([
      { name: Skill.name, schema: SkillSchema },
      { name: Hero.name, schema: HeroSchema },
    ]),
  ],

  controllers: [SkillController],

  providers: [
    SkillService,
    SkillRepository,
    HeroRepository,
  ],

  exports: [
    SkillService,
    SkillRepository,
  ],
})
export class SkillModule {}
