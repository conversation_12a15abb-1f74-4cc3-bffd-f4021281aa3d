import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { SkillType, SkillPosition } from '../../common/types';
import { SkillDocument, SkillActiveStatus } from '@hero/common/schemas/skill.schema';
import { SkillRepository } from '@hero/common/repositories/skill.repository';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import {
  LearnSkillDto,
  UpgradeSkillDto,
  ActivateSkillDto,
  SkillInfoDto,
  GetSkillListDto,
  GetSkillConfigListDto
} from '@hero/common/dto/skill.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import {MICROSERVICE_NAMES} from "@libs/shared";
import {GameConfigFacade} from "@libs/game-config";
import {MicroserviceClientService} from "@libs/service-mesh";

@Injectable()
export class SkillService {
  private readonly logger = new Logger(SkillService.name);

  constructor(
    private readonly skillRepository: SkillRepository,
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  // ==================== 技能配置管理 ====================

  /**
   * 获取技能配置信息
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   */
  async getSkillConfig(skillId: number): Promise<any> {
    try {
      const skillDefinition = await this.gameConfig.heroSkill.get(skillId);
      if (!skillDefinition) {
        throw new NotFoundException({
          code: ErrorCode.SKILL_NOT_FOUND,
          message: ErrorMessages[ErrorCode.SKILL_NOT_FOUND],
        });
      }

      return this.toSkillConfigDto(skillDefinition);
    } catch (error) {
      this.logger.error('获取技能配置信息失败', error);
      throw error;
    }
  }

  /**
   * 获取技能配置列表
   * 修复：使用GameConfigFacade的高级查询方法，充分利用filter功能
   */
  async getSkillConfigList(query: GetSkillConfigListDto): Promise<any> {
    try {
      let filteredSkills;

      // 使用GameConfigFacade的高级查询方法
      if (query.skillRank) {
        // 根据技能等级筛选（S, A, B, C等）
        filteredSkills = await this.gameConfig.heroSkill.findBy('skillRank', query.skillRank);
      } else if (query.skillType) {
        // 根据技能类型筛选（使用typeA字段）
        filteredSkills = await this.gameConfig.heroSkill.findBy('typeA', query.skillType);
      } else if (query.position) {
        // 根据位置筛选，使用filter方法进行复杂条件筛选
        filteredSkills = await this.gameConfig.heroSkill.filter(skill => {
          // 根据typeA, typeB, typeC, typeD字段判断适用位置
          // 这些字段通常对应不同的位置类型
          return skill.typeA === query.position ||
                 skill.typeB === query.position ||
                 skill.typeC === query.position ||
                 skill.typeD === query.position;
        });
      } else {
        // 获取所有技能配置
        filteredSkills = await this.gameConfig.heroSkill.getAll();
      }

      // 如果有多个筛选条件，使用filter进行组合筛选
      if (query.minStarValue || query.maxStarValue) {
        filteredSkills = await this.gameConfig.heroSkill.filter(skill => {
          const starMatch = (!query.minStarValue || skill.starValue >= query.minStarValue) &&
                           (!query.maxStarValue || skill.starValue <= query.maxStarValue);

          // 组合其他已有的筛选条件
          const rankMatch = !query.skillRank || skill.skillRank === query.skillRank;
          const typeMatch = !query.skillType || skill.typeA === query.skillType;

          return starMatch && rankMatch && typeMatch;
        });
      }

      // 分页处理
      const page = query.page || 1;
      const limit = query.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedSkills = filteredSkills.slice(startIndex, endIndex);

      return {
        list: paginatedSkills.map(skillDef => this.toSkillConfigDto(skillDef)),
        total: filteredSkills.length,
        page: page,
        limit: limit,
        pages: Math.ceil(filteredSkills.length / limit),
        hasNext: endIndex < filteredSkills.length,
        hasPrev: page > 1,
      };
    } catch (error) {
      this.logger.error('获取技能配置列表失败', error);
      throw error;
    }
  }

  /**
   * 根据位置获取可用技能配置
   * 修复：使用GameConfigFacade的filter方法进行高效筛选
   */
  async getSkillConfigsByPosition(position: SkillPosition, limit: number = 50): Promise<any[]> {
    try {
      // 使用GameConfigFacade的filter方法进行位置筛选
      const positionSkills = await this.gameConfig.heroSkill.filter(skill => {
        // 根据typeA, typeB, typeC, typeD字段判断技能是否适用于指定位置
        // 这些字段通常对应不同的位置类型或触发条件
        return skill.typeA === position ||
               skill.typeB === position ||
               skill.typeC === position ||
               skill.typeD === position;
      });

      // 应用数量限制并按星级排序（高星级优先）
      const limitedSkills = positionSkills
        .sort((a, b) => b.starValue - a.starValue) // 按星级降序排序
        .slice(0, limit);

      return limitedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
    } catch (error) {
      this.logger.error('根据位置获取技能配置失败', error);
      throw error;
    }
  }

  /**
   * 根据技能等级获取技能配置列表
   * 充分利用GameConfigFacade的findBy方法
   */
  async getSkillConfigsByRank(skillRank: string): Promise<any[]> {
    try {
      // 使用findBy方法直接根据skillRank字段筛选
      const rankSkills = await this.gameConfig.heroSkill.findBy('skillRank', skillRank);

      // 按星级降序排序
      const sortedSkills = rankSkills.sort((a, b) => b.starValue - a.starValue);

      return sortedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
    } catch (error) {
      this.logger.error('根据技能等级获取技能配置失败', error);
      throw error;
    }
  }

  /**
   * 获取高星级技能配置
   * 使用GameConfigFacade的filter方法进行复杂筛选
   */
  async getHighStarSkillConfigs(minStarValue: number = 7): Promise<any[]> {
    try {
      // 使用filter方法筛选高星级技能
      const highStarSkills = await this.gameConfig.heroSkill.filter(skill =>
        skill.starValue >= minStarValue
      );

      // 按星级降序排序
      const sortedSkills = highStarSkills.sort((a, b) => b.starValue - a.starValue);

      return sortedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
    } catch (error) {
      this.logger.error('获取高星级技能配置失败', error);
      throw error;
    }
  }

  /**
   * 根据技能名称查找技能配置
   * 使用GameConfigFacade的findOneBy方法
   */
  async getSkillConfigByName(skillName: string): Promise<any | null> {
    try {
      // 使用findOneBy方法根据技能名称查找
      const skill = await this.gameConfig.heroSkill.findOneBy('skillName', skillName);

      return skill ? this.toSkillConfigDto(skill) : null;
    } catch (error) {
      this.logger.error('根据技能名称查找技能配置失败', error);
      return null;
    }
  }

  // ==================== 球员技能管理 ====================

  /**
   * 球员学习技能
   */
  async learnSkill(learnDto: LearnSkillDto): Promise<SkillDocument> {
    try {
      // 检查技能配置是否存在
      // 修复：使用GameConfigFacade获取静态配置
      const skillDefinition = await this.gameConfig.heroSkill.get(learnDto.configId);
      if (!skillDefinition) {
        throw new NotFoundException({
          code: ErrorCode.SKILL_NOT_FOUND,
          message: ErrorMessages[ErrorCode.SKILL_NOT_FOUND],
        });
      }

      // 检查球员是否已拥有该技能
      const hasSkill = await this.skillRepository.hasSkill(learnDto.heroId, learnDto.configId);
      if (hasSkill) {
        throw new BadRequestException({
          code: ErrorCode.SKILL_ALREADY_LEARNED,
          message: ErrorMessages[ErrorCode.SKILL_ALREADY_LEARNED],
        });
      }

      // 获取球员信息以获取characterId和serverId
      const hero = await this.heroRepository.findById(learnDto.heroId);
      if (!hero) {
        throw new BadRequestException({
          code: ErrorCode.HERO_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_NOT_FOUND],
        });
      }

      // 生成球员技能实例ID
      const skillId = this.generateSkillId();

      // 创建球员技能实例
      const skillData = {
        skillId,
        heroId: learnDto.heroId,
        characterId: hero.characterId, // 从Hero数据获取characterId
        serverId: hero.serverId || 'server_001', // 从Hero数据获取serverId，默认server_001
        configId: learnDto.configId,
        level: 1,
        experience: 0,
        activeStatus: SkillActiveStatus.INACTIVE,
        slotPosition: 0,
        obtainSource: learnDto.obtainSource || 'manual',
        obtainCost: learnDto.obtainCost || 0,
      };

      const savedSkill = await this.skillRepository.createSkill(skillData);

      this.logger.log(`球员学习技能成功: ${learnDto.heroId}, 技能: ${learnDto.configId}`);
      return savedSkill;
    } catch (error) {
      this.logger.error('球员学习技能失败', error);
      throw error;
    }
  }

  /**
   * 升级球员技能
   */
  async upgradeSkill(upgradeDto: UpgradeSkillDto): Promise<SkillDocument> {
    try {
      const skill = await this.skillRepository.findSkillById(upgradeDto.skillId);
      if (!skill) {
        throw new NotFoundException({
          code: ErrorCode.HERO_SKILL_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],
        });
      }

      // 检查是否可以升级
      if (!skill.canUpgrade) {
        throw new BadRequestException({
          code: ErrorCode.SKILL_CANNOT_UPGRADE,
          message: ErrorMessages[ErrorCode.SKILL_CANNOT_UPGRADE],
        });
      }

      // 检查目标等级是否有效
      if (upgradeDto.targetLevel <= skill.level) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_UPGRADE_LEVEL,
          message: ErrorMessages[ErrorCode.INVALID_UPGRADE_LEVEL],
        });
      }

      // 检查是否超过最大等级（假设最大等级为10）
      if (upgradeDto.targetLevel > 10) {
        throw new BadRequestException({
          code: ErrorCode.EXCEED_MAX_LEVEL,
          message: ErrorMessages[ErrorCode.EXCEED_MAX_LEVEL],
        });
      }

      // 计算升级费用
      const upgradeCost = this.calculateUpgradeCost(skill.level, upgradeDto.targetLevel);

      // 检查玩家是否有足够的资源进行升级（基于old项目逻辑）
      const resourceCheckResult = await this.checkUpgradeResources(skill, upgradeDto.targetLevel, upgradeCost);
      if (!resourceCheckResult.sufficient) {
        throw new BadRequestException({
          code: ErrorCode.INSUFFICIENT_HEROES,
          message: resourceCheckResult.message,
          data: {
            required: resourceCheckResult.required,
            current: resourceCheckResult.current,
          },
        });
      }

      // 执行升级
      const updateData = {
        level: upgradeDto.targetLevel,
        upgradeCount: skill.upgradeCount + (upgradeDto.targetLevel - skill.level),
        totalUpgradeCost: skill.totalUpgradeCost + upgradeCost,
        lastUpgradeTime: Date.now(),
      };

      const updatedSkill = await this.skillRepository.updateSkill(
        upgradeDto.skillId, 
        updateData
      );

      this.logger.log(`技能升级成功: ${upgradeDto.skillId}, 等级: ${upgradeDto.targetLevel}`);
      return updatedSkill;
    } catch (error) {
      this.logger.error('升级球员技能失败', error);
      throw error;
    }
  }

  /**
   * 激活球员技能
   */
  async activateSkill(activateDto: ActivateSkillDto): Promise<SkillDocument> {
    try {
      const skill = await this.skillRepository.findSkillById(activateDto.skillId);
      if (!skill) {
        throw new NotFoundException({
          code: ErrorCode.HERO_SKILL_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],
        });
      }

      // 检查技能是否可以激活
      if (skill.isLocked) {
        throw new BadRequestException({
          code: ErrorCode.SKILL_IS_LOCKED,
          message: ErrorMessages[ErrorCode.SKILL_IS_LOCKED],
        });
      }

      // 清空目标槽位的其他技能
      await this.skillRepository.clearSkillSlot(skill.heroId, activateDto.slotPosition);

      // 激活技能
      const updateData = {
        activeStatus: SkillActiveStatus.ACTIVE,
        slotPosition: activateDto.slotPosition,
      };

      const updatedSkill = await this.skillRepository.updateSkill(
        activateDto.skillId, 
        updateData
      );

      this.logger.log(`技能激活成功: ${activateDto.skillId}, 槽位: ${activateDto.slotPosition}`);
      return updatedSkill;
    } catch (error) {
      this.logger.error('激活球员技能失败', error);
      throw error;
    }
  }

  /**
   * 取消激活球员技能
   */
  async deactivateSkill(skillId: string): Promise<SkillDocument> {
    try {
      const skill = await this.skillRepository.findSkillById(skillId);
      if (!skill) {
        throw new NotFoundException({
          code: ErrorCode.HERO_SKILL_NOT_FOUND,
          message: ErrorMessages[ErrorCode.HERO_SKILL_NOT_FOUND],
        });
      }

      const updateData = {
        activeStatus: SkillActiveStatus.INACTIVE,
        slotPosition: 0,
      };

      const updatedSkill = await this.skillRepository.updateSkill(skillId, updateData);

      this.logger.log(`技能取消激活成功: ${skillId}`);
      return updatedSkill;
    } catch (error) {
      this.logger.error('取消激活球员技能失败', error);
      throw error;
    }
  }

  /**
   * 获取球员技能列表
   */
  async getSkillList(query: GetSkillListDto): Promise<SkillInfoDto[]> {
    try {
      const skills = await this.skillRepository.findSkillsByHeroId(query);
      return skills.map(skill => this.toSkillInfoDto(skill));
    } catch (error) {
      this.logger.error('获取球员技能列表失败', error);
      throw error;
    }
  }

  /**
   * 获取球员已激活的技能
   */
  async getActiveSkills(heroId: string): Promise<SkillInfoDto[]> {
    try {
      const activeSkills = await this.skillRepository.findActiveSkills(heroId);
      return activeSkills.map(skill => this.toSkillInfoDto(skill));
    } catch (error) {
      this.logger.error('获取球员已激活技能失败', error);
      throw error;
    }
  }

  /**
   * 获取技能统计
   */
  async getSkillStats(heroId: string): Promise<any> {
    try {
      return await this.skillRepository.getSkillStats(heroId);
    } catch (error) {
      this.logger.error('获取技能统计失败', error);
      throw error;
    }
  }

  /**
   * 计算升级费用
   */
  private calculateUpgradeCost(fromLevel: number, toLevel: number): number {
    let totalCost = 0;
    
    for (let level = fromLevel + 1; level <= toLevel; level++) {
      // 基础升级费用公式
      totalCost += level * 1000;
    }
    
    return totalCost;
  }

  /**
   * 生成球员技能实例ID
   */
  private generateSkillId(): string {
    return `skill_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 转换为技能配置DTO
   */
  private toSkillConfigDto(skillDefinition: any): any {
    return {
      skillId: skillDefinition.skillId,
      name: skillDefinition.name,
      description: skillDefinition.description,
      icon: skillDefinition.icon,
      type: skillDefinition.type,
      position: skillDefinition.position,
      rarity: skillDefinition.rarity,
      maxLevel: skillDefinition.maxLevel,
      cooldown: skillDefinition.cooldown,
      energyCost: skillDefinition.energyCost,
      upgrades: skillDefinition.upgrades,
      unlockLevel: skillDefinition.unlockLevel,
      unlockItems: skillDefinition.unlockItems,
      unlockCondition: skillDefinition.unlockCondition,
      isActive: skillDefinition.isActive,
      isLimited: skillDefinition.isLimited,
      limitEndTime: skillDefinition.limitEndTime,
      isExpired: skillDefinition.isExpired,
      isAvailable: skillDefinition.isAvailable,
      baseEffects: skillDefinition.baseEffects,
      maxLevelEffects: skillDefinition.maxLevelEffects,
    };
  }

  /**
   * 转换为球员技能信息DTO
   */
  private toSkillInfoDto(skill: SkillDocument): SkillInfoDto {
    return {
      skillId: skill.skillId,
      heroId: skill.heroId,
      configId: skill.configId,
      level: skill.level,
      experience: skill.experience,
      activeStatus: skill.activeStatus,
      slotPosition: skill.slotPosition,
      usageCount: skill.usageCount,
      lastUsedTime: skill.lastUsedTime,
      upgradeCount: skill.upgradeCount,
      totalUpgradeCost: skill.totalUpgradeCost,
      obtainTime: skill.obtainTime,
      obtainSource: skill.obtainSource,
      isLocked: skill.isLocked,
      isActive: skill.isActive,
      isEquipped: skill.isEquipped,
      canUpgrade: skill.canUpgrade,
      nextLevelExp: skill.nextLevelExp,
      upgradeProgress: skill.upgradeProgress,
    };
  }

  /**
   * 检查升级资源
   * 基于old项目: checkResourceIsEnough和BeliefSkillExpend配置表
   *
   * 实现逻辑：
   * 1. 获取升级配置
   * 2. 检查不同类型的资源需求
   * 3. 验证资源是否足够
   */
  private async checkUpgradeResources(skill: any, targetLevel: number, upgradeCost: number): Promise<any> {
    try {
      // 1. 获取升级配置（基于old项目BeliefSkillExpend配置表）
      const upgradeConfig = await this.getSkillUpgradeConfig(skill.skillType, targetLevel);
      if (!upgradeConfig) {
        return {
          sufficient: false,
          message: '升级配置不存在',
          required: {},
          current: {},
        };
      }

      // 2. 检查金币资源（基于old项目逻辑）
      if (upgradeCost > 0) {
        const goldCheckResult = await this.checkCharacterGold(skill.characterId, upgradeCost);
        if (!goldCheckResult.sufficient) {
          return {
            sufficient: false,
            message: '金币不足',
            required: { gold: upgradeCost },
            current: { gold: goldCheckResult.current },
          };
        }
      }

      // 3. 检查信仰活跃度（基于old项目beliefLiveness）
      if (upgradeConfig.contribution > 0) {
        const beliefCheckResult = await this.checkCharacterBelief(skill.characterId, upgradeConfig.contribution);
        if (!beliefCheckResult.sufficient) {
          return {
            sufficient: false,
            message: '信仰活跃度不足',
            required: { belief: upgradeConfig.contribution },
            current: { belief: beliefCheckResult.current },
          };
        }
      }

      // 4. 检查道具资源（基于old项目ItemID和ItemNumber）
      if (upgradeConfig.itemId && upgradeConfig.itemNumber > 0) {
        const itemCheckResult = await this.checkCharacterItem(skill.characterId, upgradeConfig.itemId, upgradeConfig.itemNumber);
        if (!itemCheckResult.sufficient) {
          return {
            sufficient: false,
            message: '道具不足',
            required: { [`item_${upgradeConfig.itemId}`]: upgradeConfig.itemNumber },
            current: { [`item_${upgradeConfig.itemId}`]: itemCheckResult.current },
          };
        }
      }

      return {
        sufficient: true,
        message: '资源充足',
        required: {
          gold: upgradeCost,
          belief: upgradeConfig.contribution || 0,
          [`item_${upgradeConfig.itemId}`]: upgradeConfig.itemNumber || 0,
        },
        current: {},
      };
    } catch (error) {
      this.logger.error('检查升级资源失败', error);
      return {
        sufficient: false,
        message: '资源检查异常',
        required: {},
        current: {},
      };
    }
  }

  /**
   * 获取技能升级配置
   * 基于old项目: BeliefSkillExpend配置表
   * 修复：使用GameConfigFacade的高效查询方法
   * 注意：BeliefSkillExpend表中id字段就是等级，只有id和contribution两个字段
   */
  private async getSkillUpgradeConfig(skillType: string, level: number): Promise<any> {
    try {
      // 使用get方法根据等级获取配置（id就是等级）
      const config = await this.gameConfig.beliefSkillExpend?.get(level);

      if (config) {
        // 返回包含等级信息的配置
        return {
          level: config.id, // id就是等级
          contribution: config.contribution, // 信仰活跃度消耗
          skillType, // 传入的技能类型
          itemId: 20001, // 道具ID（从old项目逻辑推断）
          itemNumber: Math.floor(level / 2) + 1, // 道具数量（从old项目逻辑推断）
        };
      }

      // 如果配置不存在，返回默认配置
      this.logger.warn(`技能升级配置不存在: skillType=${skillType}, level=${level}, 使用默认配置`);
      return {
        level,
        skillType,
        contribution: level * 100, // 信仰活跃度消耗
        itemId: 20001, // 道具ID
        itemNumber: Math.floor(level / 2) + 1, // 道具数量
      };
    } catch (error) {
      this.logger.error('获取技能升级配置失败', error);
      return null;
    }
  }

  /**
   * 检查角色金币
   * 基于old项目: checkResourceIsEnough逻辑
   */
  private async checkCharacterGold(characterId: string, requiredAmount: number): Promise<any> {
    try {
      // TODO: 调用Character服务检查金币
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.checkCurrency',
      //   { characterId, currencyType: 'gold', amount: requiredAmount }
      // );
      //
      // return {
      //   sufficient: result.data.sufficient,
      //   current: result.data.current,
      // };

      // 暂时返回模拟数据
      const mockCurrentGold = Math.floor(Math.random() * 1000000) + requiredAmount;
      return {
        sufficient: mockCurrentGold >= requiredAmount,
        current: mockCurrentGold,
      };
    } catch (error) {
      this.logger.error('检查角色金币失败', error);
      return { sufficient: false, current: 0 };
    }
  }

  /**
   * 检查角色信仰活跃度
   * 基于old项目: beliefLiveness资源检查
   */
  private async checkCharacterBelief(characterId: string, requiredAmount: number): Promise<any> {
    try {
      // TODO: 调用Character服务检查信仰活跃度
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.checkResource',
      //   { characterId, resourceType: 'beliefLiveness', amount: requiredAmount }
      // );
      //
      // return {
      //   sufficient: result.data.sufficient,
      //   current: result.data.current,
      // };

      // 暂时返回模拟数据
      const mockCurrentBelief = Math.floor(Math.random() * 10000) + requiredAmount;
      return {
        sufficient: mockCurrentBelief >= requiredAmount,
        current: mockCurrentBelief,
      };
    } catch (error) {
      this.logger.error('检查角色信仰活跃度失败', error);
      return { sufficient: false, current: 0 };
    }
  }

  /**
   * 检查角色道具
   * 基于old项目: 道具数量检查逻辑
   */
  private async checkCharacterItem(characterId: string, itemId: number, requiredQuantity: number): Promise<any> {
    try {
      // 调用Character服务检查道具
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'inventory.checkItem',
        { characterId, itemId, quantity: requiredQuantity }
      );

      return {
        sufficient: result.data.sufficient,
        current: result.data.current,
      };
    } catch (error) {
      this.logger.error('检查角色道具失败', error);
      return { sufficient: false, current: 0 };
    }
  }
}
