import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { GroundService } from './ground.service';

import { InjectedContext } from '@libs/common/types';

/**
 * 场地训练控制器
 * 严格基于old项目的groundService功能
 * 
 * old项目核心功能：
 * - getHeroTrainInfo: 获取场地训练信息
 * - heroTrainInGround: 场地训练
 * - getHeroTrainReward: 获取场地训练奖励
 * - inputNotablePos: 名人堂入驻
 * - getNotablePosInfo: 获取名人堂列表
 */
@Controller()
export class GroundController {
  private readonly logger = new Logger(GroundController.name);

  constructor(private readonly groundService: GroundService) {}

  /**
   * 获取场地训练信息
   * 对应old项目: game.groundService.getHeroTrainInfo
   */
  @MessagePattern('ground.getTrainInfo')
  @Cacheable({
    key: 'ground:train:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getHeroTrainInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取场地训练信息: ${payload.characterId}`);
    const result = await this.groundService.getHeroTrainInfo(payload.characterId);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 场地训练
   * 对应old项目: game.groundService.heroTrainInGround
   */
  @MessagePattern('ground.train')
  @CacheEvict({
    key: 'ground:train:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async heroTrainInGround(@Payload() payload: { characterId: string; heroId: string; index: number; type: number; isLock?: boolean; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`场地训练: 球员${payload.heroId}, 位置${payload.index}, 类型${payload.type}`);
    const result = await this.groundService.heroTrainInGround(
      payload.characterId,
      payload.heroId,
      payload.index, 
      payload.type, 
      payload.isLock
    );
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取场地训练奖励
   * 对应old项目: game.groundService.getHeroTrainReward
   */
  @MessagePattern('ground.getReward')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getHeroTrainReward(@Payload() payload: { characterId: string; index: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取场地训练奖励: ${payload.characterId}, 位置${payload.index}`);
    const result = await this.groundService.getHeroTrainReward(payload.characterId, payload.index);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 名人堂入驻
   * 对应old项目: game.groundService.inputNotablePos
   */
  @MessagePattern('ground.inputNotable')
  @CacheEvict({
    key: 'ground:notable:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async inputNotablePos(@Payload() payload: { characterId: string; heroId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`名人堂入驻: 球员${payload.heroId}`);
    const result = await this.groundService.inputNotablePos(payload.characterId, payload.heroId);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取名人堂列表
   * 对应old项目: game.groundService.getNotablePosInfo
   */
  @MessagePattern('ground.getNotableList')
  @Cacheable({
    key: 'ground:notable:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getNotablePosInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取名人堂列表: ${payload.characterId}`);
    const result = await this.groundService.getNotablePosInfo(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 获取医疗中心信息
   * 对应old项目: game.groundService.getHospitalPosInfo
   */
  @MessagePattern('ground.getHospitalInfo')
  @Cacheable({
    key: 'ground:hospital:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getHospitalPosInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取医疗中心信息: ${payload.characterId}`);
    const result = await this.groundService.getHospitalPosInfo(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }

  /**
   * 医疗中心治疗
   * 对应old项目: game.groundService.inputHospitalPos
   */
  @MessagePattern('ground.inputHospital')
  @CacheEvict({
    key: 'ground:hospital:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async inputHospitalPos(@Payload() payload: { characterId: string; heroId: string; index: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`医疗中心治疗: 球员${payload.heroId}, 位置${payload.index}`);
    const result = await this.groundService.inputHospitalPos(payload.characterId, payload.heroId, payload.index);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取医疗奖励
   * 对应old项目: game.groundService.getHospitalPosHero
   */
  @MessagePattern('ground.getHospitalReward')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getHospitalPosHero(@Payload() payload: { characterId: string; index: number; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取医疗奖励: ${payload.characterId}, 位置${payload.index}`);
    const result = await this.groundService.getHospitalPosHero(payload.characterId, payload.index);
    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取球迷信息
   * 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank()
   * 用于商业赛系统获取对手的球迷数量和排名信息
   */
  @MessagePattern('ground.getFansInfo')
  @Cacheable({
    key: 'ground:fans:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getFansInfo(@Payload() payload: { characterId: string; serverId?: string; injectedContext?: InjectedContext }) {
    this.logger.log(`获取球迷信息: ${payload.characterId}`);
    const result = await this.groundService.getFansInfo(payload.characterId);
    return {
      code: 0,
      message: '获取成功',
      data: result,
    };
  }
}
