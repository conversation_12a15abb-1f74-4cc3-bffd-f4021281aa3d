import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { SkillType, SkillPosition } from '../types';
import { Skill, SkillDocument, SkillActiveStatus } from '../schemas/skill.schema';
import { GetSkillListDto, GetSkillConfigListDto } from '../dto/skill.dto';

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class SkillRepository {
  private readonly logger = new Logger(SkillRepository.name);

  constructor(
    @InjectModel(Skill.name) private skillModel: Model<SkillDocument>,
  ) {}



  /**
   * 创建球员技能实例
   */
  async createSkill(skillData: Partial<Skill>): Promise<SkillDocument> {
    try {
      const skill = new this.skillModel({
        ...skillData,
        obtainTime: Date.now(),
      });
      return await skill.save();
    } catch (error) {
      this.logger.error('创建球员技能实例失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找球员技能实例
   */
  async findSkillById(skillId: string): Promise<SkillDocument | null> {
    try {
      return await this.skillModel.findOne({ skillId }).exec();
    } catch (error) {
      this.logger.error(`根据ID查找球员技能实例失败: ${skillId}`, error);
      throw error;
    }
  }

  /**
   * 根据球员ID查找技能列表
   */
  async findSkillsByHeroId(query: GetSkillListDto): Promise<SkillDocument[]> {
    try {
      const filter: FilterQuery<SkillDocument> = { heroId: query.heroId };

      if (query.activeStatus !== undefined) {
        filter.activeStatus = query.activeStatus;
      }
      if (query.equippedOnly) {
        filter.slotPosition = { $gt: 0 };
      }

      return await this.skillModel
        .find(filter)
        .sort({ slotPosition: 1, level: -1, obtainTime: 1 })
        .exec();
    } catch (error) {
      this.logger.error(`根据球员ID查找技能列表失败: ${query.heroId}`, error);
      throw error;
    }
  }

  /**
   * 更新球员技能实例
   */
  async updateSkill(
    skillId: string, 
    updateData: UpdateQuery<SkillDocument>,
    options?: QueryOptions
  ): Promise<SkillDocument | null> {
    try {
      return await this.skillModel.findOneAndUpdate(
        { skillId },
        updateData,
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新球员技能实例失败: ${skillId}`, error);
      throw error;
    }
  }

  /**
   * 删除球员技能实例
   */
  async deleteSkill(skillId: string): Promise<SkillDocument | null> {
    try {
      const result = await this.skillModel.findOneAndDelete({ skillId }).exec();
      return result as unknown as SkillDocument | null;
    } catch (error) {
      this.logger.error(`删除球员技能实例失败: ${skillId}`, error);
      throw error;
    }
  }

  /**
   * 检查球员是否已拥有指定技能
   */
  async hasSkill(heroId: string, configId: number): Promise<boolean> {
    try {
      const count = await this.skillModel.countDocuments({ heroId, configId });
      return count > 0;
    } catch (error) {
      this.logger.error('检查球员技能失败', error);
      throw error;
    }
  }

  /**
   * 获取球员已激活的技能
   */
  async findActiveSkills(heroId: string): Promise<SkillDocument[]> {
    try {
      return await this.skillModel
        .find({ 
          heroId, 
          activeStatus: SkillActiveStatus.ACTIVE,
          slotPosition: { $gt: 0 }
        })
        .sort({ slotPosition: 1 })
        .exec();
    } catch (error) {
      this.logger.error(`获取球员已激活技能失败: ${heroId}`, error);
      throw error;
    }
  }

  /**
   * 清空指定槽位的技能
   */
  async clearSkillSlot(heroId: string, slotPosition: number): Promise<void> {
    try {
      await this.skillModel.updateMany(
        { heroId, slotPosition },
        { 
          $set: { 
            slotPosition: 0,
            activeStatus: SkillActiveStatus.INACTIVE
          }
        }
      );
    } catch (error) {
      this.logger.error(`清空技能槽位失败: ${heroId}, 槽位: ${slotPosition}`, error);
      throw error;
    }
  }

  /**
   * 批量更新球员技能
   */
  async bulkUpdateSkills(updates: Array<{
    filter: FilterQuery<SkillDocument>;
    update: UpdateQuery<SkillDocument>;
  }>): Promise<any> {
    try {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
        }
      }));

      return await this.skillModel.bulkWrite(bulkOps);
    } catch (error) {
      this.logger.error('批量更新球员技能失败', error);
      throw error;
    }
  }

  /**
   * 获取球员技能统计
   */
  async getSkillStats(heroId: string): Promise<any> {
    try {
      const stats = await this.skillModel.aggregate([
        {
          $match: { heroId }
        },
        {
          $group: {
            _id: null,
            totalSkills: { $sum: 1 },
            activeSkills: {
              $sum: {
                $cond: [{ $eq: ['$activeStatus', SkillActiveStatus.ACTIVE] }, 1, 0]
              }
            },
            averageLevel: { $avg: '$level' },
            totalUpgradeCost: { $sum: '$totalUpgradeCost' },
            totalUsage: { $sum: '$usageCount' }
          }
        }
      ]);

      return stats[0] || {
        totalSkills: 0,
        activeSkills: 0,
        averageLevel: 0,
        totalUpgradeCost: 0,
        totalUsage: 0
      };
    } catch (error) {
      this.logger.error('获取球员技能统计失败', error);
      throw error;
    }
  }

  /**
   * 根据配置ID获取技能使用统计
   */
  async getConfigUsageStats(configId: number): Promise<any> {
    try {
      const stats = await this.skillModel.aggregate([
        {
          $match: { configId }
        },
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            totalUsage: { $sum: '$usageCount' },
            totalDamage: { $sum: '$totalDamage' },
            totalHealing: { $sum: '$totalHealing' },
            averageLevel: { $avg: '$level' },
            maxLevel: { $max: '$level' },
            activeUsers: {
              $sum: {
                $cond: [{ $eq: ['$activeStatus', SkillActiveStatus.ACTIVE] }, 1, 0]
              }
            }
          }
        }
      ]);

      return stats[0] || {
        totalUsers: 0,
        totalUsage: 0,
        totalDamage: 0,
        totalHealing: 0,
        averageLevel: 0,
        maxLevel: 0,
        activeUsers: 0
      };
    } catch (error) {
      this.logger.error('获取技能使用统计失败', error);
      throw error;
    }
  }
}
