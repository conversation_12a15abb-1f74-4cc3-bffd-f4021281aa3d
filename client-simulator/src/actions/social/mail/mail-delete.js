/**
 * 删除邮件
 * 
 * 微服务: social
 * 模块: mail
 * Controller: mail
 * Pattern: mail.delete
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.817Z
 */

const BaseAction = require('../../../core/base-action');

class MaildeleteAction extends BaseAction {
  static metadata = {
    name: '删除邮件',
    description: '删除邮件',
    category: 'social',
    serviceName: 'social',
    module: 'mail',
    actionName: 'mail.delete',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "mailUid": {
            "type": "string",
            "required": true,
            "description": "mailUid参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, mailUid } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      mailUid
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '删除邮件成功'
      };
    } else {
      throw new Error(`删除邮件失败: ${response.message}`);
    }
  }
}

module.exports = MaildeleteAction;