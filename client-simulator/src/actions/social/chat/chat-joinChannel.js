/**
 * 加入聊天频道
 * 
 * 微服务: social
 * 模块: chat
 * Controller: chat
 * Pattern: chat.joinChannel
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.722Z
 */

const BaseAction = require('../../../core/base-action');

class ChatjoinChannelAction extends BaseAction {
  static metadata = {
    name: '加入聊天频道',
    description: '加入聊天频道',
    category: 'social',
    serviceName: 'social',
    module: 'chat',
    actionName: 'chat.joinChannel',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "channelId": {
            "type": "string",
            "required": true,
            "description": "channelId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, channelId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      channelId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '加入聊天频道成功'
      };
    } else {
      throw new Error(`加入聊天频道失败: ${response.message}`);
    }
  }
}

module.exports = ChatjoinChannelAction;