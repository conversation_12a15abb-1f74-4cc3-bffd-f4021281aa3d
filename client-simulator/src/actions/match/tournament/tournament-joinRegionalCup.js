/**
 * 参加区域杯赛 基于old项目的joinRegionalCup接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.joinRegionalCup
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.687Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentjoinRegionalCupAction extends BaseAction {
  static metadata = {
    name: '参加区域杯赛 基于old项目的joinRegionalCup接口',
    description: '参加区域杯赛 基于old项目的joinRegionalCup接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.joinRegionalCup',
    prerequisites: ["login","character"],
    params: {
      "joinRegionalCupDto": {
            "type": "object",
            "required": true,
            "description": "joinRegionalCupDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "cupType": {
                        "type": "string",
                        "required": true,
                        "description": "cupType参数"
                  },
                  "teamIdList": {
                        "type": "array",
                        "required": true,
                        "description": "teamIdList参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "cupType": "示例cupType",
                  "teamIdList": [],
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { joinRegionalCupDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      joinRegionalCupDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '参加区域杯赛 基于old项目的joinRegionalCup接口成功'
      };
    } else {
      throw new Error(`参加区域杯赛 基于old项目的joinRegionalCup接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentjoinRegionalCupAction;