/**
 * 领取世界杯奖励 基于old项目的getWorldCupReward接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.getWorldCupReward
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.682Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentgetWorldCupRewardAction extends BaseAction {
  static metadata = {
    name: '领取世界杯奖励 基于old项目的getWorldCupReward接口',
    description: '领取世界杯奖励 基于old项目的getWorldCupReward接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.getWorldCupReward',
    prerequisites: ["login","character"],
    params: {
      "getWorldCupRewardDto": {
            "type": "object",
            "required": true,
            "description": "getWorldCupRewardDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getWorldCupRewardDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getWorldCupRewardDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取世界杯奖励 基于old项目的getWorldCupReward接口成功'
      };
    } else {
      throw new Error(`领取世界杯奖励 基于old项目的getWorldCupReward接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentgetWorldCupRewardAction;