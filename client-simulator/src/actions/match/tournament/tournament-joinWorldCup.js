/**
 * 参加世界杯 基于old项目的joinWorldCup接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.joinWorldCup
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.672Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentjoinWorldCupAction extends BaseAction {
  static metadata = {
    name: '参加世界杯 基于old项目的joinWorldCup接口',
    description: '参加世界杯 基于old项目的joinWorldCup接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.joinWorldCup',
    prerequisites: ["login","character"],
    params: {
      "joinWorldCupDto": {
            "type": "object",
            "required": true,
            "description": "joinWorldCupDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "worldCupId": {
                        "type": "number",
                        "required": true,
                        "description": "worldCupId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "worldCupId": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { joinWorldCupDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      joinWorldCupDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '参加世界杯 基于old项目的joinWorldCup接口成功'
      };
    } else {
      throw new Error(`参加世界杯 基于old项目的joinWorldCup接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentjoinWorldCupAction;