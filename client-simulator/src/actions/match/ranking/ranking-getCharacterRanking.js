/**
 * 获取玩家排名信息 基于old项目的玩家个人排名功能
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.getCharacterRanking
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.662Z
 */

const BaseAction = require('../../../core/base-action');

class RankinggetCharacterRankingAction extends BaseAction {
  static metadata = {
    name: '获取玩家排名信息 基于old项目的玩家个人排名功能',
    description: '获取玩家排名信息 基于old项目的玩家个人排名功能',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.getCharacterRanking',
    prerequisites: ["login","character"],
    params: {
      "getCharacterRankingDto": {
            "type": "object",
            "required": true,
            "description": "getCharacterRankingDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getCharacterRankingDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getCharacterRankingDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取玩家排名信息 基于old项目的玩家个人排名功能成功'
      };
    } else {
      throw new Error(`获取玩家排名信息 基于old项目的玩家个人排名功能失败: ${response.message}`);
    }
  }
}

module.exports = RankinggetCharacterRankingAction;