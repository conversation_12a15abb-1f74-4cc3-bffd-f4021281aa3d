/**
 * 商业赛搜索 基于old项目的businessSearch接口
 * 
 * 微服务: match
 * 模块: business
 * Controller: business
 * Pattern: business.businessSearch
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.632Z
 */

const BaseAction = require('../../../core/base-action');

class BusinessbusinessSearchAction extends BaseAction {
  static metadata = {
    name: '商业赛搜索 基于old项目的businessSearch接口',
    description: '商业赛搜索 基于old项目的businessSearch接口',
    category: 'match',
    serviceName: 'match',
    module: 'business',
    actionName: 'business.businessSearch',
    prerequisites: ["login","character"],
    params: {
      "businessSearchDto": {
            "type": "object",
            "required": true,
            "description": "businessSearchDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "name": {
                        "type": "string",
                        "required": true,
                        "description": "name参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "name": "示例name",
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { businessSearchDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      businessSearchDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '商业赛搜索 基于old项目的businessSearch接口成功'
      };
    } else {
      throw new Error(`商业赛搜索 基于old项目的businessSearch接口失败: ${response.message}`);
    }
  }
}

module.exports = BusinessbusinessSearchAction;