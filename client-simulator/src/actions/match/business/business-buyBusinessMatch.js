/**
 * 购买商业赛次数 基于old项目的buyBusinessMatch接口
 * 
 * 微服务: match
 * 模块: business
 * Controller: business
 * Pattern: business.buyBusinessMatch
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.635Z
 */

const BaseAction = require('../../../core/base-action');

class BusinessbuyBusinessMatchAction extends BaseAction {
  static metadata = {
    name: '购买商业赛次数 基于old项目的buyBusinessMatch接口',
    description: '购买商业赛次数 基于old项目的buyBusinessMatch接口',
    category: 'match',
    serviceName: 'match',
    module: 'business',
    actionName: 'business.buyBusinessMatch',
    prerequisites: ["login","character"],
    params: {
      "buyBusinessMatchDto": {
            "type": "object",
            "required": true,
            "description": "buyBusinessMatchDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "num": {
                        "type": "number",
                        "required": true,
                        "description": "num参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "num": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { buyBusinessMatchDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      buyBusinessMatchDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买商业赛次数 基于old项目的buyBusinessMatch接口成功'
      };
    } else {
      throw new Error(`购买商业赛次数 基于old项目的buyBusinessMatch接口失败: ${response.message}`);
    }
  }
}

module.exports = BusinessbuyBusinessMatchAction;