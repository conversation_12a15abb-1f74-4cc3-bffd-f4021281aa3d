/**
 * 领取精力消耗奖励
 * 
 * 微服务: activity
 * 模块: energy
 * Controller: energy
 * Pattern: energy.claimReward
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.216Z
 */

const BaseAction = require('../../../core/base-action');

class EnergyclaimRewardAction extends BaseAction {
  static metadata = {
    name: '领取精力消耗奖励',
    description: '领取精力消耗奖励',
    category: 'activity',
    serviceName: 'activity',
    module: 'energy',
    actionName: 'energy.claimReward',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "rewardIndex": {
            "type": "number",
            "required": true,
            "description": "rewardIndex参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, rewardIndex } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      rewardIndex
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取精力消耗奖励成功'
      };
    } else {
      throw new Error(`领取精力消耗奖励失败: ${response.message}`);
    }
  }
}

module.exports = EnergyclaimRewardAction;