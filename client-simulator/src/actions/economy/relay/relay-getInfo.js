/**
 * 获取联赛转播信息
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.getInfo
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.447Z
 */

const BaseAction = require('../../../core/base-action');

class RelaygetInfoAction extends BaseAction {
  static metadata = {
    name: '获取联赛转播信息',
    description: '获取联赛转播信息',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.getInfo',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取联赛转播信息成功'
      };
    } else {
      throw new Error(`获取联赛转播信息失败: ${response.message}`);
    }
  }
}

module.exports = RelaygetInfoAction;