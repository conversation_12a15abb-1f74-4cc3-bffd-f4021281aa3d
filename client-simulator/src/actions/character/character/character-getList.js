/**
 * 获取角色列表
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.getList
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.314Z
 */

const BaseAction = require('../../../core/base-action');

class CharactergetListAction extends BaseAction {
  static metadata = {
    name: '获取角色列表',
    description: '获取角色列表',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.getList',
    prerequisites: ["login","character"],
    params: {
      "getCharacterListDto": {
            "type": "object",
            "required": true,
            "description": "getCharacterListDto参数",
            "properties": {
                  "userId": {
                        "type": "string",
                        "required": false,
                        "description": "userId参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  },
                  "page": {
                        "type": "number",
                        "required": false,
                        "description": "page参数"
                  },
                  "limit": {
                        "type": "number",
                        "required": false,
                        "description": "limit参数"
                  }
            },
            "example": {
                  "userId": "示例userId",
                  "serverId": "示例serverId",
                  "page": 1,
                  "limit": 1
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { getCharacterListDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      getCharacterListDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取角色列表成功'
      };
    } else {
      throw new Error(`获取角色列表失败: ${response.message}`);
    }
  }
}

module.exports = CharactergetListAction;