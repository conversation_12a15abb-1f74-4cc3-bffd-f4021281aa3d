/**
 * 批量操作物品 对应old项目的批量操作逻辑
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.batchAddItems
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.386Z
 */

const BaseAction = require('../../../core/base-action');

class ItembatchAddItemsAction extends BaseAction {
  static metadata = {
    name: '批量操作物品 对应old项目的批量操作逻辑',
    description: '批量操作物品 对应old项目的批量操作逻辑',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.batchAddItems',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "items": {
            "type": "array",
            "required": true,
            "description": "items参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, items, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      items,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量操作物品 对应old项目的批量操作逻辑成功'
      };
    } else {
      throw new Error(`批量操作物品 对应old项目的批量操作逻辑失败: ${response.message}`);
    }
  }
}

module.exports = ItembatchAddItemsAction;