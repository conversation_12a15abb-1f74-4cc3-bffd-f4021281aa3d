/**
 * 根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.getItemQuantityByConfigId
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.382Z
 */

const BaseAction = require('../../../core/base-action');

class ItemgetItemQuantityByConfigIdAction extends BaseAction {
  static metadata = {
    name: '根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名',
    description: '根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.getItemQuantityByConfigId',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "configId": {
            "type": "number",
            "required": true,
            "description": "configId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, configId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      configId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名成功'
      };
    } else {
      throw new Error(`根据配置ID获取物品总数量 对应old项目: getItemNumByResID方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = ItemgetItemQuantityByConfigIdAction;