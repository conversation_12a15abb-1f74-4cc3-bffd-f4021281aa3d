/**
 * 增加球员合约天数 对应old项目: addHeroLeftDay
 * 
 * 微服务: hero
 * 模块: career
 * Controller: career
 * Pattern: career.addContractDays
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.481Z
 */

const BaseAction = require('../../../core/base-action');

class CareeraddContractDaysAction extends BaseAction {
  static metadata = {
    name: '增加球员合约天数 对应old项目: addHeroLeftDay',
    description: '增加球员合约天数 对应old项目: addHeroLeftDay',
    category: 'hero',
    serviceName: 'hero',
    module: 'career',
    actionName: 'career.addContractDays',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "days": {
            "type": "number",
            "required": true,
            "description": "days参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, days, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      days,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '增加球员合约天数 对应old项目: addHeroLeftDay成功'
      };
    } else {
      throw new Error(`增加球员合约天数 对应old项目: addHeroLeftDay失败: ${response.message}`);
    }
  }
}

module.exports = CareeraddContractDaysAction;