/**
 * 球员突破 对应old项目: breakOutHero
 * 
 * 微服务: hero
 * 模块: cultivation
 * Controller: cultivation
 * Pattern: cultivation.breakOut
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.495Z
 */

const BaseAction = require('../../../core/base-action');

class CultivationbreakOutAction extends BaseAction {
  static metadata = {
    name: '球员突破 对应old项目: breakOutHero',
    description: '球员突破 对应old项目: breakOutHero',
    category: 'hero',
    serviceName: 'hero',
    module: 'cultivation',
    actionName: 'cultivation.breakOut',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      },
      "arr": {
            "type": "array",
            "required": false,
            "description": "arr参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, index, arr, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      index,
      arr,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员突破 对应old项目: breakOutHero成功'
      };
    } else {
      throw new Error(`球员突破 对应old项目: breakOutHero失败: ${response.message}`);
    }
  }
}

module.exports = CultivationbreakOutAction;