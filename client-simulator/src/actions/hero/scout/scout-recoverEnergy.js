/**
 * 手动恢复球探体力 基于old项目的体力恢复机制
 * 
 * 微服务: hero
 * 模块: scout
 * Controller: scout
 * Pattern: scout.recoverEnergy
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.590Z
 */

const BaseAction = require('../../../core/base-action');

class ScoutrecoverEnergyAction extends BaseAction {
  static metadata = {
    name: '手动恢复球探体力 基于old项目的体力恢复机制',
    description: '手动恢复球探体力 基于old项目的体力恢复机制',
    category: 'hero',
    serviceName: 'hero',
    module: 'scout',
    actionName: 'scout.recoverEnergy',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '手动恢复球探体力 基于old项目的体力恢复机制成功'
      };
    } else {
      throw new Error(`手动恢复球探体力 基于old项目的体力恢复机制失败: ${response.message}`);
    }
  }
}

module.exports = ScoutrecoverEnergyAction;