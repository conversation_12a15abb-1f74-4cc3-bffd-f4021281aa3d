/**
 * 获取技能配置信息
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.getConfig
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.592Z
 */

const BaseAction = require('../../../core/base-action');

class SkillgetConfigAction extends BaseAction {
  static metadata = {
    name: '获取技能配置信息',
    description: '获取技能配置信息',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.getConfig',
    prerequisites: ["login","character"],
    params: {
      "skillId": {
            "type": "number",
            "required": true,
            "description": "skillId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { skillId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      skillId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取技能配置信息成功'
      };
    } else {
      throw new Error(`获取技能配置信息失败: ${response.message}`);
    }
  }
}

module.exports = SkillgetConfigAction;