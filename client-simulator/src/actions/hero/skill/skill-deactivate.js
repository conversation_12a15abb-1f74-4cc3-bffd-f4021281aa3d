/**
 * 取消激活球员技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.deactivate
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.600Z
 */

const BaseAction = require('../../../core/base-action');

class SkilldeactivateAction extends BaseAction {
  static metadata = {
    name: '取消激活球员技能',
    description: '取消激活球员技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.deactivate',
    prerequisites: ["login","character"],
    params: {
      "skillId": {
            "type": "string",
            "required": true,
            "description": "skillId参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { skillId, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      skillId,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '取消激活球员技能成功'
      };
    } else {
      throw new Error(`取消激活球员技能失败: ${response.message}`);
    }
  }
}

module.exports = SkilldeactivateAction;