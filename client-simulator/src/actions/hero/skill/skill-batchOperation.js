/**
 * 批量操作球员技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.batchOperation
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.605Z
 */

const BaseAction = require('../../../core/base-action');

class SkillbatchOperationAction extends BaseAction {
  static metadata = {
    name: '批量操作球员技能',
    description: '批量操作球员技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.batchOperation',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "operation": {
            "type": "object",
            "required": true,
            "description": "operation参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "'activate' | 'deactivate' | 'upgrade' | 'delete'类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      },
      "skillIds": {
            "type": "array",
            "required": true,
            "description": "skillIds参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, operation, skillIds, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      operation,
      skillIds,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '批量操作球员技能成功'
      };
    } else {
      throw new Error(`批量操作球员技能失败: ${response.message}`);
    }
  }
}

module.exports = SkillbatchOperationAction;