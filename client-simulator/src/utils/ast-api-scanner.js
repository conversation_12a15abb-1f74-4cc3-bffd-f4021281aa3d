/**
 * 基于AST的API扫描器
 * 
 * 功能：
 * 1. 使用TypeScript AST解析Controller文件
 * 2. 跨文件解析DTO类型定义
 * 3. 自动提取完整的参数结构
 * 4. 生成准确的Action类
 */

const fs = require('fs');
const path = require('path');
const { Project } = require('ts-morph');
const logger = require('./logger');

class ASTAPIScanner {
  constructor(options = {}) {
    this.options = {
      projectRoot: options.projectRoot || path.join(__dirname, '../../../..'),
      outputDir: options.outputDir || path.join(__dirname, '../actions'),
      ...options
    };

    // 加载TypeScript路径映射
    this.pathMappings = this.loadPathMappings();

    // 不使用tsconfig.json，直接配置编译选项
    this.project = new Project({
      compilerOptions: {
        target: 'ES2020',
        module: 'CommonJS',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        experimentalDecorators: true,
        emitDecoratorMetadata: true,
        baseUrl: this.options.projectRoot,
        paths: this.pathMappings
      },
      skipAddingFilesFromTsConfig: true
    });

    this.microservices = [];
    this.scannedAPIs = new Map();
    this.dtoCache = new Map();
  }

  /**
   * 加载TypeScript路径映射
   */
  loadPathMappings() {
    try {
      const tsconfigPath = path.join(this.options.projectRoot, 'tsconfig.json');
      if (fs.existsSync(tsconfigPath)) {
        const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf-8'));
        const paths = tsconfig.compilerOptions?.paths || {};

        logger.debug('📁 加载路径映射:', Object.keys(paths));
        return paths;
      }
    } catch (error) {
      logger.warn('⚠️ 加载tsconfig.json失败:', error.message);
    }

    return {};
  }

  /**
   * 扫描所有微服务接口
   */
  async scanAllAPIs() {
    logger.info('🔍 开始AST扫描微服务接口...');
    
    try {
      // 1. 发现微服务
      await this.discoverMicroservices();
      
      // 2. 扫描每个微服务的接口
      for (const microservice of this.microservices) {
        await this.scanMicroserviceAPIs(microservice);
      }
      
      // 3. 清理旧的Action文件
      await this.cleanOldActions();

      // 4. 生成Action类
      await this.generateActions();

      // 5. 生成接口文档
      await this.generateDocumentation();
      
      logger.success(`AST扫描完成！发现 ${this.scannedAPIs.size} 个API接口`);
      
      return {
        microservices: this.microservices.length,
        apis: this.scannedAPIs.size,
        categories: this.getCategories()
      };
      
    } catch (error) {
      logger.failure('AST扫描失败', error);
      throw error;
    }
  }

  /**
   * 发现微服务
   */
  async discoverMicroservices() {
    const appsDir = path.join(this.options.projectRoot, 'apps');
    
    if (!fs.existsSync(appsDir)) {
      throw new Error(`微服务目录不存在: ${appsDir}`);
    }
    
    const dirs = fs.readdirSync(appsDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
    
    for (const serviceName of dirs) {
      const servicePath = path.join(appsDir, serviceName);
      const srcPath = path.join(servicePath, 'src');
      
      if (fs.existsSync(srcPath)) {
        // 查找所有Controller文件
        const controllers = await this.findAllControllers(srcPath);
        
        this.microservices.push({
          name: serviceName,
          path: servicePath,
          srcPath: srcPath,
          controllers: controllers
        });
      }
    }
    
    logger.info(`📦 发现 ${this.microservices.length} 个微服务: ${this.microservices.map(s => s.name).join(', ')}`);
    
    // 显示每个服务的Controller数量
    for (const service of this.microservices) {
      logger.info(`  📂 ${service.name}: ${service.controllers.length} 个Controller`);
    }
  }

  /**
   * 查找所有Controller文件
   */
  async findAllControllers(srcPath) {
    const controllers = [];
    await this.scanDirectoryForControllers(srcPath, controllers, 0, srcPath);
    return controllers;
  }

  /**
   * 递归扫描目录查找Controller文件
   */
  async scanDirectoryForControllers(dirPath, controllers, depth = 0, srcPath = null) {
    if (depth > 5) return;
    
    try {
      const items = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          await this.scanDirectoryForControllers(itemPath, controllers, depth + 1, srcPath);
        } else if (item.isFile() && item.name.endsWith('.controller.ts')) {
          const controllerName = item.name.replace('.controller.ts', '');
          const moduleInfo = this.parseModulePath(itemPath, srcPath || dirPath);
          
          controllers.push({
            name: controllerName,
            path: path.dirname(itemPath),
            controllerPath: itemPath,
            module: moduleInfo.module,
            category: moduleInfo.category
          });
        }
      }
    } catch (error) {
      logger.warn(`扫描目录失败: ${dirPath} - ${error.message}`);
    }
  }

  /**
   * 解析模块路径
   */
  parseModulePath(controllerPath, srcPath) {
    const relativePath = path.relative(srcPath, controllerPath);
    const pathParts = relativePath.split(path.sep);
    
    pathParts.pop(); // 移除文件名
    
    let module = 'unknown';
    let category = 'general';
    
    const moduleIndex = pathParts.findIndex(part => part === 'modules' || part === 'domain');
    
    if (moduleIndex !== -1 && moduleIndex + 1 < pathParts.length) {
      category = pathParts[moduleIndex];
      module = pathParts[moduleIndex + 1];
    } else if (pathParts.length >= 1) {
      module = pathParts[pathParts.length - 1];
      category = pathParts[0];
    }
    
    return { module, category };
  }

  /**
   * 扫描微服务API
   */
  async scanMicroserviceAPIs(microservice) {
    logger.info(`🔍 AST扫描微服务: ${microservice.name}`);
    
    for (const controller of microservice.controllers) {
      await this.scanControllerAPIs(microservice, controller);
    }
  }

  /**
   * 使用AST扫描Controller API
   */
  async scanControllerAPIs(microservice, controller) {
    try {
      logger.debug(`📋 AST扫描Controller: ${microservice.name}.${controller.module}.${controller.name}`);
      
      // 添加Controller文件到项目
      const sourceFile = this.project.addSourceFileAtPath(controller.controllerPath);
      
      // 获取Controller类
      const controllerClass = sourceFile.getClasses()[0];
      if (!controllerClass) {
        logger.warn(`⚠️ 未找到Controller类: ${controller.controllerPath}`);
        return;
      }
      
      // 扫描所有方法
      const methods = controllerClass.getMethods();
      const apis = [];
      
      for (const method of methods) {
        const api = await this.parseMethodAPI(method, microservice.name, controller.module);
        if (api) {
          apis.push(api);
        }
      }
      
      // 保存API信息
      for (const api of apis) {
        const apiKey = `${microservice.name}.${api.pattern}`;
        this.scannedAPIs.set(apiKey, {
          ...api,
          microservice: microservice.name,
          module: controller.module,
          controller: controller.name,
          category: this.mapCategory(microservice.name, controller.module)
        });
      }
      
      logger.debug(`📋 ${microservice.name}.${controller.module}.${controller.name}: 发现 ${apis.length} 个API`);
      
    } catch (error) {
      logger.warn(`⚠️ AST扫描Controller失败: ${microservice.name}.${controller.module}.${controller.name} - ${error.message}`);
    }
  }

  /**
   * 解析方法API
   */
  async parseMethodAPI(method, serviceName, moduleName) {
    try {
      // 查找MessagePattern装饰器
      const messagePatternDecorator = method.getDecorator('MessagePattern');
      if (!messagePatternDecorator) {
        return null; // 不是API方法
      }
      
      // 提取pattern
      const args = messagePatternDecorator.getArguments();
      if (args.length === 0) {
        return null;
      }
      
      const pattern = args[0].getText().replace(/['"]/g, '');
      
      // 提取方法信息
      const methodName = method.getName();
      const returnType = method.getReturnTypeNode()?.getText() || 'any';
      
      // 提取注释
      const jsDoc = method.getJsDocs()[0];
      let description = jsDoc ? jsDoc.getDescription().trim() : this.generateDescription(pattern, methodName);

      // 清理描述中的换行符和多余空白
      description = description.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
      
      // 解析参数
      const params = await this.parseMethodParameters(method);
      
      return {
        pattern: pattern,
        method: methodName,
        params: params,
        returnType: returnType,
        description: description,
        category: this.mapCategory(serviceName, moduleName)
      };
      
    } catch (error) {
      logger.warn(`⚠️ 解析方法API失败: ${method.getName()} - ${error.message}`);
      return null;
    }
  }

  /**
   * 解析方法参数
   */
  async parseMethodParameters(method) {
    const params = {};

    try {
      const parameters = method.getParameters();

      for (const param of parameters) {
        // 查找@Payload装饰器
        const payloadDecorator = param.getDecorator('Payload');
        if (payloadDecorator) {
          // 获取参数类型
          const typeNode = param.getTypeNode();
          if (typeNode) {
            const typeName = typeNode.getText();

            // 解析DTO类型
            const dtoParams = await this.parseDTOType(typeName, method.getSourceFile());

            // 过滤掉injectedContext参数
            const filteredParams = this.filterInjectedParams(dtoParams);
            Object.assign(params, filteredParams);
          }
        }
      }

    } catch (error) {
      logger.warn(`⚠️ 解析方法参数失败: ${method.getName()} - ${error.message}`);
    }

    return params;
  }

  /**
   * 过滤网关注入的参数
   */
  filterInjectedParams(params) {
    const filteredParams = {};
    const excludeParams = ['injectedContext', 'context', 'injected'];

    for (const [key, value] of Object.entries(params)) {
      // 排除网关注入的参数
      if (!excludeParams.some(exclude => key.toLowerCase().includes(exclude.toLowerCase()))) {
        filteredParams[key] = value;
      } else {
        logger.debug(`🚫 过滤网关注入参数: ${key}`);
      }
    }

    return filteredParams;
  }

  /**
   * 解析DTO类型
   */
  async parseDTOType(typeName, sourceFile) {
    try {
      // 检查缓存
      if (this.dtoCache.has(typeName)) {
        return this.dtoCache.get(typeName);
      }

      logger.debug(`🔍 解析DTO类型: ${typeName}`);

      // 跳过基础类型
      if (['any', 'string', 'number', 'boolean', 'object', 'undefined', 'null'].includes(typeName.toLowerCase())) {
        logger.debug(`⏭️ 跳过基础类型: ${typeName}`);
        return {
          data: {
            type: this.mapTypeScriptTypeToJS(typeName),
            required: true,
            description: `${typeName}类型参数`
          }
        };
      }

      // 检查是否是内联类型定义（包含大括号）
      if (typeName.includes('{') && typeName.includes('}')) {
        return await this.parseInlineType(typeName);
      }

      // 如果sourceFile为null，尝试全局搜索DTO定义
      if (!sourceFile) {
        return await this.searchDTOGlobally(typeName);
      }

      // 在当前文件中查找类型定义
      let typeDefinition = sourceFile.getInterface(typeName) || sourceFile.getClass(typeName);

      // 如果在当前文件中没找到，查找导入
      if (!typeDefinition) {
        const importDeclarations = sourceFile.getImportDeclarations();

        for (const importDecl of importDeclarations) {
          const namedImports = importDecl.getNamedImports();
          const hasImport = namedImports.some(imp => imp.getName() === typeName);

          if (hasImport) {
            const moduleSpecifier = importDecl.getModuleSpecifierValue();
            const importPath = this.resolveImportPath(moduleSpecifier, sourceFile.getFilePath());

            logger.debug(`🔍 尝试导入路径: ${moduleSpecifier} -> ${importPath}`);

            if (importPath && fs.existsSync(importPath)) {
              try {
                const importedFile = this.project.addSourceFileAtPath(importPath);
                typeDefinition = importedFile.getInterface(typeName) || importedFile.getClass(typeName);

                if (typeDefinition) {
                  logger.debug(`✅ 在导入文件中找到DTO: ${typeName}`);
                  break;
                } else {
                  logger.debug(`⚠️ 导入文件中未找到DTO: ${typeName}`);
                }
              } catch (error) {
                logger.warn(`⚠️ 解析导入文件失败: ${importPath} - ${error.message}`);
              }
            } else {
              logger.warn(`⚠️ 导入文件不存在: ${importPath}`);
            }
          }
        }
      }

      const params = {};

      if (typeDefinition) {
        // 解析接口或类的属性
        const properties = typeDefinition.getProperties ? typeDefinition.getProperties() : [];

        for (const prop of properties) {
          const propName = prop.getName();
          const isOptional = prop.hasQuestionToken();

          // 获取类型信息 - 尝试多种方式
          let propTypeText = '';

          // 方式1：从类型节点获取
          const typeNode = prop.getTypeNode();
          if (typeNode) {
            propTypeText = typeNode.getText();
          } else {
            // 方式2：从类型获取
            const propType = prop.getType();
            propTypeText = propType.getText();
          }

          // 获取JSDoc注释
          const jsDoc = prop.getJsDocs()[0];
          const propDescription = jsDoc ? jsDoc.getDescription().trim() : `${propName}参数`;

          logger.debug(`🔍 属性解析: ${propName} -> ${propTypeText}`);

          params[propName] = {
            type: this.mapTypeScriptTypeToJS(propTypeText),
            required: !isOptional,
            description: propDescription
          };
        }
      } else {
        logger.warn(`⚠️ 未找到DTO类型定义: ${typeName}`);
        // 返回通用参数
        params.data = {
          type: 'object',
          required: true,
          description: `${typeName}类型的数据对象`
        };
      }

      // 缓存结果
      this.dtoCache.set(typeName, params);

      return params;

    } catch (error) {
      logger.warn(`⚠️ 解析DTO类型失败: ${typeName} - ${error.message}`);
      return {
        data: {
          type: 'object',
          required: true,
          description: `${typeName}类型的数据对象`
        }
      };
    }
  }

  /**
   * 解析内联类型定义
   */
  async parseInlineType(typeString) {
    const params = {};

    try {
      // 移除外层大括号和空白
      const content = typeString.replace(/^\s*\{\s*/, '').replace(/\s*\}\s*$/, '');

      // 更智能的属性分割，处理嵌套类型
      const properties = this.splitInlineProperties(content);

      for (const prop of properties) {
        const trimmed = prop.trim();
        if (!trimmed) continue;

        // 解析属性：name: type 或 name?: type
        const match = trimmed.match(/^(\w+)(\?)?:\s*(.+)$/);
        if (match) {
          const [, propName, optional, propType] = match;
          const cleanPropType = propType.trim();

          // 如果是复杂DTO类型，递归解析
          if (this.isComplexDTOType(cleanPropType)) {
            logger.debug(`🔍 递归解析复杂DTO类型: ${propName} -> ${cleanPropType}`);
            const nestedParams = await this.parseDTOType(cleanPropType, null);

            // 将嵌套参数作为对象结构保存
            params[propName] = {
              type: 'object',
              required: !optional,
              description: `${propName}参数`,
              properties: nestedParams
            };
          } else {
            params[propName] = {
              type: this.mapTypeScriptTypeToJS(cleanPropType),
              required: !optional,
              description: `${propName}参数`
            };
          }
        }
      }

    } catch (error) {
      logger.warn(`⚠️ 解析内联类型失败: ${error.message}`);
    }

    return params;
  }

  /**
   * 智能分割内联属性，处理嵌套类型
   */
  splitInlineProperties(content) {
    const properties = [];
    let current = '';
    let braceLevel = 0;
    let inString = false;
    let stringChar = '';

    for (let i = 0; i < content.length; i++) {
      const char = content[i];
      const prevChar = i > 0 ? content[i - 1] : '';

      // 处理字符串
      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = '';
        }
      }

      if (!inString) {
        // 处理大括号嵌套
        if (char === '{') {
          braceLevel++;
        } else if (char === '}') {
          braceLevel--;
        }

        // 在顶层遇到分号或逗号时分割
        if ((char === ';' || char === ',') && braceLevel === 0) {
          if (current.trim()) {
            properties.push(current.trim());
          }
          current = '';
          continue;
        }
      }

      current += char;
    }

    // 添加最后一个属性
    if (current.trim()) {
      properties.push(current.trim());
    }

    return properties;
  }

  /**
   * 判断是否为复杂DTO类型
   */
  isComplexDTOType(typeName) {
    // 排除基础类型
    const basicTypes = ['string', 'number', 'boolean', 'any', 'object', 'undefined', 'null'];
    const cleanType = typeName.replace(/[?\s]|undefined|null/g, '').toLowerCase();

    // 如果是数组类型，检查数组元素类型
    if (cleanType.includes('[]') || cleanType.startsWith('array<')) {
      return false; // 暂时不递归解析数组元素类型
    }

    // 如果是基础类型，不需要递归解析
    if (basicTypes.includes(cleanType)) {
      return false;
    }

    // 如果包含大括号，说明是内联类型，不需要递归
    if (typeName.includes('{') && typeName.includes('}')) {
      return false;
    }

    // 其他情况认为是复杂DTO类型，需要递归解析
    return true;
  }

  /**
   * 全局搜索DTO定义
   */
  async searchDTOGlobally(typeName) {
    logger.debug(`🌐 全局搜索DTO: ${typeName}`);

    // 常见的DTO文件路径模式
    const searchPaths = [
      `apps/*/src/**/*.dto.ts`,
      `apps/*/src/common/dto/*.ts`,
      `apps/*/src/modules/*/dto/*.ts`,
      `libs/*/src/**/*.dto.ts`,
      `libs/game-types/src/**/*.ts`,
      `libs/*/src/dtos/*.ts`
    ];

    for (const pattern of searchPaths) {
      const foundDTO = await this.searchDTOInPattern(typeName, pattern);
      if (foundDTO) {
        return foundDTO;
      }
    }

    // 如果没找到，返回通用对象
    logger.warn(`⚠️ 全局搜索未找到DTO: ${typeName}`);
    return {
      data: {
        type: 'object',
        required: true,
        description: `${typeName}类型的数据对象`
      }
    };
  }

  /**
   * 在指定模式中搜索DTO
   */
  async searchDTOInPattern(typeName, pattern) {
    const glob = require('glob');
    // 修复Windows路径问题：使用正斜杠进行glob搜索
    const fullPattern = path.join(this.options.projectRoot, pattern).replace(/\\/g, '/');

    try {
      const files = glob.sync(fullPattern);
      logger.debug(`🔍 搜索模式 ${pattern} (${fullPattern}) 找到 ${files.length} 个文件`);

      for (const filePath of files) {
        if (fs.existsSync(filePath)) {
          try {
            // 避免重复添加同一个文件
            let sourceFile;
            try {
              sourceFile = this.project.getSourceFile(filePath);
              if (!sourceFile) {
                sourceFile = this.project.addSourceFileAtPath(filePath);
              }
            } catch {
              sourceFile = this.project.addSourceFileAtPath(filePath);
            }

            const typeDefinition = sourceFile.getInterface(typeName) || sourceFile.getClass(typeName);

            if (typeDefinition) {
              logger.debug(`✅ 在文件中找到DTO: ${typeName} -> ${filePath}`);
              const result = await this.parseDTOFromDefinition(typeDefinition, typeName);
              logger.debug(`📋 解析结果:`, JSON.stringify(result, null, 2));
              return result;
            }
          } catch (error) {
            logger.debug(`⚠️ 搜索文件失败: ${filePath} - ${error.message}`);
          }
        }
      }
    } catch (error) {
      logger.debug(`⚠️ 搜索模式失败: ${pattern} - ${error.message}`);
    }

    return null;
  }

  /**
   * 从类型定义解析DTO
   */
  async parseDTOFromDefinition(typeDefinition, typeName) {
    const params = {};

    try {
      // 解析接口或类的属性
      const properties = typeDefinition.getProperties ? typeDefinition.getProperties() : [];

      for (const prop of properties) {
        const propName = prop.getName();
        const isOptional = prop.hasQuestionToken();

        // 获取类型信息
        let propTypeText = '';
        const typeNode = prop.getTypeNode();
        if (typeNode) {
          propTypeText = typeNode.getText();
        } else {
          const propType = prop.getType();
          propTypeText = propType.getText();
        }

        // 获取JSDoc注释
        const jsDoc = prop.getJsDocs()[0];
        const propDescription = jsDoc ? jsDoc.getDescription().trim() : `${propName}参数`;

        logger.debug(`🔍 属性解析: ${propName} -> ${propTypeText}`);

        params[propName] = {
          type: this.mapTypeScriptTypeToJS(propTypeText),
          required: !isOptional,
          description: propDescription
        };
      }

      // 缓存结果
      this.dtoCache.set(typeName, params);

    } catch (error) {
      logger.warn(`⚠️ 解析DTO定义失败: ${typeName} - ${error.message}`);
    }

    return params;
  }

  /**
   * 解析导入路径
   */
  resolveImportPath(moduleSpecifier, currentFilePath) {
    const currentDir = path.dirname(currentFilePath);

    if (moduleSpecifier.startsWith('.')) {
      // 相对路径
      return this.resolveRelativePath(moduleSpecifier, currentDir);
    } else {
      // TypeScript路径映射
      return this.resolvePathMapping(moduleSpecifier);
    }
  }

  /**
   * 解析相对路径
   */
  resolveRelativePath(moduleSpecifier, currentDir) {
    let resolvedPath = path.resolve(currentDir, moduleSpecifier);

    // 尝试不同的扩展名
    const extensions = ['.ts', '.js', '.d.ts'];

    for (const ext of extensions) {
      const pathWithExt = resolvedPath + ext;
      if (fs.existsSync(pathWithExt)) {
        logger.debug(`✅ 找到相对路径文件: ${pathWithExt}`);
        return pathWithExt;
      }
    }

    // 如果没有扩展名，尝试作为目录查找index文件
    if (!path.extname(resolvedPath)) {
      for (const ext of extensions) {
        const indexPath = path.join(resolvedPath, `index${ext}`);
        if (fs.existsSync(indexPath)) {
          logger.debug(`✅ 找到相对路径index文件: ${indexPath}`);
          return indexPath;
        }
      }
    }

    logger.warn(`⚠️ 未找到相对路径文件: ${moduleSpecifier} -> ${resolvedPath}`);
    return '';
  }

  /**
   * 解析TypeScript路径映射
   */
  resolvePathMapping(moduleSpecifier) {
    for (const [pattern, mappings] of Object.entries(this.pathMappings)) {
      // 将通配符模式转换为正则表达式
      const regexPattern = pattern.replace(/\*/g, '(.*)');
      const regex = new RegExp(`^${regexPattern}$`);
      const match = moduleSpecifier.match(regex);

      if (match) {
        // 找到匹配的模式
        for (const mapping of mappings) {
          let resolvedPath = mapping;

          // 替换通配符
          if (match[1]) {
            resolvedPath = resolvedPath.replace(/\*/g, match[1]);
          }

          // 转换为绝对路径
          resolvedPath = path.resolve(this.options.projectRoot, resolvedPath);

          // 尝试不同的扩展名
          const extensions = ['.ts', '.js', '.d.ts'];

          for (const ext of extensions) {
            const pathWithExt = resolvedPath + ext;
            if (fs.existsSync(pathWithExt)) {
              logger.debug(`✅ 找到路径映射文件: ${moduleSpecifier} -> ${pathWithExt}`);
              return pathWithExt;
            }
          }

          // 尝试作为目录查找index文件
          if (!path.extname(resolvedPath)) {
            for (const ext of extensions) {
              const indexPath = path.join(resolvedPath, `index${ext}`);
              if (fs.existsSync(indexPath)) {
                logger.debug(`✅ 找到路径映射index文件: ${moduleSpecifier} -> ${indexPath}`);
                return indexPath;
              }
            }
          }
        }
      }
    }

    logger.warn(`⚠️ 未找到路径映射: ${moduleSpecifier}`);
    return '';
  }

  /**
   * 映射TypeScript类型到JavaScript类型
   */
  mapTypeScriptTypeToJS(tsType) {
    if (!tsType) return 'any';

    // 清理类型字符串，但保持大小写
    const cleanType = tsType.replace(/[?\s]|undefined|null/g, '');

    // 检查数组类型
    if (cleanType.includes('[]') || cleanType.toLowerCase().startsWith('array<')) {
      return 'array';
    }

    // 基础类型映射（不区分大小写）
    const lowerType = cleanType.toLowerCase();
    if (lowerType === 'string') return 'string';
    if (lowerType === 'number') return 'number';
    if (lowerType === 'boolean') return 'boolean';
    if (lowerType === 'any') return 'any';

    // 枚举类型（通常以大写字母开头，如HeroPosition、HeroQuality）
    if (/^[A-Z][a-zA-Z]*$/.test(cleanType)) {
      return 'enum';
    }

    // 联合类型（包含 | 符号）
    if (cleanType.includes('|')) {
      return 'union';
    }

    // 默认为object
    return 'object';
  }

  /**
   * 生成描述
   */
  generateDescription(pattern, methodName) {
    const actionMap = {
      'create': '创建',
      'get': '获取',
      'update': '更新',
      'delete': '删除',
      'list': '列表',
      'join': '加入',
      'leave': '离开',
      'recruit': '招募',
      'train': '训练',
      'evolve': '升星',
      'battle': '战斗',
      'take': '领取',
      'buy': '购买'
    };
    
    for (const [key, desc] of Object.entries(actionMap)) {
      if (methodName.toLowerCase().includes(key) || pattern.toLowerCase().includes(key)) {
        return `${desc}操作`;
      }
    }
    
    return `${methodName}操作`;
  }

  /**
   * 映射分类
   */
  mapCategory(serviceName, moduleName) {
    const categoryMap = {
      'auth': 'auth',
      'character': 'character',
      'hero': 'hero',
      'social': 'social',
      'economy': 'economy',
      'match': 'match',
      'activity': 'activity'
    };
    
    return categoryMap[serviceName] || categoryMap[moduleName] || serviceName;
  }

  /**
   * 获取所有分类
   */
  getCategories() {
    const categories = new Set();
    for (const api of this.scannedAPIs.values()) {
      categories.add(api.category);
    }
    return Array.from(categories);
  }

  /**
   * 清理旧的Action文件
   */
  async cleanOldActions() {
    logger.info('🧹 清理旧的Action文件...');

    if (fs.existsSync(this.options.outputDir)) {
      await this.cleanDirectory(this.options.outputDir);
      logger.info('✅ 旧Action文件清理完成');
    }
  }

  /**
   * 递归清理目录
   */
  async cleanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        await this.cleanDirectory(itemPath);
        // 如果目录为空，删除目录
        if (fs.readdirSync(itemPath).length === 0) {
          fs.rmdirSync(itemPath);
        }
      } else if (item.isFile() && item.name.endsWith('.js')) {
        fs.unlinkSync(itemPath);
      }
    }
  }

  /**
   * 生成Action类
   */
  async generateActions() {
    logger.info('🏗️ 生成Action类...');

    const serviceModules = this.getServiceModules();

    for (const [serviceName, modules] of serviceModules) {
      for (const [moduleName, apis] of modules) {
        await this.generateModuleActions(serviceName, moduleName, apis);
      }
    }
  }

  /**
   * 获取服务/模块结构
   */
  getServiceModules() {
    const serviceModules = new Map();
    
    for (const api of this.scannedAPIs.values()) {
      const serviceName = api.microservice;
      const moduleName = api.module;
      
      if (!serviceModules.has(serviceName)) {
        serviceModules.set(serviceName, new Map());
      }
      
      const serviceMap = serviceModules.get(serviceName);
      if (!serviceMap.has(moduleName)) {
        serviceMap.set(moduleName, []);
      }
      
      serviceMap.get(moduleName).push(api);
    }
    
    return serviceModules;
  }

  /**
   * 生成模块Action
   */
  async generateModuleActions(serviceName, moduleName, apis) {
    let moduleDir;
    if (serviceName === 'gateway' || serviceName === 'auth') {
      moduleDir = path.join(this.options.outputDir, serviceName);
    } else {
      moduleDir = path.join(this.options.outputDir, serviceName, moduleName);
    }
    
    if (!fs.existsSync(moduleDir)) {
      fs.mkdirSync(moduleDir, { recursive: true });
    }
    
    for (const api of apis) {
      await this.generateSingleAction(moduleDir, api, serviceName, moduleName);
    }
    
    logger.debug(`📁 ${serviceName}/${moduleName}: 生成 ${apis.length} 个Action类`);
  }

  /**
   * 生成单个Action类
   */
  async generateSingleAction(moduleDir, api, serviceName, moduleName) {
    // 直接使用MessagePattern中的字符串作为actionName
    const actionName = api.pattern;
    const fileName = `${actionName.replace(/\./g, '-')}.js`;  // 文件名用短横线分隔
    const filePath = path.join(moduleDir, fileName);

    const actionCode = this.generateActionCode(api, actionName, serviceName, moduleName);

    fs.writeFileSync(filePath, actionCode);
  }

  /**
   * 生成Action代码
   */
  generateActionCode(api, actionName, serviceName, moduleName) {
    const className = this.toPascalCase(actionName.replace(/\./g, '')) + 'Action';
    
    let baseActionPath;
    if (serviceName === 'gateway' || serviceName === 'auth') {
      baseActionPath = '../../core/base-action';
    } else {
      baseActionPath = '../../../core/base-action';
    }
    
    return `/**
 * ${api.description}
 * 
 * 微服务: ${api.microservice}
 * 模块: ${api.module}
 * Controller: ${api.controller}
 * Pattern: ${api.pattern}
 * 
 * 使用AST自动生成于 ${new Date().toISOString()}
 */

const BaseAction = require('${baseActionPath}');

class ${className} extends BaseAction {
  static metadata = {
    name: '${api.description}',
    description: '${api.description}',
    category: '${serviceName}',
    serviceName: '${serviceName}',
    module: '${moduleName}',
    actionName: '${actionName}',
    prerequisites: ${this.generatePrerequisites(api)},
    params: ${this.formatParamsForGeneration(api.params)},
    timeout: 10000
  };

  async perform(client, params) {
    ${this.generateParamExtraction(api.params)}
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = \`\${this.constructor.metadata.serviceName}.\${this.constructor.metadata.actionName}\`;
    const response = await client.callAPI(fullActionName, {
      ${this.generateAPIParams(api.params)}
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '${api.description}成功'
      };
    } else {
      throw new Error(\`${api.description}失败: \${response.message}\`);
    }
  }
}

module.exports = ${className};`;
  }

  /**
   * 格式化参数用于代码生成
   */
  formatParamsForGeneration(params) {
    return JSON.stringify(params, (key, value) => {
      // 如果参数有properties属性，说明是嵌套对象，需要特殊处理
      if (value && typeof value === 'object' && value.properties) {
        return {
          ...value,
          // 将properties展开为更直观的结构
          example: this.generateExampleFromProperties(value.properties)
        };
      }
      return value;
    }, 6);
  }

  /**
   * 从属性生成示例对象
   */
  generateExampleFromProperties(properties) {
    const example = {};
    for (const [key, prop] of Object.entries(properties)) {
      if (prop.type === 'string') {
        example[key] = `示例${key}`;
      } else if (prop.type === 'number') {
        example[key] = 1;
      } else if (prop.type === 'boolean') {
        example[key] = true;
      } else if (prop.type === 'array') {
        example[key] = [];
      } else {
        example[key] = {};
      }
    }
    return example;
  }

  /**
   * 生成前置条件
   */
  generatePrerequisites(api) {
    const prerequisites = [];

    // 所有操作都需要登录（除了注册）
    if (!api.pattern.includes('register')) {
      prerequisites.push('login');
    }

    // 需要角色的操作（排除创建角色的操作）
    if (api.category !== 'auth' && !api.pattern.includes('create') && !api.pattern.includes('register')) {
      prerequisites.push('character');
    }

    // 需要球员的操作
    if (api.category === 'hero' && api.pattern.includes('formation')) {
      prerequisites.push('heroes');
    }

    return JSON.stringify(prerequisites);
  }

  /**
   * 生成参数提取代码
   */
  generateParamExtraction(params) {
    const paramNames = Object.keys(params);
    if (paramNames.length === 0) {
      return '// 无参数';
    }
    
    return `const { ${paramNames.join(', ')} } = params;`;
  }

  /**
   * 生成API参数
   */
  generateAPIParams(params) {
    const paramNames = Object.keys(params);
    return paramNames.join(',\n      ');
  }

  /**
   * 转换为PascalCase
   */
  toPascalCase(str) {
    // 如果已经是驼峰命名，直接转换首字母为大写
    if (!str.includes('-')) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    }
    // 如果是短横线命名，转换为PascalCase
    return str.replace(/(^|-)([a-z])/g, (match, p1, p2) => p2.toUpperCase());
  }

  /**
   * 生成接口文档
   */
  async generateDocumentation() {
    // 修正路径：保存到 game-client-simulator/docs 目录
    const docPath = path.join(__dirname, '../../docs/api-reference.md');
    const docContent = this.generateDocumentationContent();

    const docDir = path.dirname(docPath);
    if (!fs.existsSync(docDir)) {
      fs.mkdirSync(docDir, { recursive: true });
    }

    fs.writeFileSync(docPath, docContent);
    logger.info(`📚 API文档已生成: ${docPath}`);
  }

  /**
   * 生成文档内容
   */
  generateDocumentationContent() {
    let content = `# API接口文档\n\n`;
    content += `> 使用TypeScript AST自动生成于 ${new Date().toISOString()}\n\n`;
    content += `## 概览\n\n`;
    content += `- 微服务数量: ${this.microservices.length}\n`;
    content += `- API接口数量: ${this.scannedAPIs.size}\n`;
    content += `- 分类数量: ${this.getCategories().length}\n\n`;
    
    const categories = this.getCategories();
    
    for (const category of categories) {
      content += `## ${category.toUpperCase()} 分类\n\n`;
      
      const categoryAPIs = Array.from(this.scannedAPIs.values())
        .filter(api => api.category === category);
      
      for (const api of categoryAPIs) {
        content += `### ${api.pattern}\n\n`;
        content += `- **描述**: ${api.description}\n`;
        content += `- **微服务**: ${api.microservice}\n`;
        content += `- **模块**: ${api.module}\n`;
        content += `- **方法**: ${api.method}\n\n`;
        
        if (Object.keys(api.params).length > 0) {
          content += `**参数**:\n\n`;
          content += this.formatParametersForDoc(api.params, 0);
          content += `\n`;
        }
      }
    }
    
    return content;
  }

  /**
   * 格式化参数用于文档显示
   */
  formatParametersForDoc(params, indent = 0) {
    let content = '';
    const indentStr = '  '.repeat(indent);

    for (const [name, param] of Object.entries(params)) {
      const typeDisplay = this.getTypeDisplayName(param.type);
      const requiredText = param.required ? '**必需**' : '*可选*';

      content += `${indentStr}- \`${name}\` (${typeDisplay}) ${requiredText} - ${param.description}\n`;

      // 如果有嵌套属性，递归显示
      if (param.properties && Object.keys(param.properties).length > 0) {
        content += this.formatParametersForDoc(param.properties, indent + 1);
      }

      // 如果有示例，显示示例值
      if (param.example !== undefined) {
        const exampleStr = typeof param.example === 'object'
          ? JSON.stringify(param.example, null, 2)
          : param.example;
        content += `${indentStr}  - 示例: \`${exampleStr}\`\n`;
      }
    }

    return content;
  }

  /**
   * 获取类型的显示名称
   */
  getTypeDisplayName(type) {
    switch (type) {
      case 'string': return '字符串';
      case 'number': return '数字';
      case 'boolean': return '布尔值';
      case 'array': return '数组';
      case 'enum': return '枚举';
      case 'union': return '联合类型';
      case 'object': return '对象';
      default: return type;
    }
  }
}

module.exports = ASTAPIScanner;
