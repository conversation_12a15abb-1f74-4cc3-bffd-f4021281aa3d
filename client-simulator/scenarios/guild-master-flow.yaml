name: "公会会长流程"
description: "模拟玩家创建和管理公会的完整流程"
version: "1.0.0"
estimatedTime: "15分钟"
tags: ["公会", "管理", "社交"]

# 玩家配置
player:
  username: "guild_master_{{timestamp}}"
  password: "GuildMaster123!"
  characterName: "会长_{{timestamp}}"

# 执行步骤
steps:
  # 账号准备
  - name: "登录账号"
    action: "auth.login"
    description: "登录已有账号"
    params:
      username: "{{player.username}}"
      password: "{{player.password}}"
    
  - name: "确保角色存在"
    action: "character.ensure-exists"
    description: "确保有可用的角色"
    params:
      name: "{{player.characterName}}"
    
  # 资源准备
  - name: "检查金币"
    action: "character.get-resources"
    description: "检查当前资源状况"
    saveAs: "resources"
    
  - name: "充值金币"
    action: "economy.add-gold"
    description: "确保有足够金币创建公会"
    params:
      amount: 50000
      reason: "公会创建准备"
    condition: "context.resources.gold < 10000"
    
  # 公会创建
  - name: "创建公会"
    action: "social.create-guild"
    description: "创建新公会"
    params:
      name: "测试公会_{{timestamp}}"
      description: "这是一个自动化测试创建的公会"
      isPublic: true
    saveAs: "guild"
    
  # 公会管理
  - name: "设置公会公告"
    action: "social.set-guild-notice"
    description: "设置公会公告"
    params:
      guildId: "{{guild.guildId}}"
      notice: "欢迎加入我们的公会！"
    
  - name: "升级公会"
    action: "social.upgrade-guild"
    description: "升级公会等级"
    params:
      guildId: "{{guild.guildId}}"
    condition: "context.resources.gold >= 20000"
    
  # 成员管理
  - name: "邀请成员"
    action: "social.invite-guild-members"
    description: "邀请其他玩家加入公会"
    params:
      guildId: "{{guild.guildId}}"
      count: 3
    
  - name: "查看公会信息"
    action: "social.get-guild-info"
    description: "查看公会详细信息"
    params:
      guildId: "{{guild.guildId}}"
    saveAs: "guildInfo"

# 后置验证
postconditions:
  - condition: "context.guild.success === true"
    description: "公会创建成功"
  - condition: "context.guildInfo.memberCount >= 1"
    description: "公会至少有1名成员（会长）"
