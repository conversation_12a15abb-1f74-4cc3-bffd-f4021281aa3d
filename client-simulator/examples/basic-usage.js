/**
 * 基础使用示例
 * 
 * 演示如何使用游戏客户端模拟器进行基本的游戏操作
 */

const { GameClient, VirtualPlayer, logger } = require('../src');

async function basicExample() {
  logger.info('🚀 开始基础使用示例');
  
  // 创建游戏客户端
  const client = new GameClient({
    gateway: {
      url: 'http://127.0.0.1:3000'
    }
  });
  
  try {
    // 连接到游戏服务器
    await client.connect();
    logger.success('已连接到游戏服务器');
    
    // 创建虚拟玩家
    const player = new VirtualPlayer(client, {
      username: `test_${Date.now()}`,
      password: 'SecureP@ssw0rd!',
      email: `test_${Date.now()}@example.com`,
      characterName: `测试角色_${Date.now()}`
    });
    
    logger.info(`👤 创建虚拟玩家: ${player.playerData.username}`);
    
    // 执行基本操作流程
    logger.info('📋 开始执行基本操作流程...');
    
    // 1. 用户注册
    const registerResult = await player.perform('auth.register');
    if (registerResult.success) {
      logger.success('✅ 用户注册成功');
    }
    
    // 2. 用户登录
    const loginResult = await player.perform('auth.login');
    if (loginResult.success) {
      logger.success('✅ 用户登录成功');
    }
    
    // 3. 创建角色
    const characterResult = await player.perform('character.create', {
      userId: player.playerData.username,  // 使用用户名作为userId
      serverId: 'server_001',
      openId: `openid_${player.playerData.username}`,  // 生成openId
      name: player.playerData.characterName,
      avatar: 'default_avatar',
      faceIcon: 1
    });
    if (characterResult.success) {
      logger.success('✅ 角色创建成功');
    }
    
    // 4. 获取角色信息
    const infoResult = await player.perform('character.getInfo', {
      characterId: characterResult.data?.characterId || characterResult.characterId,  // 使用创建的角色ID
      serverId: 'server_001'
    });
    if (infoResult.success) {
      logger.success('✅ 获取角色信息成功');
      logger.info(`角色信息: ${JSON.stringify(infoResult.data, null, 2)}`);
    }
    
    // 显示玩家状态
    const playerSummary = player.getPlayerSummary();
    logger.info('🎮 玩家状态摘要:');
    logger.info(`  用户名: ${playerSummary.username}`);
    logger.info(`  角色ID: ${playerSummary.characterId}`);
    logger.info(`  执行操作数: ${playerSummary.actionCount}`);
    
    logger.success('🎉 基础操作流程完成！');
    
  } catch (error) {
    logger.failure('❌ 示例执行失败', error);
  } finally {
    // 断开连接
    await client.disconnect();
    logger.info('🔌 已断开连接');
  }
}

// 场景执行示例
async function scenarioExample() {
  logger.info('🎬 开始场景执行示例');
  
  const client = new GameClient();
  
  try {
    await client.connect();
    
    // 执行新手流程场景
    const result = await client.playScenario('newbie-complete-flow', {
      username: `newbie_${Date.now()}`,
      characterName: `新手角色_${Date.now()}`
    });
    
    if (result.success) {
      logger.success('✅ 新手流程场景执行成功');
      logger.info(`执行时长: ${result.duration}ms`);
      logger.info(`完成步骤: ${result.execution.steps.length}`);
    } else {
      logger.failure('❌ 新手流程场景执行失败', result.error);
    }
    
  } catch (error) {
    logger.failure('❌ 场景示例执行失败', error);
  } finally {
    await client.disconnect();
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--scenario')) {
    await scenarioExample();
  } else {
    await basicExample();
  }
}

// 运行示例
if (require.main === module) {
  main().catch(error => {
    logger.failure('程序异常退出', error);
    process.exit(1);
  });
}

module.exports = { basicExample, scenarioExample };
