let mongoClient = require("mongodb").MongoClient;
let ObjectID = require('mongodb').ObjectID;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let utils = require('../utils');
let timeUtils = require('../timeUtils');
let fs = require('fs');

let clusterConfig = require('../../../config/cluster.json');
let serversConfig = require('../../../config/servers.json');
let gameConfig = require('../../../config/game.json');

let toFixedRepeatData = require('../find_db/repeatData.json');

/******************** 查询过期的[玩家]数据，并处理删除 ********************/

let isJustFind = false;

let clusterDb = {};
let gameDbList = {};

let allModifyData = [];
let FileName = __dirname + "/allModifyData.json";

//通过解析文件修复数据

async.waterfall([
    function (callback) {
        if (!toFixedRepeatData) {
            logger.debug('no fix data');
            return callback('no fix data.');
        }
        if (isJustFind) {
            //logger.debug('to fix data: ', toFixedRepeatData);
        }
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            clusterDb = dbclient;
            let db = dbclient.db(clusterConfig.clusterDBName);
            let clusterCollections = [];
            for (let key in toFixedRepeatData.cluster) {
                if (toFixedRepeatData.cluster[key].length > 0) {
                    clusterCollections.push(key);
                }
            }
            logger.debug("need update collection name: ", clusterCollections);
            async.eachSeries(clusterCollections, function (colName, cb1) {
                db.collection(colName, function (err, col) {
                    async.eachSeries(toFixedRepeatData.cluster[colName], function (dataArr, cb2) {
                        let dateList = [];
                        // logger.error("2222222222222", colName, dataArr);
                        async.eachSeries(dataArr, function (fixObj, cb3) {
                            logger.debug('cluster to fix srcUid, toUid: ', fixObj.srcUid, fixObj.toUid);
                            if (isJustFind) {
                                //logger.debug('cluster colName[%s] to fix obj:', colName, fixObj);
                                return cb3();
                            }
                            let cursor = col.find({_id: new ObjectID(fixObj.objectId)});
                            cursor.forEach(function (date) {
                                dateList.push(date);
                                cb3(null);
                            })
                        }, function (err) {
                            let wrongDataList = perform(colName, dateList);
                            async.eachSeries(wrongDataList, function (wrongData, cb5) {
                                //logger.error("7777777777777777", colName, wrongData);
                                allModifyData.push({colName: colName, _id: wrongData._id, uid: wrongData.uid});
                                col.updateOne({
                                    _id: new ObjectID(wrongData._id)
                                },{
                                    $set: wrongData
                                    //$set: {oid: wrongData.oid}
                                }, function (err) {
                                    if(!!err){
                                        logger.warn('update err',err);
                                    }
                                    cb5(null);
                                });
                            });
                            cb2()
                        });
                    }, function () {
                        cb1();
                    })
                });
            },function(err) {
                logger.debug("fix cluster ready.");
                callback(null);
            });
        });
    },
    function (callback) {
        let gameServerList = [];
        for (let sid in toFixedRepeatData.game) {
            gameServerList.push(sid);
        }
        logger.debug('need fix game server list:', gameServerList);
        async.eachSeries(gameServerList, function (serverId, cb1) {
            let gameDbUrl = "mongodb://" + serverId+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbclient) {
                if (error) {
                    logger.error("connect gameDbUrl failed! err: " + error);
                    return callback(error);
                }
                gameDbList[serverId] = dbclient;
                let db = dbclient.db(serverId);
                let gameCollections = [];
                for (let key in toFixedRepeatData.game[serverId]) {
                    if (toFixedRepeatData.game[serverId][key].length > 0) {
                        gameCollections.push(key);
                    }
                }
                logger.debug("need update game collection name: ", gameCollections);
                async.eachSeries(gameCollections, function (colName, cb2) {
                    db.collection(colName, function (err, col) {
                        async.eachSeries(toFixedRepeatData.game[serverId][colName], function (dataArr, cb3) {
                            logger.debug('fix collection name: ', colName, dataArr.length);
                            let dateList = [];
                            // logger.error("************", colName, dataArr);
                            async.eachSeries(dataArr, function (fixObj, cb4) {
                                logger.debug('gameserver[%s] to fix srcUid, toUid: ', serverId, fixObj.srcUid,
                                    fixObj.toUid, fixObj.objectId);
                                if (isJustFind) {
                                    return cb4();
                                }
                                let cursor = col.find({_id: new ObjectID(fixObj.objectId)});
                                cursor.forEach(function (date) {
                                    dateList.push(date);
                                    cb4(null);
                                })
                            }, function (err) {
                                let wrongDataList = perform(colName, dateList);
                                async.eachSeries(wrongDataList, function (wrongData, cb5) {
                                    //logger.error("88888888888888888", colName, wrongData);
                                    allModifyData.push({colName: colName, _id: wrongData._id, uid: wrongData.uid});
                                    col.updateOne({
                                        _id: new ObjectID(wrongData._id)
                                    },{
                                        $set: {uid: wrongData.uid}
                                    }, function (err) {
                                        if(!!err){
                                            logger.warn('update err',err);
                                        }
                                        cb5(null);
                                    });
                                });
                                cb3()
                            });
                        }, function () {
                            cb2();
                        })
                    });
                },function(err) {
                    logger.debug("fix game[%s] ready.", serverId);
                    cb1(null);
                });
            })
        }, function (err) {
            logger.debug("all game fix finished.");
            callback(null);
        });
    },
], function (err) {
    if (!!err) {
        process.exit();
        return ;
    }
    clusterDb.close();
    for(let key in gameDbList) {
        gameDbList[key].close();
    }
    let fd = fs.openSync(FileName, 'w');//打开文件
    fs.writeSync(fd, JSON.stringify(allModifyData,"","\t"));
    fs.closeSync(fd);//关闭文件
    process.exit();
});

function perform(colName, arr) {
    let wrongDataList = [];
    switch (colName) {
        case "account":
            wrongDataList = handling2(arr, "level", "fansCount", "actualStrength", "");
            for(let i in wrongDataList)
            {
                wrongDataList[i].oid = wrongDataList[i].oid + "!";
            }
            break;
        case "matchRank":
            wrongDataList = handling2(arr, "lastUpdateTimes", "", "", "");
            break;
        case "player":
            wrongDataList = handling2(arr, "level", "fame", "", "");
            break;
        case "heros":
            wrongDataList = handling3(arr, "heros", "", "", "");
            break;
        case "email":
            wrongDataList = handling2(arr, "emails", "CreateTime", "", "");
            break;
        case "item":
            wrongDataList = handling2(arr, "item", "", "", "");
            break;
        case "bag":
            wrongDataList = handling2(arr, "itemUidToBookMarkId", "", "", "");
            break;
        case "teamFormation":
            wrongDataList = handling4(arr, "teamFormations", "ActualStrength", "", "");
            break;
        case "leagueCopy":
            wrongDataList = handling4(arr, "leagueCopys", "CopyData", "IsFinshed", "");
            break;
        case "scout":
            wrongDataList = handling2(arr, "reTime", "", "", "");
            break;
        case "tasks":
            wrongDataList = handling2(arr, "resetTime", "", "", "");
            break;
        case "footballGround":
            wrongDataList = handling3(arr, "adminGround", "Level", "", "");
            break;
        case "businessMatch":
            wrongDataList = handling3(arr, "matchRecordList", "", "", "");
            break;
        case "trophyCopy":
            wrongDataList = handling2(arr, "lastUpdateTimes", "", "", "");
            break;
        case "trainer":
            wrongDataList = handling3(arr, "allTrainer", "", "", "");
            break;
        case "follow":
            wrongDataList = handling2(arr, "refreshTime", "", "", "");
            break;
        case "store":
            wrongDataList = handling2(arr, "everyDayReTime", "weeklyReTime", "monthlyReTime", "");
            break;
        case "newPlayerSign":
            wrongDataList = handling5(arr, "signInfo", "state");
            break;
        case "sevenDaySign":
            wrongDataList = handling5(arr, "signInfo", "state", "", "");
            break;
        case "everyDaySign":
            wrongDataList = handling5(arr, "signInfo", "state", "", "");
            break;
        case "vipShop":
            wrongDataList = handling3(arr, "goodsBuyList", "", "", "");
            break;
        case "seasonStore":
            wrongDataList = handling2(arr, "refreshTime", "", "", "");
            break;
        case "act":
            wrongDataList = handling3(arr, "globalActMgrInfo", "", "", "");
            break;
        case "limitStore":
            wrongDataList = handling2(arr, "everyDayReTime", "", "", "");
            break;
        case "offlineEvent":
            wrongDataList = handling3(arr, "offlineEventList", "", "", "");
            break;
        case "newerGuide":
            wrongDataList = handling3(arr, "guideFinnishList", "", "", "");
            break;
        case "worldCup":
            wrongDataList = handling2(arr, "reTime", "", "", "");
            break;
        case "everyDayEnergy":
            wrongDataList = handling2(arr, "lastUpdateTime", "", "", "");
            break;
        case "newerTask":
            wrongDataList = handling3(arr, "finishTask", "", "", "");
            break;
        case "middleEastCup":
            wrongDataList = handling2(arr, "flashTime", "", "", "");
            break;
        case "gulfCup":
            wrongDataList = handling2(arr, "flashTime", "", "", "");
            break;
        case "relay":
            wrongDataList = handling2(arr, "buyRelayTime", "", "", "");
            break;
        case "MLS":
            wrongDataList = handling2(arr, "flashTime", "", "", "");
            break;
        case "sign":
            wrongDataList = handling2(arr, "startTime", "", "", "");
            break;
        case "commonActivity":
            wrongDataList = handling2(arr, "startTime", "", "", "");
            break;
        case "beliefSkill":
            wrongDataList = handling4(arr, "skillList", "level", "", "");
            break;
    }
    return wrongDataList
}
//param1 - 4 为属性名 如果属性是数组 比长度
function handling2(arr, param1, param2, param3, param4) {
    let wrongDataList = [];
    for(let i = 0; i < arr.length; i++)
    {
        for(let k = i + 1; k < arr.length; k++)
        {
            if(!arr[i][param1])
            {
                wrongDataList.push(arr[i]);
                continue;
            }
            if(!arr[k][param1])
            {
                wrongDataList.push(arr[k]);
                continue;
            }
            if(param1 !== "" && arr[i][param1] < arr[k][param1])
            {
                wrongDataList.push(arr[i]);
            }
            else if(param2 !== "" && arr[i][param2] < arr[k][param2])
            {
                wrongDataList.push(arr[i]);
            }
            else if(param3 !== "" && arr[i][param3] < arr[k][param3])
            {
                wrongDataList.push(arr[i]);
            }
            else if(param4 !== "" && arr[i][param4] < arr[k][param4])
            {
                wrongDataList.push(arr[i]);
            }
            else
            {
                wrongDataList.push(arr[k]);
            }
        }
    }
    for(let i in wrongDataList)
    {
        wrongDataList[i].uid = wrongDataList[i].uid + "!";
    }
    //logger.error("!!!!!!!!!!!!!!!!!!1", wrongDataList);
    return unique(wrongDataList);
}
//主要比较数组长度 然后比较数组第一个元素的param2
function handling3(arr, param1, param2, param3, param4) {
    let wrongDataList = [];
    for(let i = 0; i < arr.length; i++)
    {
        for(let k = i + 1; k < arr.length; k++)
        {
            if(!arr[i][param1])
            {
                wrongDataList.push(arr[i]);
                continue;
            }
            if(!arr[k][param1])
            {
                wrongDataList.push(arr[k]);
                continue;
            }
            if(arr[i][param1].length < arr[k][param1].length)
            {
                wrongDataList.push(arr[i]);
            }
            else if(param2 !== "" && arr[i][param1][0][param2] < arr[k][param1][0][param2])
            {
                wrongDataList.push(arr[i]);
            }
            else
            {
                wrongDataList.push(arr[k]);
            }
        }
    }
    for(let i in wrongDataList)
    {
        wrongDataList[i].uid = wrongDataList[i].uid + "!";
    }
    //logger.error("!!!!!!!!!!!!!!!!!!2", wrongDataList);
    return unique(wrongDataList);
};
//循环得出数组里的param2最大的进行比较
function handling4(arr, param1, param2, param3, param4) {
    let wrongDataList = [];
    for(let i = 0; i < arr.length; i++)
    {
        for(let k = i + 1; k < arr.length; k++)
        {
            if(!arr[i][param1])
            {
                wrongDataList.push(arr[i]);
                continue;
            }
            if(!arr[k][param1])
            {
                wrongDataList.push(arr[k]);
                continue;
            }
            let iIndex = 0;
            let kIndex = 0;
            if(param3 === "")
            {
                for(let p in arr[i][param1])
                {
                    if(iIndex < arr[i][param1][p][param2])
                        iIndex = arr[i][param1][p][param2];
                }
                for(let p in arr[k][param1])
                {
                    if(kIndex < arr[k][param1][p][param2])
                        kIndex = arr[k][param1][p][param2];
                }
            }
            else if(param4 === "")
            {
                for(let p in arr[i][param1])
                {
                    for(let o in arr[i][param1][p][param2])
                    {
                        if(arr[i][param1][p][param2][o][param3] === 1)
                            iIndex++;
                    }
                }
                for(let p in arr[k][param1])
                {
                    for(let o in arr[k][param1][p][param2])
                    {
                        if(arr[k][param1][p][param2][o][param3] === 1)
                            kIndex++;
                    }
                }
            }
            else
            {

            }

            if(iIndex < kIndex)
            {
                wrongDataList.push(arr[i]);
            }
            else
            {
                wrongDataList.push(arr[k]);
            }
        }
    }
    for(let i in wrongDataList)
    {
        wrongDataList[i].uid = wrongDataList[i].uid + "!";
    }
    //logger.error("!!!!!!!!!!!!!!!!!!2", wrongDataList);
    return unique(wrongDataList);
};

//签到类基本是这个亚子
function handling5(arr, param1, param2) {
    let wrongDataList = [];
    for(let i = 0; i < arr.length; i++)
    {
        for(let k = i + 1; k < arr.length; k++)
        {
            if(!arr[i][param1])
            {
                wrongDataList.push(arr[i]);
                continue;
            }
            if(!arr[k][param1])
            {
                wrongDataList.push(arr[k]);
                continue;
            }
            let iIndex = 0;
            let kIndex = 0;
            for(let p in arr[i][param1])
            {
                if(arr[i][param1][p][param2] !== 0)
                    iIndex++;
            }
            for(let p in arr[k].signInfo)
            {
                if(arr[k][param1][p][param2] !== 0)
                    kIndex++;
            }
            if(iIndex < kIndex)
            {
                wrongDataList.push(arr[i]);
            }
            else
            {
                wrongDataList.push(arr[k]);
            }
        }
    }
    for(let i in wrongDataList)
    {
        wrongDataList[i].uid = wrongDataList[i].uid + "!";
    }
    //logger.error("!!!!!!!!!!!!!!!!!!2", wrongDataList);
    return unique(wrongDataList);
};

function unique(arr){
    for(var i=0; i<arr.length; i++){
        for(var j=i+1; j<arr.length; j++){
            if(arr[i]._id==arr[j]._id){         //第一个等同于第二个，splice方法删除第二个
                arr.splice(j,1);
                j--;
            }
        }
    }
    return arr;
}

// function perform(colName, arr) {
//     let date;
//     let wrongDataList = [];
//
//     if(colName === "account")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].level)
//                 {
//                     arr[i].oid = arr[i].oid + "!";
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(!arr[k].level)
//                 {
//                     arr[k].oid = arr[k].oid + "!";
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].level < arr[k].level)
//                 {
//                     date = arr[k].uid;
//                     arr[i].oid = arr[i].oid + "!";
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].fansCount < arr[k].fansCount)
//                 {
//                     date = arr[k].uid;
//                     arr[i].oid = arr[i].oid + "!";
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].actualStrength < arr[k].actualStrength)
//                 {
//                     date = arr[k].uid;
//                     arr[i].oid = arr[i].oid + "!";
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].oid = arr[k].oid + "!";
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "matchRank")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].lastUpdateTimes)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].lastUpdateTimes)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].lastUpdateTimes < arr[k].lastUpdateTimes)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "player")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].level)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].level)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].level < arr[k].level)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].fame < arr[k].fame)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "heros")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].heros)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].heros)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 for(let p in arr[i].heros)
//                 {
//                     if(arr[i].heros[p].ResID === arr[k].heros[p].ResID && arr[i].heros[p].twoLevelAttr.BallerRating.Cur < arr[k].heros[p].twoLevelAttr.BallerRating.Cur)
//                     {
//                         date = arr[k].uid;
//                         arr[i].uid = arr[i].uid + "!";
//                         wrongDataList.push(arr[i]);
//                         break;
//                     }
//                     else
//                     {
//                         date = arr[i].uid;
//                         arr[k].uid = arr[k].uid + "!";
//                         wrongDataList.push(arr[k]);
//                         break;
//                     }
//                 }
//             }
//         }
//     }
//     else if(colName === "email")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].emails)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].emails)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].emails.length < arr[k].emails.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].emails[0].CreateTime < arr[k].emails[0].CreateTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "item")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].item)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].item)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].item.length < arr[k].item.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "bag")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].itemUidToBookMarkId)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].itemUidToBookMarkId)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].itemUidToBookMarkId.length < arr[k].itemUidToBookMarkId.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "teamFormation")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 let iActualStrength = 0;
//                 let kActualStrength = 0;
//                 if(!arr[i].teamFormations)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].teamFormations)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 for(let p in arr[i].teamFormations)
//                 {
//                     if(iActualStrength < arr[i].teamFormations[p].ActualStrength)
//                         iActualStrength = arr[i].teamFormations[p].ActualStrength;
//                 }
//                 for(let p in arr[k].teamFormations)
//                 {
//                     if(kActualStrength < arr[k].teamFormations[p].ActualStrength)
//                         kActualStrength = arr[k].teamFormations[p].ActualStrength;
//                 }
//                 if(iActualStrength < kActualStrength)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "leagueCopy")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 let kNum = 0;
//                 let iNum = 0;
//                 if(!arr[i].leagueCopys)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].leagueCopys)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 for(let p in arr[i].leagueCopys)
//                 {
//                     for(let o in arr[i].leagueCopys[p].CopyData)
//                     {
//                         if(arr[i].leagueCopys[p].CopyData[o].IsFinshed === 1)
//                             iNum++;
//                     }
//                 }
//                 for(let p in arr[k].leagueCopys)
//                 {
//                     for(let o in arr[k].leagueCopys[p].CopyData)
//                     {
//                         if(arr[k].leagueCopys[p].CopyData[o].IsFinshed === 1)
//                             kNum++;
//                     }
//                 }
//                 if(iNum < kNum)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "scout")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].reTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].reTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].reTime < arr[k].reTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "tasks")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].resetTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].resetTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].resetTime < arr[k].resetTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "footballGround")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].adminGround)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].adminGround)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].adminGround.length < arr[k].adminGround.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].adminGround[0].Level < arr[k].adminGround[0].Level)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else  if(arr[i].mainGround.length < arr[k].mainGround.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].mainGround[0].Level < arr[k].mainGround[0].Level)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "businessMatch")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].matchRecordList)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].matchRecordList)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].matchRecordList.length < arr[k].matchRecordList.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "trophyCopy")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].lastUpdateTimes)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].lastUpdateTimes)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].lastUpdateTimes < arr[k].lastUpdateTimes)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "trainer")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].allTrainer)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].allTrainer)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].allTrainer.length < arr[k].allTrainer.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "follow")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].refreshTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].refreshTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].refreshTime < arr[k].refreshTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     } else if(colName === "store")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].everyDayReTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].everyDayReTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].everyDayReTime < arr[k].everyDayReTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].weeklyReTime < arr[k].weeklyReTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].monthlyReTime < arr[k].monthlyReTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "newPlayerSign")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].signInfo)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].signInfo)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 let iNum = 0;
//                 let kNum = 0;
//                 for(let p in arr[i].signInfo)
//                 {
//                     if(arr[i].signInfo[p].state !== 0)
//                         iNum++;
//                 }
//                 for(let p in arr[k].signInfo)
//                 {
//                     if(arr[k].signInfo[p].state !== 0)
//                         kNum++;
//                 }
//                 if(iNum < kNum)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "sevenDaySign")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].signInfo)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].signInfo)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 let iNum = 0;
//                 let kNum = 0;
//                 for(let p in arr[i].signInfo)
//                 {
//                     if(arr[i].signInfo[p].state !== 0)
//                         iNum++;
//                 }
//                 for(let p in arr[k].signInfo)
//                 {
//                     if(arr[k].signInfo[p].state !== 0)
//                         kNum++;
//                 }
//                 if(iNum < kNum)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "everyDaySign")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].signInfo)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].signInfo)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 let iNum = 0;
//                 let kNum = 0;
//                 for(let p in arr[i].signInfo)
//                 {
//                     if(arr[i].signInfo[p].state !== 0)
//                         iNum++;
//                 }
//                 for(let p in arr[k].signInfo)
//                 {
//                     if(arr[k].signInfo[p].state !== 0)
//                         kNum++;
//                 }
//                 if(iNum < kNum)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "vipShop")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].goodsBuyList)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].goodsBuyList)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 let iNum = 0;
//                 let kNum = 0;
//                 for(let p in arr[i].goodsBuyList)
//                 {
//                     for(let o in arr[i].goodsBuyList[p])
//                     {
//                         if(arr[i].goodsBuyList[p][o].takeStatus !== 0)
//                             iNum++;
//                     }
//                 }
//                 for(let p in arr[k].goodsBuyList)
//                 {
//                     for(let o in arr[k].goodsBuyList[p])
//                     {
//                         if(arr[k].goodsBuyList[p][o].takeStatus !== 0)
//                             kNum++;
//                     }
//                 }
//
//                 if(iNum < kNum)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     } else if(colName === "seasonStore")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].refreshTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].refreshTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].refreshTime < arr[k].refreshTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "act")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].globalActMgrInfo)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].globalActMgrInfo)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].globalActMgrInfo.length < arr[k].globalActMgrInfo.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "limitStore")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].everyDayReTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].everyDayReTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].everyDayReTime < arr[k].everyDayReTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "offlineEvent")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].offlineEventList)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].offlineEventList)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].offlineEventList.length < arr[k].offlineEventList.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "newerGuide")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].guideFinnishList)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].guideFinnishList)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].guideFinnishList.length < arr[k].guideFinnishList.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else if(arr[i].triggerFinishList.length < arr[k].triggerFinishList.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "worldCup")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].reTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].reTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].reTime < arr[k].reTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "everyDayEnergy")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].lastUpdateTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].lastUpdateTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].lastUpdateTime < arr[k].lastUpdateTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     } else if(colName === "newerTask")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].finishTask)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].finishTask)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].finishTask.length < arr[k].finishTask.length)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "middleEastCup")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].flashTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].flashTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].flashTime < arr[k].flashTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "gulfCup")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].flashTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].flashTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].flashTime < arr[k].flashTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "relay")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].buyRelayTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].buyRelayTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].buyRelayTime < arr[k].buyRelayTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "MLS")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].flashTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].flashTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].flashTime < arr[k].flashTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "sign")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].startTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].startTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].startTime < arr[k].startTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     else if(colName === "commonActivity")
//     {
//         for(let i = 0; i < arr.length; i++)
//         {
//             for(let k = i + 1; k < arr.length; k++)
//             {
//                 if(!arr[i].startTime)
//                 {
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                     continue;
//                 }
//                 if(!arr[k].startTime)
//                 {
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                     continue;
//                 }
//                 if(arr[i].startTime < arr[k].startTime)
//                 {
//                     date = arr[k].uid;
//                     arr[i].uid = arr[i].uid + "!";
//                     wrongDataList.push(arr[i]);
//                 }
//                 else
//                 {
//                     date = arr[i].uid;
//                     arr[k].uid = arr[k].uid + "!";
//                     wrongDataList.push(arr[k]);
//                 }
//             }
//         }
//     }
//     return unique(wrongDataList);
// }
