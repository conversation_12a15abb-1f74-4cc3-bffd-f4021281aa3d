let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');

let clusterConfig = require("../../../config/cluster.json");
let gameConfig = require("../../../config/game.json");

let playerUid = "w0SVG7OGtyrkEHrTEqTKD6dN";     //郭小丑666
let playerGid = "game-outerTest-3";

// let playerUid = "WkwpYp_twSXT7EmAv__wZM-X";     //m02 - 内网
// let playerGid = "game-outerTest-1";

//var clusterDbUser = clusterConfig.clusterDBName + '-admin';
//var clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;

async.waterfall([
    function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }
            let db = dbclient.db(playerGid);
            let allheros = {};
            db.collection("heros", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    logger.debug("err2, doc", err2, doc);
                    allheros = doc.heros;
                    for(let i=0, len=allheros.length; i<len; i++) {
                        logger.debug("uid: %s, TreatyDay: %d", allheros[i].Uid, allheros[i].TreatyDay, allheros[i].TreatyReTime);
                        allheros[i].TreatyDay = 0;
                    }
                    col1.updateOne({uid: playerUid}, 
                        {$set: {heros: allheros}},
                        function (err3) {
                            logger.debug("update err: ", err3);
                            dbclient.close();
                            callback(null);
                        });
                })
            });
        });
    }], function (err) {
        logger.debug("fix hero data finished .");
        process.exit();
});
