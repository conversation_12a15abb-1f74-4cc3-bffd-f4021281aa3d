let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require("fs");

let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dataApi = require('../dataApi');

let isFixed = false;

let dbMap = new Map();
let resultMap = new Map();

let transConfig = {};

let searchDate = "2020-04-01 00:00:00";
let searchTime = (new Date(searchDate)).getTime();

async.waterfall([
    function (callback) {
        //初始化配置表
        setTimeout(()=>{
            let config = dataApi.allData.data["Field"];
            for(let k in config) {
                //球队等级，新政楼等级
                if(config[k].Type === 2) {
                    let condition = config[k].Condition;
                    let arr = condition.split('|');
                    for(let i=0;i<arr.length;i++) {
                        let tmp = arr[i].split('*');
                        let type = parseInt(tmp[0]);
                        let level = parseInt(tmp[1]);
                        if(type === 7) {
                            transConfig[level] = config[k];
                        }
                    }
                }
            }
            //logger.debug("transConfig: ", transConfig);
            callback();
        },2000)
    },
    function (callback) {
        async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let dbUser = serverId + '-admin';
            let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if(!!error) {
                    logger.error("connect game db fail.", error, serverId);
                    return cb1(error);
                }
                dbMap.set(serverId, dbclient);
                let db = dbclient.db(serverId);
                let num = 0;
                db.collection("player", function (err, col) {
                    col.find({leaveTime: {$gt: searchTime}}).toArray(function (err, playerList) {
                        db.collection("footballGround", function (err, col1) {
                            async.eachSeries(playerList, function (player, cb2) {
                                col1.findOne({uid: player.uid}, function (err, groundDoc) {
                                    if (err) {
                                        logger.debug("find footballGround err: ", player.uid);
                                        return cb2();
                                    }
                                    //logger.debug("groundDoc: ", groundDoc);
                                    if (!groundDoc || !groundDoc.mainGround) {
                                        return cb2();
                                    }
                                    if(!groundDoc.mainGround[0]) {
                                        logger.debug("groundDoc.mainGround[0] is null: ", groundDoc.mainGround, player.level);
                                        return cb2();
                                    }
                                    let mainGroundLevel = groundDoc.mainGround[0].Level;
                                    let maxLevel = 0;
                                    for (let k in transConfig) {
                                        if (player.level < k) {
                                            break;
                                        }
                                        maxLevel = transConfig[k].Level + 1;
                                    }
                                    if(mainGroundLevel > maxLevel && mainGroundLevel !== 1) {
                                        num++;
                                        logger.debug("mainGroundLevel, maxLevel: ", mainGroundLevel, maxLevel, player.level,
                                            serverId, num, player.name);
                                        resultMap.set(player.uid, {name: player.name, level: player.level, srcMainLevel: mainGroundLevel,
                                            fixMainLevel: maxLevel, openId: player.openId, sid: serverId});
                                        if(isFixed) {
                                            groundDoc.mainGround[0].Level = maxLevel;
                                            col1.updateOne({uid: player.uid}, {$set: {
                                                    mainGround: groundDoc.mainGround
                                                }}, function (err) {
                                                logger.debug("update finish, err: ", player.openId, player.name, err);
                                                cb2();
                                            })
                                        }else {
                                            cb2();
                                        }
                                    }else {
                                        cb2();
                                    }
                                });
                            }, function () {
                                cb1();
                            });
                        });
                    })
                });
            });
        }, function (err) {
            logger.debug('connect game servers mongodb finish.');
            callback(null);
        })

    }], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        logger.debug("resultMap size: ", resultMap.size);
        let fixData = [];
        for(let [k,v] of resultMap) {
            v.uid = k;
            fixData.push(v);
        }
        let filePath = __dirname + "/fix_mainground_level_list.json";
        fs.writeFileSync(filePath, JSON.stringify(fixData));
        process.exit();
});

