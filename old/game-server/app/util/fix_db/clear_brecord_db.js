let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');

let brecordConfig = require('../../../config/brecord.json');

/******************** 查询过期的战报数据，并处理删除 ********************/

let donnotDelete = true;

let dbUser = brecordConfig.brecordDBName + '-admin';
let drecordDBUrl = "mongodb://" + dbUser + ':' + brecordConfig.dbPasswd + '@' + brecordConfig.brecordDBUrl + '/' + brecordConfig.brecordDBName;

let dbClient;
//let clusterDbUser = clusterConfig.clusterDBName + '-admin';
//let clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;

let expireTime = 10 * 24 * 60 * 60 * 1000;  //10天过期时间
//expireTime = 0;
let nowTime = new Date().getTime();

//线上11.14日统计结果: [ 531993, 0, 58481, 1376, 242588, 0, 0 ]
//策略删除过期的pve推图, 世界杯战报, 世界BOSS战报
let deletePveUidList = [];
let deleteWorldCupUidList = [];
let deleteWorldBossUidList = [];

async.waterfall([
    function (callback) {
        mongoClient.connect(drecordDBUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect drecordDBUrl failed! err: " + error);
                return callback(error);
            }
            dbClient = dbclient;
            let db = dbclient.db(brecordConfig.brecordDBName);
            db.collection("brecord", function (err1, col) {
                let typeNumList = [];
                for(let v in commonEnum.BATTLE_TYPE) {
                    typeNumList.push(0);
                }
                var cursor = col.find({beginTime: {$lte: (nowTime - expireTime)}});
                //var cursor = col.find({beginTime: {$gte: 0}});
                logger.debug("start get delete info, less than time:", nowTime - expireTime);
                cursor.forEach( function (item) {
                    //logger.debug("battleType: ", item.result.preBattleInfo[0].battleType);
                    typeNumList[item.result.preBattleInfo[0].battleType-1]++;
                    if(item.result.preBattleInfo[0].battleType === commonEnum.BATTLE_TYPE.PveLeagueCopy) {
                        deletePveUidList.push(item.uid);
                    }else if(item.result.preBattleInfo[0].battleType === commonEnum.BATTLE_TYPE.PveWorldCup) {
                        deleteWorldCupUidList.push(item.uid);
                    }else if(item.result.preBattleInfo[0].battleType === commonEnum.BATTLE_TYPE.PveWorldBoss) {
                        deleteWorldBossUidList.push(item.uid);
                    }
                }, function () {
                    logger.debug("all list num: ", typeNumList, deletePveUidList.length, deleteWorldCupUidList.length, deleteWorldBossUidList.length);
                    callback(null);
                });
            })
        });
    },
    function (callback) {
        let db = dbClient.db(drecordDBUrl.clusterDBName);
        db.collection("brecord", function (err1, col) {
            logger.debug("delete start!");
            if(donnotDelete) {
                logger.debug("now do not delete, only search.");
                return callback(null);
            }
            //1. 删除PVE推图 type = 1
            let pveDeleteNum = 0;
            let worldCupDeleteNum = 0;
            let worldBossDeleteNum = 0;
            async.waterfall([
                function (cb1) {
                    logger.debug("to delete pve record ...");
                    async.eachSeries(deletePveUidList, function (uid, cb2) {
                        col.deleteOne({uid: uid}, function (err) {
                            //logger.debug("delete one pve record: uid: ", uid);
                            if(!err) {
                                pveDeleteNum++;
                            }
                            cb2(null);
                        });
                    }, function (err) {
                        cb1();
                    });
                },
                function (cb1) {
                    logger.debug("to delete world cup record ...");
                    async.eachSeries(deleteWorldCupUidList, function (uid, cb2) {
                        col.deleteOne({uid: uid}, function (err) {
                            //logger.debug("delete one worldcup record: uid: ", uid);
                            if(!err) {
                                worldCupDeleteNum++;
                            }
                            cb2(null);
                        });
                    }, function (err) {
                        cb1();
                    });
                },
                function (cb1) {
                    logger.debug("to delete world boss record ...");
                    async.eachSeries(deleteWorldBossUidList, function (uid, cb2) {
                        col.deleteOne({uid: uid}, function (err) {
                            //logger.debug("delete one world boss record: uid: ", uid);
                            if(!err) {
                                worldBossDeleteNum++;
                            }
                            cb2(null);
                        });
                    }, function (err) {
                        cb1();
                    });
                }
            ], function (err) {
                logger.debug("pve record delete num: ", pveDeleteNum);
                logger.debug("worldcup record delete num: ", worldCupDeleteNum);
                logger.debug("world boss record delete num: ", worldBossDeleteNum);
                return callback(null);
            });
        });
    }], function (err) {
        logger.debug("clear brecord DB finished !");
        dbClient.close();
        process.exit();
});
