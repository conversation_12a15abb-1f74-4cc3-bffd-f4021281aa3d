var logger = require("pomelo-logger").getLogger("match");
//var clusterConfig = require('../../config/cluster');
var serversConfig = require('../../config/servers');

var routeUtil = function() {

};

//RPC路由规则 (三个参数)
//session参数: { frontendId(必选): 发送服id, toServerId(可选): 接收服id }
//msg参数: {  }
//cb: 回调
routeUtil.prototype.commonRouter = function(session, msg, app, cb) {
	if(!msg || !msg.args || !msg.args[0]) {
		cb(new Error('fail common router for msg is null'));
		return;
	}
	if(!session) {
		cb(new Error('fail to route to gconnector server for session is empty'));
		return;
	}
	if(!session.frontendId) {
		cb(new Error('fail to find frontend id in session'));
		return;
	}
	//logger.debug("commonRouter session: ", session, msg);
	//1.如果toServerId存在，则进行特殊路由转发
	if(!!session.toServerId) {
		return cb(null, session.toServerId);
	}
	//2.通用规则处理
	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	let sourceServerType = session.frontendId.split("-")[0];
	let targetServerType = msg.serverType;
	if(!serversConfig[env][targetServerType] || !serversConfig[env][sourceServerType]) {
		cb(new Error('fail common router for targetServerType or sourceServerType error'));
		return;
	}
	let index = 0;
	if((sourceServerType === "gconnector" || sourceServerType === "bconnector" || sourceServerType === "game" || sourceServerType === "battle")
		&& (targetServerType === "game" || targetServerType === "battle")) {
		for(let i=0, len=serversConfig[env][sourceServerType].length; i<len; i++) {
			if(session.frontendId === serversConfig[env][sourceServerType][i].id) {
				index = i;
				break;
			}
		}
	}
	//logger.debug("rpc router fromServer: ", session.frontendId, " , toServer: ", serversConfig[env][targetServerType][index].id);
	cb(null, serversConfig[env][targetServerType][index].id);
};


routeUtil.prototype.connector = function(session, msg, app, cb) {
	if(!session) {
		cb(new Error('fail to route to gconnector server for session is empty'));
		return;
	}

	if(!session.frontendId) {
		cb(new Error('fail to find frontend id in session'));
		return;
	}

	cb(null, session.frontendId);
};

routeUtil.prototype.getRouterGameId = function (session, app) {
	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	let index = 0;
	for(let i=0, len=serversConfig[env].gconnector.length; i<len; i++) {
		if(session.frontendId === serversConfig[env].gconnector[i].id) {
			index = i;
			break;
		}
	}
	return serversConfig[env].game[index].id;
};

routeUtil.prototype.getRouterBattleId = function (session, app) {
	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	let index = 0;
	for(let i=0, len=serversConfig[env].game.length; i<len; i++) {
		if(session.frontendId === serversConfig[env].game[i].id) {
			index = i;
			break;
		}
	}
	return serversConfig[env].battle[index].id;
};

routeUtil.prototype.getAllGameGid = function (app) {
	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	let arr = [];
	for(let i=0, len=serversConfig[env].game.length; i<len; i++) {
		arr.push(serversConfig[env].game[i].id);
	}
	return arr;
};

routeUtil.prototype.gameRouter = function (session, msg, app, cb) {
	/*
	if(!session) {
		cb(new Error('fail to route to gconnector server for session is empty'));
		return;
	}
	if(!session.frontendId) {
		cb(new Error('fail to find frontend id in session'));
		return;
	}
	*/
	if(!msg || !msg.args || !msg.args[0] || !msg.args[0].gid || !msg.args[0].targetServerType) {
		cb(new Error('fail to route to data node server for msg or msg.gid or msg.targetServerType is null'));
		return;
	}
	let gid = msg.args[0].gid;
	let targetServerType = msg.args[0].targetServerType;
	if(!msg || !gid || !targetServerType) {
		cb(new Error('fail to route to data node server for msg or msg.gid or msg.targetServerType is null'));
		return;
	}
	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	cb(null, serversConfig[env].game[index].id);
	/*
	let index = 0;
	for(let i=0, len=serversConfig[env].gconnector.length; i<len; i++) {
		if(session.frontendId === serversConfig[env].gconnector[i].id) {
			index = i;
			break;
		}
	}
	cb(null, serversConfig[env].game[index].id);
	*/
};

routeUtil.prototype.dataNodeRouter = function (session, msg, app, cb) {
	if(!msg || !msg.args || !msg.args[0] || !msg.args[0].gid || !msg.args[0].targetServerType) {
		cb(new Error('fail to route to data node server for msg or msg.gid or msg.targetServerType is null'));
		return;
	}
	let gid = msg.args[0].gid;
	let targetServerType = msg.args[0].targetServerType;
	if(!msg || !gid || !targetServerType) {
		cb(new Error('fail to route to data node server for msg or msg.gid or msg.targetServerType is null'));
		return;
	}

	let serversConfig = app.get('serversConfig');
	let env = app.get('env');
	if(!serversConfig[env][targetServerType] || !serversConfig[env][targetServerType][0]) {
		cb(new Error('fail to route to data node server for targetServerType error'));
		return;
	}
	/*
	let index = 0;
	for(let i=0, len=serversConfig[env].game.length; i<len; i++) {
		if(msg.gid === serversConfig[env].game[i].id) {
			index = i;
			break;
		}
	}
	*/
	switch (targetServerType) {
		case "game":
			cb(null, gid);
			break;
		case "battle":
			let index = Math.floor(Math.random() * serversConfig[env].battle.length);
			cb(null, serversConfig[env].battle[index].id);
			break;
		default :
			cb(null, serversConfig[env][targetServerType][0].id);
	}
};

routeUtil.prototype.getClusterHost = function() {
	return serversConfig.development.datanode[0].host;
	//return clusterConfig.clusterDBUrl.split(":")[0];
};

module.exports = new routeUtil();
