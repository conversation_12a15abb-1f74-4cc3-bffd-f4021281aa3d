/**
 * Idea and Persist
 * Created by June on 2018/2/4.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var gameUrl = "mongodb://xxx:xxxx@127.0.0.1:27017/game";
var async = require('async');

//1. Drop data. Save the users who have already recharge the money and modify their data.
require('mongodb').MongoClient.connect(gameUrl, function(error, dbclient) {
    if(error){
        logger.error("connect game DB failed! err: " + error);
        return;
    }
    var playerList = [];
    var collectionsName = ["bag", "friends", "heros", "player"];
    var saveData = [];
    async.waterfall([
            function (callback) {
                dbclient.collection("recharge", function (error, collection) {
                    if(error) {
                        logger.debug("connect recharge fail",error);
                        return callback(error);
                    }
                    collection.find().toArray(function (err, doc) {
                        if(err) {
                            logger.debug("find recharge fail",err);
                            return callback(err);
                        }
                        for(var i=0; i<doc.length; i++) {
                            playerList.push(doc[i].playerId);
                        }
                        logger.debug('save player List:', playerList);
                        callback(null);
                    });
                });
                dbclient.collection("player", function (error, collection) {
                    if(error) {
                        logger.debug("connect player fail",error);
                        return callback(error);
                    }
                    collection.find().toArray(function (err, doc) {
                        if(err) {
                            logger.debug("find player fail",err);
                            return callback(err);
                        }
                        for(var i=0; i<doc.length; i++) {
                            if(playerList.indexOf(doc[i].playerId) !== -1) {
                                saveData.push(doc[i]);
                            }
                        }
                        collection.drop(function (err) {
                            if(err) {
                                logger.debug("drop player fail",err);
                                return callback(err);
                            }
                            for(var j=0; j<saveData.length; j++) {
                                //collection.insert()
                            }

                        })
                    });
                });
            }
        ]
    );

});


