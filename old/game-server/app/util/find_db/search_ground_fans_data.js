let fs = require("fs");

let searchName = process.argv[2];
if(!searchName) {
    console.log('参数输入错误! 请输入查询的用户名字.');
}else {
    console.log('开始查询用户 '+ searchName + ' 数据...');
    let isFound = false;
    fs.readFile('ground_fans_data.json', 'utf8', (err, data) => {
        if(err) {
            console.log("read file err: ", err);
            return ;
        }
        let obj = JSON.parse(data);
        for(let i=0;i<obj.length;i++) {
            if(obj[i].name === searchName) {
                isFound = true;
                console.log("该用户改版前的球迷数: " + obj[i].oldBallFan);
                console.log("商业赛计算后增加的球迷数: " + obj[i].oldAddBallFan);
                console.log("更新前球迷数: " + obj[i].nowBallFan);
                console.log("更新后球迷数: " + obj[i].newBallFan);
                break;
            }
        }
        if(!isFound) {
            console.log("未找到该用户,请检查输入的用户名.");
        }
    })
}


