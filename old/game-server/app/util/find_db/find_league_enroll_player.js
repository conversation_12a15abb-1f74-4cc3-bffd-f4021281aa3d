let mongodb = require("mongodb");
let mongoClient = require("mongodb").MongoClient;
let mongoAdmin = require("mongodb").Admin;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let timeUtils = require('../timeUtils');

let clusterConfig = require("../../../config/cluster.json");
//let timeUtils = require("../timeUtils");

var fs = require('fs');

var CollectionsName = 'account';
var pathArr = __dirname.split('\\');
var path = '';
for(let i=0; i<pathArr.length-4;i++) {
    path += pathArr[i] + '\\';
}
let dateFormat = function() {
    var d = new Date();
    return d.getFullYear().toString() + (d.getMonth()+1).toString() + d.getDate().toString();
};
//var fileName = path + "/oid2uid/openId2Uid" + dateFormat() + ".json";
//var fileName = __dirname + "/leagueEnrollPlayer.json";
var now = timeUtils.timeFormat1(timeUtils.now());

let nowSeasonId = 4;

let nowSeasonFileName = __dirname + "/NowNameList.csv";
let historyFileName = __dirname + "/HistoryNameList.csv";


//fileName = fileName.replace(' ', '-').replace(':', '-');
//logger.debug("pathArr: ", pathArr, path, fileName);



let collectionNames = [
    "community", 		//社区赛
    "normal", 			//常规赛
    //"knockout", 		//淘汰赛
    //"profession" 		//专业赛
];

//历史玩家
let historyEnrollList = [];       //{(seasonId-1) : {uid, seasonId}}
for(let i=0;i<nowSeasonId;i++) {
    historyEnrollList.push(new Map());
}

//去重后的数据
let historySeasonList = [];
for(let i=0;i<nowSeasonId;i++) {
    historySeasonList[i] = [];
}

/********************提取玩家oid，uid写入文件openId2Uid.json********************/
var clusterDbUser = clusterConfig.clusterDBName + '-admin';
var clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;

mongoClient.connect(clusterDBUrl, { useNewUrlParser: true },function(error, dbclient){
    if(error){
        logger.error("gate connect clusterDBUrl failed! err: " + error);
        return;
    }
    let db = dbclient.db(clusterConfig.clusterDBName);

    //初始化赛季Id数组
    let seasonList = [];
    for(let i=0;i<nowSeasonId;i++) {
        seasonList.push(i+1);
    }

    async.waterfall([
        //收集数据
        function (callback) {
            let index = 0;
            let length = collectionNames.length;
            async.whilst(
                function() {return index < length;},
                function(cb){
                    let name = collectionNames[index++];
                    //每个表下面每个赛季的报名历史记录
                    async.eachSeries(seasonList, function (seasonId, cb1) {
                        db.collection(name, function (err, collection){
                            collection.findOne({uid: seasonId}, function(err1, doc) {
                                if(!!err1) {
                                    logger.error("find data err: ", name, seasonId, err1);
                                    dbclient.close();
                                    process.exit();
                                    return;
                                }
                                if(!doc) {
                                    logger.error("find data err: ", name, seasonId, err1);
                                    dbclient.close();
                                    process.exit();
                                    return;
                                }
                                let uidMap = historyEnrollList[seasonId-1];
                                for(let i=0,lens=doc.joinerList.length;i<lens;i++) {
                                    uidMap.set(doc.joinerList[i], seasonId);
                                }
                                cb1(null);
                            });
                        });
                    }, function (err) {
                        cb(null);
                    })
                },
                function (err) {
                    callback(null);
                }
            );
        },
        //过滤结果
        function (callback) {
            let lens = historyEnrollList.length;
            for(let i=0; i<lens; i++) {
                let map = historyEnrollList[i];
                for(let [uid,v] of map) {
                    let isExist = false;
                    for(let j=i; j>0; j--) {
                        let checkMap = historyEnrollList[j-1];
                        if(checkMap.has(uid)) {
                            isExist = true;
                            break;
                        }
                    }
                    if(!isExist) {
                        historySeasonList[i].push(uid);
                    }
                }
            }
            for(let i=0; i<nowSeasonId; i++) {
                logger.debug("historySeasonList: i", i, historySeasonList[i].length);
            }
            callback(null);
        },
        //查询玩家姓名并输出
        function (callback) {
            let nowList = [];
            let historyList = [];

            let index = 0;
            async.eachSeries(historySeasonList, function(list, cb1) {
                index++;
                async.eachSeries(list, function (uid, cb2) {
                    let db = dbclient.db(clusterConfig.clusterDBName);
                    db.collection("account", function (err, collection){
                        collection.findOne({uid: uid}, function(err1, doc) {
                            if(!!err1 || !doc) {
                                //logger.debug("find no player: ", uid);
                                return cb2()
                            }
                            if(index === nowSeasonId) {
                                nowList.push(doc.name);
                            }else {
                                historyList.push(doc.name);
                            }
                            cb2();
                        });
                    });
                }, function (err) {
                    cb1(null);
                });
            }, function(err) {
                let fd = fs.openSync(nowSeasonFileName, 'w');
                fs.writeSync(fd, "报名玩家列表: \n");
                for(let i=0,lens=nowList.length;i<lens;i++) {
                    fs.writeSync(fd,  nowList[i]+"\n");
                }
                fs.closeSync(fd);
                fd = fs.openSync(historyFileName, 'w');
                fs.writeSync(fd, "报名玩家列表: \n");
                for(let i=0,lens=historyList.length;i<lens;i++) {
                    fs.writeSync(fd,  historyList[i]+"\n");
                }
                fs.closeSync(fd);
                callback(null);
            });
        }
    ], function (err) {
        logger.debug("find league enroll data finish !");
        dbclient.close();
        process.exit();
    });



/*
    async.eachSeries(seasonList, function (seasonId, callback) {
        db.collection("leagueFsm", function (err, collection){


        self.getAccountNumByGameServerId(server.id, function (err, ret) {
            logger.debug("afterAllServerStartUp ", server.id, err, ret);
            if(!self.gameServerInfo[server.id]) {
                self.gameServerInfo[server.id] = {};
            }
            self.gameServerInfo[server.id].accountNum = ret;
            callback(err);
        });
    }, function (err) {
        logger.debug("err : ", err);
    })


    //获取赛季
    db.collection("leagueFsm", function (err, collection){
        let seasonId = 0;
        let uidArrays = [];
        collection.findOne({uid: 1}, function(err1, doc){
            seasonId = doc.seasonId;//得到当前赛季
            if(fixSeasonId !== 0) {
                seasonId = fixSeasonId;
            }
            //获取玩家名
            db.collection("enrollTime", function (err, collection) {
                collection.findOne({uid: seasonId}, function(err1, doc) {
                    if (!!err1) {
                        logger.error("find enrollTime err: ", err1);
                        dbclient.close();
                        process.exit();
                        return;
                    }
                    if (!doc.playerEnrollTime) {
                        logger.error("find enrollTime not have playerEnrollTime data.");
                        dbclient.close();
                        process.exit();
                        return;
                    }
                    logger.debug("doc num:", doc.playerEnrollTime.length);
                    for (let i = 0, lens = doc.playerEnrollTime.length; i < lens; i++) {
                        if (doc.playerEnrollTime[i].uid.indexOf("robot_") >= 0) {
                            continue;
                        }
                        uidArrays.push(doc.playerEnrollTime[i].uid);
                    }
                    logger.debug("player num:", uidArrays.length);
                    db.collection("account", function (err, col2) {
                        let fd = fs.openSync(fileName, 'w');//打开文件
                        let title = "当前赛季：" + seasonId;
                        fs.writeSync(fd, title+'\n');
                        title = "报名玩家列表:";
                        fs.writeSync(fd, title+'\n');
                        async.eachSeries(uidArrays, function (uid, callback) {
                            col2.findOne({uid: uid}, function (err3, doc) {
                                if(!!err3 || !doc) {
                                    logger.error("find account err, uid", err3, uid);
                                    return callback(err3);
                                }
                                //let str = JSON.stringify({uid: uid, name: doc.name});
                                let str = doc.name;
                                fs.writeSync(fd, '\t'+str+'\n');
                                callback(null);
                            });
                            }, function (err) {
                            logger.debug("导出数据完毕!");
                            fs.closeSync(fd);//关闭文件
                            dbclient.close();
                            process.exit();
                        });
                    });
                });
            });
        });
    });
    logger.info("----------Player writes file finish!----------");
    */
});




