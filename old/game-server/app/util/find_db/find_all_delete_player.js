let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require('fs');

let clusterConfig = require('../../../config/cluster.json');
let serversConfig = require('../../../config/servers.json');
let gameConfig = require('../../../config/game.json');

/******************** 查询过期的[玩家]数据，并处理删除 ********************/


//to do list: 1. 连接cluster, 连接game, 2. 查询统计过期的用户(level=0, gold=0, vip=0)  3.删除对应表的用户数据
let clusterDb = {};
let gameDbList = {};

//过期时间
let expireTime = 10 * 24 * 60 * 60 * 1000;  //10天过期时间
let nowTime = new Date().getTime();

//要被删除的uid列表
let allDeleteMap = new Map();

let gameCollections = [
    "player",
    "heros",
    "email",
    "item",
    "bag",
    "teamFormation",
    "leagueCopy",
    "scout",
    "tasks",
    "footballGround",
    "businessMatch",
    "trophyCopy",
    "trainer",
    "follow",
    "store",
    "newPlayerSign",
    "sevenDaySign",
    "everyDaySign",
    "vipShop",
    "seasonStore",
    "act",
    "seasonStore",
    "limitStore",
    "offlineEvent",
    "newerGuide",
    "worldCup",
    "everyDayEnergy",
    "newerTask",
    "middleEastCup",
    "gulfCup",
    "relay",
    "MLS",
    "sign",
    "commonActivity",
    "beliefSkill"
];

let clusterCollections = [
    "account",      //uid
    "matchRank",    //uid
    //"renameCard"   //playerId
];

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            clusterDb = dbclient;
            callback(null);
        });
    },
    function (callback) {
        async.eachSeries(serversConfig.production.game, function (gameServer, cb) {
            let serverId = gameServer.id;
            let gameDbUrl = "mongodb://" + serverId+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbclient) {
                if (error) {
                    logger.error("connect clusterDBUrl failed! err: " + error);
                    return callback(error);
                }
                //gameDbList.push(dbclient);
                gameDbList[serverId] = dbclient;
                let db = dbclient.db(serverId);
                db.collection("player", function (err, col) {
                    var cursor = col.find({level: {$eq: 1}, gold: {$eq: 0}, vip: {$eq: 0}, createTime: {$lte: (nowTime - expireTime)}});
                    cursor.forEach(function (player) {
                        allDeleteMap.set(player.uid, player.openId);
                    }, function () {
                        cb();
                    });
                });
            })
        }, function (err) {
            callback(null)
        });
    }
], function (err) {
    logger.debug("all clear player num: ", allDeleteMap.size);
    clusterDb.close();
    for(let key in gameDbList) {
        gameDbList[key].close();
    }
    //for each
    let fd = fs.openSync('./allDeletePlayer.json', 'w');//打开文件
    for(let [k,v] of allDeleteMap)//循环写入数据
    {
        let array = {oid: v, uid: k};
        let str = JSON.stringify(array);
        fs.writeSync(fd, str+'\n');
    }
    fs.closeSync(fd);//关闭文件
    process.exit();
});
