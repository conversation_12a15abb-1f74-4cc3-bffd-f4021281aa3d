let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
var fs = require("fs");

let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');

//修复数据五大联赛冠军记录数据

let isUpdate = false;

let dbClient = {};
//6:是首席争夺战信息 7:是巅峰战信息（详情请看枚举：COMMON_DB_ID）
let id = 6;
let resultText = "";

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterDBUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
            if(!!error) {
                logger.error("connect cluster db fail.", error);
                return callback(error);
            }
            dbClient = dbclient;
            let db = dbclient.db(clusterConfig.clusterDBName);
            let collection = db.collection("common");
            collection.findOne({id: id}, function (err, doc) {
                if(!!err){
                    logger.debug("find common fail", id);
                    return callback(err);
                }
                let recordList = doc.recordList;
                if(id === commonEnum.COMMON_DB_ID.CHMATCH_DATA)
                {
                    for(let i in recordList)
                    {
                        resultText += "首席争夺战：" + recordList[i].time + "\n";
                        resultText += "报名人数：" + recordList[i].enrollNum + "\n";
                        resultText += "竞选人数：" + recordList[i].campaignNum + "\n";
                        resultText += "竞选总金额：" + recordList[i].campaignCash + "\n";
                        resultText += "职位记录：" + "\n";

                        let campaignList = recordList[i].campaignList;
                        for(let k in campaignList)
                        {
                            let post = "";
                            switch (campaignList[k].pos) {
                                case 1:
                                    post = "董事长"; break;
                                case 2:
                                    post = "副董事长"; break;
                                case 3:
                                    post = "总经理"; break;
                                case 4:
                                    post = "主教练"; break
                            }
                            resultText += "信仰Id：" + campaignList[k].beliefId + "\t"+ "玩家名：" + campaignList[k].name + "\t" + "职位：" + post + "\t" + "当选金额：" + campaignList[k].cash + "\n";
                        }
                        resultText += "\n";
                    }

                }
                else if(id === commonEnum.COMMON_DB_ID.PKMATCH_DATA)
                {
                    for(let i in recordList)
                    {
                        resultText += "首席巅峰战：" + recordList[i].time + "\n";
                        resultText += "下注玩家uid及金额：" + "\n";
                        for(let k in recordList[i].betList)
                        {
                            resultText += "uid：" + recordList[i].betList[k].uid + "\t" + "金额: " + recordList[i].betList[k].num + "\n";
                        }
                        resultText += "下注人数：" + recordList[i].betNum + "\n";
                        resultText += "各俱乐部被下注额：" + "\n";

                        let clubBetList = recordList[i].clubBetList;
                        for(let k in clubBetList)
                        {
                            resultText += "俱乐部名: " + clubBetList[k].name + "\t" + "信仰Id: " + clubBetList[k].beliefId + "\t" + "被下注额: " + clubBetList[k].num + "\n";
                        }
                        resultText += "总奖池金额数：" + recordList[i].total + "\n";
                        resultText += "职位记录：" + "\n";
                        resultText += "董事长: " + recordList[i].postList.chairman.name + "\t" + "信仰Id: " + recordList[i].postList.chairman.beliefId + "\t" + "副董事长: " + recordList[i].postList.coChairman.name + "\t" + "信仰Id: " + recordList[i].postList.coChairman.beliefId + "\n";
                    }
                    resultText += "\n";
                }
                callback(null, db);
            });
        });
    }
], function (err) {
    dbClient.close();
    // logger.debug("enrollNum: %d, betNum: %d", enrollNum, betNum);
    logger.info("统计数据如下：", "\n", resultText);
    process.exit();
});
