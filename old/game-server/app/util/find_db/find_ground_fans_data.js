let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
var fs = require("fs");

let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dataApi = require('../dataApi');

let dbMap = new Map();
let resultMap = new Map();
let allServerGold = 0;

let lastTime = 1585670400000;  //2020-4-1 时间戳
/******************** 查询过期的战报数据，并处理删除 ********************/

let nowTime = new Date().getTime();
let fieldConfig = {};
let errDataList = [];

let filePath = __dirname + "/ground_fans_data.json";

async.waterfall([
    function (callback) {
        //初始化配置表
        setTimeout(()=>{
            let config = dataApi.allData.data["Field"];
            for(let k in config) {
                let key = config[k].Type + '_' + config[k].Level;
                fieldConfig[key] = config[k];
            }
            //logger.debug("fieldConfig: ", fieldConfig, config);
            callback();
        },2000)
    },
    function (callback) {
        async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let dbUser = serverId + '-admin';
            let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if(!!error) {
                    logger.error("connect game db fail.", error, serverId);
                    return cb1(error);
                }
                dbMap.set(serverId, dbclient);
                let db = dbclient.db(serverId);
                let arr = [];
                db.collection("player", function (err, col1) {
                    let cursor = col1.find({leaveTime: {$gt: lastTime}});
                    cursor.forEach( function (player) {
                        resultMap.set(player.uid, {name: player.name, gid: serverId});
                        arr.push(player.uid);
                    }, function () {
                        logger.debug("result severId & size", serverId, resultMap.size);
                        db.collection("footballGround", function (err, col2) {
                            async.eachSeries(arr, function (uid, cb2) {
                                col2.findOne({uid: uid}, function (err, groundDoc) {
                                    let baseBallFan = 0;
                                    let v = resultMap.get(uid);
                                    logger.debug("v1: ", v);
                                    if (!err && !!groundDoc && !!groundDoc.groundMatch) {
                                        //球场等级, 当前球迷数, 球迷数增加/减少
                                        let keyArr = ["adminGround", "mainGround", "trainGround",
                                            "transferGround", "hospitalGround", "notableGround"];
                                        for (let i = 0, len = keyArr.length; i < len; i++) {
                                            //logger.debug("groundDoc: ", groundDoc[keyArr[i]]);
                                            let ground = groundDoc[keyArr[i]][0];
                                            let addFans = 0;
                                            if (ground.Type === 2 && ground.Level === 1) {
                                                addFans = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.mainFans].Param;
                                            } else if (ground.Level > 1 && fieldConfig[ground.Type + '_' + ground.Level]) {
                                                logger.debug("ground.Type + ground.Level: ", ground.Type + '_' + ground.Level);
                                                addFans = fieldConfig[ground.Type + '_' + (ground.Level-1)].FansShow;
                                            }
                                            if (addFans > 0) {
                                                logger.debug("BallFan key, type, level: ", keyArr[i], ground.Type, ground.Level, addFans);
                                                baseBallFan += addFans;
                                            }
                                            if(ground.Level > 20) {
                                                errDataList.push({uid: uid, name: v.name, level: ground.Level, type: ground.Type});
                                            }
                                        }
                                        v.baseBallFan = baseBallFan;
                                        v.nowBallFan = groundDoc.ballFan;
                                        logger.debug("v2: ", v);
                                    }else {
                                        resultMap.delete(uid);
                                    }
                                    cb2();
                                })
                            }, function () {
                                cb1();
                            });
                        });
                    });
                });
            });
        }, function (err) {
            logger.debug('connect game servers mongodb finish.');
            callback(null);
        })
    }], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        let resultArr = [];
        for(let [k,v] of resultMap) {
            v.uid = k;
            resultArr.push(v);
        }
        //logger.debug("all result: ", resultMap.size, JSON.stringify(resultArr));
        logger.debug("all result: ", resultMap.size, errDataList);

        fs.writeFileSync(filePath, JSON.stringify(resultArr));
        process.exit();
});
