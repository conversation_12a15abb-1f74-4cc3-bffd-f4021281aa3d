let logger = require('pomelo-logger').getLogger("pomelo", __filename)
let clusterConfig = require('../../../config/cluster')
let async = require('async')
let fs = require('fs')

let clusterDBUrl = "mongodb://" + clusterConfig.clusterDBName + '-admin' + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName

let mvpHeroId = 40000;

require('mongodb').MongoClient.connect(clusterDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
    if (error) {
        logger.error("connect game DB failed! err: " + error)
        return
    }

    let db = dbclient.db(clusterConfig.clusterDBName)
    db.collection("mvpFootball", function (error, collection) {
        if (!!error) {
            logger.debug('updateMvpHeroId data err, collectionName:', error)
            return
        }
        collection.findOne({
            mvpId: 1
        }, function(err, doc) {
            if(!!err){
                logger.error("updateMvpHeroId fail")
                dbclient.close();
                process.exit();
            }

            if(!!doc){
                //更新数据
                collection.updateOne({
                    mvpId: 1
                },{
                    $set : { mvpHeroId: mvpHeroId}
                }, function (err) {
                    if(!!err){
                        logger.error('updateMvpHeroId err');
                        return;
                    }
                    logger.error("updateMvpHeroId succeed")
                    dbclient.close();
                    process.exit()
                });
            }else {
                let buyList = [];
                collection.insertOne({
                    mvpId: 1,
                    mvpHeroId: mvpHeroId,         //Mvp球员id
                    buyList: buyList
                }, {w: 1}, function(err){
                    logger.error("create mvpHeroDb")
                    dbclient.close();
                    process.exit();
                });
            }
        })
    });
})
