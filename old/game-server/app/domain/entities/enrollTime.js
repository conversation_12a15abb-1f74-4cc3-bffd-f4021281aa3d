var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Player = require("./player");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");
var async = require("async");

//存储每个玩家的报名时间
var EnrollTime = function() {      
    this.playerEnrollTime = new Map(); //uid <=> EnrollTime
};

util.inherits(EnrollTime, EventEmitter);

module.exports = EnrollTime;

EnrollTime.prototype.test = function()
{

};

EnrollTime.prototype.initByDB = function(doc) 
{
    this.playerEnrollTime = this.toMap(doc.playerEnrollTime) || new Map();
};

EnrollTime.prototype.toJSONforDB = function() {
    var profession = {
        playerEnrollTime: this.toArray(this.playerEnrollTime)
    };
    return profession;
};

EnrollTime.prototype.toMap = function(arr)
{
    let map = new Map();
    if (!arr)
    {
        return map;
    }
    for(let idx in arr)
    {
        let data = arr[idx];
        let uid = data.uid;
        let enrollTime = data.enrollTime;
        map.set(uid, enrollTime);
    }
    return map;
};

EnrollTime.prototype.toArray = function(map)
{
    let arr = [];
    if (!map)
    {
        return arr;
    }

    for (let [k,v] of map) 
    {
        let obj = {
            uid: k,
            enrollTime: v,
        };
        arr.push(obj);
    }
    return arr;
};

EnrollTime.prototype.updateEnrollTime = function(playUid, enrollTime)
{
    this.playerEnrollTime.set(playUid, enrollTime);
    //logger.info("updateEnrollTime: uid, enrollTime", playUid, enrollTime);
};

EnrollTime.prototype.getEnrollTime = function(playUid)
{
    let enrollTime = this.playerEnrollTime.get(playUid);
    if (!enrollTime)
    {
        enrollTime = 0;
        this.updateEnrollTime(playUid, enrollTime);
    }
    return enrollTime;
};

EnrollTime.prototype.reset = function()
{
    this.playerEnrollTime.clear();
};

