/**
 * Created by scott on 2019/8/20.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');
var Code = require('../../../../shared/code');
var TimeUtils = require('../../util/timeUtils');

var EveryDayEnergy = function(player) 
{
    this.player = player;
    this.uid = player.playerId;
    this.everyDayEnergyList = new Map(); //id => Obj
    this.lastUpdateTime = 0;
};

util.inherits(EveryDayEnergy, EventEmitter);
module.exports = EveryDayEnergy;

EveryDayEnergy.prototype.initByDB = function(doc) 
{
    this.uid = doc.uid || this.uid;
    this.everyDayEnergyList = this.toMap(doc.everyDayEnergyList) || new Map();
    this.lastUpdateTime = doc.lastUpdateTime || 0;
};

EveryDayEnergy.prototype.toMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
  
    for (var i in arr)
    {
       const object = arr[i];
       var id = object["id"];
       var list = object["takeEnergyInfo"];
       map.set(id, list);
    }
    return map;
};

EveryDayEnergy.prototype.toArr = function(map) {
    var arr =  [];
    if (!map)
    {
        return arr;
    }
  
    for (var [k, v] of map)
    {
       let obj = {
           id: k,
           takeEnergyInfo: v,
       };
       arr.push(obj);
    }
    return arr;
};

EveryDayEnergy.prototype.initByConfig = function() 
{
};

EveryDayEnergy.prototype.getEnergyValue = function()
{
    //免费领取的精力值
    let takeEnergyValue = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_VALUE].Param;
    //takeEnergyValue = 5;
    return takeEnergyValue;
};

EveryDayEnergy.prototype.getMiddayStart = function()
{
    let startMidday = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_START_MIDDAY].Param;
    //startMidday = 12;
    return startMidday * 60 * 60 * 1000;
};

EveryDayEnergy.prototype.getMiddayEnd = function()
{
    let endMidday = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_END_MIDDY].Param;
    //endMidday = 22;
    return endMidday * 60 * 60 * 1000;
};

EveryDayEnergy.prototype.getEveningStart = function()
{
    let start = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_START_EVENING].Param;
    //start = 20;
    return start * 60 * 60 * 1000;
};

EveryDayEnergy.prototype.getEveningEnd = function()
{
    let end = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_END_EVENING].Param;
    //end = 21;
    return end * 60 * 60 * 1000;
};

EveryDayEnergy.prototype.toJSONforDB = function() {
    var doc = {
        uid: this.uid,
        everyDayEnergyList: this.toArr(this.everyDayEnergyList),
        lastUpdateTime: this.lastUpdateTime,
    };
    return doc;
};

EveryDayEnergy.prototype.toJSONforClient = function() {
    return this.makeClientList();
};

EveryDayEnergy.prototype.checkUpdateTime = function()
{
    if (!TimeUtils.isToday(this.lastUpdateTime))
    {
        this.updateEnergy();
        return true;
    }
    return false;
};

EveryDayEnergy.prototype.updateEnergy = function()
{
    this.lastUpdateTime = TimeUtils.now();
    this.everyDayEnergyList = new Map();
};

EveryDayEnergy.prototype.checkTakeEnergy = function(id)
{
    let has = false;
    if (!this.everyDayEnergyList.has(id))
    {
        return has;
    }

    has = true;
    return has;
};

EveryDayEnergy.prototype.makeTakeEnergyInfo = function(id)
{
    let obj = {
        id: id, 
        takeTime: TimeUtils.now(),
    };
    
    return obj;
};

EveryDayEnergy.prototype.addEveryDayEnergyList = function(id)
{
    let obj = this.makeTakeEnergyInfo(id);
    this.everyDayEnergyList.set(id, obj);
};

EveryDayEnergy.prototype.takeEnergy = function(type)
{
    let retCode = Code.FAIL;
    let now = TimeUtils.now(); 
    let diffTime = now - TimeUtils.beginningOfTodayByTime(now);
    //先检查中午
    let startMidday = this.getMiddayStart(); //时间戳
    let endMidday = this.getMiddayEnd();  
    let startEvening = this.getEveningStart();
    let endEvening = this.getEveningEnd();
    let addValue = this.getEnergyValue();

    //补领花费
    let cost = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.EVERY_DAY_TAKE_ENERGY_LAKE_COST].Param;
    switch (type) {
        case commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY:
               //先处理中午的
            if (diffTime < startMidday) //中午前 两者都不可以领取
            {
                retCode = Code.ACT_TAKE_CODE.CAN_NOT_TAKE;
            }
            else if (diffTime >= startMidday && diffTime <= endMidday) //中午 中午可领取, 晚间不能领
            {
                //检查下玩家领取了
                if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY)) //领取了
                {
                    retCode = Code.ACT_TAKE_CODE.ALREADY_TAKE;
                }else
                {
                    logger.info("take MIDDAY: addValue", addValue);
                    this.player.addResource(commonEnum.PLAY_INFO.energy, addValue);
                    this.addEveryDayEnergyList(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY);
                    this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.player.energy}]); //体力变更通知
                    retCode = Code.OK;
                }
            }else
            {
                if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY)) //领取了
                {
                    retCode = Code.ACT_TAKE_CODE.ALREADY_TAKE;
                }else 
                {
                    //补领
                    if (!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, cost))
                    {
                        logger.error("take MIDDAY : glod is not enough! ", this.player.gold, cost);
                        retCode = Code.GOLD_FALL;
                    }else
                    {
                        logger.info("take MIDDAY: late addValue, cost", addValue, cost);
                        this.player.subtractResource(commonEnum.PLAY_INFO.gold, cost)
                        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: this.player.gold}]);     //金币变更通知

                        this.player.addResource(commonEnum.PLAY_INFO.energy, addValue);
                        this.addEveryDayEnergyList(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY);
                        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.player.energy}]); //体力变更通知
                        retCode = Code.OK;
                    }
                }
            }
            break;

        case commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING:
            //在处理晚间
            if (diffTime < startEvening) //中午前 两者都不可以领取
            {
                retCode = Code.ACT_TAKE_CODE.ALREADY_TAKE;
            }
            else if (diffTime >= startEvening && diffTime <= endEvening) //中午 中午可领取, 晚间不能领
            {
                //检查下玩家领取了
                if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING)) //领取了
                {
                    retCode = Code.ACT_TAKE_CODE.ALREADY_TAKE;
                }else
                {
                    logger.info("take EVENING: addValue", addValue);
                    this.player.addResource(commonEnum.PLAY_INFO.energy, addValue);
                    this.addEveryDayEnergyList(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING);
                    this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.player.energy}]); //体力变更通知
                    //this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.ADD_ENERGY, [3, addValue, this.player.energy], {});
                    retCode = Code.OK;
                }
            }else
            {
                if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING)) //领取了
                {
                    retCode = Code.ACT_TAKE_CODE.ALREADY_TAKE;
                }else
                {
                    //补领
                    if (!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, cost))
                    {
                        logger.error("take evening: glod is not enough", this.player.gold, cost);
                        retCode = Code.GOLD_FALL;
                    }else
                    {
                        logger.info("take EVENING: late addValue, cost", addValue, cost);
                        this.player.subtractResource(commonEnum.PLAY_INFO.gold, cost)
                        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: this.player.gold}]);     //金币变更通知

                        this.player.addResource(commonEnum.PLAY_INFO.energy, addValue);
                        this.addEveryDayEnergyList(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING);
                        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.player.energy}]); //体力变更通知
                        //this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.ADD_ENERGY, [3, addValue, this.player.energy], {});
                        retCode = Code.OK;
                    }
                }
            }
            break;
        default:
            break;
    }
    return retCode;
};

//**************************************************消息结构体构造 Begin************************************************************* */
EveryDayEnergy.prototype.makeClientList = function() 
{
    //中午
    let clientEntryMidday = {
        type: commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY, //默认状态
        btnStatus: commonEnum.ACT_BTN_STATUS.ALREADY_TAKE, 
    };

    //晚间
    let clientEntryEvening = {
        type: commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING, //默认状态
        btnStatus: commonEnum.ACT_BTN_STATUS.ALREADY_TAKE, 
    };

    //note: 这里基于时间去进行状态判断
    // ---(上午前)--12 --(中午可领取区间)-- 14 ---晚间前---18--晚间领取区间--20--晚间后---
    //基于时间返回数据
    let now = TimeUtils.now(); 
    let diffTime = now - TimeUtils.beginningOfTodayByTime(now);
    //先检查中午
    let startMidday = this.getMiddayStart(); //时间戳
    let endMidday = this.getMiddayEnd();  
    let startEvening = this.getEveningStart();
    let endEvening = this.getEveningEnd();

    logger.info("diffTime, startMidday, endMidday, startEvening, endEvening: ", diffTime, startMidday, endMidday, startEvening, endEvening);

    //先处理中午的
    if (diffTime < startMidday) //中午前 两者都不可以领取
    {
        clientEntryMidday.btnStatus  = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    }
    else if (diffTime >= startMidday && diffTime <= endMidday) //中午 中午可领取, 晚间不能领
    {
        //检查下玩家领取了
        if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY)) //领取了
        {
            clientEntryMidday.btnStatus = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            clientEntryMidday.btnStatus = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.ENERGY);//可领取发送红点
        }
    }else
    {
        if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.MIDDAY)) //领取了
        {
            clientEntryMidday.btnStatus = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            clientEntryMidday.btnStatus = commonEnum.ACT_BTN_STATUS.LATE_TAKE;
        }
    }

    //在处理晚间
    if (diffTime < startEvening) //中午前 两者都不可以领取
    {
        clientEntryEvening.btnStatus  = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    }
    else if (diffTime >= startEvening && diffTime <= endEvening) //中午 中午可领取, 晚间不能领
    {
        //检查下玩家领取了
        if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING)) //领取了
        {
            clientEntryEvening.btnStatus = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            clientEntryEvening.btnStatus = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.ENERGY);//可领取发送红点
        }
    }else
    {
        if (this.checkTakeEnergy(commonEnum.EVERY_DAY_TAKE_ENERGY_TYPE.EVENING)) //领取了
        {
            clientEntryEvening.btnStatus = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            clientEntryEvening.btnStatus = commonEnum.ACT_BTN_STATUS.LATE_TAKE;
        }
    }

    let clientEntryList = [];
    clientEntryList.push(clientEntryMidday);
    clientEntryList.push(clientEntryEvening);
    return clientEntryList;
};
//**************************************************消息结构体构造 End************************************************************* */