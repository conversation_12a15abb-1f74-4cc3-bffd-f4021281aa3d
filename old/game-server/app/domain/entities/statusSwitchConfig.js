var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Code = require("../../../../shared/code");
var Constant = require("../../../../shared/constant");

//存储每个玩家的报名时间
var StatusSwitchConfig = function() 
{      
    this.config = [];
    this.init();
};

util.inherits(StatusSwitchConfig, EventEmitter);

module.exports = StatusSwitchConfig;


StatusSwitchConfig.prototype.init = function()
{
    this.config = this.initStatusConfig();
};

StatusSwitchConfig.prototype.getConfig = function()
{
    return this.config;
};

const ACT_TYPE= {
    action: 1,           //持续性状态,在本状态持续期间会一直调用action对应的函数,
    onceAction: 2,       //一次性状态,与action互斥
};
//初始化每个状态id对应的函数id
StatusSwitchConfig.prototype.initStatusConfig = function()
{
    var SWITCH_ARR = [];
    //初始化
    SWITCH_ARR[Constant.STATE.INIT_DATA       ] = { action: Constant.FUNC_ID.onInitialize};
    SWITCH_ARR[Constant.STATE.WAIT_GAME_START ] = { action: Constant.FUNC_ID.onWaitGameStart, type: ACT_TYPE.action};
    //报名
    SWITCH_ARR[Constant.STATE.ENROLL_BEGIN    ] = { action: Constant.FUNC_ID.onEnrollBegin };
    SWITCH_ARR[Constant.STATE.ENROLL_RUNNING  ] = { action: Constant.FUNC_ID.doNothing,     type: ACT_TYPE.action, setTeam: true};
    SWITCH_ARR[Constant.STATE.ENROLL_OVER     ] = { action: Constant.FUNC_ID.onEnrollEnd,   type: ACT_TYPE.action /*, next_status: Constant.STATE.CALC_NEXT_GAME */};
    //每一轮比赛结束后结算时生成下一轮名单
    //社区联赛 128组 每组64支队伍
    SWITCH_ARR[Constant.STATE.SHOW_COMMUNITY_ROUND_INFO      ] = { action: Constant.FUNC_ID.onShowCommunity,        type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_BEGIN         ] = { action: Constant.FUNC_ID.onCommunityNextRound};
    SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_RUNNING       ] = { action: Constant.FUNC_ID.onCommunityGroup64to32, type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_WAIT          ] = { action: Constant.FUNC_ID.onCommunityWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_PLAY          ] = { action: Constant.FUNC_ID.onCommunityPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_SETTLE        ] = { action: Constant.FUNC_ID.onCommunitySettle};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND1_END           ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_COMMUNITY_ROUND_INFO_32_16] = { action: Constant.FUNC_ID.onShowCommunity,        type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_BEGIN         ] = { action: Constant.FUNC_ID.onCommunityNextRound};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_RUNNING       ] = { action: Constant.FUNC_ID.onCommunityGroup32to16, type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_WAIT          ] = { action: Constant.FUNC_ID.onCommunityWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_PLAY          ] = { action: Constant.FUNC_ID.onCommunityPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_SETTLE        ] = { action: Constant.FUNC_ID.onCommunitySettle};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND2_END           ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_COMMUNITY_ROUND_INFO_16_8 ] = { action: Constant.FUNC_ID.onShowCommunity,        type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_BEGIN         ] = { action: Constant.FUNC_ID.onCommunityNextRound};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_RUNNING       ] = { action: Constant.FUNC_ID.onCommunityGroup16to8,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_WAIT          ] = { action: Constant.FUNC_ID.onCommunityWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_PLAY          ] = { action: Constant.FUNC_ID.onCommunityPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_SETTLE        ] = { action: Constant.FUNC_ID.onCommunitySettle};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND3_END           ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_COMMUNITY_ROUND_INFO_4_4  ] = { action: Constant.FUNC_ID.onShowCommunity,        type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_BEGIN         ] = { action: Constant.FUNC_ID.onCommunityNextRound};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_RUNNING       ] = { action: Constant.FUNC_ID.onCommunityGroup8to4,   type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_WAIT          ] = { action: Constant.FUNC_ID.onCommunityWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_PLAY          ] = { action: Constant.FUNC_ID.onCommunityPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_SETTLE        ] = { action: Constant.FUNC_ID.onCommunityFinalSettle};
    SWITCH_ARR[Constant.STATE.COMMUNITY_ROUND4_END           ] = { action: Constant.FUNC_ID.doNothing};

    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_RESET_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_RESET           ] = { action: Constant.FUNC_ID.doNothing, type: ACT_TYPE.action};

     //公布常规联赛对阵
    //常规赛 八个赛区 每个赛区64支队伍
    //  SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO        ] = { action: Constant.FUNC_ID.onShowNormal, type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_BEGIN            ] = { action: Constant.FUNC_ID.onNormalNextRound};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_RUNNING          ] = { action: Constant.FUNC_ID.onNormal64to32,      type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_WAIT             ] = { action: Constant.FUNC_ID.onNormalWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_PLAY             ] = { action: Constant.FUNC_ID.onNormalPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_SETTLE           ] = { action: Constant.FUNC_ID.onNormalSettle};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND1_END              ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_32_16   ] = { action: Constant.FUNC_ID.onShowNormal, type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_BEGIN            ] = { action: Constant.FUNC_ID.onNormalNextRound};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_RUNNING          ] = { action: Constant.FUNC_ID.onNormal32to16,      type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_WAIT             ] = { action: Constant.FUNC_ID.onNormalWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_PLAY             ] = { action: Constant.FUNC_ID.onNormalPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_SETTLE           ] = { action: Constant.FUNC_ID.onNormalSettle};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND2_END              ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_16_8    ] = { action: Constant.FUNC_ID.onShowNormal, type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_BEGIN            ] = { action: Constant.FUNC_ID.onNormalNextRound};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_RUNNING          ] = { action: Constant.FUNC_ID.onNormal16to8,       type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_WAIT             ] = { action: Constant.FUNC_ID.onNormalWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_PLAY             ] = { action: Constant.FUNC_ID.onNormalPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_SETTLE           ] = { action: Constant.FUNC_ID.onNormalSettle};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND3_END              ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_8_4     ] = { action: Constant.FUNC_ID.onShowNormal, type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_BEGIN            ] = { action: Constant.FUNC_ID.onNormalNextRound};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_RUNNING          ] = { action: Constant.FUNC_ID.onNormal8to4,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_WAIT             ] = { action: Constant.FUNC_ID.onNormalWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_PLAY             ] = { action: Constant.FUNC_ID.onNormalPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_SETTLE           ] = { action: Constant.FUNC_ID.onNormalSettle};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND4_END              ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_NORMAL_ROUND_INFO_4_2     ] = { action: Constant.FUNC_ID.onShowNormal, type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_BEGIN            ] = { action: Constant.FUNC_ID.onNormalNextRound};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_RUNNING          ] = { action: Constant.FUNC_ID.onNormal4to2,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_WAIT             ] = { action: Constant.FUNC_ID.onNormalWait,        type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_PLAY             ] = { action: Constant.FUNC_ID.onNormalPlayRecord,  type: ACT_TYPE.action, setTeam: false};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_SETTLE           ] = { action: Constant.FUNC_ID.onNormalFinalSettle};
    // SWITCH_ARR[Constant.STATE.NORMAL_ROUND5_END              ] = { action: Constant.FUNC_ID.doNothing};
    //
    // SWITCH_ARR[Constant.STATE.SHOW_KNOCKOUT_ROUND_INFO_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_KNOCKOUT_ROUND_INFO_RESET     ] = { action: Constant.FUNC_ID.doNothing, type: ACT_TYPE.action};

    // //公布淘汰联赛对阵
    // SWITCH_ARR[Constant.STATE.SHOW_KNOCKOUT_ROUND_INFO       ] = { action: Constant.FUNC_ID.onShowKnock, type: ACT_TYPE.action};
   // //附加淘汰赛 从业务赛中选出靠后16支队伍进行比赛
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_BEGIN          ] = { action: Constant.FUNC_ID.onKnockoutNextRound};
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_RUNNING        ] = { action: Constant.FUNC_ID.onKnock,              type: ACT_TYPE.action, setTeam: false};
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_WAIT           ] = { action: Constant.FUNC_ID.onKnockoutWait,       type: ACT_TYPE.action, setTeam: false};
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_PLAY           ] = { action: Constant.FUNC_ID.onKnockoutPlayRecord, type: ACT_TYPE.action, setTeam: false};
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_SETTLE         ] = { action: Constant.FUNC_ID.onKnockoutFinalSettle};
   //  SWITCH_ARR[Constant.STATE.KNOCKOUT_ROUND1_END            ] = { action: Constant.FUNC_ID.doNothing};
    
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_5_INFO_RESET     ] = { action: Constant.FUNC_ID.doNothing,  type: ACT_TYPE.action};
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_6_INFO_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_7_INFO_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_8_INFO_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_9_INFO_NOT_USE   ] = { action: Constant.FUNC_ID.doNothing};
    // SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND_10_INFO_NOT_USE  ] = { action: Constant.FUNC_ID.doNothing};

    //公布专业联赛对阵
    SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND1_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
    //专业联赛 五个联赛并行比赛
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND1_END           ] = { action: Constant.FUNC_ID.doNothing};

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND2_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND2_END           ] = { action: Constant.FUNC_ID.doNothing};

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND3_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND3_END           ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND4_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND4_END           ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND5_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND5_END           ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND6_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND6_END           ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND7_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND7_END           ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND8_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND8_END           ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND9_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_BEGIN         ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_RUNNING       ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_WAIT          ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_PLAY          ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_SETTLE        ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND9_END           ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND10_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND10_END          ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND11_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND11_END          ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND12_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND12_END          ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND13_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND13_END          ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND14_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND14_END          ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND15_INFO    ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_BEGIN        ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_RUNNING      ] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_SETTLE       ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND15_END          ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND16_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND16_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND17_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND17_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND18_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND18_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND19_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND19_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND20_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND20_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND21_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND21_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND22_INFO  ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND22_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND23_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND23_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND24_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND24_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND25_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND25_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND26_INFO] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND26_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND27_INFO] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND27_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND28_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND28_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND29_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND29_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND30_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND30_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND31_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_RUNNING] = { action: Constant.FUNC_ID.onProfession,                 type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_WAIT         ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_PLAY         ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND31_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND32_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND32_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND33_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND33_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND34_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND34_END    ] = { action: Constant.FUNC_ID.doNothing };

   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND35_INFO ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND35_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND36_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND36_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND37_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_SETTLE ] = { action: Constant.FUNC_ID.onPFCommonSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND37_END    ] = { action: Constant.FUNC_ID.doNothing };
    
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_ROUND38_INFO     ] = { action: Constant.FUNC_ID.onShowProfession, type: ACT_TYPE.action};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_BEGIN  ] = { action: Constant.FUNC_ID.onProfessionNextRound };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_RUNNING] = { action: Constant.FUNC_ID.onProfession,           type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_WAIT   ] = { action: Constant.FUNC_ID.onProfessionWait,       type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_PLAY   ] = { action: Constant.FUNC_ID.onProfessionPlayRecord, type: ACT_TYPE.action, setTeam: false};
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_SETTLE ] = { action: Constant.FUNC_ID.onProfessionFinalSettle };
   SWITCH_ARR[Constant.STATE.PROFESSION_ROUND38_END    ] = { action: Constant.FUNC_ID.doNothing};

   //等待发送奖励
   SWITCH_ARR[Constant.STATE.WAIT_SEND_REWARD          ] = { action: Constant.FUNC_ID.doNothing, type: ACT_TYPE.action};
   //发放奖励
   SWITCH_ARR[Constant.STATE.FINAL_SEND_REWARD         ] = { action: Constant.FUNC_ID.onFinalSendReward };
   //计算计算展示时间
   SWITCH_ARR[Constant.STATE.CALC_NEXT_GAME            ] = { action: Constant.FUNC_ID.onCalcNextGame};
   //公布结果
   SWITCH_ARR[Constant.STATE.SHOW_PROFESSION_SETTLE_AND_REWARD_INFO] = { action: Constant.FUNC_ID.onShowFinalResult, type: ACT_TYPE.action};
   //下一轮联赛开启
   SWITCH_ARR[Constant.STATE.NEXT_GAME] = { action: Constant.FUNC_ID.onNextGame, next_status: Constant.STATE.INIT_DATA};
    return SWITCH_ARR;
};