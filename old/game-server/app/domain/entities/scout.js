/**
 * Created by sea on 2019/5/21.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var timeUtils = require("../../util/timeUtils");
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');


var Scout = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.scoutRp = 0; //球探Rp
    this.scoutEnergy = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.scoutEnergyInit].Param; //球探体力
    this.isFrist = 0;   //是否第一次抽   0是   1不是
    this.scoutGroup = this.initScout();
    this.reTime = Date.now();             //刷新时间
    this.scoutPack = []; //用于存储抽出来的球员
};

util.inherits(Scout, EventEmitter);

module.exports = Scout;

Scout.prototype.initByDB = function (doc) {
    this.uid = doc.uid;
    this.scoutRp = doc.scoutRp || 0;
    this.scoutEnergy = doc.scoutEnergy || 0;
    if(this.scoutEnergy < 0) {
        this.scoutEnergy = 0;
    }
    this.scoutGroup = doc.scoutGroup || this.initScout();
    this.scoutPack = doc.scoutPack || [];
    this.isFrist = doc.isFrist || 0;
    this.reTime = doc.reTime || Date.now();
    return this;
};

Scout.prototype.toJSONforClient = function () {
    let scout = {
        scoutRp: this.scoutRp,
        scoutEnergy: this.scoutEnergy,
        scoutGroup: this.scoutGroup,
        sysTime: Date.now() / 1000,
        scoutPack: this.scoutPack
    }
    return [scout];
};

Scout.prototype.toJSONforDB = function () {
    let scout = {
        uid: this.uid,
        scoutRp: this.scoutRp,
        scoutEnergy: this.scoutEnergy,
        scoutGroup: this.scoutGroup,
        scoutPack: this.scoutPack,
        isFrist: this.isFrist,
        reTime: this.reTime
    };
    return scout;
};

Scout.prototype.initScout = function() {
    let scoutInfo = [];
    let index = 0;
    for(let i = 1; i < 4; ++i) {
        scoutInfo[index] = {};
        scoutInfo[index].type = i;
        if(i === 1) {
            scoutInfo[index].count = 5;
        } else {
            scoutInfo[index].count = 1;
        }
        scoutInfo[index].getTime = 1;
        index++;
    }
    return scoutInfo;
}


/**
 * 获取球探信息
 */
Scout.prototype.getScoutInfo = function () {
    //刷新时间
    let seniorRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.seniorRe].Param;
    let topRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.topRe].Param;
    let scoutGroup = this.scoutGroup;
    for (let i in scoutGroup) {
        let time = this.reTime;
        switch (i) {
            case "0":
                if (!timeUtils.isToday(time)) {
                    this.scoutGroup[i].count = 5; //初级隔天刷新次数
                    this.reTime = timeUtils.now();
                }
                break;
            case "1":
                if (timeUtils.secInterval(this.scoutGroup[i].getTime) > seniorRe) {
                    this.scoutGroup[i].count = 1; //高级24小时刷新次数
                }
                break;
            case "2":
                if (timeUtils.secInterval(this.scoutGroup[i].getTime) > topRe) {
                    this.scoutGroup[i].count = 1; //顶级72小时刷新次数
                }
                break;
            default:
                break;
        }
    }
    let sysTime = Math.floor(Date.now() / 1000);
    let scout = {
        scoutRp: this.scoutRp,
        scoutEnergy: this.scoutEnergy,
        scoutGroup: this.scoutGroup,
        scoutPack: this.scoutPack,
        sysTime: sysTime
    }
    return scout;
}

/**
 * 补充球探体力
 * @param Id   档位Id
 */
Scout.prototype.addScoutEnergy = function (Id) {
    let ret = {}
    let energyNum = dataApi.allData.data["FootballerEnergy"][Id].Energy;
    let Gold = dataApi.allData.data["FootballerEnergy"][Id].Gold;
    let scoutEnergy = this.scoutEnergy;
    if (!energyNum || !Gold) {
        // logger.error("--------------addScoutEnergy energyNum or Gold fail");
        ret.code = Code.CONFIG_FAIL;
        ret.scoutEnergy = this.scoutEnergy;
        return ret;
    }

    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, Gold)){
        // logger.error("----------------addScoutEnergy GOLD_FALL", Gold);
        ret.code = Code.GOLD_FALL;
        ret.scoutEnergy = this.scoutEnergy;
        return ret;
    }
    // 体力已满
    let scoutEnergyMax = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.scoutEnergyMax].Param;
    if (scoutEnergy >= scoutEnergyMax) {
        // logger.error("--------------addScoutEnergy ENERGY_FULL");
        ret.code = Code.SCOUT.ENERGY_FULL;
        ret.scoutEnergy = this.scoutEnergy;
        return ret;
    }
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, Gold);
    this.scoutEnergy += energyNum;
    //超出体力
    if (this.scoutEnergy > scoutEnergyMax) {
        this.scoutEnergy = scoutEnergyMax;
    }
    ret.code = Code.OK;
    ret.scoutEnergy = this.scoutEnergy;
    return ret;
}

/**
 * 兑换球员(消耗球探体力或者球探Rp值)
 * @param  type 探索类型  
 */
Scout.prototype.exchangeScout = function (type) {
    let ret = {
        code: Code.FAIL,
        resId: 0,
        scoutRp: 0,
        scoutEnergy: 0,
    };

    //消耗体力兑换
    let needEnergy = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.needEnergy].Param;
    let scoutMaxRp = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.scoutMaxRp].Param;

    if (this.scoutEnergy < needEnergy) {
        logger.error("-----------------exchangeScout ENERGY_FAIL");
        ret.code = Code.SCOUT.ENERGY_FAIL;
        return ret;
    }
    if (this.scoutRp < scoutMaxRp) {
        logger.error("-------------------exchangeScout RP_FAIL");
        ret.code = Code.SCOUT.RP_FAIL;
        return ret;
    }
    //先扣再给
    this.scoutRp = 0;
    this.scoutEnergy -= needEnergy;
    if(this.scoutEnergy < 0) {
        this.scoutEnergy = 0;
    }

    //写死获得黑卡
    //获取球员id
    let scoutResId = this._weightRandom(type);
    this.scoutPack.push(scoutResId);
    ret.code = Code.OK;
    ret.scoutRp = this.scoutRp;
    ret.scoutEnergy = this.scoutEnergy;
    ret.resId = scoutResId.resId;
    return ret;
}

/**
 * 探索球员奖励
 * @param type   探索类型
 * @param index  1免费   2消耗体力
 */
Scout.prototype.getScoutReward = function (type, index) {
    let id = type - 1;
    let ret = {
        code: 0,
        resId: 0,
        scoutRp: 0,
        scoutEnergy: 0,
        getTime: 0,
        sysTime: 0,
        count: 0,
    };

    //不同的探索消耗的体力不同
    let primaryEnergyInit = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.primaryEnergyInit].Param;
    let seniorEnergyInit = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.seniorEnergyInit].Param;
    let topEnergyInit = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.topEnergyInit].Param;
    //探索加的Rp值
    let primaryRp = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.primaryRp].Param;
    let seniorRp = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.seniorRp].Param;
    let topRp = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.topRp].Param;
    //Rp最大值
    let scoutMaxRp = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.scoutMaxRp].Param;
    //刷新时间
    let primaryRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.primaryRe].Param;
    let seniorRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.seniorRe].Param;
    let topRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.topRe].Param;
    let getTime = this.scoutGroup[id].getTime;
    if (!getTime) {
        logger.error("----------getScoutReward   getTime is undefine", getTime);
        ret.code = Code.FAIL
        return ret;
    }
    let count = this.scoutGroup[id].count;
    let time = timeUtils.secInterval(getTime);
    //各种探索  1免费   2消耗体力
    if (index === 1) {
        if (count < 1) {
            ret.code = Code.COUNT_FALL;
            return ret;
        }
        switch (id) {
            case 0:
                if (time > primaryRe) {
                    this.scoutGroup[id].count -= 1;
                    this.scoutRp += primaryRp;
                } else {
                    logger.error("----------getScoutReward   time is fail");
                    ret.code = Code.FAIL
                    return ret
                }
                break;
            case 1:
                if (time > seniorRe) {
                    this.scoutGroup[id].count -= 1;
                    this.scoutRp += seniorRp;
                } else {
                    logger.error("----------getScoutReward   time is fail");
                    ret.code = Code.FAIL
                    return ret
                }
                break;
            case 2:
                if (time > topRe) {
                    this.scoutGroup[id].count -= 1;
                    this.scoutRp += topRp;
                } else {
                    logger.error("----------getScoutReward   time is fail");
                    ret.code = Code.FAIL
                    return ret
                }
                break;
            default:
                break;
        }
        //记录当前时间
        this.scoutGroup[id].getTime = Math.floor(Date.now() / 1000);
    } else {
        let isOK = true;
        switch (id) {
            case 0:
                if (this.scoutEnergy < primaryEnergyInit) {
                    isOK = false;
                    break;
                }
                this.scoutEnergy -= primaryEnergyInit;
                this.scoutRp += primaryRp;
                break;
            case 1:
                if (this.scoutEnergy < seniorEnergyInit) {
                    isOK = false;
                    break;
                }
                this.scoutEnergy -= seniorEnergyInit;
                this.scoutRp += seniorRp;
                break;
            case 2:
                if (this.scoutEnergy < topEnergyInit) {
                    isOK = false;
                    break;
                }
                this.scoutEnergy -= topEnergyInit;
                this.scoutRp += topRp;
                // this.scoutRp += 500;
                break;
            default:
                break;
        }

        if(!isOK) {
            ret.code = Code.FAIL
            return ret;
        }
    }

    //超出Rp值
    if (this.scoutRp > scoutMaxRp) {
        this.scoutRp = scoutMaxRp;
    }
    let scoutResId = {};
    //获取球员id
    if(this.isFrist == 0) {
        scoutResId = this._weightRandom(5);
        this.isFrist = 1;
    }else {
        scoutResId = this._weightRandom(type);
        // scoutResId.resId = 520;
    }
    let Order;
    switch (type) {
        case 2:
            Order = "高级球探";
            break;
        case 3:
            Order = "超级球探";
            break;
    }
    //抽到黑卡跑马灯
    let config = dataApi.allData.data["Footballer"][scoutResId.resId];
    if(config.Color == commonEnum.HERO_SYSTEM.COLOR_27)//如果是黑卡
    {
        let msg = "<font color=0x83db43>" + this.player.name + "</font>" + "在"+ Order + "中喜获" + "<font color=0x83db43>" + config.CnName + "</font>" + "的合约";
        var sendMsg = {
            senderName: "系统",
            type: commonEnum.CHAT_TYPE.HORN,// -1系统 0普通 1物品超链接 2球员超链接 3喇叭
            msg: msg,
            channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
            //heroUid: scoutResId.resId,
        };
        //获得黑卡球员跑马灯
        this.player.updateScrolling(sendMsg);
    }

    //超级球探
    if(id === 2) {
        this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.SEARCH_HERO)
    }

    this.scoutPack.push(scoutResId);
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THIRTY_SIX);
    ret.code = Code.OK;
    ret.scoutRp = this.scoutRp;
    ret.scoutEnergy = this.scoutEnergy;
    ret.getTime = this.scoutGroup[id].getTime;
    ret.sysTime = Date.now() / 1000;
    ret.count = this.scoutGroup[id].count;
    ret.resId = scoutResId.resId;
    return ret;
}

/**
 * 检查是否有相同球员
 * @param resId   物品id
 */
Scout.prototype.checkHaveSameHero = function (scoutList) {
    let ret = {
        code: Code.FAIL,
        scoutList: []
    };
    let heroList = this.player.heros.makeClientHeroList();
    let isErr = false;
    for (let i = 0; i < scoutList.length; ++i) {
        for (let j = 0; j < heroList.length; ++j) {
            if (scoutList[i] !== heroList[j].ResID) {
                continue;
            }
            let resId = scoutList[i];
            let config = dataApi.allData.data["Footballer"][resId];
            if (!config) {
                logger.error("scou config not found");
                isErr = true;
                break;
            }
            scoutList[i] = config.ItemID;
        }
        //有错立刻返回
        if (isErr) {
            break;
        }
    }

    if (isErr) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    ret.code = Code.OK;
    ret.scoutList = scoutList;
    return ret;
}

/**
 * 购买球员
 * @param index   下标
 */
Scout.prototype.buyScoutReward = function (index) {
    let ret = {
        code: Code.FAIL,
        index: index,
        uid: "",
    };


    let value = this.scoutPack[index];
    if(value === undefined) {
        logger.warn("buyScoutReward is not find", index);
        return ret;
    }
    let arr = [value.resId];
    //已经存在的球员在购买需要转成球星卡
    let result = this.checkHaveSameHero(arr);
    if (result.code !== Code.OK) {
        ret.code = result.code;
        return ret;
    }

    arr = result.scoutList;
    let heroConfig = dataApi.allData.data["Footballer"][arr[0]];
    let itemConfig = dataApi.allData.data["Item"][arr[0]];
    if (!heroConfig && !itemConfig) {
        logger.error("config is fail~~~~~~~buyScoutReward~~~~~~~")
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    let cost = 0;
    //如果不是球员 即球员卡
    if (!heroConfig) {
        cost = itemConfig.InMoney;
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, cost)){
            ret.code = Code.CASH_FALL;
            return ret;
        }

        //扣钱发货
        this.player.deductMoney(commonEnum.PLAY_INFO.cash, cost);
        this.player.bag.addItem(arr[0], 1);
    } else {
        //检查钱是否足够
        cost = heroConfig.ReleaseClause;
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, cost)){
            ret.code = Code.CASH_FALL;
            return ret;
        }
        //扣钱发货
        this.player.deductMoney(commonEnum.PLAY_INFO.cash, cost);
        let hero = this.player.heros.addHero(arr[0]);
        ret.uid = hero.Uid;
    }

    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.SIGN_HERO);

    //删除存储的球员
    this.scoutPack.splice(index, 1);
    ret.code = Code.OK;
    ret.index = index;
    ret.resId = arr[0];
    ret.money = cost;
    return ret;
}

/**
 * 权重随机
 * @param  type 探索类型
 */
Scout.prototype._weightRandom = function (type) {
    let randomConfig = [];
    let index = 0;
    //根据类型读表
    let config = dataApi.allData.data["FootballerPool"];
    for (let i in config) {
        if (type === config[i].Pool) {
            randomConfig[index] = {};
            randomConfig[index].resId = config[i].ResId;
            randomConfig[index].weight = config[i].Weight;
            index++;
        }
    }
    let randomList = [];
    for (let i in randomConfig) {
        for (let j = 0; j < randomConfig[i].weight; j++) {
            randomList.push(randomConfig[i].resId);
        }
    }
    // let randomValue = []
    // let num = Math.floor(Math.random() * 3 + 1)
    // for (let i = 0; i < num; ++i) {
    // randomValue.push(randomList[Math.floor(Math.random() * randomList.length)]);
    // }
    let randomValue = randomList[Math.floor(Math.random() * randomList.length)];
    let hero = {};
    hero.resId = randomValue;
    hero.getTime = timeUtils.now();
    return hero;
};

Scout.prototype.addScoutEnergyByPlayer = function (num) {
    logger.info("addScoutEnergyByPlayer", this.scoutEnergy, num);
    this.scoutEnergy += num;
};


Scout.prototype.getScoutPackInfo = function () {
    return this.scoutPack.sort(scoutCompare);
};

Scout.prototype.delScoutPackHero = function (index) {
    if(index < 0 || index > this.scoutPack.length) {
        return Code.RANGE_FAIL;
    }
.1
    this.scoutPack.splice(index, 1);
    return Code.OK;
};


function scoutCompare (obj1, obj2) {
    let getTime = obj1.getTime;
    let getTime2 = obj2.getTime;
    return getTime < getTime2;
}

Scout.prototype.signScoutHero = function (resId) {
    let ret = {
        code: Code.FAIL,
        resId: 0,
        uid: "",
    }
   
    let index = -1;
    for(let i  = 0; i < this.scoutPack.length; ++i) {
        if(resId === this.scoutPack[i].resId) {
            index = i;
            break;
        }
    }

    if(index === -1) {
        ret.code = Code.PARAM_FAIL;
        return ret;
    }

    let value = this.scoutPack[index];
    let arr = [value.resId];
    //已经存在的球员在购买需要转成球星卡
    let result = this.checkHaveSameHero(arr);
    if (result.code !== Code.OK) {
        ret.code = result.code;
        return ret;
    }

    arr = result.scoutList;
    let heroConfig = dataApi.allData.data["Footballer"][arr[0]];
    let itemConfig = dataApi.allData.data["Item"][arr[0]];
    if (!heroConfig && !itemConfig) {
        logger.error("config is fail~~~~~~~signScoutHero~~~~~~~")
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    let cost = 0;
    //如果不是球员 即球员卡
    if (!heroConfig) {
        let config = dataApi.allData.data["Footballer"];
        for (let i in config) {
            if (itemConfig.ID !== config[i].ItemID) {
                continue;
            }
            cost = config[i].ReleaseClause;
        }
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, cost)){
            ret.code = Code.CASH_FALL;
            return ret;
        }

        //扣钱发货
        this.player.deductMoney(commonEnum.PLAY_INFO.cash, cost);
        this.player.bag.addItem(arr[0], 1);
    } else {
        //检查钱是否足够
        cost = heroConfig.ReleaseClause;
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, cost)){
            ret.code = Code.CASH_FALL;
            return ret;
        }
        //扣钱发货
        this.player.deductMoney(commonEnum.PLAY_INFO.cash, cost);
        let hero = this.player.heros.addHero(arr[0]);
        ret.uid = hero.Uid;
    }

    this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.SIGN_HERO);
    //删除存储的球员
    this.scoutPack.splice(index, 1);
    ret.code = Code.OK;
    ret.resId = arr[0];
    ret.money = cost;
    return ret;
};