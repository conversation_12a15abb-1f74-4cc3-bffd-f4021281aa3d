/**
 * Created by sea on 2020/02/03.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);;
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');


let Sign = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.signInfo = init();
    this.week = 0;
    this.signDay = 0;
    this.meedFlag = 0;
    this.startTime = 0;
}

util.inherits(Sign, EventEmitter);

module.exports = Sign;

Sign.prototype.initByDB = function (doc) {
    this.signInfo = doc.signInfo || init();
    this.week = doc.week || 0;
    this.meedFlag = doc.meedFlag || 0;
    this.startTime = doc.startTime || 0;
};

Sign.prototype.toJSONforClient = function () {
};

Sign.prototype.toJSONforDB = function () {
    let sign = {
        signInfo: this.signInfo,
        week: this.week,
        meedFlag: this.meedFlag,
        startTime: this.startTime
    }
    return sign;
};

//初始化签到数据
let init = function () {
    let signInfo = [];
    for(let i = 0; i < 7; ++i){
        signInfo[i] = {};
        signInfo[i].state = 0;
    }
    return signInfo;
};

//活动   startTime 活动开启时间
Sign.prototype.getSignInfo = function (startTime) {
    let ret = {code: Code.FAIL, signInfo: []}
    // if(startTime !== this.startTime) {
    //     this.signInfo = init();
    //     this.startTime = startTime;
    //     this.week = 0;
    // }

    let day = timeUtils.dayInterval(this.startTime);
    let week = Math.floor(day / 7); //第几周

    //不是同一周初始化数据
    if(this.week != week){
        this.signInfo = init();   //初始化数据
        this.week = week;
        this.startTime = startTime;
        this.meedFlag = 0;
    }

    let signDay = day % 7;   //对7取余   表示第几天

    for(let i = 0; i < signDay; ++i){
        //找出未签的天数和待补签的天数
        if(this.signInfo[i].state === 0 || this.signInfo[i].state === 1){
            this.signInfo[i].state = 5;    //0未签  1可签到  2已签  3可补签  4已补签  5待补签
        }
    }

    //设置可补签
    for(let i = 0; i < signDay; ++i) {
        if(this.signInfo[i].state === 5 && this.meedFlag === 0) {
            this.signInfo[i].state = 3;
            this.meedFlag = 1;
            break;
        }
    }

    //如果当天未签 改转态为可签
    if(this.signInfo[signDay].state === 0){
        this.signInfo[signDay].state = 1;
    }
    // logger.error("getSignInfo::::::::::::::::::::::::::",this.signInfo);
    ret.code = Code.OK;
    ret.signInfo = this.signInfo;
    return ret;
};

//获取配置表  index 天数
Sign.prototype.getConfig = function (index) {
    let cfg = {};
    let config = dataApi.allData.data["LoginSign"];
    let day = index + 1;
    for(let i in config){
        if(config[i].SignType === 4 && day === config[i].DateOrder){
            cfg.awardItemId = config[i].AwardItemId;   //奖励物品
            cfg.itemNum = config[i].ItemNum;     //物品数量
            cfg.vipLevel = config[i].VipLevel;   //VIP等级
            cfg.exBei = config[i].ExBei;      //奖励翻倍倍数
            cfg.rePlace = config[i].RePlace;   //是否可补签
            cfg.awardItemId_1 = config[i].AwardItemId2;   //奖励物品
            cfg.itemNum_1 = config[i].ItemNum2;     //物品数量
            cfg.vipLevel_1 = config[i].VipLevel2;   //VIP等级
            cfg.exBei_1 = config[i].ExBei2;      //奖励翻倍倍数
            cfg.rePlace_1 = config[i].RePlace2;   //是否可补签
            break;
        }
    }
    return cfg;
}

//签到  id 活动id  index  第几天   
Sign.prototype.onSign = function (index) {
    let ret = {code : Code.FAIL, state: 0}
    if(index < 0 || index > 6){
        ret.code = Code.RANGE_FAIL;
        return ret;
    }

    //已经签到过了
    if(this.signInfo[index].state == 2 || this.signInfo[index].state == 4){
        ret.code = Code.SIGN_CODE.SIGNED;
        return ret;
    }

    let config = this.getConfig(index);
    if(JSON.stringify(config) === "{}"){
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //正常签
    if(this.signInfo[index].state == 1){
        //0未签  1可签到  2已签  3可补签  4已补签
        this.signInfo[index].state = 2;
        if(config.itemNum > 0) {
            this.player.bag.addItem(config.awardItemId, config.itemNum);
        }
        if(config.itemNum_1 > 0) {
            this.player.bag.addItem(config.awardItemId_1, config.itemNum_1);
        }
    } else if(this.signInfo[index].state == 3){        //补签
        let costMoney = 0;
        switch(index){
            case 0:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.ONE].Param;
                break;
            case 1:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.TWO].Param;
                break;
            case 2:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.THREE].Param;
                break;
            case 3:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.FOUR].Param;
                break;
            case 4:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.FIVE].Param;
                break;
            case 5:
                costMoney = dataApi.allData.data["SystemParam"][commonEnum.SIGN_PRICE.SIX].Param;
                break;
            default:
                break;
        }

        if(costMoney === 0){
            ret.code = Code.FAIL;
            return ret;
        }

        if(this.player.cash < costMoney){
            ret.code = Code.CASH_FALL;
            return ret;
        }

        //扣钱
        this.player.subtractResource(commonEnum.PLAY_INFO.cash, costMoney);
        //0未签  1可签到  2已签  3可补签  4已补签  5待补签
        this.signInfo[index].state = 4;
        this.meedFlag = 0;
        if(config.itemNum > 0) {
            this.player.bag.addItem(config.awardItemId, config.itemNum);
        }
        if(config.itemNum_1 > 0) {
            this.player.bag.addItem(config.awardItemId_1, config.itemNum_1);
            //this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.ADD_ENERGY, [config.awardItemId_1, config.itemNum_1, this.player.energy], {});
        }
    }else{
        ret.code = Code.RANGE_FAIL;
        return ret;
    }
    ret.code = Code.OK;
    ret.state = this.signInfo[index].state;
    return ret;
};


