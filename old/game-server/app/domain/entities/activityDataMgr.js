/**
 * Created by sea on 2019/7/23.
 */
var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Player = require("./player");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");
var async = require("async");

var ActivityDataMgr = function () {
  this.activityDataCache = new Map();
  this.changeIdList = [];
};

util.inherits(ActivityDataMgr, EventEmitter);

module.exports = ActivityDataMgr;

ActivityDataMgr.prototype.initActivity = function (cb) {
  // logger.error("initActivity init", dataApi.allData.data["ActivityConfig"]);
  let config = dataApi.allData.data["ActivityConfig"];
  if (!config) {
    logger.error("not found config~~~~~initActivity~~~")
    cb("not found config initActivity!");
    return;
  }

  for (let i in config) {
    let data = {}
    data.Id = config[i].ActivityId;
    data.timeType = config[i].TimeType;
    if(data.timeType === 1){
      data.startTime = config[i].StartTime;
      data.endTime = config[i].EndTime
    }else{
      data.startTime = config[i].StartTime * 1000;
      data.endTime = config[i].EndTime * 1000;
    }
    data.state = 1; //状态   1未开启   2正在进行
    data.updateTime = 1;
    this.set(data.Id, data);
  }

  cb(null);
};

//获取时间
ActivityDataMgr.prototype.getActivityTimeById = function (id) 
{
  let retCode = {code: Code.FAIL, startTime: 0, endTime: 0}
  let config = dataApi.allData.data["ActivityConfig"][id];
  if (!config) 
  {
    logger.error("getActivityTimeById: not config found !", id)
    return retCode;
  }

  let startTime = 0;
  let endTime = 0;
  if (config.TimeType === 1)
  {
    startTime = config.StartTime;
    endTime = config.EndTime
  }else
  {
    startTime = config.StartTime * 1000;
    endTime = config.EndTime * 1000;
  }

  retCode.code = Code.OK;
  retCode.startTime = startTime;
  retCode.endTime = endTime;
  return retCode;
};

//从DB读取内容
ActivityDataMgr.prototype.load = function (doc) {
  let count = 0;
  for (let i in doc) {
    let obj = doc[i];
    let Id = obj.Id;
    if (!Id) continue;

    let startTime = obj.startTime;
    let endTime = obj.endTime;
    //logger.info("ActivityDataMgr load", Id, startTime, endTime);
    //从DB加载的数据会把构造函数内初始化的数据覆盖掉，所以直接在这里修改时间
    let result = this.getActivityTimeById(Id);
    if (result.code === Code.OK)
    {
      startTime = result.startTime;
      endTime = result.endTime;
      //logger.info("ActivityDataMgr.load: Id, startTime, endTime", Id, startTime, endTime);
    }else
    {
      logger.error("load: getActivityTimeById failed!", Id);
    }

    let newObj = {
      Id: Id,
      timeType : obj.timeType,
      startTime: startTime,
      endTime: endTime,
      state: obj.state,
      updateTime: obj.updateTime,
    };
    count++;
    // logger.error("load:::::::::::",newObj.Id, newObj)
    this.set(Id, newObj, true);

  }
  return count;
};

ActivityDataMgr.prototype.set = function (Id, obj, isLoad) {
  let data = {
    Id: Id,
    timeType : obj.timeType,
    startTime: obj.startTime,
    endTime: obj.endTime,
    state: obj.state,
    updateTime: obj.updateTime,
  };

  if (!isLoad) {
    this.changeIdList.push(Id);
    //   logger.info("set", Id, obj);
  }
  //logger.error("新加---------------------", Id);
  this.activityDataCache.set(Id, data);
};

ActivityDataMgr.prototype.get = function (Id) {
  return this.activityDataCache.get(Id);
};

ActivityDataMgr.prototype.update = function (Id, startTime, endTime) {
  let activity = this.get(Id);
  if (!activity) {
    let data = {
      Id: Id, //活动类型
      timeType : 2,     //指定时间
      startTime: startTime, // 开始时间
      endTime: endTime, //结束时间
      state: 1, //状态   1未开启   2正在进行 
      updateTime: TimeUtils.now(), //更新时间
    };
    this.set(data.Id, data, false);
    return;
  }

  let data = {
    Id: activity.Id, //活动类型
    timeType: activity.timeType,  
    startTime: startTime, // 开始时间
    endTime: endTime, //结束时间
    state: activity.state, //状态   1未开启   2正在进行
    updateTime: TimeUtils.now(), //更新时间
  }
  this.set(data.Id, data, false);
};

ActivityDataMgr.prototype.getLastUpdateList = function () {
  let lastUpdateList = [];
  // logger.info("getLastUpdateList", this.changeIdList);
  for (let idx in this.changeIdList) {
    let typeId = this.changeIdList[idx];
    if (!typeId) {
      continue;
    }
    let v = this.get(typeId);
    if (!v) {
      continue;
    }
    lastUpdateList.push(v);
  }

  return lastUpdateList;
};

ActivityDataMgr.prototype.restLastUpdateList = function () {
  this.changeIdList = [];
};

// let config = dataApi.allData.data["ActivityConfig"];
// if (!config) {
//   logger.error("not found config~~~~~initActivity~~~")
//   cb("not found config initActivity!");
//   return;
// }
//
// for (let i in config) {
//   let data = {}
//   data.Id = config[i].ActivityId;
//   data.timeType = config[i].TimeType;
//   if(data.timeType === 1){
//     data.startTime = config[i].StartTime;
//     data.endTime = config[i].EndTime
//   }else{
//     data.startTime = config[i].StartTime * 1000;
//     data.endTime = config[i].EndTime * 1000;
//   }
//   data.state = 1; //状态   1未开启   2正在进行
//   data.updateTime = 1;
//   this.set(data.Id, data);
// }

ActivityDataMgr.prototype.getAllActivity = function (createTime) {
  let allActivity = [];
  let config = dataApi.allData.data["ActivityConfig"];
  if (!config) {
    logger.error("not found config~~~~~initActivity~~~")
    return allActivity;
  }

  let time = Date.now();
  //当天时间  0点
  let startTime = new Date(createTime).setHours(0,0,0,0);
  let day = TimeUtils.dayInterval(startTime);

  let index = 0;
  for (let i in config) {
    let id = config[i].ActivityId;
    let timeType = config[i].TimeType;
    let startTime = config[i].StartTime;
    let endTime = config[i].EndTime;
    if (timeType === 1) {
      if(day >= config[i].EndTime || day < config[i].StartTime){
        continue;
      }
    } else {
      if (time > config[i].EndTime * 1000) {
        continue;
      }
      startTime = config[i].StartTime * 1000;
      endTime = config[i].EndTime * 1000;
    }
    allActivity[index] = {};
    allActivity[index].Id = id;
    allActivity[index].startTime = startTime;
    allActivity[index].endTime = endTime;
    index++;
  }

  // for (let [Id, v] of this.activityDataCache) {
  //   //过滤不在时间范围内的  创角
  //   if(v.timeType === 1){
  //     if(day >= v.endTime || day < v.startTime){
  //       continue;
  //     }
  //   } else{
  //     if (time > v.endTime) {
  //       continue;
  //     }
  //   }
  //   allActivity[index] = {};
  //   allActivity[index].Id = v.Id;
  //   allActivity[index].startTime = v.startTime;
  //   allActivity[index].endTime = v.endTime;
  //   // if (time > allActivity[index].startTime && time < allActivity[index].endTime) {
  //   //   allActivity[index].state = 2; //开放
  //   //   index++;
  //   //   continue;
  //   // }
  //   // allActivity[index].state = v.state;
  //   index++;
  // }
  return allActivity;
};
