var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Code = require('../../../../shared/code');
var Constant = require("../../../../shared/constant");
var TimeUtils = require('../../util/timeUtils');
var League = require("./league");
var async = require('async');
var EnrollTime = require("./enrollTime");
var ScheduleDetail =  require("./scheduleDetail");
var StatusSwitchConfig = require("./statusSwitchConfig");

const ACT_TYPE = 
{
    action: 1,           //持续性状态,在本状态持续期间会一直调用action对应的函数,
    onceAction: 2,       //一次性状态,与action互斥
};

//负责调度
var LeagueFsm = function(time, app, leagueService) 
{  
    this.leagueService = leagueService;
    this.app = app;
    this.time = time;
    this.uid = 1;  //这个值一般不会改变      
    this.global_sys_data = {} ;                 //这里记录运行状态
    this.status_switch_map = new Map();         //这里每个状态对应的函数
    this.seasonId = 0;
    this.league = null;        
    this.waitGetAllAccount  = false;                     //debug 拉取玩家玩家是否完成了 
    this.firstSeasonCanJoinPlayer = new Map();           //第一赛能够加入到比赛的玩家
    this.directToProfession = 0;
    this.enrollEndCallBackStatus = commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.NOT_START;
    this.leagueChampionRecord = new Map();		//各赛季的联赛的冠军记录 (seasonId => [{typeId, championUid, Name, FaceUrl}, ...])

    this.enrollTime         = new EnrollTime();          //报名时间
    this.scheduleDetail     = new ScheduleDetail();      //赛程详细时间
    this.statusSwitchConfig = new StatusSwitchConfig();  //状态机
    //辅助数据(不保存数据)
    this.openTime           = 0;                        //计数等于MAXOpenTime 状态机才开始往后跑
    this.MAXOpenTime       = 60 * 5;
};

util.inherits(LeagueFsm, EventEmitter);
module.exports = LeagueFsm ;

LeagueFsm.prototype.start = function()
{     
    this.initSwitchStatus();
    var self = this;
    let time = this.time;
    if(time){
		setInterval(function(){
		    self.updateFsmState();
		}, time);
    }
};

//初始化联赛数据
LeagueFsm.prototype.init = function()
{   
    this.seasonId = 1;
    this.initGlobalSystemData();
    this.loadLeague(this.seasonId);
};

//加载league数据
LeagueFsm.prototype.loadLeague = function(seasonId)
{   
    //logger.info("LeagueFsm: loadLeague seasonId", seasonId);
    this.league = new League(seasonId, this, this.app);               //联赛数据
};

LeagueFsm.prototype.getStatus = function(k) 
{
    return this.status_switch_map.get(k);
};

LeagueFsm.prototype.initSwitchStatus = function()
{
    var arr = this.statusSwitchConfig.getConfig();//得到状态表map
    if (!arr) return;

    let preState = 0;
    this.status_switch_map.clear();
    let nextTimeMap = this.scheduleDetail.getScheduleDetail();//得到赛程时间表map
    for (let i in arr) 
    {
        let status_obj = arr[i];
        if (!status_obj) continue;

        if (!status_obj.type)//状态为空设置为一次性状态
        {
            status_obj.type = ACT_TYPE.onceAction;
        }

        if (!status_obj.next_status) //下个节点为空设为0
        {
            status_obj.next_status = 0;
        }
        
        let next_time = nextTimeMap.get(parseInt(i));//得到对应的赛程时间
        if (!next_time || next_time < 0)
        {
            status_obj.next_time = 0;
        }else
        {
            status_obj.next_time = next_time;
        }

        if(!status_obj.setTeam && false !== status_obj.setTeam) status_obj.setTeam = true;
        //logger.info("status_obj.setTeam", i, status_obj.setTeam);
        if (this.status_switch_map.has(parseInt(i)))
        {
            logger.error("initSwitchStatus: repeat id: i, status_obj", i, status_obj);
        }

        if (preState + 1 != parseInt(i))
        {
            logger.error("initSwitchStatus: lost id: preState, i, status_obj", i, preState, status_obj);
        }

        status_obj.id = parseInt(i);
        preState = parseInt(i);

        //logger.info("initSwitchStatus: id,status_obj", i, status_obj);
        this.status_switch_map.set(parseInt(i), utils.deepCopy(status_obj));
    }
};
//增加新的冠军
LeagueFsm.prototype.addLeagueChampion = function()
{
    let self = this;
    let finalRank = this.league.profession.finalRank;
    let ChampionMap = new Map();
    let seasonId = this.league.profession.uid - 3;
    let uidList = [];
    let value = [];
    if(this.leagueChampionRecord.has(seasonId))
    {
        return;
    }
    //logger.error("::::::::::::::::::::::::::::", finalRank);
    let i = 5;
    //因为DB里保存的数据顺序和联赛等级是倒过来的，所以这里用i来记录联赛等级
    for(let [k, v] of finalRank)
    {
        if("robot_" !== v[0].playerUid.substr(0, 6))
        {
            let obj = {typeId: i, championUid: v[0].playerUid};
            value.push(utils.deepCopy(obj));
            uidList.push(v[0].playerUid);
            i--;
        }
    }
    ChampionMap.set(seasonId, value);
    //logger.error("增加新的冠军：", uidList, ChampionMap);
    this.leagueService.getLeagueChampionSimpleInfo(uidList, function (result) {
        let tmpList = [];
        for(let k in value)
        {
            for(let p in result)
            {
                if(value[k].championUid !== result[p].uid)
                    continue;
                let tmp = {typeId: value[k].typeId, championUid: value[k].championUid, Name: result[p].Name, FaceUrl: result[p].FaceUrl};
                tmpList.push(utils.deepCopy(tmp));
            }
        }
        self.leagueChampionRecord.set(seasonId, tmpList);
        //logger.error("冠军列表：", seasonId, self.leagueChampionRecord);
    });
};

LeagueFsm.prototype.setStatusObjNextTime = function(curr_status, next_time)
{
    let status_obj = this.status_switch_map.get(curr_status);
    if (!status_obj)
    {
        return;
    }
    status_obj.next_time = next_time;
    logger.info("setStatusObjNextTime", curr_status, next_time);
};

LeagueFsm.prototype.setStatusObjNextStatus = function(start_status, next_status)
{
    let status_obj = this.status_switch_map.get(start_status);
    if (!status_obj)
    {
        return;
    }
    status_obj.next_status = next_status;
    logger.info("setStatusObjNextStatus", start_status, next_status, status_obj);
};

/**
 * @return {boolean}
 */
LeagueFsm.prototype.FSMFunctionExAction = function(status_obj, curr_time)
{
    let switchable = false;
	if (!status_obj)
	{	
		logger.error("FSMFunctionEx: not status_obj!", status_obj);
		return switchable;
	}

    if (!curr_time)
	{	
		logger.error("FSMFunctionEx: not curr_time!", curr_time);
		return switchable;
    }

    let isFinish = false;
    let action = status_obj.action;
	switch (action) {
		default:
            logger.error("not found funcId", action, status_obj);
            isFinish = false;
            break;
        case Constant.FUNC_ID.onShowCommunity:        isFinish = this.onShowCommunity(status_obj, curr_time); break;        //第一次分组 生成战斗队列
        case Constant.FUNC_ID.onCommunityGroup64to32: isFinish = this.onCommunityGroup64to32(status_obj, curr_time); break; //步骤2 预选赛疯狂战斗选出前20
        // case Constant.FUNC_ID.onCommunityGroup32to16: isFinish = this.onCommunityGroup32to16(status_obj, curr_time); break;
        // case Constant.FUNC_ID.onCommunityGroup16to8:  isFinish = this.onCommunityGroup16to8(status_obj, curr_time); break;
        // case Constant.FUNC_ID.onCommunityGroup8to4:   isFinish = this.onCommunityGroup8to4(status_obj, curr_time); break;

        // case Constant.FUNC_ID.onCommunityWait:        isFinish = this.onCommunityWait(status_obj, curr_time); break;        //步骤3
        // case Constant.FUNC_ID.onCommunityPlayRecord:  isFinish = this.onCommunityPlayRecord(status_obj, curr_time); break;  //步骤4
        // case Constant.FUNC_ID.onCommunitySettle:      isFinish = this.onCommunitySettle(status_obj, curr_time); break;      //      发邮件通知
        case Constant.FUNC_ID.onCommunityFinalSettle: isFinish = this.onCommunityFinalSettle(status_obj, curr_time); break; //      最后发邮件并且晋级到预备赛
        // case Constant.FUNC_ID.onCommunityNextRound:   isFinish = this.onCommunityNextRound(status_obj, curr_time); break;//步骤1

        case Constant.FUNC_ID.onShowNormal:   isFinish = this.onShowNormal(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormal64to32: isFinish = this.onNormal64to32(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormal32to16: isFinish = this.onNormal32to16(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormal16to8:  isFinish = this.onNormal16to8(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormal8to4:   isFinish = this.onNormal8to4(status_obj, curr_time);  break; 
        case Constant.FUNC_ID.onNormal4to2:   isFinish = this.onNormal4to2(status_obj, curr_time);  break; 

        case Constant.FUNC_ID.onNormalWait:        isFinish = this.onNormalWait(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormalPlayRecord:  isFinish = this.onNormalPlayRecord(status_obj, curr_time); break;
        case Constant.FUNC_ID.onNormalSettle:      isFinish = this.onNormalSettle(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormalFinalSettle: isFinish = this.onNormalFinalSettle(status_obj, curr_time); break; 
        case Constant.FUNC_ID.onNormalNextRound:   isFinish = this.onNormalNextRound(status_obj, curr_time); break;

        case Constant.FUNC_ID.onShowKnock: isFinish = this.onShowKnock(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onKnock:     isFinish = this.onKnock(status_obj, curr_time);break; 

        case Constant.FUNC_ID.onKnockoutWait:        isFinish = this.onKnockoutWait(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onKnockoutPlayRecord:  isFinish = this.onKnockoutPlayRecord(status_obj, curr_time);break;
        case Constant.FUNC_ID.onKnockoutFinalSettle: isFinish = this.onKnockoutFinalSettle(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onKnockoutNextRound:   isFinish = this.onKnockoutNextRound(status_obj, curr_time);break;

        case Constant.FUNC_ID.onShowProfession:        isFinish = this.onShowProfession(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onProfession:            isFinish = this.onProfession();break; 
        case Constant.FUNC_ID.onProfessionWait:        isFinish = this.onProfessionWait(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onProfessionPlayRecord:  isFinish = this.onProfessionPlayRecord(status_obj, curr_time);break;
        case Constant.FUNC_ID.onPFCommonSettle:        isFinish = this.onPFCommonSettle(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onProfessionFinalSettle: isFinish = this.onProfessionFinalSettle(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onFinalSendReward:       isFinish = this.onFinalSendReward();break;
        case Constant.FUNC_ID.onShowFinalResult:       isFinish = this.onShowFinalResult(status_obj, curr_time);break; 
        case Constant.FUNC_ID.onProfessionNextRound:   isFinish = this.onProfessionNextRound(status_obj, curr_time);break;

        case Constant.FUNC_ID.onInitialize:    isFinish = this.onInitialize(status_obj, curr_time);break; 
		case Constant.FUNC_ID.onWaitGameStart: isFinish = this.onWaitGameStart(status_obj, curr_time);break;
		case Constant.FUNC_ID.onEnrollBegin:   isFinish = this.onEnrollBegin(status_obj, curr_time);break;
        case Constant.FUNC_ID.onEnrollEnd:     isFinish = this.onEnrollEnd(status_obj, curr_time);break;
        case Constant.FUNC_ID.doNothing:       isFinish = this.doNothing(status_obj, curr_time);break;//步骤6
        case Constant.FUNC_ID.onCalcNextGame:  isFinish = this.onCalcNextGame(status_obj, curr_time); break;
        case Constant.FUNC_ID.onNextGame:      isFinish = this.onNextGame(status_obj, curr_time); break; //赛季结束初始化下赛季
    }
    
    if (isFinish) {
        switchable = true;
    }

    return switchable;
};

//状态转化
LeagueFsm.prototype.updateFsmState = function() 
{
    if(this.openTime < this.MAXOpenTime)
    {
        logger.error("开服计时：", this.openTime);
        this.openTime++;
        return;
    }
    let curr_time = TimeUtils.now();
    let isStart = this.checkLeagueStart(curr_time);
	if (!isStart) {
        //logger.error('updateFsmState:  check_league_start failed! the time have reach!', curr_time);
		return;
    }
	
	if (!this.global_sys_data) {
		logger.error('get global_sys_data failed');
        return;
    }
	
	let curr_status = this.global_sys_data.curr_status;
    let status_obj = this.getStatus(curr_status);
	if (!status_obj) {
		logger.error("updateFsmState: invalid status", curr_status, status_obj);
		return;
    }

    let type = status_obj.type;
	if (ACT_TYPE.onceAction === type) {
        //一次性的状态
        logger.info("once action", curr_status);
        this.FSMFunctionExAction(status_obj, curr_time);					// 参数todo
        if (status_obj.next_status > 0) 
        {
            curr_status = status_obj.next_status;
        }else
        {
            curr_status = curr_status + 1;
        }

        let new_status_obj = this.getStatus(curr_status);
        if (!new_status_obj) {
            logger.error("updateFsmState: invalid status", curr_status, new_status_obj);
            return;
        }
		// 保存状态
        this.global_sys_data.curr_status = curr_status;
        this.saveWhole();
        return;
    }
    logger.info("action", curr_status, this.global_sys_data.next_game_init_time + status_obj.next_time);
    // 有持续性action 或者没有action
    let switchable = this.FSMFunctionExAction(status_obj, curr_time);
     // 还没有到状态的时候
    if  (!switchable) {
        return;
    }
    
    let next_game_init_time =  this.global_sys_data.next_game_init_time;
    let switch_time = status_obj.next_time + next_game_init_time;
    // 即使本阶段(状态)的工作全部完成，也要等下个阶段开始才能转换状态
    if (curr_time < switch_time ) 
    {
        //logger.info("updateFsmState: no reach time: diff_time", (switch_time - curr_time) / 1000);
        return;
    }

    //logger.info("action __________", curr_status, curr_time/1000, switch_time/ 1000, status_obj.next_time);
    // 转换状态
    //logger.info("action: ",curr_status, status_obj.next_status, this.seasonId);
    if (status_obj.next_status > 0) 
    {
        curr_status = status_obj.next_status;  
    }
    else
    {
        curr_status = curr_status + 1;
    }

    //logger.info("action 1: ",curr_status, status_obj);
    // 保存状态和数据
    let new_status_obj = this.getStatus(curr_status);
    if (!new_status_obj) {
        logger.error("updateFsmState: invalid status", curr_status, new_status_obj);
        return;
    }

    this.global_sys_data.curr_status = curr_status;
    this.saveWhole();
};

LeagueFsm.prototype.getRoundBattleTime = function(typeId, roundId)
{
    if (!typeId)
    {
        logger.error("getRoundBattleTime", typeId);
        return 0;
    }

    if (!roundId)
    {
        logger.error("getRoundBattleTime", typeId, roundId);
        return 0;
    }

    let roundBattleTime = 0;
    switch (typeId) {
        case commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY:
            roundBattleTime = this.getRoundBattleTimeCT(roundId);
            break;
        case commonEnum.LEAGUE_RUN_TYPE_ID.NORMAL:
            roundBattleTime = this.getRoundBattleTimeNL(roundId);
            break;
        case commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT:
            roundBattleTime = this.getRoundBattleTimeKT(roundId);
            break;
        case commonEnum.LEAGUE_RUN_TYPE_ID.PROFESSION:
            roundBattleTime = this.getRoundBattleTimePF(roundId);
            break;
        default:
            logger.error("no case", typeId);
            break;
    }
    //logger.info("getRoundBattleTime", typeId, roundId, roundBattleTime/1000);
    return roundBattleTime;
};

LeagueFsm.prototype.getCommonActionNextTime = function(status)
{
    let nextTimeMap = this.scheduleDetail.getScheduleDetail(); //加载debug时间配置
    let next_time = nextTimeMap.get(parseInt(status));
    if (!next_time || next_time < 0)
    {
        //logger.error("getCommonActionNextTime: status not found!", status);
        next_time = 0;
    }
    return this.global_sys_data.next_game_init_time + next_time;
};

LeagueFsm.prototype.getRoundBattleTimeCT = function(roundId)
{
    let roundBattleTime = Date.now();
    let statusId = 0;
    switch(roundId)
    {
        case 1: statusId = Constant.STATE.COMMUNITY_ROUND1_RUNNING; break;
        // case 2: statusId = Constant.STATE.COMMUNITY_ROUND2_WAIT; break;
        // case 3: statusId = Constant.STATE.COMMUNITY_ROUND3_WAIT; break;
        // case 4: statusId = Constant.STATE.COMMUNITY_ROUND4_WAIT; break;
        default:
            logger.error("getRoundBattleTimeCT: roundId not case", roundId); break;
    }
    if (statusId > 0)
    {
        roundBattleTime = this.getCommonActionNextTime(statusId);
    }
    return roundBattleTime;
};

LeagueFsm.prototype.getRoundBattleTimeNL = function(roundId)
{
    let roundBattleTime = 0;
    let statusId = 0;
    switch(roundId)
    {
        case 1: statusId = Constant.STATE.NORMAL_ROUND1_WAIT; break;
        case 2: statusId = Constant.STATE.NORMAL_ROUND2_WAIT; break;
        case 3: statusId = Constant.STATE.NORMAL_ROUND3_WAIT; break;
        case 4: statusId = Constant.STATE.NORMAL_ROUND4_WAIT; break;
        case 5: statusId = Constant.STATE.NORMAL_ROUND5_WAIT; break;
        default:
            logger.error("getRoundBattleTimeNL: roundId not case", roundId); break;
    }
    if (statusId > 0)
    {
        roundBattleTime = this.getCommonActionNextTime(statusId);
    }
    return roundBattleTime;
};

LeagueFsm.prototype.getRoundBattleTimeKT = function(roundId)
{
    let roundBattleTime = 0;
    let statusId = 0;
    switch(roundId)
    {
        case 1: statusId = Constant.STATE.KNOCKOUT_ROUND1_WAIT; break;
        default:
            logger.error("getRoundBattleTimeKT: roundId not case", roundId); break;
    }
    
    if (statusId > 0)
    {
        roundBattleTime = this.getCommonActionNextTime(statusId);
    }
    return roundBattleTime;
};

LeagueFsm.prototype.getRoundBattleTimePF = function(roundId)
{
    let roundBattleTime = 0;
    let statusId = 0;
    switch(roundId)
    {
        case 1: statusId = Constant.STATE.PROFESSION_ROUND1_WAIT; break;
        case 2: statusId = Constant.STATE.PROFESSION_ROUND2_WAIT; break;
        case 3: statusId = Constant.STATE.PROFESSION_ROUND3_WAIT; break;
        case 4: statusId = Constant.STATE.PROFESSION_ROUND4_WAIT; break;
        case 5: statusId = Constant.STATE.PROFESSION_ROUND5_WAIT; break;
        case 6: statusId = Constant.STATE.PROFESSION_ROUND6_WAIT; break;
        case 7: statusId = Constant.STATE.PROFESSION_ROUND7_WAIT; break;
        case 8: statusId = Constant.STATE.PROFESSION_ROUND8_WAIT; break;
        case 9: statusId = Constant.STATE.PROFESSION_ROUND9_WAIT; break;
        case 10: statusId = Constant.STATE.PROFESSION_ROUND10_WAIT; break;
        case 11: statusId = Constant.STATE.PROFESSION_ROUND11_WAIT; break;
        case 12: statusId = Constant.STATE.PROFESSION_ROUND12_WAIT; break;
        case 13: statusId = Constant.STATE.PROFESSION_ROUND13_WAIT; break;
        case 14: statusId = Constant.STATE.PROFESSION_ROUND14_WAIT; break;
        case 15: statusId = Constant.STATE.PROFESSION_ROUND15_WAIT; break;
        case 16: statusId = Constant.STATE.PROFESSION_ROUND16_WAIT; break;
        case 17: statusId = Constant.STATE.PROFESSION_ROUND17_WAIT; break;
        case 18: statusId = Constant.STATE.PROFESSION_ROUND18_WAIT; break;
        case 19: statusId = Constant.STATE.PROFESSION_ROUND19_WAIT; break;
        case 20: statusId = Constant.STATE.PROFESSION_ROUND20_WAIT; break;
        case 21: statusId = Constant.STATE.PROFESSION_ROUND21_WAIT; break;
        case 22: statusId = Constant.STATE.PROFESSION_ROUND22_WAIT; break;
        case 23: statusId = Constant.STATE.PROFESSION_ROUND23_WAIT; break;
        case 24: statusId = Constant.STATE.PROFESSION_ROUND24_WAIT; break;
        case 25: statusId = Constant.STATE.PROFESSION_ROUND25_WAIT; break;
        case 26: statusId = Constant.STATE.PROFESSION_ROUND26_WAIT; break;
        case 27: statusId = Constant.STATE.PROFESSION_ROUND27_WAIT; break;
        case 28: statusId = Constant.STATE.PROFESSION_ROUND28_WAIT; break;
        case 29: statusId = Constant.STATE.PROFESSION_ROUND29_WAIT; break;
        case 30: statusId = Constant.STATE.PROFESSION_ROUND30_WAIT; break;
        case 31: statusId = Constant.STATE.PROFESSION_ROUND31_WAIT; break;
        case 32: statusId = Constant.STATE.PROFESSION_ROUND32_WAIT; break;
        case 33: statusId = Constant.STATE.PROFESSION_ROUND33_WAIT; break;
        case 34: statusId = Constant.STATE.PROFESSION_ROUND34_WAIT; break;
        case 35: statusId = Constant.STATE.PROFESSION_ROUND35_WAIT; break;
        case 36: statusId = Constant.STATE.PROFESSION_ROUND36_WAIT; break;
        case 37: statusId = Constant.STATE.PROFESSION_ROUND37_WAIT; break;
        case 38: statusId = Constant.STATE.PROFESSION_ROUND38_WAIT; break;
        default:
            logger.error("getRoundBattleTimePF: roundId not case", roundId); break;
    }

    if (statusId > 0)
    {
        roundBattleTime = this.getCommonActionNextTime(statusId);
    }
    return roundBattleTime;
};

/**************************************************通知类函数 End*********************************************************** */
LeagueFsm.prototype.sendWaitLeagueStart = function(leagueName, round ,data)
{
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PREPARE;
    this.leagueService.asyncSomeTaskClusterToGameFuncService(leagueName, round, data, funcType, function(err){ //异步并行
        if (!!err) 
        {
            logger.error("sendWaitLeagueStart failed!", err);
            return;
        }
        
        logger.info("sendWaitLeagueStart success!");
        return; 
    });
};

LeagueFsm.prototype.sendLeagueStart = function(data)
{
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_START;
    this.leagueService.clusterToAllGameFuncService(data, funcType, function(err) {   //单条消息通知game
        if (!!err) 
        {
            logger.error("sendLeagueStart failed");
            return;
        }

        logger.info("sendLeagueStart success!");
        return;
    });
};

LeagueFsm.prototype._winAndLoseCallBack = function(data, cb)
{
    let self = this;
    let funcType   = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_WIN_AND_LOSE_MAIL;
	self.leagueService.foreachFuncService(data, funcType, function(err){
        if (!!err) 
        {
            cb(Code.FAIL);
            return;
		}

		cb(Code.OK);
		return;
    });
};

LeagueFsm.prototype.sendWinAndLose = function(promotionList, eliminateList)
{
    let self = this;
    async.waterfall([
		function (callback) {
            self._winAndLoseCallBack(promotionList, function(code){
                if (code !== Code.OK)
                {
                    logger.error("promotionList failed", promotionList);
                    callback("promotionList failed");
                    return;
                }
        
                logger.info("promotionList success!");
                callback(null);
                return;
            });
		},
		function (callback) {
			self._winAndLoseCallBack(eliminateList, function(code) {
                if (code !== Code.OK)
                {
                    logger.error("eliminateList failed", eliminateList);
                    callback("eliminateList failed");
                    return;
                }
                logger.info("eliminateList success!");
                callback(null);
                return;
			});
        },
	], function(err) {
		if(!!err){
			logger.error("sendWinAndLose: waterfall fall fail: error msg: ", err);
			return;
        }
        
        logger.info("sendWinAndLose send all data success!");
		return;
    });
};

LeagueFsm.prototype.sendFinalWin = function(data, cb)
{
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_FINAL_WIN_MAIL;
	this.leagueService.foreachFuncService(data, funcType, function(err){
        if (!!err) 
        {
            cb(Code.FAIL);
            return;
		}

		cb(Code.OK);
    });
};

LeagueFsm.prototype._enrollResultCallBack = function(data, cb)
{
    let self = this;
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_ENROLL_RESULT_MAIL;
	self.leagueService.foreachFuncService(data, funcType, function(err){
        if (!!err) 
        {
            logger.error("enrollResultCallBack failed");
            cb(Code.FAIL);
            return;
        }
        //logger.info("enrollResultCallBack success!");
		cb(Code.OK);
    });
};

LeagueFsm.prototype.sendEnrollResult = function(enrollSuccess, enrollFailed, cb)
{
    logger.info("sendEnrollResult", enrollSuccess.length, enrollFailed.length);
    let self = this;
    async.waterfall([
		function (callback) {
            self._enrollResultCallBack(enrollSuccess, function(code){
                if (code !== Code.OK)
                {
                    logger.error("enrollSuccess failed");
                    callback("enrollSuccess failed");
                    return;
                }
        
                logger.info("enrollSuccess success!");
                callback(null);
            });
		},
		function (callback) {
			self._enrollResultCallBack(enrollFailed, function(code) {
                if (code !== Code.OK)
                {
                    logger.error("enrollFailed failed");
                    callback("enrollFailed failed");
                    return;
                }

                logger.info("enrollFailed success!");
                callback(null);
			});
        },
	], function(err) {
		if(!!err){
            logger.error("sendEnrollResult: waterfall fall fail: error msg: ", err);
            cb(Code.FAIL);
			return;
        }
        
        logger.info("sendEnrollResult send all data success!");
        cb(Code.OK);
		return;
    });
};

LeagueFsm.prototype._finalRewardCallBack = function(data, cb)
{
    let self = this;
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_FINAL_REWARD;
	self.leagueService.foreachFuncService(data, funcType, function(err){
        if (!!err) 
        {
            logger.error("_finalRewardCallBack failed");
            cb(Code.FAIL);
            return;
        }
        
        //logger.info("_finalRewardCallBack success!");
		cb(Code.OK);
		return;
    });
};
//赛季结束转播任务结算
LeagueFsm.prototype.relayFinalupData = function(flag, seasonId)
{
    let ctRankData = this.league.community.getFinalGroupIdRank();
    let nrRankData = this.league.normal.getFinalGroupIdRank();
    let pfRankData = this.league.profession.getFinalGroupIdRank();
    for(let i = 0; i < pfRankData.length; i++)//升到高级联赛就不发低级联赛的奖励了
    {
        let puid = pfRankData[i].uid;
        for(let k = 0; k < nrRankData.length; k++)
        {
            if(puid === nrRankData[k].uid)
            {
                nrRankData.splice(k, 1);
                continue;
            }
            let nuid = nrRankData[k].uid;
            for(let j = 0; j < ctRankData.length; j++)
            {
                if(puid === ctRankData[j].uid || nuid === ctRankData[j].uid)
                {
                    ctRankData.splice(j, 1);
                }
            }
        }
    }
    for(let i in nrRankData)
    {
        pfRankData.push(nrRankData[i]);
    }
    for(let i in ctRankData)
    {
        pfRankData.push(ctRankData[i]);
    }
    let tmpList = utils.cloneArray(pfRankData);

    let self = this;
    let allRankData = [];
    for(let i in tmpList)
    {
        let obj = tmpList[i];
        let msg = {
            playerId: obj.uid,
            groupId: obj.groupId,
            rank: obj.rank,
            typeId: obj.typeId,
            onNext: flag,
            seasonId: seasonId,
        }
        allRankData.push(utils.deepCopy(msg));
    }
    self.leagueService.finalupDataToGameFuncService(allRankData, function (code) {

    });
};

LeagueFsm.prototype.sendFinalReward = function(ctRankData, nrRankData, pfRankData)
{
    for(let i = 0; i < pfRankData.length; i++)//升到高级联赛就不发低级联赛的奖励了
    {
        let puid = pfRankData[i].uid;
        for(let k = 0; k < nrRankData.length; k++)
        {
            if(puid === nrRankData[k].uid)
            {
                nrRankData.splice(k, 1);
                continue;
            }
            let nuid = nrRankData[k].uid;
            for(let j = 0; j < ctRankData.length; j++)
            {
                if(puid === ctRankData[j].uid || nuid === ctRankData[j].uid)
                {
                    ctRankData.splice(j, 1);
                }
            }
        }
    }
    let self = this;
    async.waterfall([
		function (callback) {
			self._finalRewardCallBack(ctRankData, function(code) {
				if(code !== Code.OK) {
                    logger.error("ctRankData send failed!", ctRankData);
                    callback(null);
					return;
                }
                logger.info("ctRankData send success!");
                callback(null);
			});
		},
		function (callback) {
			self._finalRewardCallBack(nrRankData, function(code) {
				if(code !== Code.OK) {
                    logger.error("nrRankData send failed!", nrRankData);
                    callback(null);
					return;
                }

                logger.info("nrRankData send success!");
                callback(null);
			});
        },
        function (callback) {
			self._finalRewardCallBack(pfRankData, function(code) {
				if(code !== Code.OK) {
                    logger.error("pfRankData send failed!", pfRankData);
                    callback(null);
					return;
                }

                logger.info("pfRankData send success!");
                callback(null);
			});
		},
        function (callback) {
            let list = [];
            //TODO:因为季前赛所以赛季减3
            let map = new Map();
            for(let i in pfRankData)
            {
                if(!map.has(pfRankData[i].uid))
                {
                    map.set(pfRankData[i].uid, {uid: pfRankData[i].uid, seasonId: self.seasonId - 3, typeId: pfRankData[i].typeId, rank: pfRankData[i].rank});
                }
            }
            for(let i in ctRankData)
            {
                if(!map.has(ctRankData[i].uid))
                {
                    map.set(ctRankData[i].uid, {uid: ctRankData[i].uid, seasonId: self.seasonId - 3, typeId: ctRankData[i].typeId, rank: ctRankData[i].rank});
                }
            }
            for(let [k, v] of map)
            {
                list.push(v);
            }

            // logger.error("荣耀墙数据更新=======================1", list.length, list);
            //荣耀墙数据组装
            self.leagueService.getLeaguePlayerHonorData(list, function (playerList) {
               callback(null, playerList);
            });
        },
        function (playerList, callback) {
            // logger.error("荣耀墙数据更新=======================2", playerList.length, playerList);
            //发完奖励更新荣誉墙数据
            self.leagueService.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.DATA, function () {
                callback(null);
                logger.info("league updataHonorData success!");
            });
        }
	], function(err) {
		if(!!err){
			logger.error("onFinalSendReward: waterfall fall fail: error msg: ", err);
			return;
        }
        logger.info("onFinalSendReward send all data success!");
		return;
    });
};

LeagueFsm.prototype.sendPublicNotifyMail = function(data, cb)
{
    let self = this;
    let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PUBLIC_NOTIFY_PLAYER;
    //logger.info("sendPublicNotifyMail", data, funcType);
	self.leagueService.clusterToAllGameFuncService(data, funcType, function(err){
        if (!!err) 
        {
            logger.error("_finalRewardCallBack failed");
            cb(Code.FAIL);
            return;
        }
        
        //logger.info("_finalRewardCallBack success!");
		cb(Code.OK);
		return;
    });
};

/**************************************************通知类函数 End*********************************************************** */

/**************************************************状态执行函数 Begin*********************************************************** */

LeagueFsm.prototype.doNothing = function() 
{
    return true;
};

LeagueFsm.prototype.onShowCommunity = function(status_obj, curr_time)
{
    return this.league.community.showCommunity(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityAudition = function(status_obj, curr_time)
{
    return this.league.community.eliminationAudition(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityWait = function()
{
    let roundNotifyData = this.league.community.commonWait();
    if (roundNotifyData.length > 0)
    {
        let round = this.league.community.getCurrRound();
        this.sendWaitLeagueStart("社区", round, roundNotifyData); //先发一次，再起一个定时器再发一次
    }
    return true;    
};

LeagueFsm.prototype.onCommunityPlayRecord = function()
{
    let result = this.league.community.commonPlayRecord();
    if (result.isNeedSend)
    {
        logger.info("onCommunityPlayRecord isNeedSend", result.isNeedSend, result.playRecord);
        this.sendLeagueStart(result.playRecord);
    }
    //不影响主流程继续走
    return true;
};

LeagueFsm.prototype.onNormalWait = function()
{
    let roundNotifyData = this.league.normal.commonWait();
    if (roundNotifyData.length > 0)
    {
        let round = this.league.normal.getCurrRound();
        this.sendWaitLeagueStart("常规", round, roundNotifyData); 
    }
    return true;    
};

LeagueFsm.prototype.onNormalPlayRecord = function()
{
    let result = this.league.normal.commonPlayRecord();
    if (result.isNeedSend)
    {
        logger.info("onNormalPlayRecord isNeedSend", result.isNeedSend, result.playRecord);
        this.sendLeagueStart(result.playRecord);
    }
    //不影响主流程继续走
    return true;
};

LeagueFsm.prototype.onKnockoutWait = function()
{
    let roundNotifyData = this.league.knockout.commonWait();
    if (roundNotifyData.length > 0)
    {
        let round = this.league.knockout.getCurrRound();
        this.sendWaitLeagueStart("淘汰", round, roundNotifyData); 
    }
    return true;    
};

LeagueFsm.prototype.onKnockoutPlayRecord = function()
{
    let result = this.league.knockout.commonPlayRecord();
    if (result.isNeedSend)
    {
        logger.info("onNormalPlayRecord isNeedSend", result.isNeedSend, result.playRecord);
        this.sendLeagueStart(result.playRecord);
    }
    //不影响主流程继续走
    return true;
};

LeagueFsm.prototype.onProfessionWait = function()
{
    let roundNotifyData = this.league.profession.commonWait();
    if (roundNotifyData.length > 0)
    {
        let round = this.league.profession.getCurrRound();
        this.sendWaitLeagueStart( "专业", round, roundNotifyData); 
    }
    return true;    
};

LeagueFsm.prototype.onProfessionPlayRecord = function()
{
    let result = this.league.profession.commonPlayRecord();
    if (result.isNeedSend)
    {
        logger.info("onProfessionPlayRecord isNeedSend", result.isNeedSend, result.playRecord);
        this.sendLeagueStart(result.playRecord);
    }
    //不影响主流程继续走
    return true;
};

LeagueFsm.prototype.onCommunitySettle = function(status_obj, curr_time)
{
    //每轮结束计算排名
    for (let groupId = 1; groupId <= this.league.community.groupMaxCount; groupId++)
    {
        this.league.community.makeFinalGroupRank(groupId);
    }

    let round = this.league.community.getCurrRound();
    this.league.community.commonSettle(status_obj, curr_time);
    let promotionList = this.league.community.getNotifyMailPromotionList(round);
    let eliminateList = this.league.community.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onCommunitySettle promotionList failed");
            return;
        }

        logger.info("onCommunitySettle promotionList success!", round, promotionList.length, eliminateList.length);
        return;
    });
    return true;
};

LeagueFsm.prototype.onCommunityFinalSettle = function(status_obj, curr_time)
{
    this.league.community.finalSettle(status_obj, curr_time);
    //let round = this.league.community.getCurrRound();
    //预选赛就不用发每轮的对战邮件了
    // let promotionList = this.league.community.getNotifyMailPromotionList(round);
    // let eliminateList = this.league.community.getNotifyMailEliminateList(round);
    // this.sendWinAndLose(promotionList, eliminateList, function(code){
    //     if (code !== Code.OK)
    //     {
    //         logger.error("onCommunityFinalSettle: sendWinAndLose failed");
    //         return;
    //     }
    //     logger.info("onCommunityFinalSettle: sendWinAndLose success!", round, promotionList.length, eliminateList.length);
    //     return;
    // });
    //发预选赛最终结果邮件
    let finalPromotionList = this.league.community.getNotifyMailFinalPromotion();
    this.sendFinalWin(finalPromotionList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onCommunityFinalSettle: finalPromotionList failed");
            return;
        }
        logger.info("onCommunityFinalSettle: finalPromotionList success!", finalPromotionList.length);
        return;
    });

    return true;
};

LeagueFsm.prototype.onShowCommunity = function(status_obj, curr_time)
{
    return this.league.community.showCommunity(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityGroup64to32 = function(status_obj, curr_time)
{
    return this.league.community.comGroup64to32(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityGroup32to16 = function(status_obj, curr_time)
{
    return this.league.community.comGroup32to16(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityGroup16to8 = function(status_obj, curr_time)
{
    return this.league.community.comGroup16to8(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityGroup8to4 = function(status_obj, curr_time)
{
    return this.league.community.comGroup8to4(status_obj, curr_time);
};

LeagueFsm.prototype.onCommunityNextRound = function(status_obj, curr_time)
{
    return this.league.community.commonNextRound(status_obj);
};

LeagueFsm.prototype.onNormalNextRound = function(status_obj, curr_time)
{
    return this.league.normal.commonNextRound(status_obj);
};

LeagueFsm.prototype.onKnockoutNextRound = function(status_obj, curr_time)
{
    return this.league.knockout.commonNextRound(status_obj);
};

LeagueFsm.prototype.onProfessionNextRound = function(status_obj, curr_time)
{
    return this.league.profession.commonNextRound(status_obj);
};

LeagueFsm.prototype.onShowNormal = function(status_obj, curr_time)
{
    return this.league.normal.onShowNormal(status_obj, curr_time);
};

LeagueFsm.prototype.onNormal64to32 = function(status_obj, curr_time)
{
    return this.league.normal.NLGroup64to32(status_obj, curr_time);
};

LeagueFsm.prototype.onNormal32to16 = function(status_obj, curr_time)
{
    return this.league.normal.NLGroup32to16(status_obj, curr_time);
};

LeagueFsm.prototype.onNormal16to8 = function(status_obj, curr_time)
{
    return this.league.normal.NLGroup16to8(status_obj, curr_time);
};

LeagueFsm.prototype.onNormal8to4 = function(status_obj, curr_time)
{
    return this.league.normal.NLGroup8to4(status_obj, curr_time);
};

LeagueFsm.prototype.onNormal4to2 = function(status_obj, curr_time)
{
    return this.league.normal.NLGroup4to2(status_obj, curr_time);
};

LeagueFsm.prototype.onNormalSettle = function(status_obj, curr_time)
{
    //每轮结束计算排名
    for (let groupId = 1; groupId <= this.league.normal.groupMaxCount; groupId++)
    {
        this.league.normal.makeFinalGroupRank(groupId);
    }
    let round = this.league.normal.getCurrRound();
    this.league.normal.commonSettle(status_obj, curr_time);
    let promotionList = this.league.normal.getNotifyMailPromotionList(round);
    let eliminateList = this.league.normal.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onNormalSettle failed");
            return;
        }
        logger.info("onNormalSettle success!",  round, promotionList.length, eliminateList.length);
        return;
    });

    return true;
};

LeagueFsm.prototype.onNormalFinalSettle = function(status_obj, curr_time)
{
    let round = this.league.normal.getCurrRound();

    let promotionList = this.league.normal.getNotifyMailPromotionList(round);
    let eliminateList = this.league.normal.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onNormalFinalSettle:  sendWinAndLose failed");
            return;
        }
        logger.info("onNormalFinalSettle: sendWinAndLose success!", round, promotionList.length, eliminateList.length);
        return;
    });

    this.league.normal.finalSettle(status_obj, curr_time);

    let finalPromotionList = this.league.normal.getNotifyMailFinalPromotion();
    this.sendFinalWin(finalPromotionList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onNormalFinalSettle failed");
            return;
        }

        logger.info("onNormalFinalSettle success!", finalPromotionList.length);
        return;
    });

    return true;
};

LeagueFsm.prototype.onShowKnock = function(status_obj, curr_time)
{
    return this.league.knockout.onShowKnockout(status_obj, curr_time);
};

LeagueFsm.prototype.onKnock = function(status_obj, curr_time)
{
    return this.league.knockout.eliminationGroup32to16(status_obj, curr_time);
};

LeagueFsm.prototype.onKnockoutFinalSettle = function(status_obj, curr_time)
{
    this.league.knockout.finalSettle(status_obj, curr_time);
    let round = this.league.knockout.getCurrRound();
    let promotionList = this.league.knockout.getNotifyMailPromotionList(round);
    let eliminateList = this.league.knockout.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onNormalFinalSettle: sendWinAndLose failed");
            return;
        }
        logger.info("onNormalFinalSettle: sendWinAndLose success!", round, promotionList.length, eliminateList.length);
        return;
    });

    return true;
};

LeagueFsm.prototype.onShowProfession = function(status_obj, curr_time)
{
    return this.league.profession.onShowProfession(status_obj, curr_time);
};

LeagueFsm.prototype.onProfession = function()
{
    let retCode = false;
    let round = this.league.profession.getCurrRound();
    switch(round)
    {
        default:
            logger.error("onProfession not found round!", round);
            break;
        case 1: retCode = this.league.profession.PFRound_1(); break;
        case 2: retCode = this.league.profession.PFRound_2(); break;
        case 3: retCode = this.league.profession.PFRound_3(); break;
        case 4: retCode = this.league.profession.PFRound_4(); break;
        case 5: retCode = this.league.profession.PFRound_5(); break;
        case 6: retCode = this.league.profession.PFRound_6(); break;
        case 7: retCode = this.league.profession.PFRound_7(); break;
        case 8: retCode = this.league.profession.PFRound_8(); break;
        case 9: retCode = this.league.profession.PFRound_9(); break;
        case 10: retCode = this.league.profession.PFRound_10(); break;
        case 11: retCode = this.league.profession.PFRound_11(); break;
        case 12: retCode = this.league.profession.PFRound_12(); break;
        case 13: retCode = this.league.profession.PFRound_13(); break;
        case 14: retCode = this.league.profession.PFRound_14(); break;
        case 15: retCode = this.league.profession.PFRound_15(); break;
        case 16: retCode = this.league.profession.PFRound_16(); break;
        case 17: retCode = this.league.profession.PFRound_17(); break;
        case 18: retCode = this.league.profession.PFRound_18(); break;
        case 19: retCode = this.league.profession.PFRound_19(); break;
        case 20: retCode = this.league.profession.PFRound_20(); break;
        case 21: retCode = this.league.profession.PFRound_21(); break;
        case 22: retCode = this.league.profession.PFRound_22(); break;
        case 23: retCode = this.league.profession.PFRound_23(); break;
        case 24: retCode = this.league.profession.PFRound_24(); break;
        case 25: retCode = this.league.profession.PFRound_25(); break;
        case 26: retCode = this.league.profession.PFRound_26(); break;
        case 27: retCode = this.league.profession.PFRound_27(); break;
        case 28: retCode = this.league.profession.PFRound_28(); break;
        case 29: retCode = this.league.profession.PFRound_29(); break;
        case 30: retCode = this.league.profession.PFRound_30(); break;
        case 31: retCode = this.league.profession.PFRound_31(); break;
        case 32: retCode = this.league.profession.PFRound_32(); break;
        case 33: retCode = this.league.profession.PFRound_33(); break;
        case 34: retCode = this.league.profession.PFRound_34(); break;
        case 35: retCode = this.league.profession.PFRound_35(); break;
        case 36: retCode = this.league.profession.PFRound_36(); break;
        case 37: retCode = this.league.profession.PFRound_37(); break;
        case 38: retCode = this.league.profession.PFRound_38(); break;
    }

    return retCode;
};

LeagueFsm.prototype.onPFCommonSettle = function(status_obj, curr_time)
{
    //每轮结束计算排名
    for (let groupId = 1; groupId <= this.league.profession.groupMaxCount; groupId++)
    {
        this.league.profession.makeFinalGroupRank(groupId);
    }
    this.league.profession.commonSettle(status_obj, curr_time);

    let round = this.league.profession.getCurrRound();

    let promotionList = this.league.profession.getNotifyMailPromotionList(round);
    let eliminateList = this.league.profession.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onPFCommonSettle failed");
            return;
        }
        logger.info("onPFCommonSettle success!", round, promotionList.length, eliminateList.length);
        return;
    });
    return true;
};
//信仰备战发邮件
LeagueFsm.prototype.onProfessionFinalSettle = function(status_obj, curr_time)
{
    this.league.profession.finalSettle(status_obj, curr_time);
    let round = this.league.profession.getCurrRound();

    let promotionList = this.league.profession.getNotifyMailPromotionList(round);
    let eliminateList = this.league.profession.getNotifyMailEliminateList(round);
    this.sendWinAndLose(promotionList, eliminateList, function(code){
        if (code !== Code.OK)
        {
            logger.error("onProfessionFinalSettle: sendWinAndLose failed");
            return;
        }
        logger.info("onProfessionFinalSettle:  sendWinAndLose success!", round, promotionList.length, eliminateList.length);
        return;
    });

    let finalPromotionList = this.league.profession.getNotifyMailFinalPromotion();
    this.sendFinalWin(finalPromotionList, function(code) {
        if (code !== Code.OK)
        {
            logger.error("onProfessionFinalSettle: sendFinalWin finalPromotionList failed");
            return;
        }
        logger.info("onProfessionFinalSettle:  sendFinalWin finalPromotionList success!", finalPromotionList.length);
        return;
    });

    let finalEliminateList = this.league.profession.getNotifyMailFinalEliminate();
    this.sendFinalWin(finalEliminateList, function(code) {
        if (code !== Code.OK)
        {
            logger.error("onProfessionFinalSettle: sendFinalWin finalEliminateList failed!");
            return;
        }
        logger.info("onProfessionFinalSettle sendFinalWin finalEliminateList success!", finalEliminateList.length);
        return;
    });

    return true;
};

LeagueFsm.prototype.onFinalSendReward = function()
{
    let ctRankData = this.league.community.getFinalGroupIdRank();
    let nrRankData = this.league.normal.getFinalGroupIdRank();
    let pfRankData = this.league.profession.getFinalGroupIdRank();
    this.sendFinalReward(ctRankData, nrRankData, pfRankData);
    return true;
};

//检查赛程是否已经开放
LeagueFsm.prototype.checkLeagueStart = function(curr_time) 
{
    var config = dataApi.allData.data["LeagueSwitch"][1];
    if (!config) {
        logger.error("checkLeagueStart: LeagueSwitch config not found!");
        return false;
    }

    let timeStr = config.OpenTime;
    let openTime = new Date(timeStr).getTime(); //转化为timestamp时间戳
    if (curr_time < openTime) {
        //logger.info("checkLeagueStart: no reach open time! left_time", (openTime - curr_time) / 1000);
        return false;
    }

    return true;
};

//比赛开始时，做一些初始化工作
LeagueFsm.prototype.onInitialize = function(status_obj, curr_time) 
{
    if (!status_obj) {
        logger.error("nextGameInit: not status_obj");
        return false;
    }

    var config = dataApi.allData.data["LeagueSwitch"][1];
    if (!config) {
        logger.error("onInitialize: config not found!");
        return false;
    }

    this.directToProfession = 0;
    let enrollTimeBegin = this.scheduleDetail.getEnrollStart();
    let enrollTimeEnd = this.scheduleDetail.getEnrollEnd();

    logger.info("onInitialize", enrollTimeBegin, enrollTimeEnd);
    //检查下参赛人数是否足够，然后再确认比赛进行流程
    //第一赛季没有报名时间，从专业赛第一轮开始打
    if (this.seasonId === 1)
    {
        let openTime = new Date(config.OpenTime).getTime(); //转化为timestamp时间戳
        let initTime = 0;
        let joinCount = this.getFirstJoinPlayerCount();
        if (joinCount >= Constant.LEAGUE.MAX_PROFESSION_COUNT) //可以开赛
        {
            logger.warn("onInitialize: join count can join profession", joinCount, Constant.LEAGUE.MAX_PROFESSION_COUNT);
            this.directToProfession = 1;
            initTime = openTime - 6 * 24 * 60 * 60 * 1000;
            enrollTimeBegin = 0;
            enrollTimeEnd = 0;

            //设置跳转状态 报名结束====>>>>>懂联赛展示名单
            this.setStatusObjNextStatus(Constant.STATE.ENROLL_OVER, Constant.STATE.SHOW_PROFESSION_ROUND_5_INFO_RESET);
        }
        else //不满足条件,通知玩家开始报名,确定报名时间
        {
            logger.error("onInitialize: join player not reach count", joinCount, Constant.LEAGUE.MAX_PROFESSION_COUNT);
            initTime = openTime;
            let data = {
                publicMailType: commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START,
                uidList: [],
                time: this.scheduleDetail.getEnrollStart()
            };
            this.sendPublicNotifyMail(data, function(code) {
                if (code !== Code.OK)
                {
                    logger.info("onInitialize.sendPublicNotifyMail: send public mail failed!");
                    return;
                }
                logger.info("onInitialize.sendPublicNotifyMail: send public mail success!");
            });
        }
        this.global_sys_data.next_game_init_time = TimeUtils.beginningOfTodayByTime(initTime);
        logger.info("first season open time: openTime, next_game_init_time",  config.OpenTime, this.global_sys_data.next_game_init_time);
    }else
    {
        this.global_sys_data.next_game_init_time = TimeUtils.beginningOfTodayByTime(curr_time);
    }

    this.global_sys_data.enroll_begin_time = TimeUtils.beginningOfTodayByTime(this.global_sys_data.next_game_init_time) + enrollTimeBegin;
    this.global_sys_data.enroll_end_time = TimeUtils.beginningOfTodayByTime(this.global_sys_data.next_game_init_time) + enrollTimeEnd;

    //回调状态重置
    this.setWaitCBStatus(commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.NOT_START);

    logger.info("onInitialize: status_obj, curr_time", status_obj, curr_time, this.global_sys_data);
    this.league.init();
    return true;
};

//比赛开始时，做一些初始化工作
LeagueFsm.prototype.onWaitGameStart = function(status_obj, curr_time) 
{
    //判断今天是否已经错过了报名时间，如果错过了报名时间, 延迟一天开启报名(todo)
    if (curr_time < this.global_sys_data.enroll_begin_time) {
        logger.info("onWaitGameStart: curr_time, enroll_begin_time", 
        curr_time, this.global_sys_data.enroll_begin_time, (this.global_sys_data.enroll_begin_time - curr_time)/1000);
        return false;
    }
    return true;
};

LeagueFsm.prototype.fetchGetAllAccount = function(self)
{
    //debug
    self.waitGetAllAccount = true;
    self.league.battleQueue.getAllAccount(function(code, result){
      logger.info("fetchGetAllAccount", code, result.length);
      if (code !== Code.OK)
      {
        cb(Code.FAIL);
        return;
      }
      
      for (const idx in result) {
        let obj = result[idx];
        if (!obj)
        {
          continue;
        }
        
        //检查玩家是否存在专业联赛中
        if (self.league.isProfessionJoiner(obj.uid))
        {
            logger.info("fetchGetAllAccount check profession player", obj.uid);
            continue;
        }

        let simplePlayerObj= {
          uid: obj.uid,
          actualStrength: obj.actualStrength,
        };
  
        self.league.community.Enroll(simplePlayerObj);
      }

      logger.warn("fetch data is OK");
      self.waitGetAllAccount = false;
      return;
    });

    return false;
}

//开始报名
LeagueFsm.prototype.onEnrollBegin = function() 
{
    //debug
    //this.fetchGetAllAccount(this);
    return true;
};

LeagueFsm.prototype.checkJoinPlayer = function(isFirstSeason)
{
    let maxEnrollCount = Constant.LEAGUE.MAX_ENROLL_PLAYER_COUNT;
    if (isFirstSeason)
    {
        maxEnrollCount += Constant.LEAGUE.MAX_PROFESSION_COUNT;
    }

    logger.info("checkJoinPlayer: joinerList, maxEnrollCount", this.league.community.joinerList.length, maxEnrollCount);
    if (this.league.community.joinerList.length > maxEnrollCount)
    {
      this.league.community.CropEnroll(maxEnrollCount);
    }
};

LeagueFsm.prototype.getAccountActValue = function(openId)
{
    let actValue = 0;
    let accountConfig = dataApi.allData.data["account"];
    if (!accountConfig)
    {
        logger.error("getAccountActValue: not account config", openId);
        return actValue;
    }

	for(let idx in accountConfig)
	{
		let row = accountConfig[idx];
		if (openId === row.OpenId)
		{
            actValue = row.ActualStrength;
            break;
		}
	}

    return actValue;
};

function first_season_sort_compare(rankObj1, rankObj2)
{
    let actValue1 = rankObj1.power;
    let joinTime1 = rankObj1.enrollTime;

    let actValue2 = rankObj2.power;   
    let joinTime2 = rankObj2.enrollTime;

    if (actValue1 !== actValue2)
    {
        if (actValue1 < actValue2)
        {
            return 1;
        }else if (actValue1 > actValue2)
        {
            return -1;
        }
    }

    if (joinTime1 !== joinTime2)
    {
        if (joinTime1 > joinTime2)
        {
            return 1;
        }else if (joinTime1 < joinTime2)
        {
            return -1;
        }
    }

    return 0;
}

//按照配置表上的实力进行排序，剔除多余的玩家
LeagueFsm.prototype.firstSeasonSort = function()
{
    let rankObjList = [];
    let sortList = [];
    for(let [k, v] of this.firstSeasonCanJoinPlayer)
    {
        let sortObj = 
        {
            playerUid: k, 
            power: this.getAccountActValue(v.openId),
            enrollTime: v.joinTime,
        };
        sortList.push(sortObj);
    }
    sortList.sort(first_season_sort_compare);
    let rank = 0;
    for(let idx in sortList)
    {
        let player = sortList[idx];
        if (rank > Constant.LEAGUE.MAX_ENROLL_PLAYER_COUNT)
        {
            break;
        }
        rankObjList.push(utils.deepCopy(player));
        rank++;
    }

    return rankObjList;
};

LeagueFsm.prototype.firstSeasonDirectToProfession = function()
{
    let promotionList = [];
    let noSendPublicMailUidList = [];
    let groupIdRankUidMap = new Map(); //groupId => [uid, uid]
    let groupId = 5;
    let count = 1;
    let rankObjList = this.firstSeasonSort();
    for (let i in rankObjList) 
    {
      const playerObj = rankObjList[i];
      const uid = playerObj.playerUid;
  
      if (!groupIdRankUidMap.has(groupId)) 
      {
        groupIdRankUidMap.set(groupId, []);
      }
  
      let uidList = groupIdRankUidMap.get(groupId);
      uidList.push(playerObj);

      let notifyMail = 
      {
        uid: uid,
        name: this.league.getProfessionLeagueName(groupId),
        typeId: this.league.profession.getTypeIdByGroupId(groupId),
        notifyType: commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION,
      };
      promotionList.push(notifyMail);
      noSendPublicMailUidList.push(uid);
      if (count !== 0 && count % 20 === 0) 
      {
        count = 0;
        groupId--;
      }
  
      if (groupId <= 0) 
      {
        break;
      }
  
      count++;
    }

    logger.info("firstSeasonDirectToProfession: rankObjList", promotionList.length, promotionList);
    logger.info("firstSeasonDirectToProfession: promotionList", noSendPublicMailUidList.length, noSendPublicMailUidList);
    return {map: groupIdRankUidMap, mailList: promotionList, uidList: noSendPublicMailUidList};
};
//报名结束处理
LeagueFsm.prototype.enrollEndCallBack = function(cb)
{
    let isFirstSeason = false;
    if (this.seasonId === 1)
    {
        isFirstSeason = true;
    }

    //检查参赛人数，多了进行裁剪
    this.checkJoinPlayer(isFirstSeason);

    let promotionList = [];
    let noSendPublicMailUidList = [];
    if (isFirstSeason) 
    { //首赛季
        //支持直接进入懂联赛
        if (this.directToProfession === 1)
        {
            let result = this.firstSeasonDirectToProfession();
            promotionList = result.mailList;
            noSendPublicMailUidList = result.uidList;
            //把这些玩家发送专业联赛
            this.league.profession.insertProfessionRank(result.map);
        }
        else
        {
            //正常流程
            //转移前100名玩家到专业联赛
            this.league.community.playerPromotionToProfession();
            promotionList = this.league.community.getFirstNotifyMailFinalPromotion();
        }
    }

    let enrollSuccess = this.league.community.getEnrollSuccess();
     if (isFirstSeason) //第一赛季升级到专业联赛的玩家也发送报名成功通知
     {
        for(let idx in promotionList)
        {
            let data = promotionList[idx];
            let uid = data.uid;
            let obj = 
            {
                uid: uid,
                success: commonEnum.ENROLL_NOTIFY.SUCCESS,
                profession: 0,
            };

            //logger.info("isFirstSeason enrorll", uid);
            enrollSuccess.push(obj);
        }
     }else //不是第一个赛季就发送专业联赛的特殊通知
     {
        let proEnrollUid = this.league.profession.getEnrollSuccess();
        for (let index in proEnrollUid) 
        {
            const uid = proEnrollUid[index];
            let obj = {
                uid: uid,
                success: commonEnum.ENROLL_NOTIFY.SUCCESS,
                profession: 1,
            };

            logger.info("not isFirstSeason enroll", uid);
            enrollSuccess.push(obj);
        }
    }

    let self = this;
    let enrollFailed = this.league.community.getEnrollFailed();
    async.waterfall([
		function (callback) {
            self.sendEnrollResult(enrollSuccess, enrollFailed, function(code) {
                if (code !== Code.OK)
                {
                    callback(code);
                    return;
                }
        
                callback(null);
                return;
            });
		},
		function (callback) {
			self.sendFinalWin(promotionList, function(code) {
                if (code !== Code.OK)
                {
                    callback("enrollEndCallBack: sendFinalWin failed", promotionList);
                    return;
                }
        
                logger.info("enrollEndCallBack: sendFinalWin success!");
                callback(null)
                return;
			});
        },
        function (callback) {
            if (this.directToProfession === 0 || noSendPublicMailUidList.length <= 0) //不需要发送邮件
            {
                logger.info("not direct To Profession", this.directToProfession);
                callback(null);
                return;
            }
            //发送邮件给其他玩家通知懂联赛已开始
            let data = {
                publicMailType: commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_DIRECT_START,
                uidList: noSendPublicMailUidList,
            };
            self.sendPublicNotifyMail(data, function(code) {
                if (code !== Code.OK)
                {
                    logger.info("sendPublicNotifyMail: send public mail failed!");
                    callback("sendPublicNotifyMail: send public mail failed!");
                    return;
                }

                logger.info("onInitialize.sendPublicNotifyMail: send public mail success!");
                callback(null);
            });
        },
        function (callback) {   //更新所有玩家荣誉墙参加次数
            let uidList = [];
            for(let i in self.league.community.joinerList)
            {
                if(self.league.community.joinerList[i].substr(0, 6) !== "robot_")
                {
                    uidList.push(self.league.community.joinerList[i]);
                }
            }
            for(let [k, v] of self.league.profession.groupPlayerMap)
            {
                for(let i in v)
                {
                    if(v[i].substr(0, 6) !== "robot_")
                    {
                        uidList.push(v[i]);
                    }
                }
            }
            self.leagueService.getUidListGid(uidList, function (playerList) {
                callback(null, playerList);
            });
        },
        function (playerList, callback) {
            // logger.error("荣耀墙次数更新=======================", playerList.length);
            self.leagueService.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM, function () {
                callback(null);
            });
        }
    ], function(err) 
    {
        if(!!err) 
        {
            logger.error("onEnrollEnd: waterfall fall fail: error msg: ", err);
            cb(Code.FAIL);
			return;
        }
        
        logger.info("onEnrollEnd send all data success!");
        cb(Code.OK);
		return;
    });
};

LeagueFsm.prototype.setWaitCBStatus = function(status)
{
    this.enrollEndCallBackStatus = status;
};

//报名结束
LeagueFsm.prototype.onEnrollEnd = function() 
{
    //debug
    // if (this.waitGetAllAccount)
    // {
    //     logger.info("onEnrollEnd waitGetAllAccount false");
    //     return false;
    // }

    if (this.enrollEndCallBackStatus === commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.NOT_START)
    {
        let self = this;
        this.enrollEndCallBack(function(code) {
            if (code !== Code.OK)
            {
                logger.error("enrollEndCallBack failed!");
                return;
            }

            self.setWaitCBStatus(commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.END);
            logger.info("enrollEndCallBack success!");
        });

        this.setWaitCBStatus(commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.RUNNING);
    }else if (this.enrollEndCallBackStatus === commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.RUNNING)
    {
        return false;
    }else
    {
        return true;
    }
};

//计算展示时间
LeagueFsm.prototype.onCalcNextGame = function(status_obj, curr_time) 
{
    let next_game_init_time = this.global_sys_data.next_game_init_time;
    var config = dataApi.allData.data["LeagueSwitch"][1];
    if (!config) {
        logger.error("_calc_next_enroll_time: config not found!");
        return false;
    }

    // let intervalTimeMS = config.IntervalTime * Constant.TIME.ONE_DAY_MS; //延迟展示时间
    // let nextDayBeginTime = TimeUtils.beginningOfTodayByTime(curr_time + Constant.TIME.ONE_DAY_MS); //下一天的零点
    // let diffTime = nextDayBeginTime - curr_time;
    // let showTimeMs = (curr_time + diffTime + intervalTimeMS) - next_game_init_time; //1S延迟

    // let configSchedule = dataApi.allData.data["LeagueSchedule"];
    // let day = 0;
    // for(let i in configSchedule)
    // {
    //     day = configSchedule[i].DayCount;
    // }
    // day += 1;
    // let timeLag = (this.seasonId - 1) * day * 24 * 60 * 60 * 1000;//赛季时间差
    // let showTimeMs = config.openTime + enrollTimeBegin + timeLag;
    // logger.error("报名时间：一个赛季多少天：%d天, 赛季：第%d赛季, 时差：%d, 第一场开服时间：%d", day, this.seasonId, timeLag, enrollTimeBegin, next_game_init_time, showTimeMs);


    let showTimeMs = this.scheduleDetail.scheduleDetailMap.get(Constant.STATE.SHOW_PROFESSION_SETTLE_AND_REWARD_INFO) + next_game_init_time;
    //logger.error("================================", showTimeMs, this.scheduleDetail.scheduleDetailMap.get(Constant.STATE.SHOW_PROFESSION_SETTLE_AND_REWARD_INFO), next_game_init_time);
    //this.setStatusObjNextTime(Constant.STATE.SHOW_PROFESSION_SETTLE_AND_REWARD_INFO, showTimeMs);

    //logger.info("onCalcNextGame real: intervalTimeMS, nextDayBeginTime, diffTime, curr_time, showTimeMs", intervalTimeMS, nextDayBeginTime, diffTime, curr_time, showTimeMs);
    logger.info("onCalcNextGame real: intervalTimeMS, nextDayBeginTime, diffTime, curr_time, showTimeMs", showTimeMs);
    return true;
};

LeagueFsm.prototype.onShowFinalResult = function(status_obj, curr_time)
{
    return this.league.profession.showFinalResult(status_obj, curr_time);
};

//初始化下一场赛季数据
LeagueFsm.prototype.initNextSeason = function(seasonId, cb)
{
    let lastSeasonId = seasonId - 1;
    if (lastSeasonId <= 0) 
    {
        return cb("lastSeasonId is error!");
    }

    //--debug
    this.scheduleDetail.init();
    this.statusSwitchConfig.init();
    this.initSwitchStatus();
    logger.info("initNextSeason", seasonId);
    let self = this;
    async.waterfall([
        function(callback){
            self.leagueService.reLoadLeagueData(seasonId, function(err){
				if(!!err){
					return callback(err);
                }
                
                callback(null);
            });
        },
        function(callback){
			self.leagueService.getProfession(lastSeasonId , function(err, doc){
                if (!!err)
                {
                    callback(err);
                    return;
                }

                if (!doc)
                {
                    callback("not found res");
                    return;
                }
                //logger.info("initNextSeason getProfession", lastSeasonId, doc);
                self.league.profession.loadFinalReserveList(doc);
                callback(null);
			});
        }
    ], function(err){
        if(err){
            return cb("catch error: " + err);
        }

        cb(null);
        return;
    });
};

LeagueFsm.prototype.onNextGame = function(status_obj, curr_time) 
{
    this.relayFinalupData(true, this.seasonId);
    this.addLeagueChampion();
    this.seasonId++;
    let self = this;
    this.initNextSeason(this.seasonId, function(err)
    {
        if (!!err   )
        {
            logger.error("initNextSeason error");
            return;
        }

        logger.info("initNextSeason next game: ___________________________ seasonId: ", self.seasonId);
    });

    return true;
};

LeagueFsm.prototype.initByDB = function(doc) {
    if (doc.global_sys_data)
    {
        this.global_sys_data = doc.global_sys_data;  
    }else
    {
        this.initGlobalSystemData();
    }

    if (doc.seasonId)
        this.seasonId = doc.seasonId;
    else
    {
        this.seasonId = 1;
    }

    this.firstSeasonCanJoinPlayer = this.toMap(doc.firstSeasonCanJoinPlayer) || new Map();
    this.directToProfession = doc.directToProfession || 0;
    this.enrollEndCallBackStatus = doc.enrollEndCallBackStatus || commonEnum.LEAGUE_WAIT_CALL_BACK_STATUS.NOT_START
    //初始化联赛
    this.loadLeague(this.seasonId);
};

//全局系统数据
LeagueFsm.prototype.initGlobalSystemData = function() 
{
    //初始化相关数据	
    this.global_sys_data = {
        enroll_begin_time: 0,
        enroll_end_time: 0,
        curr_status: Constant.STATE.INIT_DATA,
        next_game_init_time: 0,
    };
};

LeagueFsm.prototype.toJSONforDB = function() {
    let leagueFsm = {
        uid: this.uid,
        seasonId: this.seasonId,
        global_sys_data: this.global_sys_data,
        firstSeasonCanJoinPlayer: this.toArr(this.firstSeasonCanJoinPlayer),
        directToProfession: this.directToProfession,
        enrollEndCallBackStatus: this.enrollEndCallBackStatus,
    }
    return leagueFsm;
};

LeagueFsm.prototype.saveLeagueFsm = function()
{
    this.emit("saveLeagueFsm", true);
};

LeagueFsm.prototype.saveEnrollTime = function()
{
    this.emit("saveEnrollTime", true);
};

LeagueFsm.prototype.saveWhole = function()
{
    this.saveLeagueFsm();
    this.saveEnrollTime();
    this.league.saveCommunity();
    this.league.saveNormal();
    this.league.saveKnockout();
    this.league.saveProfession();
};

LeagueFsm.prototype.getId = function() {
	return this.uid;
};

LeagueFsm.prototype.getGlobalSysData = function()
{
    let data ={
        seasonId: this.seasonId,
        global_sys_data: this.global_sys_data,
    }
    return data;
};

LeagueFsm.prototype.checkCanEnroll = function()
{
    let can= false;
    switch (this.global_sys_data.curr_status) {
        case Constant.STATE.ENROLL_BEGIN:
        case Constant.STATE.ENROLL_RUNNING:
            can = true;
            break;
        default:
            break;
    }
    return can;
};

LeagueFsm.prototype.getSeasonId = function()
{
    return this.seasonId;
};

LeagueFsm.prototype.getCurrStatus = function()
{
    return this.global_sys_data.curr_status;
};

LeagueFsm.prototype.getGameInitTime = function()
{
    return this.global_sys_data.next_game_init_time;
};

LeagueFsm.prototype.setFirstSeasonJoinPlayer = function(uid, obj)
{
    this.firstSeasonCanJoinPlayer.set(uid, obj);
};

LeagueFsm.prototype.getFirstSeasonJoinPlayer = function(uid)
{
    return this.firstSeasonCanJoinPlayer.get(uid);
};

LeagueFsm.prototype.hasFirstSeasonJoinPlayer = function(uid)
{
    return this.firstSeasonCanJoinPlayer.has(uid);
};

LeagueFsm.prototype.toMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
  
    for (var i in arr)
    {
       const object = arr[i];
       let obj = {
            playerId: object["playerId"],
            joinTime: object["joinTime"],
            openId: object["openId"],
       }

       map.set(obj.playerId, obj);
    }
    return map;
};

LeagueFsm.prototype.toArr = function(map) {
    var arr =  [];
    if (!map)
    {
        return arr;
    }
  
    for (var [k, v] of map)
    {
       let obj = {
        playerId: k,
        joinTime: v.joinTime,
        openId: v.openId,
       };
       arr.push(obj);
    }
    return arr;
};

LeagueFsm.prototype.getFirstJoinPlayerCount = function()
{
    let size = 0;
    for(let [k,v] of this.firstSeasonCanJoinPlayer)
    {
        size++;
    }
    //logger.info("getFirstJoinPlayerCount", size);
    return size;
};