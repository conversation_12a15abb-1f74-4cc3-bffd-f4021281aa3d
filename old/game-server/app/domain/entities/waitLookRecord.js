/**
 * Created by scott on 2019/4/26. weather: sunny
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');

//为了优化比赛开始的消息，这个把第一条的消息发给用户都存储起来
var WaitLookRecord = function() 
{
    this.waitLookRecorderList = new Map(); // uid => roomUid
};

util.inherits(WaitLookRecord, EventEmitter);

module.exports = WaitLookRecord;

WaitLookRecord.prototype.setLooker = function(uid, roomUid) {
    this.waitLookRecorderList.set(uid, roomUid);
};

WaitLookRecord.prototype.getRoomUid = function(uid) {
    return this.waitLookRecorderList.get(uid);
};

WaitLookRecord.prototype.delLooker = function(uid) {
    this.waitLookRecorderList.delete(uid);
};

WaitLookRecord.prototype.hasLooker = function(uid) {
    return this.waitLookRecorderList.has(uid);
};

WaitLookRecord.prototype.clear = function() 
{
    return this.waitLookRecorderList.clear();
};

WaitLookRecord.prototype.getAllUidList = function()
{
    let uidList = [];
    for (let [k,v] of this.waitLookRecorderList) {
        uidList.push(k);
    }
    return uidList;
};  