/**
 * Created by sea on 2020/02/03.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);;
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');


let CommonActivity = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.consumeInfo = [];
    this.week = 0;
    this.energyVal = 0;    //精力值
    this.startTime = 0;    //活动开启时间
    this.buyTeamFormationGiftNum = 0; //购买次数(限时阵型重置礼包次数)
    this.buyTeamFormationTime = 0;    //限时阵型重置礼包购买时间
};

util.inherits(CommonActivity, EventEmitter);

module.exports = CommonActivity;

CommonActivity.prototype.initByDB = function (doc) {
    this.consumeInfo = doc.consumeInfo || [];
    this.week = doc.week || 0;
    this.energyVal = doc.energyVal || 0;
    this.startTime = doc.startTime || 0;
    this.buyTeamFormationGiftNum = doc.buyTeamFormationGiftNum || 0;
    this.buyTeamFormationTime = doc.buyTeamFormationTime || 0;
};

CommonActivity.prototype.toJSONforClient = function () {
};

CommonActivity.prototype.toJSONforDB = function () {
    let consumeInfo = {
        consumeInfo: this.consumeInfo,
        week: this.week,
        energyVal: this.energyVal,
        startTime: this.startTime,
        buyTeamFormationGiftNum: this.buyTeamFormationGiftNum,
		buyTeamFormationTime: this.buyTeamFormationTime
    }
    return consumeInfo;
};

//初始化精力消耗数据
CommonActivity.prototype.initConsume = function () {
    let config = dataApi.allData.data["EnergySupply"];
    if(JSON.stringify(config) === "{}"){
        return [];
    }

    let len = Object.keys(config).length;
    let consumeInfo = [];
    let idx = 0;
    for(let i = 0; i < len; ++i) {
        consumeInfo[idx] = {};
        consumeInfo[idx].status = 0;
        consumeInfo[idx].num = config[i+1].Consume;
        idx++;
    }
    return consumeInfo;
}

CommonActivity.prototype.checkActivityIsTimeout = function () {
    let config = dataApi.allData.data["ActivityConfig"];
    let actInfo;
    for(let i in config) {
        if(config[i].ActivityId === 7) {
            actInfo = config[i];
            break;
        }
    }

    let day = timeUtils.dayInterval(this.startTime);
    let week = Math.floor(day / 7); //第几周
    //不是同一周初始化数据
    if(week !== this.week) {
        // logger.error("消耗清理-----------------", this.startTime);
        this.energyVal = 0;
        this.week = week;
        if(this.consumeInfo.length > 0) {
            for(let i = 0; i < this.consumeInfo.length; ++i) {
                if(this.consumeInfo[i].status === 1) {
                    let config = dataApi.allData.data["EnergySupply"][i+1];
                    let rewardList = {};
                    rewardList.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    rewardList.ResId = config.Reward;
                    rewardList.Num = config.Num;
                    this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.ENERGY_FEEDBACK, commonEnum.MailType.SYSMAIL, [rewardList], {roomUid: ""}, "", "", "", "");
                    this.consumeInfo[i].status = 2;
                }
            }
        }
        this.startTime = actInfo.StartTime * 1000;
        this.consumeInfo = this.initConsume();
        this.player.saveCommonActivity();
    }
}

CommonActivity.prototype.addEnergyVal = function (value) {
    this.checkActivityIsTimeout();
    this.energyVal += value;
    for(let i in this.consumeInfo) {
        if(this.energyVal >= this.consumeInfo[i].num && this.consumeInfo[i].status === 0) {
            this.consumeInfo[i].status = 1;
        }
    }
}

//活动   startTime 活动开启时间
CommonActivity.prototype.getConsumeInfo = function () {
    let ret = {code: Code.FAIL, consumeInfo: [], energyVal: 0}
    this.checkActivityIsTimeout()
    ret.code = Code.OK;
    ret.consumeInfo = this.consumeInfo;
    ret.energyVal = this.energyVal;
    return ret;
};

//领取奖励   精力消耗
CommonActivity.prototype.getConsumeReward = function (index) {
    let ret = {code : Code.FAIL, status: 0}
    if(index < 0 || index > 6){
        ret.code = Code.RANGE_FAIL;
        return ret;
    }
    this.checkActivityIsTimeout();

    if(this.consumeInfo.length < 1) {
        ret.code = Code.TIME_FAIL;
        return ret;
    }

    if(this.consumeInfo[index].status === 1){
        let config = dataApi.allData.data["EnergySupply"][index+1];
        if(JSON.stringify(config) === "{}"){
            ret.code = Code.CONFIG_FAIL;
            return ret;
        }
        this.player.bag.addItem(config.Reward, config.Num);
        this.consumeInfo[index].status = 2;
    }else if(this.consumeInfo[index].status === 2){
        ret.code = Code.REQUEST_FAIL;
        return ret;
    }else {
        ret.code = Code.GET_FAIL;
        return ret;
    }
    ret.code = Code.OK;
    ret.status = this.consumeInfo[index].status;
    return ret;
};


//MVP球员
CommonActivity.prototype.buyMvpHero = function (id, num, gid) {
    let ret = {
        code: Code.FAIL,
        itemInfo: {}
    };
    let nowTime = timeUtils.now();
    let config = dataApi.allData.data["MVP"][id];
    let activityConfig = dataApi.allData.data["ActiveControl"][21];
    if(!config) {
        return ret;
    }

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, config.Price)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, config.Price);
    this.player.bag.addItem(config.ItemId, num);

    let startTime = new Date(activityConfig.StartTime).getTime();
    let endTime = new Date(activityConfig.EndTime).getTime();
    if(nowTime >= startTime && nowTime < endTime) {
        let buyInfo = {};
        buyInfo.playerId = this.uid;
        buyInfo.itemId = config.ItemId;
        buyInfo.buyTime = nowTime;
        buyInfo.buyNum = num;
        buyInfo.gid = gid;
        buyInfo.price = config.Price;
        buyInfo.isSend = 0;
        ret.itemInfo = buyInfo;
    }

    ret.code = Code.OK;
    return ret;
}

//MVP球员-信仰礼包
CommonActivity.prototype.buyBeliefMvpHero = function (id) {
    let ret = {
        code: Code.FAIL
    };
    let nowTime = timeUtils.now();
    let config = dataApi.allData.data["Faith"][id];
    let activityConfig = dataApi.allData.data["ActiveControl"][28];
    let startTime = new Date(activityConfig.StartTime).getTime();
    let endTime = new Date(activityConfig.EndTime).getTime();
    if(nowTime < startTime && nowTime > endTime) {
        ret.code = Code.TIME_FAIL;
        return ret;
    }

    if(!config) {
        return ret;
    }

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, config.Price)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, config.Price);
    this.player.bag.addItem(config.ItemId, 1);
    ret.code = Code.OK;
    return ret;
}

CommonActivity.prototype.getBuyTeamFormationGiftNum = function () {
    let nowTime = timeUtils.now();
    let activityConfig = dataApi.allData.data["ActiveControl"][29];
    let startTime = new Date(activityConfig.StartTime).getTime();
    let endTime = new Date(activityConfig.EndTime).getTime();
    if(nowTime < startTime && nowTime > endTime) {
        return this.buyTeamFormationGiftNum;
    }

    if(this.buyTeamFormationTime !== startTime) {
        this.buyTeamFormationTime = startTime;
        this.buyTeamFormationGiftNum = 0;
    }
    return this.buyTeamFormationGiftNum;
}

//阵型重置卡礼包
CommonActivity.prototype.buyTeamFormationGift = function () {
    let ret = {
        code: Code.FAIL,
        num: this.buyTeamFormationGiftNum,
    };
    let nowTime = timeUtils.now();
    let activityConfig = dataApi.allData.data["ActiveControl"][29];
    let startTime = new Date(activityConfig.StartTime).getTime();
    let endTime = new Date(activityConfig.EndTime).getTime();
    if(nowTime < startTime && nowTime > endTime) {
        ret.code = Code.TIME_FAIL;
        return ret;
    }

    this.buyTeamFormationGiftNum = this.getBuyTeamFormationGiftNum();
    let activeParam = dataApi.allData.data["ActiveParam"];
    if(!activeParam) {
        return ret;
    }

    let config = {};
    for(let i in activeParam) {
        if(activeParam[i].GroupId === 34) {
            config.maxBuyNum = activeParam[i].Parameter2;
            config.gold = activeParam[i].Parameter1;
            config.rewardList = [];
            let index = 0;
            for(let j = 1; j < 6; ++j) {
                let resId = activeParam[i]["Reward" + j];
                let num = activeParam[i]["Number" + j];
                if(resId > 0 && num > 0) {
                    config.rewardList[index] = {};
                    config.rewardList[index].resId = resId;
                    config.rewardList[index].num = num;
                }
                index++;
            }
        }
    }

    //检查购买次数
    if(this.buyTeamFormationGiftNum >= config.maxBuyNum) {
        ret.code = Code.BUY_MAX_FAIL;
        return ret;
    }

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, config.gold)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, config.gold);
    for (let i in config.rewardList) {
        let itemId = config.rewardList[i].resId;
        let num = config.rewardList[i].num;
        this.player.bag.addItem(itemId, num);
    }
    this.buyTeamFormationGiftNum ++;
    ret.code = Code.OK;
    ret.num = this.buyTeamFormationGiftNum;
    return ret;
}


