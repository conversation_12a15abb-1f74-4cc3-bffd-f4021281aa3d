/**
 * Created by sea on 2019/12/11.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) {
    return new playerMLSService(app, dbclient);
};

var playerMLSService = function (app, dbclient)
{
    this.app = app;
};

util.inherits(playerMLSService, EventEmitter);

//得到界面
playerMLSService.prototype.getMLSSelectInterface = function(playerId, msg, cb)
{
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if(!player) {
        logger.debug('playerMLSService.getMLSSelectInterface: player not exist !');
        cb(Code.FAIL, "");
        return;
    }
    player.MLS.isToDayInit();//是否隔天，隔天刷新
    let teamList = player.MLS.getConfigTeam();//得到配置的8支队伍
    let CombatList = player.MLS.CombatList;
    for(let idx in CombatList)//检查对战列表有没有问题
    {
        if(!CombatList[idx].TeamA || !CombatList[idx].TeamB || CombatList.length > teamList.length / 2)
        {
            logger.warn("getMLSSelectInterface CombatList Data error repair", CombatList);
            player.MLS.initCombatList(true);//初始化对战列表
            break;
        }
    }
    //是否开始，奖励条件id，队伍列表，对战列表，挑战次数
    cb(Code.OK, player.MLS.isBegin, player.MLS.getConditionID(), teamList, player.MLS.CombatList, player.MLS.contestNum);

};
//参加比赛
playerMLSService.prototype.MLSJoinMatch = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if (!player) {
        logger.debug('playerMLSService.MLSJoinMatch: player not exist !');
        cb(Code.FAIL, 0, []);
        return;
    }
    if (player.MLS.isBegin !== 0 && player.MLS.CombatList)
    {
        cb(Code.OK, player.MLS.conditionID, player.MLS.CombatList);//返回奖励条件id，对战列表
        return;
    }
    if(player.MLS.contestNum >= player.MLS.contestNumMax)
    {
        //大于参加次数今天不能参加了
        cb(Code.COUNT_FALL, player.MLS.conditionID, []);
        return;
    }
    let money = 0;
    switch (player.MLS.contestNum) {
        case 0:
            //第一次免费
            break;
        case 1:
            //扣除球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.MLS_round2_Gold].Param;
            break;
        default :
            //之后每次球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.MLS_more_Gold].Param;
    }
    //logger.error("-==-=-=-=-=-=-=-=-",money, player.gulfCup.contestNum);
    if(!player.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, money))//检查钱是否足够
    {
        logger.error('MLS.joinMatch: money not enough !', playerId, money);
        return cb(Code.GOLD_FALL, player.MLS.conditionID, []);
    }

    //触发任务
    //player.tasks.triggerTask(commonEnum.TARGET_TYPE.JOIN_GULF_CUP);
    player.MLS.initCombatList(false);//初始化对战列表
    cb(Code.OK, player.MLS.conditionID, player.MLS.CombatList);//返回奖励条件id，对战列表

};
//开始比赛
playerMLSService.prototype.MLSBattle = function(playerId, msg, cb)
{
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if(!player) {
        logger.debug('playerMLSService.MLSBattle: player not exist !');
        cb(Code.FAIL, "");
        return;
    }

    let costNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.MLS_Battle_energy].Param;//每场比赛扣除精力
    let costRet = player.checkResourceIsEnough(commonEnum.PLAY_INFO.energy, costNum);//检查体力是否足够
    if(!costRet) {
        cb(Code.ENERGY_FAIL, "");
        return;
    }

    let teamId;
    //logger.error("yyyyyyyyyyyyyyyyyyyyyyy",  player.middleEastCup.awardList, player.middleEastCup.rivalTeamList);
    for(let i in player.MLS.CombatList)//遍历美职联战斗列表
    {
        if(player.MLS.CombatList[i].TeamA.id === playerId)
        {
            teamId = player.MLS.CombatList[i].TeamB.id;//得到现阶段对战的队伍id
            break;
        }
        if(player.MLS.CombatList[i].TeamB.id === playerId)
        {
            teamId = player.MLS.CombatList[i].TeamA.id;//得到现阶段对战的队伍id
            break;
        }
    }
    let retCode = 200;
    if(!teamId)
    {
        retCode = Code.PARAM_FAIL;
        cb(retCode, "");
        return;
    }

    //获取比赛列表
    let rpcMsg = {};
    rpcMsg.configId = teamId;
    rpcMsg.playerId = playerId;         //玩家本人
    rpcMsg.playerLv = player.level;     //玩家等级
    let self = this;
    let rpcSession = {frontendId: playerService.app.getServerId()};
    //测试战斗结束返回奖励
    // self.updateMLSResult(playerId, teamId, 4, 1, function (ret) {
    //     logger.error("返回的奖励:::::::::::", ret);
    // });

    //通知BattleSvr
    playerService.app.rpc.battle.battleRemote.pveMLSBattleStart(rpcSession, rpcMsg, function (code, roomUid) {
        if(code !== Code.OK) {
            logger.warn("rpc pveMLSBattleStart fail, ret code: ", code);
            cb(code, "");
            return;
        }
        cb(code, roomUid);
        return;
    });
};

//战斗服返回的战斗结果
playerMLSService.prototype.updateMLSResult = function(playerId, reamId, selfScore, otherScore, cb)
{
    //logger.error("有没有返回战斗结果呢：：：：：：：：：：：：：：：", playerId, reamId, selfScore, otherScore);
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if (!player){
        logger.error("playerMLSService.updateMLSResult player not exist!", playerId);
        cb(Code.FAIL);
        return;
    }

    // logger.error("PlayerWorldCupService.updateWorldCupResult",robotId, selfScore, otherScore);
    let ret = player.MLS.pveMLSBattleResult(reamId, selfScore, otherScore);
    if (ret.code !== Code.OK) {
        logger.error("PlayerMLSService.updateMLSResult error reason!");
        return cb(ret.code);
    }

    //计算PL
    player.heros.checkHeroLeaveTime();
    //记录时间
    player.heros.recordHeroLeaveTime();
    //增加球员战斗场数
    player.heros.addHeroBattleNum(commonEnum.HERO_BATTLE_TYPE.MLS);
    //增加球员PL
    player.heros.addHeroFatigue(selfScore, otherScore);
    //记录战败
    player.act.addPushGiftBattleNum(selfScore, otherScore);

    let msg = {
        code: ret.code,
        award: ret.award,
        awardNum: ret.awardNum,
    };

    //推送战斗结果
    playerService.app.get("pushMessageService").unicast("game.playerHandler.updateMLSResult", msg, player);

    let costNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.MLS_Battle_energy].Param;//每场比赛扣除精力
    //扣除体力
    player.consumeEnergy(costNum);

    //player.tasks.triggerTask(commonEnum.TARGET_TYPE.JOIN_MIDDLE_EAST_CUP);
    player.saveHeros();
    player.updateHeros();
    player.saveMLS();
    player.saveAct();
    player.saveTasks();
    cb(ret.code);
    return;
};
