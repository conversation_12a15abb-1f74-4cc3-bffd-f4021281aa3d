/**
 * Idea and Persist
 * Created by <PERSON> on 2019/7/4.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');
var OfflineMail = require("../entities/offlineMail");
var WaitLookRecord = require("../entities/waitLookRecord");

module.exports.create = function(app, dbclient) {
	return new PlayerLeagueService(app, dbclient);
};

var PlayerLeagueService = function (app, dbclient) 
{
	this.app = app;
	this.waitLookerRecord = new WaitLookRecord();  //观看比赛直播
};

util.inherits(PlayerLeagueService, EventEmitter);

//联赛开关
PlayerLeagueService.prototype.updateLeagueFuncSwitch = function(player) 
{
	let self = this;
	let msg ={openId: player.openId};
	let playerId = player.playerId;
	let session = {frontendId: this.app.getServerId()};
	self.app.rpc.league.leagueRemote.SSGetFuncSwitch(session, playerId, msg, function(err, result) {
		//logger.debug('----- SSGetFuncSwitch return: -----', err, result);
		if (!!err)
		{
			logger.error("SSGetFuncSwitch failed!", err);
			return;
		}

		if(result.code !== Code.OK) 
		{
			logger.error("SSFuncSwitch failed!", result.code);
			return;
		}

		let funcSwitchInfo = {
			type: commonEnum.FUNC_SWITCH_TYPE.LEAGUE,		
			status: result.status,
			openTime: result.openTime,
		};

		//logger.info("updateLeagueFuncSwitch", playerId, funcSwitchInfo);
		player.updateFuncSwitch(funcSwitchInfo);
	});
};
//得到当前赛季，线上前三个赛季为季前赛所以这里返回当前赛季减3
PlayerLeagueService.prototype.updateLeagueSeasonId = function(player) {
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetLeagueSeasonId(session, function (seasonId) {
		if(!seasonId)
		{
			seasonId = 0;
		}
		if(seasonId > 3)
		{
			seasonId -= 3;
		}
		//logger.error("---------updateLeagueSeasonId-----------", seasonId);
		player.updateLeagueSeasonId(seasonId);
	})
};
//更新报名提示, 上线通知S
PlayerLeagueService.prototype.updateLeagueEnrollTips = function(player) {
	this.CSGetLeagueEnroll(player.playerId, {}, function (code, status, beginTime, endTime, isEnroll) {
		//未报名阶段不提示
		if(code !== Code.OK || status !== commonEnum.LEAGUE_ENROLL_STATUS.ENROLL_RUNNING) {
			logger.debug("no need updateLeagueEnrollTips: ",code, status);
			return;
		}
		//已报名的，每次联赛只发一次
		let now = TimeUtils.now();
		if(isEnroll === 1 && player.leagueEnrollTipsTime > beginTime) {
			logger.debug("already send tips: ", isEnroll, player.leagueEnrollTipsTime, beginTime);
			return;
		}
		let updateInfo = {
			isEnroll : isEnroll,
			beginTime : beginTime,
			endTime : endTime,
			relayIsJoin: player.relay.isJoin,
		};
		//对于已报名的用户记录报名的时间
		if(isEnroll === 1) {
			player.leagueEnrollTipsTime = now;
		}
		player.updateLeagueEnrollTips(updateInfo);
	});
};

//获取报名信息
PlayerLeagueService.prototype.CSGetLeagueEnroll = function(playerId, msg, cb)
{
	let status = 0;
	let beginTime = 0;
	let endTime = 0;
	let isEnroll = 0;
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetLeagueEnroll player not exist !');
		cb(Code.FAIL, status, beginTime, endTime, isEnroll);
		return;
	}

	let self = this;
	let ssMsg = {playerId: playerId};
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetLeagueEnroll(session, playerId, ssMsg, function (err, result) {
		//logger.warn('----- CSGetLeagueEnroll return: -----', result);
		if (!!err)
		{
			logger.error("CSGetLeagueEnroll: catch a error!", err, result);
			cb(Code.FAIL, status, beginTime, endTime, isEnroll, player.relay.isJoin);
			return;
		}

		if(result.code !== Code.OK) {
			logger.error("CSGetLeagueEnroll return failed!", result.code);
			cb(result.code, status, beginTime, endTime, isEnroll, player.relay.isJoin);
			return;
		}

		status = result.status;
		beginTime = result.beginTime;
		endTime = result.endTime;
		isEnroll = result.isEnroll;
		cb(Code.OK, status, beginTime, endTime, isEnroll, player.relay.isJoin);
	});
};

//报名
PlayerLeagueService.prototype.CSLeagueEnroll = function(playerId, msg, cb) {
	let status = 0;
	let beginTime = 0;
	let endTime = 0;
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSLeagueEnroll player not exist !');
		cb(Code.FAIL, status, beginTime, endTime);
		return;
	}

	var leagueSwitchConfig = dataApi.allData.data["LeagueSwitch"][1];
    if (!leagueSwitchConfig) {
		logger.error("CSLeagueEnroll: leagueSwitchConfig not found!");
		cb(Code.FAIL, status, beginTime, endTime);
        return;
	}

	let playerObj ={
		uid: playerId,
		beliefId: player.getBeliefId(),
		tfId: player.teamFormations.getCurrTeamFormationId(),
		actualStrength: player.getFormationAct(),
	};
	let self = this;
	let session = {frontendId: this.app.getServerId()};
	let ssMsg = {playerObj: playerObj};
	let itemUid = player.item.getItemUidByResId(leagueSwitchConfig.ConsumeId);
	async.waterfall([function (callback) {
		self.app.rpc.league.leagueRemote.checkIsProfessionJoiner(session, playerId, function (err, result)
		{
			//先检查玩家是否在专业联赛中
			if(result)
			{
				//let groupId = self.profession.getPlayerGroupMap(playerId);
				logger.error("Enroll: this player is profession player", playerId);
				cb(Code.LEAGUE.ENROLL_PROFESSION_NOT_ALLOW);
				return;
			}
			//logger.error("result===============", result);
			//检查物品是否足够
			let isEnough = player.bag.checkItemEnough(leagueSwitchConfig.ConsumeId, leagueSwitchConfig.Num);
			if (!isEnough)
			{
				logger.error("CSLeagueEnroll: checkItemEnough failed! item not enough!", leagueSwitchConfig.ConsumeId, leagueSwitchConfig.Num);
				cb(Code.LOGIC.NOT_ENOUGH_ITEM, status, beginTime, endTime);
				return;
			}
			callback(null)
		});
	}, function (callback) {
		self.app.rpc.league.leagueRemote.SSLeagueEnroll(session, playerId, ssMsg, function (err, result) {
			if (!!err)
			{
				logger.error("SSLeagueEnroll: catch a error!", err, result);
				callback(err, result.code);
				return;
			}

			//logger.info('----- SSLeagueEnroll return: -----', result);
			if(result.code !== Code.OK) {
				logger.error("SSLeagueEnroll return failed!", result.code);
				callback(result.code, result.code);
				return;
			}

			//删除物品
			logger.error("delete item: playerId, itemUid, Num", playerId, itemUid, leagueSwitchConfig.Num);
			player.bag.removeItem(itemUid, leagueSwitchConfig.Num);
			//触发任务
			player.tasks.triggerTask(commonEnum.TARGET_TYPE.FOURTEEN);
			player.saveItem();
			player.saveBag();
			player.updateBag();

			player.recordSlog(playerId, commonEnum.STATIS_LOG_TYPE.PVP_LEAGUE_SIGNUP, [], {});

			callback(null, Code.OK);
		});
	}],function (err, code)
	{
		logger.error("CSLeagueEnroll err", err, playerId);
		cb(code);
	});

};

//获取界面数据
PlayerLeagueService.prototype.CSGetCurrLeagueData = function(playerId, msg, cb) 
{
	let faildRet = {seasonId: 0, roundScoreRankData: {}};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetCurrLeagueData player not exist !');
		cb(Code.FAIL, faildRet.seasonId, faildRet.roundScoreRankData, 0);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetCurrLeagueData(session, playerId, msg, function (err, result) {
		if (!!err)
		{
			logger.error("SSGetCurrLeagueData: catch a error!", err, result);
			cb(result.code, faildRet.seasonId, faildRet.roundScoreRankData, 0, 0);
			return;
		}

		//logger.warn('----- SSGetCurrLeagueData return: -----', result);
		if(result.code !== Code.OK) 
		{
			logger.error("SSGetCurrLeagueData return failed!", result.code, result);
			cb(result.code, result.seasonId, result.roundScoreRankData, result.isJoin, result.currTypeId);
			return;
		}
		
		let roundScoreRankData = result.roundScoreRankData;
		self.fillScoreRankPlayerInfo(roundScoreRankData, function(err){
			if (!!err)
			{
				logger.info("fillScoreRankPlayerInfo: error!", err);
				cb(Code.FAIL, result.seasonId, result.roundScoreRankData, result.isJoin, result.currTypeId);
				return;
			}

			//logger.info("roundScoreRankData", roundScoreRankData);
			cb(Code.OK, result.seasonId, roundScoreRankData, result.isJoin, result.currTypeId);
			return;
		});
	});
};

PlayerLeagueService.prototype.checkArgc = function(playerId, typeId, roundId, groupId)
{
	if (!typeId)
	{
		logger.error("checkArgc: typeId error!", playerId, typeId);
		return false;
	}

	if (typeId > commonEnum.LEAGUE_TYPE_ID.COMMUNITY || typeId < commonEnum.LEAGUE_TYPE_ID.SUPER)
	{
		logger.error("checkArgc: typeId illegal!", playerId, typeId);
		return false;
	}

	if (!roundId || roundId <= 0)
	{
		logger.error("checkArgc: roundId error!", playerId, typeId, roundId);
		return false;
	}

	if (!groupId || groupId <= 0)
	{
		logger.error("checkArgc: groupId error!", playerId, typeId, roundId, groupId);
		return false;
	}

	if (roundId > 0 && groupId > 0)
	{
		if (commonEnum.LEAGUE_TYPE_ID.COMMUNITY === typeId)
		{
			if (roundId > 5)
			{
				logger.error("checkArgc: COMMUNITY roundId error!", playerId, roundId);
				return false;
			}

			if (groupId > 128)
			{
				logger.error("checkArgc: COMMUNITY groupId error!", playerId, groupId);
				return false;
			}

		}else if (commonEnum.LEAGUE_TYPE_ID.NORMAL === typeId)
		{
			if (roundId > 6)
			{
				logger.error("checkArgc: NORMAL roundId error!", playerId, roundId);
				return false;
			}

			if (groupId > 8)
			{
				logger.error("checkArgc: NORMAL groupId error!", playerId, groupId);
				return false;
			}
		}else if (commonEnum.LEAGUE_TYPE_ID.KNOCKOUT === typeId)
		{
			if (roundId > 2)
			{
				logger.error("checkArgc: KNOCKOUT roundId error!", playerId, roundId);
				return false;
			}

			if (groupId > 1)
			{
				logger.error("checkArgc: KNOCKOUT groupId error!", playerId, groupId);
				return false;
			}
		}else if (commonEnum.LEAGUE_TYPE_ID.PREPARE === typeId
			|| commonEnum.LEAGUE_TYPE_ID.AMATEUR === typeId
			|| commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND === typeId
			|| commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST === typeId
			|| commonEnum.LEAGUE_TYPE_ID.CHAMPIONS === typeId
			|| commonEnum.LEAGUE_TYPE_ID.SUPER === typeId)
		{	
			if (roundId > 39)
			{
				logger.error("checkArgc: PROFESSION roundId error!", playerId, roundId);
				return false;
			}

			if (groupId > 5 + 16)
			{
				logger.error("checkArgc: PROFESSION groupId error!", playerId, groupId);
				return false;
			}
		}else
		{
			logger.error("checkArgc: no match roundId!", playerId, typeId, roundId);
			return false;
		}
	}

	return true;
};

//赛程数据
PlayerLeagueService.prototype.CSGetSchedule = function(playerId, msg, cb) 
{
	logger.debug("------ PlayerLeagueService CSGetSchedule --------");
	let failedRoundScheduleData = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		scheduleList: [],
	};

	let faildRet = {seasonId: 0};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetSchedule player not exist !');
		cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
		return;
	}

	let typeId = msg.typeId;
	let roundId = msg.roundId;
	let groupId = msg.groupId;
	if (!roundId)
	{
		roundId = 1;
	}

	if (!groupId)
	{
		groupId = 1;
	}

	if (!this.checkArgc(playerId, typeId, roundId, groupId)) 
	{
		logger.error("CSGetSchedule: check argc error!", playerId, typeId, roundId, groupId);
		cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetSchedule(session, playerId, msg, function(err, result) {
		//logger.warn('----- SSGetSchedule return: -----', err, result.seasonId, result.roundScheduleData);
		if (!!err)
		{
			logger.error("SSGetSchedule catach a error!", err);
			cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
			return;
		}

		if(result.code !== Code.OK) 
		{
			logger.error("SSGetSchedule return failed!", result.code);
			cb(result.code, faildRet.seasonId, failedRoundScheduleData);
			return;
		}

		//填充玩家数据
		let roundScheduleData = result.roundScheduleData;
		// if(roundScheduleData.typeId < commonEnum.LEAGUE_TYPE_ID.AMATEUR)
		// {
			self.fillSchedulePlayerInfo(roundScheduleData, function(err){
				if (!!err)
				{
					logger.info("fillSchedulePlayerInfo: error!", err);
					cb(Code.FAIL, result.seasonId, result.roundScheduleData);
					return;
				}
				//logger.info("roundScheduleData", roundScheduleData);
				cb(Code.OK, result.seasonId, roundScheduleData);
			});
		// }//资格赛加上头像url
		// else if(roundScheduleData.typeId > commonEnum.LEAGUE_TYPE_ID.AMATEUR)
		// {
		// 	//获取名字和头像
		// 	self.getScheduleFaceUrl(roundScheduleData, function () {
		// 		cb(Code.OK, result.seasonId, roundScheduleData);
		// 		return;
		// 	});
		// }
	});
};

PlayerLeagueService.prototype.fillSchedulePlayerCallback = function(scheduleList, isTeamA, cb)
{
	if (scheduleList.length <= 0)
	{
		cb(null);
		return;
	}
	//logger.info("fillSchedulePlayerCallback: ", scheduleList);
	let self = this;
	let playerService = this.app.get("playerService");
	async.eachSeries(scheduleList, function(info, callback) {
		let teamA = info.teamA;
		let teamB = info.teamB;

		let uid = "";
		if (isTeamA)
		{
			uid = teamA;
		}else
		{
			uid = teamB;
		}

		if (uid !== "" ) {
			logger.debug("fillSchedulePlayerCallback - getOtherPlayer now:", uid);
			let findDbName = new Array(commonEnum.DB_NAME.player);
			playerService.getOtherPlayer(uid, findDbName, function(otherPlayer){
				if(!otherPlayer)
				{
					logger.error("PlayerLeagueService fillSchedulePlayerCallback: get otherPlayer failed", uid);
					callback(null);
					return;
				}
				
				if (isTeamA) 
				{
					info.teamAName = otherPlayer.name;
					info.teamAfaceUrl = otherPlayer.faceUrl;
				}
				else
				{
					info.teamBName = otherPlayer.name;
					info.teamBfaceUrl = otherPlayer.faceUrl;
				}
				callback(null);
			});   
		}else
		{
			callback(null)
		}
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err);
            return;
		}
		cb(null);
	});	
};

PlayerLeagueService.prototype.fillSchedulePlayerInfo = function(roundScheduleData, cb)
{
	if (roundScheduleData.scheduleList.length <= 0)
	{
		return cb(null);
	}

	let self = this;
	async.waterfall([
		function (callback) {
			self.fillSchedulePlayerCallback(roundScheduleData.scheduleList, true, function(err) {
				if(!!err) 
				{
					callback("fillSchedulePlayerInfo teamA failed!");
					return;
				}
				callback(null);
			});
		},
		function (callback) {
			self.fillSchedulePlayerCallback(roundScheduleData.scheduleList, false, function(err) {
				if(!!err) 
				{
					callback("fillSchedulePlayerInfo teamB failed!");
					return;
				}
				callback(null);
			});
		}
	], function(err) {
		if(!!err){
			logger.debug("water fall fail: error msg: ", err);
			cb(err);
			return;
		}
		cb(null);
	});
};

PlayerLeagueService.prototype.fillScoreRankPlayerCallback = function(info, cb)
{
	let uid = info.playerUid;
	let playerService = this.app.get("playerService");
	logger.debug("fillScoreRankPlayerCallback - getOtherPlayer now:", uid);
	let findDbName = new Array(commonEnum.DB_NAME.player);
	playerService.getOtherPlayer(uid, findDbName,function(otherPlayer){
		if(!otherPlayer)
		{
			logger.error("PlayerLeagueService fillScoreRankPlayerCallback: get otherPlayer failed", uid);
			cb(null);
			return;
		}

		info.name = otherPlayer.name;
		info.faceIcon = otherPlayer.faceIcon;
		cb(null);
	});
};

PlayerLeagueService.prototype.fillScoreRankPlayerInfo = function(roundScoreRankData, cb)
{
	if (roundScoreRankData.teamInfoList.length <= 0)
	{
		cb(null);
		return;
	}

	let self = this;
	async.eachSeries(roundScoreRankData.teamInfoList, function(info, callback) {
		self.fillScoreRankPlayerCallback(info, function(err){
			if(!!err)
			{
				logger.error("PlayerLeagueService fillScoreRankPlayerInfo: get otherPlayer failed", uid);
				callback(null);
				return;
			}

			callback(null);
			return;
		});   
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err)
            return;
		}

		cb(null);
		return;
	});
};

//排名数据
PlayerLeagueService.prototype.CSGetScoreRank = function(playerId, msg, cb) 
{
	let faildRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		scheduleList: [],
	};

	logger.debug("------ PlayerLeagueService CSGetScoreRank --------");
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetScoreRank player not exist !');
		cb(Code.FAIL, 0,  faildRet);
		return;
	}

	let typeId = msg.typeId;
	let roundId = msg.roundId;
	let groupId = msg.groupId;
	if (!roundId)
	{
		roundId = 1;
	}

	if (!groupId)
	{
		groupId = 1;
	}
	if (!this.checkArgc(playerId, typeId, roundId, groupId)) 
	{
		logger.error("CSGetScoreRank: check argc error!", playerId, typeId, roundId, groupId);
		cb(Code.FAIL, 0, faildRet);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetScoreRank(session, playerId, msg, function (err, result) {
		//logger.warn('----- SSGetScoreRank return: -----', result.code, result.seasonId, result.roundScoreRankData);
		if (!!err)
		{
			logger.error("SSGetSchedule catach a error!", err);
			cb(Code.FAIL, 0, faildRet);
			return;
		}

		if(result.code !== Code.OK) 
		{
			logger.error("SSGetScoreRank return failed!", result.code);
			cb(result.code, result.seasonId, result.roundScoreRankData);
			return;
		}

		//填充玩家数据
		let roundScoreRankData = result.roundScoreRankData;
		self.fillScoreRankPlayerInfo(roundScoreRankData, function(err){
			if (!!err)
			{
				logger.info("fillScoreRankPlayerInfo: error!", err);
				cb(Code.FAIL, result.seasonId, result.roundScoreRankData);
				return;
			}
			
			cb(Code.OK, result.seasonId, roundScoreRankData);
			return;
		});
	});
};

//切换默认赛程数据
PlayerLeagueService.prototype.CSSwitchSchedule = function(playerId, msg, cb) 
{
	logger.debug("------ PlayerLeagueService CSSwitchSchedule --------");
	let failedRoundScheduleData = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		scheduleList: [],
	};

	let faildRet = {seasonId: 0};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSSwitchSchedule player not exist !');
		cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId || typeId > commonEnum.LEAGUE_TYPE_ID.COMMUNITY || typeId < commonEnum.LEAGUE_TYPE_ID.SUPER)
	{
		logger.error("CSSwitchSchedule: check argc error!", playerId, typeId, roundId, groupId);
		cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSSwitchSchedule(session, playerId, msg, function(err, result) {
		//logger.warn('----- SSSwitchSchedule return: -----', err, result.seasonId, result.roundScheduleData);
		if (!!err)
		{
			logger.error("SSSwitchSchedule catach a error!", err);
			cb(Code.FAIL, faildRet.seasonId, failedRoundScheduleData);
			return;
		}

		if(result.code !== Code.OK)
		{
			logger.error("SSSwitchSchedule return failed!", result.code);
			cb(result.code, result.seasonId, result.roundScheduleData);
			return;
		}

		//填充玩家数据
		let roundScheduleData = result.roundScheduleData;
		// if(roundScheduleData.typeId < commonEnum.LEAGUE_TYPE_ID.AMATEUR)
		// {
			self.fillSchedulePlayerInfo(roundScheduleData, function(err){
				if (!!err)
				{
					logger.info("fillSchedulePlayerInfo: error!", err);
					cb(Code.FAIL, result.seasonId, result.roundScheduleData);
					return;
				}
				//logger.info("roundScheduleData", roundScheduleData);
				cb(Code.OK, result.seasonId, roundScheduleData);
			});
		//}//资格赛加上头像url
		// else if(roundScheduleData.typeId > commonEnum.LEAGUE_TYPE_ID.AMATEUR)
		// {
		// 	//获取名字和头像
		// 	self.getScheduleFaceUrl(roundScheduleData, function () {
		// 		cb(Code.OK, result.seasonId, roundScheduleData);
		// 		return;
		// 	});
		// }
	});
};

//历届联赛冠军
PlayerLeagueService.prototype.CSSwitchLeagueChampion = function(playerId, msg, cb)
{
	logger.debug("------ PlayerLeagueService CSSwitchLeagueChampion --------");
	let failedRoundScheduleData = [];
	let faildRet = {seasonId: 0};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSSwitchLeagueChampion player not exist !');
		cb(Code.FAIL, failedRoundScheduleData);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId || typeId > commonEnum.LEAGUE_TYPE_ID.AMATEUR || typeId < commonEnum.LEAGUE_TYPE_ID.SUPER)
	{
		logger.error("CSSwitchLeagueChampion: check argc error!", playerId, typeId);
		cb(Code.FAIL, failedRoundScheduleData);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSwitchLeagueChampion(session, playerId, msg, function(err, result) {
		//logger.warn('----- SSSwitchSchedule return: -----', err, result.seasonId, result.roundScheduleData);
		if (!!err)
		{
			logger.error("SSwitchLeagueChampion catach a error!", err);
			cb(Code.FAIL, result.ChampionList);
			return;
		}

		if(result.code !== Code.OK)
		{
			logger.error("SSwitchLeagueChampion return failed!", result.code);
			cb(result.code, result.ChampionList);
			return;
		}
		let ChampionList = new Map();
		//得到玩家夺冠次数
		for(let i = 0; i < result.ChampionList.length; i++)
		{
			if(ChampionList.has(result.ChampionList[i].playerUid))
			{
				let Num = ChampionList.get(result.ChampionList[i].playerUid);
				ChampionList.set(result.ChampionList[i].playerUid, Num += 1);
			}
			else
			{
				ChampionList.set(result.ChampionList[i].playerUid, 1);
			}
		}
		//每个玩家只保存一个，并且排序
		let tempList = [];
		for(let i in result.ChampionList)
		{
			if(ChampionList.has(result.ChampionList[i].playerUid))
			{
				result.ChampionList[i].num = ChampionList.get(result.ChampionList[i].playerUid);
			}
			if(tempList.length <= 0)
			{
				tempList.push(result.ChampionList[i]);
			}
			else
			{
				let flag = true;
				for(let k in tempList)
				{
					if(tempList[k].playerUid === result.ChampionList[i].playerUid)
					{
						flag = false;
					}
				}
				if(flag)
				{
					tempList.push(result.ChampionList[i]);
				}
			}
		}
		tempList.sort(__Champion_sort_func);

		cb(result.code, tempList);
	});
};

function __Champion_sort_func(obj1, obj2)
{
	if(obj1.num > obj2.num)
	{
		return -1;
	}
	else if(obj1.num < obj2.num)
	{
		return 1;
	}
	if(obj1.num === obj2.num)
	{
		if(obj1.seasonId > obj2.seasonId)
		{
			return 1;
		}
		else if(obj1.seasonId < obj2.seasonId)
		{
			return -1;
		}
	}
};

//得到头像
PlayerLeagueService.prototype.getScheduleFaceUrl = function(roundScheduleData, cb)
{
	//logger.error("roundScheduleData:::::::::::", roundScheduleData);
	if (roundScheduleData.scheduleList.length <= 0)
	{
		return cb(null);
	}
	let playerService = this.app.get("playerService");
	let k = 0;
	for(let i = 0; i < roundScheduleData.scheduleList.length; i++)
	{
		let teamA = roundScheduleData.scheduleList[i].teamA;
		let teamB = roundScheduleData.scheduleList[i].teamB;
		//logger.error("循环！！！！！！！！！！！", teamA, teamB, roundScheduleData.scheduleList[i]);
		// let robotA = "robot_" === teamA.substr(0, 6); //home是否为机器人
		// let robotB = "robot_" === teamB.substr(0, 6); //away是否为机器人
		// if (robotA && robotB) continue;
		if(teamA === "" && teamB === "") continue;
		async.waterfall([
			function (callback) {
				let player = playerService.getPlayer(teamA);
				if(!!player && player.isOnline())//在线
				{
					roundScheduleData.scheduleList[i].teamAName = player.name;
					roundScheduleData.scheduleList[i].teamAfaceUrl = player.faceUrl;
					callback(null);
				}
				else if(teamA !== "")
				{
					logger.debug("getScheduleFaceUrl - getOtherPlayer now:", teamA);
					let findDbName = new Array(commonEnum.DB_NAME.player);
					playerService.getOtherPlayer(teamA, findDbName, function(otherPlayer) {
						if(!otherPlayer)
						{
							logger.error("getScheduleFaceIcon: get teamA otherPlayer failed!", teamA);
							callback("getScheduleFaceIcon: get teamA otherPlayer failed!");
							return;
						}
						roundScheduleData.scheduleList[i].teamAName = otherPlayer.name;
						roundScheduleData.scheduleList[i].teamAfaceUrl = otherPlayer.faceUrl;
						//logger.error("AAAAAAAAAAAAAAAAAAAAAAAAA: ", roundScheduleData.scheduleList[i]);
						callback(null);
					});
				}
				else
				{
					callback(null);
				}
			},
			function (callback) {
				let player = playerService.getPlayer(teamB);
				if(!!player && player.isOnline())//在线
				{
					roundScheduleData.scheduleList[i].teamAName = player.name;
					roundScheduleData.scheduleList[i].teamBfaceUrl = player.faceUrl;
					callback(null);
				}
				else if(teamB !== "") {
					logger.debug("getScheduleFaceUrl - getOtherPlayer now:", teamB);
					let findDbName = new Array(commonEnum.DB_NAME.player);
					playerService.getOtherPlayer(teamB, findDbName, function (otherPlayer) {
						if (!otherPlayer) {
							logger.error("getScheduleFaceIcon: get teamB otherPlayer failed!", teamB);
							callback("getScheduleFaceIcon: get teamB otherPlayer failed!");
							return;
						}
						roundScheduleData.scheduleList[i].teamBName = otherPlayer.name;
						roundScheduleData.scheduleList[i].teamBfaceUrl = otherPlayer.faceUrl;
						//simpleTeamInfoB.faceUrl = otherPlayer.faceUrl;
						//logger.error("BBBBBBBBBBBBBBBBBBBBBBBBBB: ", roundScheduleData.scheduleList[i]);

						callback(null);
						k += 1;
					});
				}
				else
				{
					callback(null);
					k += 1;
				}
			},
			function (callback) {
				if(k === roundScheduleData.scheduleList.length)
				{
					//logger.error("wwwwwwwwwwwwwwwwwww", k);
					return cb();
				}
			}
			], function (err) {
			if(!!err){
				logger.debug("water fall fail: error msg: ", err);
				//return;
			}
		});
	}//for

};

//切换默认排名数据
PlayerLeagueService.prototype.CSSwitchScore = function(playerId, msg, cb) 
{
	let faildRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		scheduleList: [],
	};

	logger.debug("------ PlayerLeagueService CSSwitchScore --------");
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSSwitchScore player not exist !');
		cb(Code.FAIL, 0,  faildRet);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId || typeId > commonEnum.LEAGUE_TYPE_ID.COMMUNITY || typeId < commonEnum.LEAGUE_TYPE_ID.SUPER)
	{
		logger.error("CSSwitchScore: check argc error!", playerId, typeId);
		cb(Code.FAIL, 0, faildRet);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSSwitchScore(session, playerId, msg, function (err, result) {
		//logger.warn('----- SSSwitchScore return: -----', result.code, result.seasonId, result.roundScoreRankData);
		if (!!err)
		{
			logger.error("SSSwitchScore catach a error!", err);
			cb(Code.FAIL, 0, faildRet);
			return;
		}

		if(result.code !== Code.OK) 
		{
			logger.error("SSSwitchScore return failed!", result.code);
			cb(result.code, result.seasonId, result.roundScoreRankData);
			return;
		}

		//填充玩家数据
		let roundScoreRankData = result.roundScoreRankData;
		self.fillScoreRankPlayerInfo(roundScoreRankData, function(err){
			if (!!err)
			{
				logger.info("fillScoreRankPlayerInfo: error!", err);
				cb(Code.FAIL, result.seasonId, result.roundScoreRankData);
				return;
			}

			//logger.info("roundScoreRankData", roundScoreRankData);
			cb(Code.OK, result.seasonId, roundScoreRankData);
			return;
		});
	});
};

//赛事历史
PlayerLeagueService.prototype.CSGetPersonalHistory = function(playerId, msg, cb) 
{
	let faildRet = {
    };
    
	logger.debug("------ PlayerLeagueService CSGetPersonalHistory --------");
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetPersonalHistory player not exist !');
		cb(Code.FAIL, faildRet);
		return;
	}

	let uid = msg.uid;
	if (!uid)
	{
		logger.debug('PlayerLeagueService CSGetPersonalHistory uid error!', uid);
		cb(Code.FAIL, faildRet);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetPersonalHistory(session, playerId, msg, function (err, result) {
		//logger.warn('----- SSGetPersonalHistory return: -----', result.code, result.personalHistoryInfo);
		if (!!err)
		{
			logger.error("SSGetPersonalHistory catach a error!", err);
			cb(Code.FAIL, faildRet);
			return;
		}

		if(result.code !== Code.OK) 
		{
			logger.error("SSGetPersonalHistory return failed!", result.code);
			cb(code, faildRet);
			return;
		}

		//填充玩家数据
		let historyInfo = result.personalHistoryInfo;
		self.fillPlayerHistoryInfo(historyInfo, function(err){
			//logger.info("historyInfo", historyInfo);
			if (!!err)
			{
				logger.info("fillPlayerHistoryInfo: error!", err);
				cb(Code.FAIL, historyInfo);
				return;
			}
			
			cb(Code.OK, historyInfo);
			return;
		});
	});
};
//预选赛程
PlayerLeagueService.prototype.CSGetPrimaryHistory = function(playerId, msg, cb)
{
	let faildRet = {
	};

	logger.debug("------ PlayerLeagueService CSGetPrimaryHistory --------");
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('PlayerLeagueService CSGetPrimaryHistory player not exist !');
		cb(Code.FAIL, faildRet);
		return;
	}

	let uid = msg.uid;
	if (!uid)
	{
		logger.debug('PlayerLeagueService CSGetPrimaryHistory uid error!', uid);
		cb(Code.FAIL, faildRet);
		return;
	}

	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetPrimaryHistory(session, playerId, msg, function (err, result) {
		//logger.warn('----- SSGetPersonalHistory return: -----', result.code, result.personalHistoryInfo);
		if (!!err)
		{
			logger.error("SSGetPersonalHistory catach a error!", err);
			cb(Code.FAIL, faildRet);
			return;
		}

		if(result.code !== Code.OK)
		{
			logger.error("SSGetPersonalHistory return failed!", result.code);
			cb(code, faildRet);
			return;
		}

		//填充玩家数据
		let historyInfo = result.personalHistoryInfo;
		self.fillPlayerHistoryInfo(historyInfo, function(err){
			//logger.info("historyInfo", historyInfo);
			if (!!err)
			{
				logger.info("fillPlayerHistoryInfo: error!", err);
				cb(Code.FAIL, historyInfo);
				return;
			}

			cb(Code.OK, historyInfo);
			return;
		});
	});
};
//个人赛程
PlayerLeagueService.prototype.CSGetPersonalAllSchedule = function(playerId, msg, cb)
{
	let faildRet = {
	};

	logger.debug("------ PlayerLeagueService CSGetPersonalAllSchedule --------");
	//let playerService = this.app.get("playerService");
	// var player = playerService.getPlayer(playerId);
	// if(!player){
	// 	logger.debug('PlayerLeagueService CSGetPersonalAllSchedule player not exist !');
	// 	cb(Code.FAIL, faildRet);
	// 	return;
	// }
	let uid = msg.uid;
	if (!uid)
	{
		logger.debug('PlayerLeagueService CSGetPersonalAllSchedule uid error!', uid);
		cb(Code.FAIL, faildRet);
		return;
	}
	let self = this;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.league.leagueRemote.SSGetPersonalAllSchedule(session, playerId, msg, function (err, result) {
		//logger.warn('----- SSGetPersonalAllSchedule return: -----', result.code, result.personalHistoryInfo);
		if (!!err)
		{
			logger.error("SSGetPersonalAllSchedule catach a error!", err);
			cb(Code.FAIL, faildRet);
			return;
		}

		if(result.code !== Code.OK)
		{
			logger.error("SSGetPersonalAllSchedule return failed!", result.code);
			cb(Code.FAIL, faildRet);
			return;
		}

		//填充玩家数据
		let historyInfo = result.PersonalAllSchedule;
		let grepId = result.grepId;
		let seasonId = result.seasonId;
		self.fillPlayerHistoryInfo(historyInfo, function(err){
			//logger.info("historyInfo", historyInfo);
			if (!!err)
			{
				logger.info("fillPlayerHistoryInfo: error!", err);
				cb(Code.FAIL, historyInfo, grepId, seasonId);
				return;
			}
			//logger.error("查询个人赛程！~！！！！！！！！！", grepId, historyInfo);
			cb(Code.OK, historyInfo, grepId, seasonId);
			return;
		});
	});
};

PlayerLeagueService.prototype.checkIsLeagueJoiner = function(playerId, cb)
{
	let self = this;
	let session = {frontendId: this.app.getServerId()};
	self.app.rpc.league.leagueRemote.checkIsLeagueJoiner(session, playerId, function (result)
	{
		if(!result)
		{
			logger.debug('player Not in the league !', playerId);
			cb(Code.REQUEST_FAIL);
			return;
		}
		cb(result);
	});
};

PlayerLeagueService.prototype.fillTeamInfo = function(personalHistoryInfo, isTeamA, cb)
{
	let self = this;
	let playerService = this.app.get("playerService");
	let findDbName = new Array(commonEnum.DB_NAME.player);
	async.eachSeries(personalHistoryInfo, function(historyInfo, callback) {
		let teamA = historyInfo.teamA;
		let teamB = historyInfo.teamB;
		let uidA = teamA.playerUid;
		let uidB = teamB.playerUid;
		
		let uid = "";
		if (isTeamA)
		{
			uid = uidA;
		}else
		{
			uid = uidB;
		}

		if (uid !== "" ) {
			logger.debug("fillTeamInfo - getOtherPlayer now:", uid);
			playerService.getOtherPlayer(uid, findDbName,function(otherPlayer){
				if(!otherPlayer)
				{
					logger.error("PlayerLeagueService fillTeamInfo: get otherPlayer failed", uid);
					callback(null);
					return;
				}
				
				if (isTeamA) 
				{
					teamA.name = otherPlayer.name;
					teamA.faceIcon = otherPlayer.faceIcon;
					//teamA.actualStrength = otherPlayer.getLeagueTFActualStrength();
				}
				else
				{
					teamB.name = otherPlayer.name;
					teamB.faceIcon = otherPlayer.faceIcon;
					//teamB.actualStrength = otherPlayer.getLeagueTFActualStrength();
				}
				callback(null);
			});   
		}else
		{
			callback(null)
		}
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err)
            return;
		}
		cb(null);
		return;
	});
};

PlayerLeagueService.prototype.fillPlayerHistoryInfo = function(personalHistoryInfo, cb)
{
	if (personalHistoryInfo.length <= 0)
	{
		return cb(null);
	}

	let self = this;
	async.waterfall([
		function (callback) {
			self.fillTeamInfo(personalHistoryInfo, true, function(err) {
				if(!!err) 
				{
					callback("fillTeamInfo teamA failed!");
					return;
				}
				callback(null);
			});
		},
		function (callback) {
			self.fillTeamInfo(personalHistoryInfo, false, function(err) {
				if(!!err) 
				{
					callback("fillTeamInfo teamB failed!");
					return;
				}
				callback(null);
			});
		}
	], function(err) {
		if(!!err){
			logger.debug("water fall fail: error msg: ", err);
			cb(err);
			return;
		}

		cb(null);
		return;
	});
};

PlayerLeagueService.prototype.fillPrepareTeamInfo = function(teamA, teamB, competitorUid, cb)
{
	let self = this;
	let competitorName = "";
	let playerService = this.app.get("playerService");
	//logger.info("fillPrepareTeamInfo: ", teamA.playerUid, teamB.playerUid);
	let findDbName = new Array(commonEnum.DB_NAME.player, commonEnum.DB_NAME.heros, commonEnum.DB_NAME.teamFormation);
	async.waterfall([
		function (callback) {
			logger.debug("fillPrepareTeamInfo - getOtherPlayer now:", teamA.playerUid);
			playerService.getOtherPlayer(teamA.playerUid, findDbName, function(otherPlayer) {
				if(!otherPlayer)
				{
					logger.error("PlayerLeagueService _fillTeamInfo: get otherPlayer failed", teamA.playerUid);
					callback(null);
					return;
				}
				
				if (competitorUid === teamA.playerUid)
				{
					competitorName = otherPlayer.name;
				}

				teamA.name = otherPlayer.name;
				teamA.faceIcon = otherPlayer.faceIcon;
				teamA.actualStrength = otherPlayer.getLeagueTFActualStrength();
				teamA.totalValue = otherPlayer.getLeagueTotalValue();
				callback(null);
			});
		},
		function (callback) {
			logger.debug("fillPrepareTeamInfo - getOtherPlayer now:", teamB.playerUid);
			playerService.getOtherPlayer(teamB.playerUid, findDbName, function(otherPlayer) {
				if(!otherPlayer)
				{
					logger.error("PlayerLeagueService _fillTeamInfo: get otherPlayer failed", teamB.playerUid);
					callback(null);
					return;
				}

				if (competitorUid === teamB.playerUid)
				{
					competitorName = otherPlayer.name;
				}

				teamB.name = otherPlayer.name;
				teamB.faceIcon = otherPlayer.faceIcon;
				teamB.actualStrength = otherPlayer.getLeagueTFActualStrength();
				teamB.totalValue = otherPlayer.getLeagueTotalValue();
				callback(null);
			});
		}
	], function(err) {
		if(!!err){
			logger.debug("water fall fail: error msg: ", err);
			cb(err, competitorName);
			return;
		}
		cb(null, competitorName);
		return;
	});
};

PlayerLeagueService.prototype.notifyLeaguePrepare = function(msg, cb) 
{
	let self = this;
	let playerId 	   = msg.playerId;
	let roomUid 	   = msg.roomUid;
	let competitorUid  = msg.competitorUid;
	let teamA          = msg.teamA;
	let teamB          = msg.teamB;
	if (!playerId  || !competitorUid)
	{
		logger.error("not playerId or competitorUid", playerId, competitorUid);
		cb(Code.FAIL);
		return;
	}

	//不管是否在线都要设置
	this.waitLookerRecord.setLooker(playerId, roomUid);
	this.waitLookerRecord.setLooker(competitorUid, roomUid);
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId); //是否有player数据
	if(!player)
	{
		//logger.info("notifyLeaguePrepare offline", playerId, roomUid);
		cb(Code.OK);
		return;
	}	

	if (!!player && !player.isOnline())
	{
		logger.info("notifyLeaguePrepare offline, but have cache", playerId, roomUid);
		cb(Code.OK);
		return;
	}

	let pushMsg = {};
	pushMsg.playerId= playerId;
	pushMsg.roomUid = roomUid;
	pushMsg.beginTime = msg.beginTime;
    pushMsg.leagueName =msg.leagueName;
    pushMsg.groupId = msg.groupId;
    pushMsg.round = msg.round;
	self.fillPrepareTeamInfo(teamA, teamB, competitorUid, function(err, competitorName){
		if(!!err)
		{
			logger.error("PlayerLeagueService fillPrepareTeamInfo: get otherPlayer failed!", competitorUid, err);
			cb(Code.FAIL);
			return;
		}

		pushMsg.teamA = teamA;
		pushMsg.teamB = teamB;
		pushMsg.competitorName = competitorName;
		self.updateLeaguePrepare(playerId, pushMsg);
		logger.info("notifyLeaguePrepare online _______________________", playerId, pushMsg.leagueName);
		cb(Code.OK);
		return;
	});
};

//通知玩家开始播放录像
PlayerLeagueService.prototype.notifyLeagueStart = function(msg, cb)
{
	let typeId         = msg.typeId;
	let leagueName 	   = msg.leagueName;
	let round  		   = msg.round;

	let uidList = this.waitLookerRecord.getAllUidList();
	let playerService = this.app.get("playerService");
	//logger.info("notifyLeagueStart", this.app.getServerId(), typeId, leagueName, round, uidList.length /*, uidList*/);
	for (let idx in uidList) {
		const playerId = uidList[idx];
		let player = playerService.getPlayer(playerId);
		if(!!player && player.isOnline()) //给在线的玩家发送消息
		{
			let roomUid = this.waitLookerRecord.getRoomUid(playerId)
			let pushMsg = {};
			pushMsg.playerId = playerId;
			pushMsg.roomUid = roomUid;
			this.updateLeagueStart(playerId, pushMsg);
			logger.info("notifyLeagueStart online", playerId, roomUid, typeId, leagueName, round);
		}
	}

	this.waitLookerRecord.clear();
	cb(Code.OK);
	return;
};

PlayerLeagueService.prototype.updateLeaguePrepare = function(playerId, msg) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.error("PlayerLeagueService updateLeaguePrepare playerId is wrong", playerId, msg.playerId);
		return;
	}
	var pushMessageService = this.app.get("pushMessageService");
	pushMessageService.unicast("game.playerHandler.PushLeaguePrepare", msg, player);
};

PlayerLeagueService.prototype.updateLeagueStart = function(playerId, msg) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player){
		logger.error("PlayerLeagueService updateLeagueStart playerId is wrong", playerId, msg.playerId);
		return;
	}
	var pushMessageService = this.app.get("pushMessageService");
	pushMessageService.unicast("game.playerHandler.PushLeagueStart", msg, player);
};

PlayerLeagueService.prototype.getRankConfig = function(rank, level)
{
	var config = null;
	var allConfig = dataApi.allData.data["LeagueReward"];
	if (!allConfig)
	{
		return config;
	}

	for(let idx in allConfig)
	{
		let cfg = allConfig[idx];
		if (!cfg) continue;
		let lv = cfg["Lv"];
		let rankBegin = cfg["RankBegin"];
		let rankEnd = cfg["RankEnd"];

		if (level === lv && rank >= rankBegin && rank <= rankEnd )
		{
			return cfg;
		}
	}

	return null;
};

//内部函数不检查参数
PlayerLeagueService.prototype.getAttchList = function(config)
{
	let index = 0;
	let rewardList = [];
	for(let idx = 1; idx <= 4; idx++)
	{
		let rewardType = config["ItemType"+ idx];
		let rewardId = config["Reward"+ idx];
		let rewardNum = config["Num"+ idx];

		if (!rewardId || rewardId <= 0) continue;
		if (!rewardNum || rewardNum<= 0) continue;
		
		rewardList[index] = {};
		rewardList[index].ItemType = rewardType;
		rewardList[index].ResId = rewardId;
		rewardList[index].Num = rewardNum;
		rewardList[index].Param1 = 0;
		index++;
	}

	return rewardList;
};

PlayerLeagueService.prototype.getLeageName = function(groupId)
{
  let name= "";
  switch(groupId)
  {
    case 1: name = commonEnum.LEAGUE_NAME.AMATEUR; break;
    case 2: name = commonEnum.LEAGUE_NAME.LEAGUE_SECOND; break;
    case 3: name = commonEnum.LEAGUE_NAME.LEAGUE_FIRST; break;
    case 4: name = commonEnum.LEAGUE_NAME.CHAMPIONS; break;
    case 5: name = commonEnum.LEAGUE_NAME.SUPER; break;
  }
  return name;
};

//赛事转播最终奖励
PlayerLeagueService.prototype.finalupData = function(allRankDataList, cb)
{
	let self = this;
	async.eachSeries(allRankDataList,
		function (msg, callback) {
			//logger.error("赛事转播最终奖励！！！！！！！！！！！！！！！！！", allRankDataList.length);
			let playerId   = msg.playerId;
			let playerService = self.app.get("playerService");
			var player = playerService.getPlayer(playerId);
			if(msg.onNext)
			{
				if(!!player) {  //在线
					playerService.updataScheduleData(playerId);
					player.relay.finalupData(msg);//本赛季结束邮件发本赛季未领取奖励
					callback(null);
					return;
				}
				//离线
				playerService.getRalayAndOfflineEventPlayer(msg.playerId, function(otherPlayer)
				{
					if(!otherPlayer)
					{
						logger.error("PlayerLeagueService finalupData: get getRalayAndOfflineEventPlayer failed", msg.playerId);
						return callback(null);
					}
					if(otherPlayer.relay.isJoin !== 1)
						return callback(null);
					//logger.error("2222222222222222222222222222222222", otherPlayer.relay.isJoin);
					let offlineEventList = otherPlayer.offlineEvent.getOfflineEventList();//获取离线事件列表
					let flag = true;
					for(let k in offlineEventList)
					{
						if(offlineEventList[k].id === commonEnum.OFFLINE_EVENT_TYPE.RELAY_FINAL)
						{
							offlineEventList[k].offlineEventList = msg;
							flag = false;
							break;
						}
					}
					if(flag)
					{
						let obj = {};
						obj.id = commonEnum.OFFLINE_EVENT_TYPE.RELAY_FINAL;
						obj.offlineEventList = msg;
						offlineEventList.push(utils.deepCopy(obj));
					}
					//logger.error("999999999999999999999999999999", playerId, offlineEventList);
					//更新离线事件
					playerService.playerDao.updateOfflineEvent(playerId, offlineEventList, function(err) {
						if (!!err) {
							logger.error("updateOfflineEvent failed!", playerId, offlineEventList);
							callback(null);
							return;
						}
						callback(null);
					});
				});
			}
		},
		function (err) {
			logger.error("PlayerLeagueService finalupData fault", err);
			cb(Code.OK);
		});

};

PlayerLeagueService.prototype.sendFinalReward = function(msg, cb) 
{
	let playerId   = msg.playerId;
	let groupId    = msg.groupId;
	let rank       = msg.rank;
	let typeId     = msg.typeId;
	let mailId = 0;
	let param1 = "";
	let level = 0;
	switch (typeId) 
	{
		case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
			mailId = commonEnum.MAIL_TRANSLATE_CONTENT.CommunityReward;
			param1 = commonEnum.LEAGUE_NAME.COMMUNITY;
			level = 1;
			break;

		case commonEnum.LEAGUE_TYPE_ID.PREPARE:
			mailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalReward;
			param1 = commonEnum.LEAGUE_NAME.PREPARE;
			level = 2;
			break;
		case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
		case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
		case commonEnum.LEAGUE_TYPE_ID.SUPER:
			mailId = commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_REWARD;
			param1 = this.getLeageName(groupId);
			level = 8 - typeId;
			break;
		default:
			logger.error("sendMailReward: no case", typeId);
			break;
	}
	
	let config = this.getRankConfig(rank, level);
	if (!config)
	{
		logger.error("sendMailReward: get rank config failed: uid, rank, level", playerId, rank, level);
		cb(Code.OK);
		return;
	}

	if (mailId <= 0)
	{	
		logger.error("sendMailReward: get mailId failed!", typeId);
		cb(Code.OK);
		return;
	}
	let beliefName = "";
	let beliefConfig = dataApi.allData.data["Belief"];
	let beliefId = 1;
	if (typeId === commonEnum.LEAGUE_TYPE_ID.COMMUNITY)
	{
		beliefId = groupId;
	}
	else if(typeId === commonEnum.LEAGUE_TYPE_ID.PREPARE)
	{
		beliefId = groupId - 5;
	}
	if(beliefId <= 0)
		beliefId = 1;
	for(let i in beliefConfig)
	{
		if(beliefId === beliefConfig[i].ID)
		{
			beliefName = beliefConfig[i].Text1;
			break;
		}
	}

	this.app.rpc.slog.slogRemote.recordStatisLog({frontendId: this.app.getServerId()}, {playerId: playerId,
		behaviorType: commonEnum.STATIS_LOG_TYPE.PVP_LEAGUE_RANK, valueArray: [ rank ], moreInfo: {}}, function (code) {});
	
	let rewardList = this.getAttchList(config);
	let specialAttachInfo = {roomUid: ""};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	//playerService.updataScheduleData(playerId);//赛季转播更新赛程
	if(!!player) {  //在线
		if (typeId === commonEnum.LEAGUE_TYPE_ID.COMMUNITY || typeId === commonEnum.LEAGUE_TYPE_ID.PREPARE)
			player.email.sendMailReward("Sys", mailId, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, beliefName, param1, rank, ""); //记得写存储
		else
			player.email.sendMailReward("Sys", mailId, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, param1, rank, "", "");

		// playerService.updataScheduleData(msg.playerId);
		player.relay.finalTask(msg);//本赛季全部轮次完成，赛事转播任务结算
		player.saveEMail();
		cb(Code.OK);
		return;
	}
	
	var offlineMail = new OfflineMail(playerId);
	let newMailList;
	if (typeId === commonEnum.LEAGUE_TYPE_ID.COMMUNITY || typeId === commonEnum.LEAGUE_TYPE_ID.PREPARE)
		newMailList = offlineMail.getNewMailRewardList("Sys", mailId, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, beliefName, param1, rank, "");
	else
		newMailList = offlineMail.getNewMailRewardList("Sys", mailId, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, param1, rank, "", "");
	let self = this;
	async.waterfall([
		function (callback) {
			self.addOfflineMailList(playerId, newMailList, function(err){
				if (!!err)
				{
					logger.error("processResultMailCallBack failed!", err);
					cb(Code.FAIL);
					return;
				}

				//logger.info("sendFinalReward: addOfflineMailList", playerId);
				//cb(Code.OK);
				callback(null);
			});
		},
		function (callback) {
			//离线
			playerService.getRalayAndOfflineEventPlayer(msg.playerId, function(otherPlayer)
			{
				if(!otherPlayer)
				{
					logger.error("PlayerLeagueService finalupData: get getRalayAndOfflineEventPlayer failed", msg.playerId);
					return cb(Code.FAIL);
				}
				if(otherPlayer.relay.isJoin !== 1)// || Date.now() > otherPlayer.relay.buyRelayTime * 7 * 24 * 60 * 60 * 1000)
					return cb(Code.OK);
				let offlineEventList = otherPlayer.offlineEvent.getOfflineEventList();//获取离线事件列表
				let flag = true;
				for(let k in offlineEventList)
				{
					if(offlineEventList[k].id === commonEnum.OFFLINE_EVENT_TYPE.RELAY_FULFIL)
					{
						offlineEventList[k].offlineEventList = msg;
						flag = false;
					}
				}
				if(flag)
				{
					let obj = {};
					obj.id = commonEnum.OFFLINE_EVENT_TYPE.RELAY_FULFIL;
					obj.offlineEventList = msg;
					offlineEventList.push(utils.deepCopy(obj));
				}
				//logger.error("22221任务完成离线事件22222", playerId, offlineEventList);
				//更新离线事件
				playerService.playerDao.updateOfflineEvent(playerId, offlineEventList, function(err) {
					if (!!err) {
						logger.error("updateOfflineEvent failed!", playerId, offlineEventList);
						cb(Code.OK);
						return;
					}
					cb(Code.OK);
					return;
				});
			});
		}
	], function (err) {
		logger.error("sendFinalReward failed!", playerId);
		cb(Code.OK);
	})
};

PlayerLeagueService.prototype.getEnemyPlayerName = function(enemyUid, cb)
{
	let self = this;
	let playerService = this.app.get("playerService");
	//logger.info("getEnemyPlayerName: ", enemyUid);
	logger.debug("getEnemyPlayerName - getOtherPlayer now:", enemyUid);
	let findDbName = new Array(commonEnum.DB_NAME.player);
	playerService.getOtherPlayer(enemyUid, findDbName, function(otherPlayer){
		if (!otherPlayer)
		{
			logger.error("sendLeagueResultEmail: getOtherPlayer playerId failed: " + enemyUid);
			cb("");
			return;
		}
		let enemyName = otherPlayer.name;
		if(!enemyName) enemyName = enemyUid;
		cb(enemyName);
	});
};

PlayerLeagueService.prototype.processResultMailCallBack = function(msg, mailId, cb)
{
	let uid   	   = msg.playerId;
    let selfScore  = msg.selfScore;
    let enemyScore = msg.enemyScore;
    let enemyUid   = msg.enemyUid;
    let enemyName  = msg.enemyName;
    let isNull 	   = msg.isNull;
	let roomUid    = msg.roomUid;
	let typeId     = msg.typeId;
	let leagueName = msg.leagueName;
	let round      = msg.round;

	let rewardList = [];
	let specialAttachInfo = {};
	specialAttachInfo.roomUid = roomUid;
	let scoreStr = selfScore + ":" + enemyScore;
	let mailType = commonEnum.MailType.SYSMAIL;
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(uid);
	let self = this;
	playerService.updataScheduleData(msg.playerId);//赛季转播更新赛程
	if(!!player) { //在线
		if (1 === isNull)
		{
			mailId = commonEnum.MAIL_TRANSLATE_CONTENT.Result_Is_Null;
			player.email.sendMailReward("Sys", mailId, mailType, rewardList, specialAttachInfo, leagueName, round, "", "");
			player.saveEMail();
			cb(null);
			return;
		}
	
		//查询对手名称
		this.getEnemyPlayerName(enemyUid, function(name){
			enemyName = name;
			//专业联赛平局
			if (typeId <= commonEnum.LEAGUE_TYPE_ID.AMATEUR && selfScore === enemyScore)
			{
				mailId = commonEnum.MAIL_TRANSLATE_CONTENT.RESULT_DRAW;
			}

			mailType = commonEnum.MailType.PLAY_RECORD;
			player.email.sendMailReward("Sys", mailId, mailType, rewardList, specialAttachInfo, enemyName, scoreStr, "", "");
			player.saveEMail();
			cb(null);
			return;
		});
	}else
	{
		//playerService.updataScheduleData(msg.playerId);//赛季转播更新赛程
		var offlineMail = new OfflineMail(uid);
		if (1 === isNull)
		{
			mailId = commonEnum.MAIL_TRANSLATE_CONTENT.Result_Is_Null;
			//离线
			let newMailList = offlineMail.getNewMailRewardList("Sys", mailId, mailType, rewardList, specialAttachInfo, leagueName, round, "", "");
			self.addOfflineMailList(uid, newMailList, function(err){
				if (!!err) 
				{
					logger.error("processResultMailCallBack failed!", err);
					cb(err);
					return;
				}

				//logger.info("processResultMailCallBack: addOfflineMailList", uid);
				cb(null);
				return;
			});
		}
		else
		{
			this.getEnemyPlayerName(enemyUid, function(name){
				enemyName = name;
				//专业联赛平局
				if (typeId <= commonEnum.LEAGUE_TYPE_ID.AMATEUR && selfScore === enemyScore)
				{
					mailId = commonEnum.MAIL_TRANSLATE_CONTENT.RESULT_DRAW;
				}

				//离线
				mailType = commonEnum.MailType.PLAY_RECORD;
				let newMailList = offlineMail.getNewMailRewardList("Sys", mailId, mailType, rewardList, specialAttachInfo, enemyName, scoreStr, "", "");
				self.addOfflineMailList(uid, newMailList, function(err){
					if (!!err) 
					{
						logger.error("processResultMailCallBack failed!", err);
						cb(err);
						return;
					}
					
					//logger.info("processResultMailCallBack: addOfflineMailList", uid);
					cb(null);
					return;
				});
			});
		}
	}
};

PlayerLeagueService.prototype.sendWinAndLose = function(msg, cb) 
{
	let typeId     = msg.typeId;
	let notifyType = msg.notifyType;
	let proMailId = 0;
	let eliMailId = 0;
	switch (typeId) 
	{
		case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
			proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.CommunityNotifyWin;
			eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.CommunityNotifyLost;
			break;

		// case commonEnum.LEAGUE_TYPE_ID.NORMAL:
		// 	proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyWin;
		// 	eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyLost;
		// 	break;
		//
		// case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
		// 	proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.KnockOutNotifyWin;
		// 	eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.KnockOutNotifyLost;
		// 	break;
		case commonEnum.LEAGUE_TYPE_ID.PREPARE:
			proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyWin;
			eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyLost;
			break;
		case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
		case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
		case commonEnum.LEAGUE_TYPE_ID.SUPER:
			proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionNotifyWin;
			eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionNotifyLost;
			break;

		default:
			logger.error("no case", typeId);
			break;
	}

	if (notifyType === commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION)
		mailId = proMailId;
	else
		mailId = eliMailId;
	
	if (mailId < 0)
	{
		logger.error("proMailId or eliMailId error! ", mailId);
		cb(Code.FAIL);
		return;
	}

	this.processResultMailCallBack(msg, mailId, function(err){
		if (!!err) 
        {
            logger.error("error", err);
            cb(Code.FAIL)
            return;
		}

		cb(Code.OK);
		return;
	});
};

//晋级玩家通知
PlayerLeagueService.prototype.notifyFinalWin = function(msg, cb) 
{
	let playerId   = msg.playerId;
	let name       = msg.name;
	let typeId     = msg.typeId;
	let notifyType = msg.notifyType;
	let proMailId = 0;
	let eliMailId = 0;
	switch (typeId) 
	{
		case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
			proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.CommunityPromotion;
			break;
		// case commonEnum.LEAGUE_TYPE_ID.NORMAL:
		// 	proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.NormalPromotion;
		// 	break;
		case commonEnum.LEAGUE_TYPE_ID.PREPARE:
		case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
		case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
		case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
		case commonEnum.LEAGUE_TYPE_ID.SUPER:
			proMailId = commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionPromotion;
			eliMailId = commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionFailed;
			break;

		default:
			logger.error("notifyFinalWin: no case", typeId);
			break;
	}

	if (notifyType === commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION)
		mailId = proMailId;
	else
		mailId = eliMailId;
	
	if (mailId < 0)
	{
		logger.error("notifyFinalWin: proMailId or eliMailId error! ", mailId);
		cb(Code.FAIL);
		return;
	}

	let rewardList = [];
	let specialAttachInfo = {roomUid: ""};
	let mailType = commonEnum.MailType.SYSMAIL;
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!!player) 
	{ //在线
		logger.info("notifyFinalWin online", playerId);
		player.email.sendMailReward("Sys", mailId, mailType, rewardList, specialAttachInfo, name, "", "", "");
		player.saveEMail();
		cb(Code.OK);
		return;
	}

	//离线
	var offlineMail = new OfflineMail(playerId);
	let newMailList = offlineMail.getNewMailRewardList("Sys", mailId, mailType, rewardList, specialAttachInfo, name, "", "", "");
	this.addOfflineMailList(playerId, newMailList, function(err){
		if (!!err) 
		{
			logger.error("notifyFinalWin failed!", err);
			cb(Code.FAIL);
			return;
		}
		
		//logger.info("addOffline: ", playerId);
		cb(Code.OK);
	});
};

PlayerLeagueService.prototype.notifyEnrollResult = function(msg, cb) 
{
	let playerId = msg.playerId;
	let success = msg.success;
	let profession = msg.profession;
	let mailId = 0;
	let message = "";
	if (success === commonEnum.ENROLL_NOTIFY.SUCCESS)
	{
		mailId = commonEnum.MAIL_TRANSLATE_CONTENT.ENROLL_SUCCESS;
		message = "success";
	}
	else
	{
		mailId = commonEnum.MAIL_TRANSLATE_CONTENT.ENROLL_FAILED;
		message = "failed;";
	}

	if (1 === profession)
	{
		mailId = commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL;
	}

	let rewardList = [];
	let mailType = commonEnum.MailType.SYSMAIL;
	let specialAttachInfo = { roomUid: ""};
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!!player) { //在线
		logger.info("notifyEnrollResult: playerId", playerId, message);
		player.email.sendMailReward("Sys", mailId, mailType, rewardList, specialAttachInfo, "", "", "", "");
		player.saveEMail();
		cb(Code.OK);
		return;
	}

	//离线
	var offlineMail = new OfflineMail(playerId);
	let newMailList = offlineMail.getNewMailRewardList("Sys", mailId, mailType, rewardList, specialAttachInfo, "", "", "", "");
	this.addOfflineMailList(playerId, newMailList, function(err){
		if (!!err) 
		{
			logger.error("notifyEnrollResult failed!", err);
			cb(Code.FAIL);
			return;
		}
		
		//logger.info("notifyFinalWin: addOfflineMailList", playerId, message);
		cb(Code.OK);
	});
};

PlayerLeagueService.prototype.publicNotifyPlayer = function(msg, cb) 
{
	let publicMailType = msg.publicMailType;
	let notifyUidList  = msg.uidList;
	let mailInfo = {};
	//let mailId = 0;
	if (publicMailType === commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START)
	{
		mailInfo.mailId = commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START;
		mailInfo.time = msg.time;
	}else
	{
		mailInfo.mailId = commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_DIRECT_START;
		mailInfo.time = msg.time;
	}
	logger.info("publicNotifyPlayer: ________", this.app.getServerId());
	let self = this;
	let playerService = this.app.get("playerService");
	playerService.getAllPlayerUidList(function(code, uidList) {
		if (code !== Code.OK)
		{
			logger.error("publicNotifyPlayer.getAllPlayerUidList failed!");
			cb(Code.FAIL);
			return;
		}
		async.eachSeries(uidList, function(playerId, callback) {
			if (utils.hasUidInList(notifyUidList, playerId))//玩家的uid包含在里面,那就不需要再发送了
			{
				logger.info("publicNotifyPlayer: promotion player ", playerId);
				callback(null);
				return;
			}
			self.publicNotifyPlayerCallback(playerId, mailInfo, function(code) {
				if (code !== Code.OK)
				{
					logger.info("publicNotifyPlayer.publicNotifyPlayerCallback failed!", playerId, mailInfo.mailId);
					callback(code);
					return;
				}

				callback(null);
			});
		}, function(err) {
			if (!!err) 
			{
				logger.error("publicNotifyPlayer failed!", err);
				cb(Code.FAIL);
				return;
			}
			cb(Code.OK);
		});
	})
};

PlayerLeagueService.prototype.publicNotifyPlayerCallback = function(playerId, mailInfo, cb)
{
	var config = dataApi.allData.data["LeagueSwitch"][1];
	let openTime = new Date(config.OpenTime).getTime(); //转化为timestamp时间戳
	let next_game_init_time = TimeUtils.beginningOfTodayByTime(openTime);
	let time = mailInfo.time + next_game_init_time;
	let mailType = commonEnum.MailType.SYSMAIL;
	let specialAttachInfo = { roomUid: ""};
	let rewardList = [];
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	let param1 = "";
	//新需求添加时间
	//1. 报名开始时间
	//2. 第一场比赛时间
	if(mailInfo.mailId === commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START) {
		param1 = TimeUtils.timeFormat(time);
	}
	if(mailInfo.mailId === commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_DIRECT_START) {
		param1 = TimeUtils.timeFormat(time);
	}
	// if(mailInfo.mailId === commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START
	// 	|| mailInfo.mailId === commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_DIRECT_START) {
	// 	param1 = TimeUtils.timeFormat(time);
	// }

	if(!!player) { //在线
		logger.info("publicNotifyPlayerCallback: playerId", playerId, mailInfo.mailId);
		player.email.sendMailReward("Sys", mailInfo.mailId, mailType, rewardList, specialAttachInfo, param1, "", "", "");
		player.saveEMail();
		cb(Code.OK);
		return;
	}

	//离线
	var offlineMail = new OfflineMail(playerId);
	let newMailList = offlineMail.getNewMailRewardList("Sys", mailInfo.mailId, mailType, rewardList, specialAttachInfo, param1, "", "", "");
	this.addOfflineMailList(playerId, newMailList, function(err){
		if (!!err) 
		{
			logger.error("publicNotifyPlayerCallback failed!", err);
			cb(Code.FAIL);
			return;
		}
		//logger.info("publicNotifyPlayerCallback: addOfflineMailList", playerId, message);
		cb(Code.OK);
	});
};

PlayerLeagueService.prototype.addOfflineMailList = function(playerId, newMailList, cb)
{
	let self = this;
	let playerService = this.app.get("playerService");
	async.eachSeries(newMailList, function(obj, callback) {
		playerService.playerDao.insertOneMail(playerId, obj, function(err){
			if (!!err)
			{	
				callback(err);
				return;
			}

			callback(null);
		});
	}, function(err) {
		if (!!err) 
		{
			logger.error("insertOneMail failed!", err, playerId);
			cb(err);
			return;
		}
		logger.error("离线邮件发送成功--------------");
		cb(null);
	});
};