/**
 * Created by sea on 2019/7/04.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let commonEnum = require('../../../../shared/enum');


module.exports.create = function(app, dbclient) {
	return new GroundService(app, dbclient);
};

let GroundService = function(app, dbclient){
	this.app = app;
};

util.inherits(GroundService, EventEmitter);


GroundService.prototype.getGroundAllInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId)
	if(!player){
		logger.debug('GroundService getGroundAllInfo player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	logger.debug("----------- getGroundAllInfo ------------------");
	//自己的数据
	let ret = {}
	if (!uid || uid === playerId ){
		logger.debug('GroundService getGroundAllInfo same', uid);
		ret = player.footballGround.getGroundAllInfo(uid);
		player.saveBallGround();

		let playerGuideService = this.app.get("playerGuideService");
		playerGuideService.ntfNewerGuide(playerId);

		playerService.notifyUpdateGlobalAccount(uid, true);   //更新排行榜
		cb(Code.OK, ret, uid, "", 0);
		return;
	}

	//查看别人的球场数据
	logger.debug("getGroundAllInfo - getOtherPlayer now:", uid);
	let findDbName = new Array(commonEnum.DB_NAME.player, commonEnum.DB_NAME.footballGround)
	playerService.getOtherPlayer(uid, findDbName, function(otherPlayer){
		if(!otherPlayer){
			logger.error("GroundService getGroundAllInfo: get otherPlayer failed", uid);
			cb(Code.FAIL)
			return;
		}
		ret = otherPlayer.footballGround.getOtherGroundAllInfo(uid);
		let name = otherPlayer.getName();
		let faceIcon = otherPlayer.getFaceIcon();
		cb(Code.OK, ret, uid, name, faceIcon);
		return;
	});
};

GroundService.prototype.getGroundInfoByType = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getGroundInfoByType player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	let type = msg.type;
	if(!uid || uid === ""){
		uid = player.playerId
	}
	logger.debug("----------- getGroundInfoByType ------------------");
	let ret = player.footballGround.getGroundInfoByType(uid, type);
	if(ret.code !== Code.OK){
		cb(ret.code,{}, uid, type);
		return;
	}
	let ground = {}
	let oneGround = {}
	switch(type){
		case 1:
			ground = player.footballGround.adminGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		break;
		case 2:
			ground = player.footballGround.mainGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		break;
		case 3:
			ground = player.footballGround.trainGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		break;
		case 4:
			ground = player.footballGround.transferGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		break;
		case 5:
			ground = player.footballGround.hospitalGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		break;
		case 6:
			ground = player.footballGround.notableGround.get(uid);
			oneGround = player.footballGround.makeClientGroundInfo(ground);
		default:
			break;
	}
	player.saveBallGround();
	//主球场升级同步数据到cache
	if(type === 2 && ret.isUpgrade) {
		let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.UPGRADE_MAIN_GROUND;
		let syncData = {uid: player.playerId, teamIndex: 0, maxResId: player.footballGround.groundMatch.fieldList[0].resId,
			ballFan: player.footballGround.ballFan, mainGroundLevel: oneGround.level};
		this.app.get("groundMatchService").syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {});
		/* june
		this.app.get("groundMatchService").syncGroundToDatanode(playerId, player.footballGround.toJSONforDB(), this.app.getServerId(),
			function (code) {});
		*/
	}
	cb(Code.OK, oneGround, uid, type);
};

GroundService.prototype.getGroundRewardByType = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getGroundRewardByType player not exist !');
		cb({code:Code.FAIL});
		return;
	}

	let uid = msg.uid;
	let type = msg.type;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- getGroundRewardByType ------------------");
	let ret = player.footballGround.getGroundRewardByType(uid, type);
	if(ret.code !== Code.OK){
		cb(ret.code, uid, type, ret.rewardNum);
		return;
	}
	player.saveBallGround();
	player.saveBag();
	player.saveItem();
	player.updateBag();
	player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: player.cash}]);
	cb(ret.code, uid, type, ret.rewardNum);
	
};

GroundService.prototype.upgradeGroundByType = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService upgradeGroundByType player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	let type = msg.type;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- upgradeGroundByType ------------------");
	let ret = player.footballGround.upgradeGroundByType(uid, type);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.upTime, uid, type);
		return;
	}
	let groundLevel = player.footballGround.getLevelByType(uid, type);
	//球场等级统计
	let self = this;
	self.app.rpc.slog.slogRemote.recordStatisLog({frontendId: self.app.getServerId()}, {playerId: player.playerId,
		behaviorType: commonEnum.STATIS_LOG_TYPE.GROUND_LEVEL_UP, valueArray: [type, groundLevel], moreInfo: {}}, (code) => {});
	player.saveBallGround();
	cb(Code.OK, ret.upTime, uid, type);
	player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: player.cash}]);
};

GroundService.prototype.heroTrainInGround = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService heroTrainInGround player not exist !');
		cb(Code.FAIL, 0, "", 0, 0);
		return;
	}

	let playerUid = msg.playerUid;
	let heroUid = msg.heroUid;
	let index = msg.index;
	let type = msg.type;
	let isLock = msg.isLock;
	if(!playerUid || playerUid === ""){
		playerUid = player.playerId;
	}
	logger.debug("----------- heroTrainInGround ------------------");
	let ret = player.footballGround.heroTrainInGround(playerUid, heroUid, index, type, isLock);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.beginTime, heroUid, index, ret.state, ret.isLockTrain);
		return;
	}
	player.saveBallGround();
	player.saveHeros();
	player.upAddHero(heroUid);
	player.updateBag();
	cb(ret.code, ret.beginTime, heroUid, index, ret.state, ret.isLockTrain);
};

GroundService.prototype.getHeroTrainReward = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getHeroTrainReward player not exist !');
		cb(Code.FAIL, 0, 0, {}, 0);
		return;
	}

	let uid = msg.uid;
	let index = msg.index;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- getHeroTrainReward ------------------");
	let ret = player.footballGround.getHeroTrainReward(uid, index);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.rand, ret.type, ret.hero, index);
		return;
	}
	player.updateRetirementHero();
	player.saveBallGround();
	player.saveHeros();
	player.saveTeamFormation();
	player.updateHeros();
	player.updateTeamFormations();
	cb(ret.code, ret.rand, ret.type, ret.hero, index);
};

GroundService.prototype.getHeroTrainInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getHeroTrainInfo player not exist !');
		cb(Code.FAIL, [], "");
		return;
	}

	let uid = msg.uid;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- getHeroTrainInfo ------------------");
	let ret = player.footballGround.getHeroTrainInfo(uid);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.trainPos, uid);
		return;
	}
	player.saveBallGround();
	cb(ret.code, ret.trainPos, uid);
};

GroundService.prototype.unlockTrainPos = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService unlockTrainPos player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	let index = msg.index;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- unlockTrainPos ------------------");
	let ret = player.footballGround.unlockTrainPos(uid, index);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.state, uid, index);
		return;
	}
	player.saveBallGround();
	cb(ret.code, ret.state, uid, index);
};

GroundService.prototype.getTransferTrainerInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getTransferTrainerInfo player not exist !');
		cb(Code.FAIL, [], 0, "");
		return;
	}
	let teamUid = msg.uid;
	let ret = player.teamFormations.getCurrTeamFormationTrainer(teamUid);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.trainerList, ret.allActual, teamUid);
		return;
	}
	player.saveTeamFormation();
	player.saveTrainer();
	cb(Code.OK, ret.trainerList, ret.allActual, teamUid);
};

GroundService.prototype.addTrainInTransfer = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService addTrainInTransfer player not exist !');
		cb(Code.FAIL, [], 0, "");
		return;
	}

	let uid = msg.uid;
	let index = msg.index;
	let teamUid = msg.teamUid;
	logger.debug("----------- addTrainInTransfer ------------------");
	let ret = player.teamFormations.addTrainInTransfer(uid, index, teamUid);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.trainerList, ret.allActual, teamUid);
		return;
	}


	player.saveBallGround();
	player.saveHeros();
	player.saveTeamFormation();
	player.saveTrainer();
	player.updateTeamFormations();
	player.updateHeros();
	cb(Code.OK, ret.trainerList, ret.allActual, teamUid);
};

/***************************************************************教练******************************************************************/
GroundService.prototype.getAllTrainer = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getAllTrainer player not exist !');
		cb(Code.FAIL, []);
		return;
	}
	logger.debug("----------- getAllTrainer ------------------");
	let trainerList = player.trainer.getAllTrainer();
	cb(Code.OK, trainerList);
};

GroundService.prototype.upgradeTrainerStar = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService upgradeTrainerStar player not exist !');
		cb(Code.FAIL);
		return;
	}
	let uid = msg.uid;
	logger.debug("----------- upgradeTrainerStar ------------------");
	let ret = player.trainer.upgradeTrainerStar(uid);
	if(ret.code !== Code.OK) {
		return cb(ret.code);

	}
	player.saveTrainer();
	// player.updateOneTrainer(uid);
	player.saveItem();
	player.saveBag();
	player.updateBag();
	player.saveHeros();
	player.saveTeamFormation();
	player.updateTeamFormations();
	player.updateHeros();
	cb(Code.OK, player.trainer.makeClientTrain(uid));
};

GroundService.prototype.upgradeTrainerLevel = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService upgradeTrainerLevel player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	logger.debug("----------- upgradeTrainerLevel ------------------");
	let ret = player.trainer.upgradeTrainerLevel(uid);
	if(ret.code !== Code.OK) {
		return cb(ret.code);
	}
	player.saveTrainer();
	// player.updateOneTrainer(uid);
	player.saveHeros();
	player.saveTeamFormation();
	player.updateTeamFormations();
	player.upPlayerInfo([{type: commonEnum.PLAY_INFO.honor, value: player.honor}]);
	cb(Code.OK, player.trainer.makeClientTrain(uid));
};

/***************************************************************教练******************************************************************/

GroundService.prototype.unlockTransferPos = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService unlockTransferPos player not exist !');
		cb(Code.FAIL);
		return;
	}

	let uid = msg.uid;
	let index = msg.index;
	if(!uid || uid === ""){
		uid = player.playerId;
	}
	logger.debug("----------- unlockTransferPos ------------------");
	let ret = player.footballGround.unlockTransferPos(uid, index);
	if(ret.code !== Code.OK){
		cb(ret, index);
		return;
	}
	player.saveBallGround();
	cb(ret, index);
};

GroundService.prototype.compoundTrainer = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService compoundTrainer player not exist !');
		cb(Code.FAIL);
		return;
	}

	let resId = msg.resId;
	logger.debug("----------- compoundTrainer ------------------");
	let ret = player.footballGround.compoundTrainer(resId);
	if(ret.code !== Code.OK){
		cb(ret, []);
		return;
	}
	let trainer = player.trainer.makeClientTrain(ret.uid);
	player.saveBallGround();
	player.saveTrainer();
	player.saveItem();
	player.saveBag();
	player.updateBag();
	cb(ret, trainer);
};

GroundService.prototype.getBallHandbook = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getBallHandbook player not exist !');
		cb(Code.FAIL, 0, [], 0, 0, 0);
		return;
	}

	logger.debug("----------- getBallHandbook ------------------");
	let ret = player.footballGround.getBallHandbook();
	player.saveBallGround();
	cb(Code.OK, ret);
};

GroundService.prototype.activeBallHandbook = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService activeBallHandbook player not exist !');
		cb(Code.FAIL);
		return;
	}

	let resId = msg.resId;
	logger.debug("----------- activeBallHandbook ------------------");
	let ret = player.footballGround.activeBallHandbook(resId);
	if(ret.code !== Code.OK){
		cb(ret.code, ret, resId);
		return;
	}
	player.saveBallGround();
	player.saveItem();
	player.saveBag();
	player.saveHeros();
	player.saveTeamFormation();
	player.updateBag();
	player.updateTeamFormations();
	cb(Code.OK, ret, resId);
};

GroundService.prototype.getRetirementList = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getRetirementList player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- getRetirementList ------------------");
	let ret = player.footballGround.getRetirementList();
	player.saveBallGround();
	cb(Code.OK, ret);
};

GroundService.prototype.getNotablePosInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getNotablePosInfo player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- getNotablePosInfo ------------------");
	let ret = player.footballGround.getNotablePosInfo();
	player.saveBallGround();
	cb(Code.OK, ret);
};

GroundService.prototype.inputNotablePos = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService inputNotablePos player not exist !');
		cb(Code.FAIL, 0, [], []);
		return;
	}

	let uid = msg.uid;
	logger.debug("----------- inputNotablePos ------------------");
	let ret = player.footballGround.inputNotablePos(uid);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.notablePrestige, ret.retirementList, ret.notablePos);
		return;
	}

	let playerGuideService = this.app.get("playerGuideService");
	playerGuideService.ntfNewerGuide(playerId);
	
	player.saveBallGround();
	cb(Code.OK, ret.notablePrestige, ret.retirementList, ret.notablePos);
};

GroundService.prototype.addHeroToRetirementList = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService addHeroToRetirementList player not exist !');
		cb(Code.FAIL, []);
		return;
	}

	let heroList = msg.heroList;
	logger.debug("----------- addHeroToRetirementList ------------------");
	let ret = player.footballGround.addHeroToRetirementList(heroList);
	if(ret.code !== Code.OK){
		cb(ret.code, ret.retirementList);
		return;
	}

	player.saveBallGround();
	player.updateHeros();
	cb(Code.OK, ret.retirementList);
};


GroundService.prototype.getHospitalPosInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getHospitalPosInfo player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- getHospitalPosInfo ------------------");
	let ret = player.footballGround.getHospitalPosInfo();
	player.saveBallGround();
	cb(Code.OK, ret.hospitalPos);
};

GroundService.prototype.inputHospitalPos = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService inputHospitalPos player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- inputHospitalPos ------------------");

	let uid = msg.uid;
	let index = msg.index;
	let ret = player.footballGround.inputHospitalPos(uid, index);
	if(ret.code !== Code.OK) {
		cb(ret.code);
		return;
	}
	player.saveBallGround();
	player.updateHospitalPosInfo();
	player.upAddHero(uid);
	cb(Code.OK);
};

GroundService.prototype.getHospitalPosHero = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getHospitalPosHero player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- getHospitalPosHero ------------------");
	let index = msg.index;
	let ret = player.footballGround.getHospitalPosHero(index);
	if(ret.code !== Code.OK) {
		cb(ret.code);
		return;
	}

	player.updateRetirementHero();
	player.saveBallGround();
	player.saveHeros();
	player.saveTeamFormation();
	player.updateHeros();
	player.updateTeamFormations();
	player.updateHospitalPosInfo();
	cb(Code.OK);
};

GroundService.prototype.accelerationHospitalPosTime = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService accelerationHospitalPosTime player not exist !');
		cb(Code.FAIL);
		return;
	}

	logger.debug("----------- accelerationHospitalPosTime ------------------");
	let index = msg.index;
	let resId = msg.resId;
	let num = msg.num;
	let ret = player.footballGround.accelerationHospitalPosTime(index, resId, num);
	if(ret.code !== Code.OK) {
		cb(ret.code);
		return;
	}
	player.saveBallGround();
	player.saveBag();
	player.saveItem();
	player.updateBag();
	player.updateHospitalPosInfo();
	cb(Code.OK);
};

GroundService.prototype.getBallFansReward = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService getBallFansReward player not exist !');
		cb(Code.FAIL, 0, 0);
		return;
	}

	logger.debug("----------- getBallFansReward ------------------");
	let ret = player.footballGround.getBallFansReward();
	if(ret.code !== Code.OK) {
		cb(ret.code, ret.num, ret.isGetBallFansRewrd);
		return;
	}
	player.save();
	player.saveBallGround();
	player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: player.cash}]);
	cb(ret.code, ret.num, ret.isGetBallFansRewrd);
};

GroundService.prototype.seckillGroundTime = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.debug('GroundService seckillGroundTime player not exist !');
		cb(Code.FAIL, {});
		return;
	}

	let type = msg.type;
	logger.debug("----------- seckillGroundTime ------------------");
	let ret = player.footballGround.seckillGroundTime(type);
	if(ret.code !== Code.OK) {
		cb(ret.code, {});
		return;
	}
	let ground = player.footballGround.makeClientGroundInfoByType(type);
	if(type === 2) {
		//同步升级数据到cache
		logger.debug("sync to datanode player cache");
		let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.UPGRADE_MAIN_GROUND;
		let syncData = {uid: player.playerId, teamIndex: 0, maxResId: player.footballGround.groundMatch.fieldList[0].resId,
			ballFan: player.footballGround.ballFan, mainGroundLevel: ground.level};
		this.app.get("groundMatchService").syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {});
		/* june
		this.app.get("groundMatchService").syncGroundToDatanode(playerId, player.footballGround.toJSONforDB(), this.app.getServerId(),
			function (code) {});
			*/
	}
	player.save();
	player.saveBallGround();
	player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: player.gold}]);
	cb(ret.code, ground);
};