/**
 * Idea and Persist
 * Created by <PERSON> on 2019/7/15.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) 
{
	return new PlayerGuideService(app, dbclient);
};

var PlayerGuideService = function(app, dbclient){
	EventEmitter.call(this);
	this.app = app;
};

util.inherits(PlayerGuideService, EventEmitter);

//获取新手引导
PlayerGuideService.prototype.updateNewerGuide = function(playerId, msg, cb)
{
	var itemList = [];
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerGuideService.updateNewerGuide: player not exist !', playerId);
		cb(Code.FAIL, itemList);
		return;
	}

	let id = msg.id;
	if (!id)
	{
		logger.error('PlayerGuideService.updateNewerGuide: id not exist !', playerId, msg);
		cb(Code.FAIL, itemList);
		return;	
	}

	let retCode = player.setFinishGuide(id);
	if (retCode.code !== Code.OK)
	{
		logger.error("PlayerGuideService.updateNewerGuide: setFinishGuide failed!", retCode.code);
		cb(retCode.code, itemList);
		return;
	}

	let itemUidList = retCode.itemUidList;
	for (let i in itemUidList) 
	{
		let item = 
		{
			resId: itemUidList[i].resId,
			count: itemUidList[i].num,
		}
		itemList.push(item);
	}

	if (itemList.length > 0)
	{
		player.saveItem();
		player.saveBag();
		player.updateBag();
	}

	this.ntfNewerGuide(playerId);
	player.saveNewerGuide();
	player.recordSlog(playerId, commonEnum.STATIS_LOG_TYPE.GUIDE_STEP, [ id], {}); 
	cb(Code.OK, itemList);
	return;
};

PlayerGuideService.prototype.ntfNewerGuide = function(playerId)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerGuideService.ntfNewerGuide: player not exist !', playerId);
		return;
	}
	let guideList = player.newerGuide.getGuideList();
	let finishEveryDayList = player.newerGuide.getFinishEveryDayList();
	this.app.get("pushMessageService").unicast("game.playerHandler.ntfNewerGuide", {guideList: guideList, finishEveryDayList: finishEveryDayList}, player);
};

PlayerGuideService.prototype.procGuideResume = function(playerId)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerGuideService.procGuideResume: player not exist !', playerId);
		return;
	}
	
	let retCode = player.newerGuide.procGuideResume();
	if (retCode.itemUidList.length > 0)
	{
		player.saveItem();
		player.saveBag();
		player.updateBag();
	}

	var heroUidList = retCode.heroUidList;
	for (let idx in heroUidList) {
		let heroUid = heroUidList[idx];
		player.upAddHero(heroUid);
		logger.info('PlayerGuideService. procGuideResume: heroUid!', heroUid);
	}

	if (heroUidList.length > 0) 
	{
		player.saveHeros();
	}
};
