/**
 * Created by sea on 2019/12/11.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');

var Player = require('../entities/player');
var debugConfig = require('../../../config/debugConfig');
var fs = require('fs');

module.exports.create = function(app, dbclient) {
    return new groundMatchService(app, dbclient);
};

var groundMatchService = function (app, dbclient)
{
    this.app = app;
    this.db = dbclient;
    if(debugConfig.isTraceGroundMatch) {
        let filePath = __dirname + '/../../../logs/june/traceDebug.log';
        this.fd = fs.openSync(filePath, 'a');
    }
};

util.inherits(groundMatchService, EventEmitter);

groundMatchService.prototype.traceLogForDebugger = function(msg, traceObj) {
    if(debugConfig.isTraceGroundMatch) {
        let writeMsg = msg + ': ' + JSON.stringify(traceObj) + '\n';
        logger.debug("traceLogForDebugger: ", writeMsg);
        fs.writeSync(this.fd, writeMsg);
    }
};

//1. 获得我的训练场数据信息
groundMatchService.prototype.getMyGroundFieldInfo = function(playerId, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.getMyGroundFieldInfo: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }

    let fieldList = [];
    let now = TimeUtils.now();
    let list = [0,1,2];
    let self = this;
    async.eachSeries(list, function (i, callback) {
        if(player.footballGround.groundMatch.fieldList[i].resId === 0) {
            return callback();
        }
        let fieldInfo = {};
        fieldInfo.resId = player.footballGround.groundMatch.fieldList[i].resId;
        let protectInfo = self.calcFieldInProtectLeftTime(player.footballGround.groundMatch.fieldList[i].protectType,
            player.footballGround.groundMatch.fieldList[i].protectEndTime, now);
        fieldInfo.leftProtectTime = protectInfo.leftTime;
        fieldInfo.leftProtectType = protectInfo.type;
        if(!!player.footballGround.groundMatch.fieldList[i].name) {
            fieldInfo.name = player.footballGround.groundMatch.fieldList[i].name;
            fieldInfo.faceUrl = player.footballGround.groundMatch.fieldList[i].faceUrl;
            fieldInfo.formationResId = player.footballGround.groundMatch.fieldList[i].formationResId || 0;
            fieldInfo.attack = player.footballGround.groundMatch.fieldList[i].attack;
            fieldInfo.defend = player.footballGround.groundMatch.fieldList[i].defend;
            fieldInfo.atkTactic = player.footballGround.groundMatch.fieldList[i].atkTactic;
            fieldInfo.defTactic = player.footballGround.groundMatch.fieldList[i].defTactic;
            //预计产出, 占领时间
            fieldInfo.occupyTime = Math.floor((now - player.footballGround.groundMatch.fieldList[i].occupyStartTime)/1000);
            if(fieldInfo.occupyTime < 0) {
                fieldInfo.occupyTime = 0;
            }
            fieldInfo.str = player.footballGround.groundMatch.fieldList[i].str;
            //获取其保护时间
            let myGid = self.app.getServerId();
            let occupyUid = player.footballGround.groundMatch.fieldList[i].beOccupiedUid;
            let occupyGid = player.footballGround.groundMatch.fieldList[i].beOccupiedGid;
            let session = {frontendId: myGid, toServerId: occupyGid};
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: occupyUid,
                collectionNames: [commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
            /* june */
            // self.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache({frontendId: myGid}, {playerId: occupyUid, gid: occupyGid,
            //     collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0}, function(code, playerGid, playerDoc) {
                let occList = playerDoc[commonEnum.DB_NAME.footballGround].groundMatch.occupyFieldList;
                for(let j=0; j<occList.length; j++) {
                    logger.debug("occList[j]: ", occList[j], i, occupyUid, occupyGid);
                    if(occList[j].occupyTeamIndex == i && occList[j].occupyUid === occupyUid) {
                        protectInfo = self.calcFieldInProtectLeftTime(occList[j].protectType, occList[j].protectEndTime, now);
                        fieldInfo.leftProtectTime = protectInfo.leftTime;
                        fieldInfo.leftProtectType = protectInfo.type;
                        fieldInfo.reportList = occList[j].beReportedList;
                    }
                    logger.debug("beReportedList: ", occList[j].beReportedList);
                }
                let systemProtectTime = dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchSystemProtectTime);
                if(fieldInfo.occupyTime < systemProtectTime && fieldInfo.leftProtectType <= 1) {
                    fieldInfo.leftProtectTime = systemProtectTime - fieldInfo.occupyTime;
                    fieldInfo.leftProtectType = 1;
                }
                fieldInfo.expectCash = player.footballGround.calcExpectCash(player.footballGround.groundMatch.fieldList, i,
                    true, player.footballGround.ballFan);
                if(!!player.footballGround.groundMatch.fieldList[i].teamUid) {
                    fieldInfo.ownerTeamUid = player.footballGround.groundMatch.fieldList[i].teamUid;
                }
                fieldInfo.teamIndex = i;
                fieldList.push(fieldInfo);
                callback();
            });
        }else {
            fieldInfo.expectCash = player.footballGround.calcExpectCash(player.footballGround.groundMatch.fieldList, i,
                true, player.footballGround.ballFan);
            if(!!player.footballGround.groundMatch.fieldList[i].teamUid) {
                fieldInfo.ownerTeamUid = player.footballGround.groundMatch.fieldList[i].teamUid;
            }
            fieldInfo.teamIndex = i;
            fieldList.push(fieldInfo);
            callback();
        }
    }, function () {
        let reportPassTime = Math.floor((now - player.footballGround.groundMatch.lastReportTime) / 1000);
        let reportLeftTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportInverval].Param - reportPassTime;
        if(reportLeftTime < 0) reportLeftTime = 0;
        //检查是否有到期的队伍
        self.checkGroundMatchTimeOver(player, function () {
            cb(Code.OK, fieldList, player.footballGround.calcOwnFieldTotalCash(), reportLeftTime, player.footballGround.groundMatch.reportNum);
        });
    });
};

//2. 获取我的占领信息
groundMatchService.prototype.getMyGroundOccupyInfo = function(playerId, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.getMyGroundOccupyInfo: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    let now = TimeUtils.now();
    let occupyList = [];
    for(let i=0;i<3;i++) {
        if(!player.footballGround.groundMatch.occupyFieldList[i].teamUid) {
            continue;
        }
        let info = {};
        info.teamUid = player.footballGround.groundMatch.occupyFieldList[i].teamUid;
        if(player.footballGround.groundMatch.occupyFieldList[i].resId !== 0) {
            info.resId = player.footballGround.groundMatch.occupyFieldList[i].resId;
            info.ownerUid = player.footballGround.groundMatch.occupyFieldList[i].occupyUid;
            info.ownerName = player.footballGround.groundMatch.occupyFieldList[i].name || "";
            if (player.footballGround.groundMatch.occupyFieldList[i].occupyTeamUid) {
                info.ownerTeamUid = player.footballGround.groundMatch.occupyFieldList[i].occupyTeamUid;
            }
            info.ownerFaceUrl = player.footballGround.groundMatch.occupyFieldList[i].occupyFaceUrl || "";
            let protectInfo = this.calcFieldInProtectLeftTime(player.footballGround.groundMatch.occupyFieldList[i].protectType,
                player.footballGround.groundMatch.occupyFieldList[i].protectEndTime, now);
            info.leftProtectTime = protectInfo.leftTime;
            info.leftProtectType = protectInfo.type;
            //预计产出, 占领时间
            let passTime = Math.floor((now-player.footballGround.groundMatch.occupyFieldList[i].occupyTime) /1000);
            if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
                passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
            }
            if(passTime < 0) passTime = 0;
            info.occupyTime = passTime;
            info.expectCash = player.footballGround.calcExpectCash(player.footballGround.groundMatch.occupyFieldList, i, false,
                player.footballGround.groundMatch.occupyFieldList[i].ballFan || 0);
            info.reportList = player.footballGround.groundMatch.occupyFieldList[i].beReportedList;
        }
        info.teamIndex = i;
        occupyList.push(info);
    }
    cb(Code.OK, occupyList);
};

groundMatchService.prototype.checkTheIndexFromClient = function(index) {
    return !((!index && index !== 0) || index < 0 || index > 3);
};

//3. 设置队伍
groundMatchService.prototype.setGroupMatchTeam = function(playerId, teamUid, dstIndex, type, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.setGroupMatchTeam: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(dstIndex) || (type !== commonEnum.FORMATION_TYPE.GROUND_ATK &&
        type !== commonEnum.FORMATION_TYPE.GROUND_DEF)) {
        logger.error('groundMatchService.setGroupMatchTeam: dstIndex error !', dstIndex, type);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }

    //校验是否是用户自己的teamUid
    let isOwnUid = false;
    for(let [k,v] of player.teamFormations.allTeamFormations) {
        if(v.Type === type && v.Uid === teamUid) {
            isOwnUid = true;
            break;
        }
    }
    if(!isOwnUid) {
        logger.debug("groundMatchService.setGroupMatchTeam: not own uid", teamUid);
        cb(Code.GROUND_MATCH.NOT_OWN_TEAM_UID);
        return;
    }

    let srcIndex = 3;
    let list;
    if(type === commonEnum.FORMATION_TYPE.GROUND_ATK) {
        list = player.footballGround.groundMatch.occupyFieldList;
    }else if(type === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        list = player.footballGround.groundMatch.fieldList;
    }
    for(let i=0; i<3; i++) {
        if(teamUid === list[i].teamUid) {
            srcIndex = i;
            break;
        }
    }
    //原队伍或者目标队伍在占领中,是不可以切换的
    //logger.debug("list[srcIndex], srcIndex: ", list[srcIndex], srcIndex, !!srcIndex);
   if((srcIndex <= 2 && list[srcIndex].occupyTeamUid) || (list[dstIndex] && !!list[dstIndex].occupyTeamUid)) {
        logger.debug("exchange team is occupying...");
        return cb(Code.GROUND_MATCH.TEAM_OCCUPYING);
    }

    //如果目标队伍有人, 则需占领者数据同步数据
    let needSyncData = {};
    if(type === commonEnum.FORMATION_TYPE.GROUND_DEF && (list[dstIndex] && list[dstIndex].beOccupiedUid)) {
        needSyncData.uid = list[dstIndex].beOccupiedUid;
        needSyncData.occupyTeamUid = teamUid;
        needSyncData.occupyTeamIndex = dstIndex;
        needSyncData.gid = list[dstIndex].beOccupiedGid;
        needSyncData.occupyUid = playerId;
    }

    logger.debug("srcIndex, dstIndex, teamUid: ", srcIndex, dstIndex, teamUid, list);
    if(srcIndex > 2) {
        //直接设置
        list[dstIndex].teamUid = teamUid;
    }else {
        //置换
        list[srcIndex].teamUid = list[dstIndex].teamUid;
        list[dstIndex].teamUid = teamUid;
    }
    logger.debug("list :", list);
    player.saveBallGround();

    let self = this;
    async.waterfall([
        function (callback) {
            if(!needSyncData.uid) {
                callback()
            }else {
                //1. 同步到占领者对应进程 (在线, 离线情况处理)
                let session = {frontendId: self.app.getServerId(), toServerId: needSyncData.gid}
                self.app.rpc.game.entryRemote.syncOccupyTeamInfo(session, needSyncData, function (code) {
                    if(code !== Code.OK) {
                        logger.error("syncOccupyTeamInfo rpc err.");
                        return callback(code);
                    }
                    callback();
                })
            }
        },
        /* june */
        // function (callback) {
        //     if(!needSyncData.uid) {
        //         callback();
        //     }else {
        //         //2. 占领者数据同步到datanode
        //         self.app.rpc.datanode.dataNodeRemote.syncGroundTeamUidToCache({frontendId: self.app.getServerId()}, needSyncData, function (code) {
        //             callback();
        //         })
        //     }
        // },
        function (callback) {
            //3. 切换队伍用户同步到datanode
            let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.SET_FORMATION;
            let syncData = {srcIndex: srcIndex, dstIndex: dstIndex, teamUid: teamUid};
            self.syncGroundMatchDataToDatanode(playerId, dstIndex, syncType, syncData, function (code) {
                callback();
            })
            /* june */
            // let rpcMsg = {
            //     uid: playerId,
            //     collections: [commonEnum.DB_NAME.teamFormation, commonEnum.DB_NAME.heros, commonEnum.DB_NAME.footballGround],
            //     doc: {}
            // };
            // rpcMsg.doc[commonEnum.DB_NAME.teamFormation] = player.teamFormations.toJSONforDB();
            // rpcMsg.doc[commonEnum.DB_NAME.heros] = player.teamFormations.toJSONforDB();
            // rpcMsg.doc[commonEnum.DB_NAME.footballGround] = player.footballGround.toJSONforDB();
            // self.app.rpc.datanode.dataNodeRemote.syncDocDataToCache({frontendId: self.app.getServerId()}, rpcMsg, function () {
            //     callback();
            // })
        }
    ], function (err) {
        cb(Code.OK);
    });

};

groundMatchService.prototype.syncOccupyTeamInfo = function(playerId, teamIndex, teamUid, occupyUid, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!!player){
        //在线
        let fieldList = player.footballGround.groundMatch.occupyFieldList;
        for(let i=0; i<fieldList.length; i++) {
            if(fieldList[i].occupyTeamIndex == teamIndex && fieldList[i].occupyTeamIndex === occupyUid) {
                fieldList[i].occupyTeamUid = teamUid;
                break;
            }
        }
        player.saveBallGround();
        cb(Code.OK);
    }else {
        playerService.playerDao.findOne(commonEnum.DB_NAME.footballGround, playerId, function (err, doc) {
            if (err || !doc) {
                logger.debug("syncOccupyTeamInfo err or doc is null. ", err, doc, playerId, teamIndex, teamUid);
                cb(Code.FAIL);
                return;
            }
            let fieldList = doc.groundMatch.occupyFieldList;
            for (let i = 0; i < fieldList.length; i++) {
                if (fieldList[i].occupyTeamIndex == teamIndex && fieldList[i].occupyTeamIndex === occupyUid) {
                    fieldList[i].occupyTeamUid = teamUid;
                    break;
                }
            }
            playerService.playerDao.updateOne(commonEnum.DB_NAME.footballGround, playerId, {groundMatch: doc.groundMatch}, function (err) {
                cb(Code.OK);
            });
        });
    }
};

//4. 匹配 - 搜索3个坑位
groundMatchService.prototype.groundMatchRandomMatch = function (playerId, teamIndex, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchRandomMatch: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(teamIndex)) {
        logger.error('groundMatchService.groundMatchRandomMatch: teamIndex error !', teamIndex);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    //已有占领队伍
    if(player.footballGround.groundMatch.occupyFieldList[teamIndex].ownerTeamUid) {
        cb(Code.GROUND_MATCH.ALREADY_OCCUPY);
        return;
    }
    //未设置进攻队伍
    if(!player.footballGround.groundMatch.occupyFieldList[teamIndex].teamUid) {
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    //到datanode去查询对应的数据 (数据返回格式: 我的占领 一致)
    let session = {frontendId: this.app.getServerId()};
    let rpcMsg = {
        myUid: playerId,
        myStr: player.teamFormations.calcTotalRating(player.footballGround.groundMatch.occupyFieldList[teamIndex].teamUid),
        myAsset: player.footballGround.calcTotalAssets()
    };
    /*
    let costCash = dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchSearchCash);
    if(player.cash < costCash) {
        logger.debug("groundMatchService.groundMatchRandomMatch: not enough cash: ", player.cash, costCash);
        return cb(Code.CASH_FALL);
    }
    */
    let self = this;
    this.app.rpc.datanode.dataNodeRemote.searchGroundMatchByRandom(session, rpcMsg, function (code, searchList) {
        logger.debug("code, searchList: ",code, searchList);
        if(code !== Code.OK) {
            return cb(code);
        }

        //存储搜索列表
        player.footballGround.groundMatch.searchList = [];
        let myGid = self.app.getServerId();
        //获取占领者的实力
        async.eachSeries(searchList, function (searchInfo, cb1) {
            player.footballGround.groundMatch.searchList.push({
                ownerUid: searchInfo.ownerUid,
                ownerName: searchInfo.ownerName,
                ownerGid: searchInfo.ownerGid,
                ownerTeamUid: searchInfo.ownerTeamUid,
                ownerFaceUrl: searchInfo.ownerFaceUrl,
                ownerIndex: searchInfo.teamIndex,
                occupyUid: searchInfo.occupyUid,
                occupyName: searchInfo.occupyName,
                occupyGid: searchInfo.occupyGid,
                occupyTeamUid: searchInfo.occupyTeamUid,
                occupyTeamName: searchInfo.occupyTeamName,
                occupyFaceUrl: searchInfo.occupyFaceUrl,
                occupyTime: searchInfo.occupyTime,
                resId: searchInfo.resId,
                mainGroundLevel: searchInfo.mainGroundLevel,
                reportList: searchInfo.reportList,
                ownerBallFan: searchInfo.ownerBallFan
            });

            let searchUid = searchInfo.ownerUid;
            let gid = searchInfo.ownerGid;
            let searchTeamUid = searchInfo.ownerTeamUid;
            if(searchInfo.occupyUid && searchInfo.occupyTeamUid) {
                searchUid = searchInfo.occupyUid;
                searchTeamUid = searchInfo.occupyTeamUid;
                gid = searchInfo.occupyGid;
            }
            if(!searchTeamUid || !gid) {
                return cb1();
            }
            logger.debug("groundMatchByRandom getOtherPlayerStr searchUid, gid, searchTeamUid, myGid: ",searchUid, gid, searchTeamUid, myGid);
            self.getOtherPlayerStr(searchUid, gid, searchTeamUid, myGid, function (ret) {
                if(ret.code === Code.OK) {
                    searchInfo.str = ret.str;
                    searchInfo.attack = ret.attack;
                    searchInfo.defend = ret.defend;
                    cb1();
                }else {
                    cb1();
                }
            })
        }, function (err) {
            logger.debug("searchGroundMatchByRandom searchList: ", searchList);
            player.saveBallGround();
            /*
            //扣除欧元 - 修改搜索不再扣除欧元
            player.subtractResource(commonEnum.PLAY_INFO.cash, costCash);
            player.save();
            player.updatePlayer();
            */
            cb(code, searchList);
        });
    });
};

//5. 昵称查找
groundMatchService.prototype.groundMatchByName = function (playerId, teamIndex, name, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchByName: player not exist !', playerId);
        return cb(Code.FAIL);
    }
    if(!this.checkTheIndexFromClient(teamIndex)) {
        logger.error('groundMatchService.groundMatchByName: teamIndex error !', teamIndex);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    //已有进攻队伍
    if(player.footballGround.groundMatch.occupyFieldList[teamIndex].ownerUid) {
        return cb(Code.GROUND_MATCH.ALREADY_OCCUPY);
    }
    if(name === player.name) {
        return cb(Code.GROUND_MATCH.CANNOT_FOUND_YOURSELF);
    }
    //未设置进攻队伍
    if(!player.footballGround.groundMatch.occupyFieldList[teamIndex].teamUid) {
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    //检测是否有足够欧元
    /*
    let costCash = dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchSearchCash);
    if(player.cash < costCash) {
        logger.debug("groundMatchService.groundMatchRandomMatch: not enough cash: ", player.cash, costCash);
        return cb(Code.CASH_FALL);
    }*/

    //先去datanode去查询, 穿透cache层后再到db去查询
    let serverId = this.app.getServerId();
    let session = {frontendId: serverId};
    let rpcMsg = {
        name: name
    };
    let self = this;
    this.app.rpc.datanode.dataNodeRemote.searchGroundMatchByName(session, rpcMsg, function (code, searchList) {
        if(code !== Code.OK) {
            return cb(code);
        }
        //存储搜索列表
        player.footballGround.groundMatch.searchList = [];
        let myGid = self.app.getServerId();
        //获取占领者的实力
        async.eachSeries(searchList, function (searchInfo, cb1) {
            player.footballGround.groundMatch.searchList.push({
                ownerUid: searchInfo.ownerUid,
                ownerName: searchInfo.ownerName,
                ownerGid: searchInfo.ownerGid,
                ownerTeamUid: searchInfo.ownerTeamUid,
                ownerFaceUrl: searchInfo.ownerFaceUrl,
                ownerIndex: searchInfo.teamIndex,
                occupyUid: searchInfo.occupyUid,
                occupyName: searchInfo.occupyName,
                occupyGid: searchInfo.occupyGid,
                occupyTeamUid: searchInfo.occupyTeamUid,
                occupyTeamName: searchInfo.occupyTeamName,
                occupyFaceUrl: searchInfo.occupyFaceUrl,
                occupyTime: searchInfo.occupyTime,
                resId: searchInfo.resId,
                mainGroundLevel: searchInfo.mainGroundLevel,
                reportList: searchInfo.reportList,
                ownerBallFan: searchInfo.ownerBallFan
            });

            let searchUid = searchInfo.ownerUid;
            let gid = searchInfo.ownerGid;
            let searchTeamUid = searchInfo.ownerTeamUid;
            if(searchInfo.occupyUid && searchInfo.occupyTeamUid) {
                searchUid = searchInfo.occupyUid;
                searchTeamUid = searchInfo.occupyTeamUid;
                gid = searchInfo.occupyGid;
            }
            if(!searchTeamUid || !gid) {
                return cb1();
            }
            logger.debug("groundMatchByName getOtherPlayerStr searchUid, gid, searchTeamUid, myGid: ",searchUid, gid, searchTeamUid, myGid);
            self.getOtherPlayerStr(searchUid, gid, searchTeamUid, myGid, function (ret) {
                if(ret.code === Code.OK) {
                    searchInfo.str = ret.str;
                    searchInfo.attack = ret.attack;
                    searchInfo.defend = ret.defend;
                    cb1();
                }else {
                    cb1();
                }
            })
        }, function (err) {
            logger.debug("groundMatchByName searchList: ", searchList);
            player.saveBallGround();
            /*
            //扣除欧元
            player.subtractResource(commonEnum.PLAY_INFO.cash, costCash);
            player.save();
            player.updatePlayer();
            */
            cb(code, searchList);
        });
    });
};

groundMatchService.prototype.getOtherPlayerStr = function(searchUid, gid, searchTeamUid, myGid, cb) {
    if(gid === myGid) {
        this.getAndCalcPlayerStr(searchUid, searchTeamUid, function (ret) {
            cb(ret);
        })
    }else {
        let session = {frontendId: this.app.getServerId(), toServerId: gid};
        this.app.rpc.game.entryRemote.getAndCalcPlayerStr(session, {searchUid: searchUid, searchTeamUid: searchTeamUid}, function (ret) {
            cb(ret);
        });
    }
};

groundMatchService.prototype.getAndCalcPlayerStr = function(searchUid, searchTeamUid, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(searchUid);
    let ret = {code: Code.OK, str: 0, attack: 0, defend: 0};
    if(!!player){
        //在线
        let formation = player.teamFormations.getOneTeamFormation(searchTeamUid);
        if(!formation) {
            ret.code = Code.FAIL;
            return cb(ret);
        }
        ret.str = player.teamFormations.calcTotalRating(searchTeamUid);
        ret.attack = (!formation) ? 0 : formation.Attack;
        ret.defend = (!formation) ? 0 : formation.Defend;
        cb(ret);
    }else {
        this.getOfflinePlayerAndDoc(searchUid, function (player, doc) {
            let formation = player.teamFormations.getOneTeamFormation(searchTeamUid);
            if(!formation) {
                ret.code = Code.FAIL;
                return cb(ret);
            }
            ret.str = player.teamFormations.calcTotalRating(searchTeamUid);
            ret.attack = (!formation) ? 0 : formation.Attack;
            ret.defend = (!formation) ? 0 : formation.Defend;
            logger.debug("getOfflinePlayerAndDoc: ", ret, formation);
            cb(ret);
        })
    }
};

//6. 掠夺/占领
groundMatchService.prototype.groundMatchRob = function (playerId, atkIndex, defIndex, clientOwnerName, clientRobName, cb) {
    if(debugConfig.isTraceGroundMatch) {
        let msgObj = {
            playerId: playerId,
            atkIndex: atkIndex,
            defIndex: defIndex,
            clientOwnerName: clientOwnerName,
        };
        this.traceLogForDebugger("groundMatchRob, client param: ", msgObj);
    }
    logger.debug("june debug groundMatchRob, client param: ", playerId, atkIndex, defIndex, clientOwnerName, clientRobName);
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchRob: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(atkIndex) || !this.checkTheIndexFromClient(defIndex)) {
        logger.error('groundMatchService.groundMatchRob: index from client error !', atkIndex, defIndex);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    //logger.debug("groundMatchRob: atkIndex, defIndex: ", atkIndex, defIndex, player.footballGround.groundMatch.occupyFieldList);
    let fieldInfo = player.footballGround.groundMatch.occupyFieldList[atkIndex];
    if(!fieldInfo) {
        logger.error('groundMatchService.groundMatchRob: fieldInfo error !', player.footballGround.groundMatch.fieldList);
        cb(Code.FAIL);
        return;
    }
    let searchInfo = player.footballGround.groundMatch.searchList[defIndex];
    if(!searchInfo) {
        logger.error('groundMatchService.groundMatchRob: searchInfo error !', player.footballGround.groundMatch.searchList);
        cb(Code.FAIL);
        return;
    }
    //logger.debug("pvpGroundMatchBattleReq searchInfo: ", player.footballGround.groundMatch.searchList, defIndex);
    //logger.error("june debug groundMatchRob, searchInfo: ", player.footballGround.groundMatch.searchList, defIndex);
    if(debugConfig.isTraceGroundMatch) {
        let msgObj = {
            searchList: player.footballGround.groundMatch.searchList,
            defIndex: defIndex
        };
        this.traceLogForDebugger("groundMatchRob, searchInfo step1", msgObj);
    }

    //花费是否足够
    let cashCost = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchCostCash].Param;
    let energyCost = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchCostEnergy].Param;
    if(player.cash < cashCost) {
        logger.debug("groundMatchService.groundMatchRob: not enough cash: ", player.cash, cashCost);
        return cb(Code.CASH_FALL);
    }
    if(player.energy < energyCost) {
        logger.debug("groundMatchService.groundMatchRob: not enough energy: ", player.energy, energyCost);
        return cb(Code.ENERGY_FAIL);
    }
    let battleObj = {
        uid: searchInfo.ownerUid,
        gid: searchInfo.ownerGid,
        teamUid: searchInfo.ownerTeamUid
    };
    if(searchInfo.occupyUid) {
        battleObj.uid = searchInfo.occupyUid;
        battleObj.gid = searchInfo.occupyGid;
        battleObj.teamUid = searchInfo.occupyTeamUid;
    }
    if(playerId === battleObj.uid || playerId === searchInfo.ownerUid || playerId === searchInfo.occupyUid) {
        logger.debug("groundMatchService.groundMatchRob: cannot rob your self", playerId, battleObj.uid, searchInfo.ownerUid);
        return cb(Code.GROUND_MATCH.CANNOT_ROB_YOURSELF);
    }
    let self = this;
    let gid = this.app.getServerId();
    let now = TimeUtils.now();
    //1. 球场拥有者
    let ownerInfo = {
        uid: searchInfo.ownerUid,
        gid: searchInfo.ownerGid,
        name: searchInfo.ownerName,
        teamUid: searchInfo.ownerTeamUid,
        ballFan: searchInfo.ownerBallFan,
        mainGroundLevel: searchInfo.mainGroundLevel,
        resId: searchInfo.resId,
        teamIndex: searchInfo.ownerIndex,
        faceUrl: searchInfo.ownerFaceUrl
    };
    let robInfo = {
        uid: searchInfo.occupyUid,
        gid: searchInfo.occupyGid,
        occupyTime: searchInfo.occupyTime,
        name: searchInfo.occupyName,
        teamUid: searchInfo.occupyTeamUid
    };
    let atkFormation = player.teamFormations.getOneTeamFormation(fieldInfo.teamUid);
    if(!atkFormation) {
        logger.error("groundMatchService.groundMatchRob: get atkFormation fail. ", fieldInfo);
        return cb(Code.FAIL);
    }
    let atkInfo = {
        formationResId: atkFormation.ResId,
        attack: atkFormation.Attack,
        defend: atkFormation.Defend,
        atkTactic: atkFormation.UseTactics,
        defTactic: atkFormation.UseDefTactics,
        str: player.teamFormations.calcTotalRating(fieldInfo.teamUid),
        teamName: atkFormation.Name,
        teamIndex: atkIndex,
        name: player.name
    };
    logger.debug("groundMatchRob start step1: ", ownerInfo, robInfo, player.footballGround.groundMatch, atkFormation);
    //logger.error("june debug groundMatchRob, atkInfo step1: ", atkInfo);
    let record = {};
    let roomUid;
    let beReportNum = 0;

    //客户端数据校验
    //if(clientOwnerName !== ownerInfo.name || (clientRobName !== robInfo.name && robInfo.name)) {
    if(clientOwnerName !== ownerInfo.name) {
        logger.error("groundMatchRob cs data not same1: ", clientOwnerName, clientRobName, ownerInfo, robInfo);
        return cb(Code.GROUND_MATCH.CS_DATA_NOT_SAME);
    }

    let syncData = {uid: ownerInfo.uid, teamIndex: ownerInfo.teamIndex, occupyInfo: {}};
    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.ROB_EVENT;
    let isNeedToSync = false;

    async.waterfall([
        function (callback) {
            //检查并增加状态锁
            self.searchAndLockOwnerField(ownerInfo.uid, ownerInfo.teamIndex, function (code) {
                if(code !== Code.OK) {
                    callback(code);
                }else {
                    callback();
                }
            });
        },
        function (callback) {
            //检查此时占领的数据是否被变更
            self.checkOwnerAndSearchData(ownerInfo.uid, ownerInfo.teamIndex, ownerInfo.gid, robInfo, function (code) {
                if(code !== Code.OK) {
                    callback(code);
                }else {
                    callback();
                }
            });
        },
        function (callback) {
            //防守没有队伍而且没有占领队伍直接占领
            if(!ownerInfo.teamUid && !robInfo.uid) {
                record = {time: now, resId: searchInfo.resId, uidA: player.playerId, nameA: player.name, faceUrlA: player.faceUrl,
                    teamUidA: fieldInfo.teamUid, gidA: gid, uidB: searchInfo.ownerUid, nameB: searchInfo.ownerName,
                    faceUrlB: searchInfo.ownerFaceUrl, teamUidB: searchInfo.ownerTeamUid, gidB: searchInfo.ownerGid};
                isNeedToSync = true;
                callback();
            }else {
                let session = {frontendId: gid};
                let rpcMsg = {
                    home: playerId,
                    homeGid: gid,
                    homeTeamUid: fieldInfo.teamUid,
                    away: battleObj.uid,
                    awayGid: battleObj.gid,
                    awayTeamUid: battleObj.teamUid
                };
                self.app.rpc.battle.battleRemote.pvpGroundMatchBattleReq(session, rpcMsg, function (code, ret) {
                    if (code !== Code.OK) {
                        logger.error("rpc pvpGroundMatchBattleReq error: ", code);
                        return cb(code);
                    }
                    //1. 处理发起抢夺用户数据
                    record = {
                        time: now,
                        resId: searchInfo.resId,
                        uidA: player.playerId,
                        nameA: player.name,
                        faceUrlA: player.faceUrl,
                        scoreA: ret.homeScore,
                        teamUidA: fieldInfo.teamUid,
                        gidA: gid,
                        uidB: searchInfo.ownerUid,
                        nameB: searchInfo.ownerName,
                        faceUrlB: searchInfo.ownerFaceUrl,
                        scoreB: ret.awayScore,
                        teamUidB: searchInfo.ownerTeamUid,
                        gidB: searchInfo.ownerGid,
                        roomUid: ret.roomUid
                    };
                    if(record.scoreA > record.scoreB) {
                        isNeedToSync = true;
                    }
                    roomUid = ret.roomUid;
                    callback();
                });
            }
        },
        function (callback) {
            //1. 处理之前占领者数据
            if(!robInfo.uid) {
                logger.debug("dealWithGroundMatchOccupier no occupier.");
                return callback();
            }
            //存在之前的占领者
            record.uidB = robInfo.uid;
            record.nameB = robInfo.name;
            record.faceUrlB = searchInfo.occupyFaceUrl;
            record.teamUidB = searchInfo.occupyTeamUid;
            record.gidB = robInfo.gid;
            let myGid = self.app.getServerId();
            if(myGid === robInfo.gid) {
                self.dealWithRobOccupier(record, ownerInfo, atkInfo, robInfo, function (code, num) {
                    beReportNum = num;
                    if(code !== Code.OK) {
                        return callback(code);
                    }
                    callback();
                })
            }else {
                self.app.rpc.game.entryRemote.dealWithGroundMatchOccupier({frontendId: myGid, toServerId: robInfo.gid},
                    {record: record, ownerInfo: ownerInfo, atkInfo: atkInfo, robInfo: robInfo},
                    function (code, num) {
                        beReportNum = num;
                        if(code !== Code.OK) {
                            return callback(code);
                        }
                        callback();
                    });
            }
        },
        function (callback) {
            //2. 处理训练场拥有者数据
            let myGid = self.app.getServerId();
            if(myGid === searchInfo.ownerGid) {
                self.dealWithRobOwner(record, ownerInfo, atkInfo, robInfo, beReportNum, function (code) {
                    logger.debug("dealWithGroundMatchRobOwner code 111: ", code);
                    if(code !== Code.OK) {
                        return callback(code);
                    }
                    callback();
                })
            }else {
                self.app.rpc.game.entryRemote.dealWithGroundMatchRobOwner({frontendId: myGid, toServerId: searchInfo.ownerGid},
                    {record: record, ownerInfo: ownerInfo, atkInfo: atkInfo, robInfo: robInfo, beReportNum: beReportNum},
                    function (code) {
                        logger.debug("dealWithGroundMatchRobOwner code 222: ", code);
                        if(code !== Code.OK) {
                            return callback(code);
                        }
                        callback();
                    });
            }
        },
        function (callback) {
            //3. 处理抢夺发起者数据
            self.dealWithRobAttacker(record, player, atkInfo, now, robInfo, cashCost, energyCost, ownerInfo, beReportNum,
                function (code, syncOccupyInfo) {
                logger.debug("dealWithRobAttacker");
                syncData.occupyInfo = syncOccupyInfo;
                callback();
            });
        },
        //同步数据到datanode
        function (callback) {
            self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function (code) {
                callback();
            })
        }
    ], function (errCode) {
        logger.debug("groundMatchRob finish.", errCode);
        self.unlockOwnerField(ownerInfo.uid, ownerInfo.teamIndex, function () {
            if(!!errCode) {
                return cb(errCode);
            }
            cb(Code.OK, roomUid);
        });
    });
};

//处理训练场所有者
groundMatchService.prototype.dealWithRobOwner = function(record, ownerInfo, atkInfo, robInfo, beReportNum, cb) {
    logger.debug("dealWithRobOwner: param: ", record, ownerInfo, atkInfo, robInfo);
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(ownerInfo.uid);
    let now = TimeUtils.now();
    let self = this;

    let _sendOwnerMail = function (mainGroundLevel, ballFan, fieldInfo) {
        let passTime = Math.floor((now-fieldInfo.occupyStartTime) /1000);
        if(passTime < 0) passTime = 0;
        if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
            passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
        }
        let reward = self.calcPerFieldReward(mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, ballFan, false, beReportNum);
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_FIELD_CHANGE;
        //您的#0#训练场位被用户#0#占领#0#个小时，已被用户#0#抢夺，#0#的占领已终止，您损失#0#欧元
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        let passHour = Math.floor(passTime / 1800) / 2;
        mailContent = mailContent.replace("#0#", passHour.toFixed(1));
        mailContent = mailContent.replace("#0#", atkInfo.name);
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        mailContent = mailContent.replace("#0#", reward.cash);
        mailContent = mailContent.replace("#0#", reward.fans);
        self.sendEmail(ownerInfo.uid, mailId, mailContent, [], function (code) {});
        return reward;
    };

    let _dealFunc = function(fieldInfo, mainGroundLevel, ballFan, footballGround) {
        //被攻击者占领
        if(record.scoreA > record.scoreB || !record.roomUid) {
            //1. 存在前一个占领者的情况, 占领变更
            if(!!robInfo.uid) {
                //发送提醒邮件
                let reward = _sendOwnerMail(mainGroundLevel, ballFan, fieldInfo);
                //损失粉丝
                if(reward.fans > 0) {
                    footballGround.ballFan -= reward.fans;
                    if(footballGround.ballFan < 0) {
                        footballGround.ballFan = 0;
                    }
                }
            }
            //2. 数据变更
            fieldInfo.beOccupiedUid = record.uidA;
            fieldInfo.beOccupiedGid = record.gidA;
            fieldInfo.beOccupiedTeamUid = record.teamUidA;
            fieldInfo.name = record.nameA;
            fieldInfo.faceUrl = record.faceUrlA;
            fieldInfo.formationResId = atkInfo.formationResId;            //阵型id
            fieldInfo.attack = atkInfo.attack;                    //进攻值
            fieldInfo.defend = atkInfo.defend;                    //防守值
            fieldInfo.atkTactic = atkInfo.atkTactic;                 //占领玩家的进攻战术
            fieldInfo.defTactic = atkInfo.defTactic;                 //占领玩家的防守战术
            fieldInfo.occupyStartTime = now;
            fieldInfo.beOccupiedTeamName = atkInfo.teamName;
            fieldInfo.str = atkInfo.str;
            //3. 不存在, 不用发邮件给球场拥有者，占领结束时才发邮件提醒拥有者
        } else {
            //占领失败, 对于拥有者无影响.
        }
        //记录
        if(record.roomUid) {
            self.addEventRecord(fieldInfo.recordList, record);
        }
    };

    let fieldInfo, fieldList, mainGroundLevel, ballFan, syncData;
    async.waterfall([
        function (callback) {
            //在线
            if(!!player) {
                fieldInfo = player.footballGround.groundMatch.fieldList[ownerInfo.teamIndex];
                if(!fieldInfo) {
                    logger.error("groundMatchService.dealWithRobOwner find fieldInfo fail.", record, fieldList);
                    return callback(Code.FAIL);
                }
                //有保护文书
                if(fieldInfo.protectEndTime > TimeUtils.now()) {
                    return callback(Code.GROUND_MATCH.TEAM_IS_PROTECTED);
                }
                mainGroundLevel = ownerInfo.mainGroundLevel || 1;
                ballFan = ownerInfo.ballFan || 0;
                robInfo.occupyTime = fieldInfo.occupyStartTime;
                _dealFunc(fieldInfo, mainGroundLevel, ballFan, player.footballGround);
                player.saveBallGround();
                syncData = player.footballGround.toJSONforDB();
                callback(null);
            }else {
                //离线
                self.getOfflinePlayerAndDoc(ownerInfo.uid, function(player, doc) {
                    fieldInfo = player.footballGround.groundMatch.fieldList[ownerInfo.teamIndex];
                    if(!fieldInfo) {
                        logger.error("groundMatchService.dealWithRobOwner offline find fieldInfo fail.", record, fieldList);
                        return callback(Code.FAIL);
                    }
                    //有保护文书
                    if(fieldInfo.protectEndTime > TimeUtils.now()) {
                        return callback(Code.GROUND_MATCH.TEAM_IS_PROTECTED);
                    }
                    mainGroundLevel = ownerInfo.mainGroundLevel || 1;
                    ballFan = ownerInfo.ballFan || 0;
                    robInfo.occupyTime = fieldInfo.occupyStartTime;
                    _dealFunc(fieldInfo, mainGroundLevel, ballFan, player.footballGround);
                    let footballGroundDoc = player.footballGround.toJSONforDB();
                    playerService.playerDao.updateOne("footballGround", ownerInfo.uid, footballGroundDoc, function () {
                        syncData = footballGroundDoc;
                        callback(null);
                    });
                });
            }
        },
        function (callback) {
            //同步数据到datanode的cache层
            if(debugConfig.isTraceGroundMatch) {
                let msgObj = {
                    fieldList: syncData.groundMatch.fieldList,
                    occupyFieldList: syncData.groundMatch.occupyFieldList,
                };
                self.traceLogForDebugger("groundMatchRob, ownerInfo step3", msgObj);
            }
            logger.debug("groundMatchRob start step2: ownerInfo", syncData.groundMatch);
            callback();
            /* june
            self.syncGroundToDatanode(ownerInfo.uid, syncData, ownerInfo.gid, function (code) {
                logger.debug("syncGroundToDatanode ret code:", code);
                callback(code);
            })
            */
        }
    ], function (err) {
        if(!!err) {
            return cb(err);
        }
        cb(Code.OK);
    });
};

groundMatchService.prototype.syncGroundToDatanode = function(playerId, groundDoc, gid, cb) {
    let session = {frontendId: this.app.getServerId()};
    let msg = {uid: playerId, groundDoc: groundDoc, gid: gid};
    this.app.rpc.datanode.dataNodeRemote.syncGroundToDatanode(session, msg, function (code) {
        cb(code);
    });
};

//计算单个奖励 (isMyReward 是否是自产收益)
groundMatchService.prototype.calcPerFieldReward = function(mainGroundLevel, fieldLevel, passTime, ballFan, isMyReward, beReportNum) {
    if (!beReportNum) {
        beReportNum = 0;
    }
    if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
        passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
    }
    let result = {cash: 0, fans: 0, beliefNum: 0};
    let cashRewardId = 0, beliefRewardId = 0;
    for(let i in dataApi.allData.data["GroundMatchReward"]) {
        if(dataApi.allData.data["GroundMatchReward"][i].Level === mainGroundLevel && dataApi.allData.data["GroundMatchReward"][i].Type === 1) {
            cashRewardId = i;
        }
        if(dataApi.allData.data["GroundMatchReward"][i].Level === fieldLevel && dataApi.allData.data["GroundMatchReward"][i].Type === 2) {
            beliefRewardId = i;
        }
    }
    logger.debug("calcPerFieldReward mainGroundLevel, fieldLevel, passTime, ballFan, isMyReward: ",
        mainGroundLevel, fieldLevel, passTime, ballFan, isMyReward, cashRewardId, beliefRewardId);
    if(isMyReward) {
        result.cash = Math.floor(passTime / 1800) * dataApi.allData.data["GroundMatchReward"][cashRewardId]["Awardid2_1"]
            - beReportNum * dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportReward);
    }else {
        result.cash = Math.floor(passTime / 1800) * dataApi.allData.data["GroundMatchReward"][cashRewardId]["Awardid1_1"]
            - beReportNum * dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportReward);
        if(beliefRewardId > 0) {
            result.beliefNum = Math.floor(passTime / dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchBeliefNumInverval))
                * dataApi.allData.data["GroundMatchReward"][beliefRewardId]["Awardid1_1"];
        }
        result.fans = Math.floor(passTime / dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchFansChangeTime))
            * dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchFansChangeNum);
    }
    return result;
};

//处理被抢夺的用户
groundMatchService.prototype.dealWithRobOccupier = function(record, ownerInfo, atkInfo, robInfo, cb) {
    if(!robInfo.uid) {
        return cb(Code.OK);
    }
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(robInfo.uid);
    //let now = TimeUtils.now();
    let self = this;
    let beReportNum = 0;
    logger.debug("dealWithRobOccupier: ownerInfo, atkInfo, robInfo", ownerInfo, atkInfo, robInfo);
    let _dealFunc = function (footballGround, cb) {
        let fieldList = footballGround.groundMatch.occupyFieldList;
        let fieldInfo;
        for(let i=0,len=fieldList.length; i<len; i++) {
            if(fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                fieldInfo = fieldList[i];
                break;
            }
        }
        logger.debug("dealWithRobOccupier: fieldList, fieldInfo, ownerInfo.teamIndex: ", fieldList, fieldInfo, ownerInfo.teamIndex);
        if(!fieldInfo) {
            logger.error("groundMatchService.dealWithRobOccupier 111 find fieldInfo fail.", record, fieldList, ownerInfo);
            return cb();
        }
        beReportNum = fieldInfo.beReportedList.length || 0;
        //1. 发邮件
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_BE_ROBBED;
        //您在用户#0#的#0#球场的占领位置已被用户#0#抢夺，损失掉全部已获得的奖励
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][ownerInfo.resId];
        mailContent = mailContent.replace("#0#", ownerInfo.name);
        mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
        mailContent = mailContent.replace("#0#", atkInfo.name);
        self.sendEmail(robInfo.uid, mailId, mailContent, [], function (code) {
            //2. 清除数据
            fieldInfo.resId = 0;               //占领的训练场resId
            fieldInfo.occupyUid = "";          //占领的玩家Uid
            fieldInfo.occupyGid = "";          //占领的玩家的Gid
            fieldInfo.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
            fieldInfo.occupyTeamIndex = 3;
            fieldInfo.occupyFaceUrl = "";
            fieldInfo.name = "";
            fieldInfo.occupyTime = 0;          //占领开始时间
            //fieldInfo.searchList = [];       //搜索列表 (可能不需要)
            fieldInfo.ballFan = 0;             //占领时的球迷数
            fieldInfo.mainGroundLevel = 0;     //占领时的球场等级
            fieldInfo.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
            fieldInfo.beReportedList = [];     //被举报列表
            //记录
            if(record.roomUid) {
                self.addEventRecord(fieldInfo.recordList, record);
            }
            logger.debug("dealWithRobOccupier: clear data. fieldInfo", fieldInfo);
            cb();
        });
    };

    let footballGround, retCode = Code.OK;
    async.waterfall([
        function (callback) {
            //在线
            if(!!player){
                footballGround = player.footballGround;
                callback();
            }else {
                //离线
                self.getOfflinePlayerAndDoc(robInfo.uid, function(playerDoc, doc) {
                    footballGround = playerDoc.footballGround.toJSONforDB();
                    callback();
                });
            }
        },
        function (callback) {
            let fieldList = footballGround.groundMatch.occupyFieldList;
            let fieldInfo;
            for(let i=0,len=fieldList.length; i<len; i++) {
                if(fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                    fieldInfo = fieldList[i];
                    break;
                }
            }
            if(!fieldInfo) {
                logger.error("groundMatchService.dealWithRobOccupier 222 find fieldInfo fail.", record, fieldList, ownerInfo);
                retCode = Code.GROUND_MATCH.PARAM_ERR;
                return callback("find fieldInfo fail");
            }
            beReportNum = fieldInfo.beReportedList.length || 0;
            //有保护文书
            if(fieldInfo.protectEndTime > TimeUtils.now()) {
                retCode = Code.GROUND_MATCH.TEAM_IS_PROTECTED;
                return callback("is protect now");
            }
            //处理数据, 并保存
            if(record.scoreA > record.scoreB) {
                logger.debug("dealWithRobOccupier attacker win.", footballGround);
                _dealFunc(footballGround, function () {
                    if(!!player) {
                        player.saveBallGround();
                        callback(null);
                    }else {
                        playerService.playerDao.updateOne("footballGround", robInfo.uid, footballGround, function () {
                            callback(null);
                        });
                    }
                });
            }else {
                //记录，挑战者失败，只记录战斗数据, 不进行其他数据变更
                if(record.roomUid) {
                    logger.debug("dealWithRobOccupier attacker lose.");
                    self.addEventRecord(fieldInfo.recordList, record);
                    if(!!player) {
                        player.saveBallGround();
                        callback(null);
                    }else {
                        playerService.playerDao.updateOne("footballGround", robInfo.uid, footballGround, function () {
                            callback(null);
                        });
                    }
                }
            }
        },
        function (callback) {
            if(debugConfig.isTraceGroundMatch) {
                let msgObj = {
                    fieldList: footballGround.groundMatch.fieldList,
                    occupyFieldList: footballGround.groundMatch.occupyFieldList,
                };
                self.traceLogForDebugger("groundMatchRob, robInfo step2", msgObj);
            }
            callback();
            //logger.debug("groundMatchRob start step3: robInfo", footballGround.groundMatch);
            /* june
            let syncData;
            if(!!player) {
                syncData = footballGround.toJSONforDB();
            }else {
                syncData = footballGround;
            }
            //同步数据
            self.syncGroundToDatanode(robInfo.uid, syncData, robInfo.gid, function (code) {
                callback(code);
            })
            */
        }
    ], function (err) {
        cb(retCode, beReportNum);
    });
};

//处理抢夺发起者数据
groundMatchService.prototype.dealWithRobAttacker = function(record, player, atkInfo, now, robInfo,
                                                            cashCost, energyCost, ownerInfo, beReportNum, cb) {
    let syncOccupyInfo = {};
    let fieldInfo = player.footballGround.groundMatch.occupyFieldList[atkInfo.teamIndex];
    logger.debug("dealWithRobAttacker ownerInfo: ", ownerInfo);
    //取消所有坑位保护, 并同步到cache
    for(let i=0; i<3; i++) {
        let field1 = player.footballGround.groundMatch.fieldList[i];
        let field2 = player.footballGround.groundMatch.occupyFieldList[i];
        let protectInfo1 = this.calcFieldInProtectLeftTime(field1.protectType, field1.protectEndTime, now);
        let protectInfo2 = this.calcFieldInProtectLeftTime(field2.protectType, field2.protectEndTime, now);
        if(protectInfo1.type !== 1) {
            field1.protectEndTime = 0;
            field1.protectType = 0;
        }
        if(protectInfo2.type !== 1) {
            field2.protectEndTime = 0;
            field2.protectType = 0;
        }
    }

    //获胜
    if(record.scoreA > record.scoreB || !record.roomUid) {
        fieldInfo.resId = ownerInfo.resId;
        fieldInfo.occupyUid = ownerInfo.uid;           //占领的玩家Uid
        fieldInfo.occupyGid = ownerInfo.gid;           //占领的玩家的Gid
        fieldInfo.occupyTeamUid = ownerInfo.teamUid;   //占领队伍的teamUid (防守队伍)
        fieldInfo.occupyFaceUrl = ownerInfo.faceUrl;
        fieldInfo.name = ownerInfo.name;
        fieldInfo.protectEndTime = now + dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchSystemProtectTime) * 1000;        //保护开始时间
        fieldInfo.protectType = 1;                              //系统保护
        fieldInfo.occupyTime = now;          //占领开始时间
        fieldInfo.lastBeReportTime = 0;      //上次被举报时间 (计算CD)
        fieldInfo.ballFan = ownerInfo.ballFan;
        fieldInfo.mainGroundLevel = ownerInfo.mainGroundLevel;
        fieldInfo.occupyTeamIndex = ownerInfo.teamIndex;
        fieldInfo.teamName = atkInfo.teamName;
        //如果是掠夺可以获取占领奖励
        if(!!robInfo && robInfo.uid) {
            let passTime = Math.floor((now - robInfo.occupyTime) / 1000);
            if(passTime < 0) {
                passTime = 0;
            }
            //占领过的半小时数
            //奖励 (欧元, 信仰积分)
            let reward = this.calcPerFieldReward(ownerInfo.mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
                passTime, ownerInfo.ballFan, false, beReportNum);
            //发邮件 - 奖励走邮件
            let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_ROB_REWARD;
            //您成功抢夺了用户#0#在#0#的#0#所占领的位置，获得#0#已获得的全部奖励，请及时领取附件
            let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
            let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
            mailContent = mailContent.replace("#0#", robInfo.name);
            mailContent = mailContent.replace("#0#", ownerInfo.name);
            mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
            mailContent = mailContent.replace("#0#", robInfo.name);
            let attachList = [];
            if(reward.cash > 0) {
                attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: reward.cash});
            }
            if(reward.beliefNum > 0) {
                attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
            }
            if(reward.fans > 0) {
                //增加粉丝
                player.footballGround.addBallFans(reward.fans);
            }
            this.sendEmail(player.playerId, mailId, mailContent, attachList, function (code) {});
        }

        //同步数据
        syncOccupyInfo.occupyUid = player.playerId;
        syncOccupyInfo.occupyName = player.name;
        syncOccupyInfo.occupyTeamUid = fieldInfo.teamUid;
        syncOccupyInfo.occupyGid = this.app.getServerId();
        syncOccupyInfo.occupyTeamName = fieldInfo.teamName;
        syncOccupyInfo.occupyFaceUrl = player.faceUrl;
        syncOccupyInfo.occupyTime = now;
        syncOccupyInfo.protectType = fieldInfo.protectType;
        syncOccupyInfo.protectEndTime = fieldInfo.protectEndTime;
        syncOccupyInfo.str = atkInfo.str;
        syncOccupyInfo.attack = atkInfo.attack;
        syncOccupyInfo.defend = atkInfo.defend;
    }
    //2. 失败
    if(record.roomUid) {
        this.addEventRecord(fieldInfo.recordList, record);
        logger.debug("dealWithRobAttacker addEventRecord:", fieldInfo.recordList);
    }

    logger.debug("groundMatchRob start step4: attackerInfo: ", player.footballGround.groundMatch);
    if(debugConfig.isTraceGroundMatch) {
        let msgObj = {
            fieldList: player.footballGround.groundMatch.fieldList,
            occupyFieldList: player.footballGround.groundMatch.occupyFieldList,
        };
        this.traceLogForDebugger("groundMatchRob, attackerInfo step4", msgObj);
    }

    player.saveBallGround();
    //扣除精力和欧元
    player.subtractResource(commonEnum.PLAY_INFO.cash, cashCost);
    player.subtractResource(commonEnum.PLAY_INFO.energy, energyCost);
    player.save();
    player.updatePlayer();

    cb(Code.OK, syncOccupyInfo);
    /* june
    this.syncGroundToDatanode(player.playerId, player.footballGround.toJSONforDB(), this.app.getServerId(), function (code) {
        cb(Code.OK);
    })
    */
};

//记录
groundMatchService.prototype.addEventRecord = function(recordList, record) {
    if(!recordList) {
        recordList = [];
    }
    if(recordList.length >= dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchMaxRecordNum)) {
        recordList.shift();
    }
    recordList.push(record);
    logger.debug("addEventRecord recordList:", recordList.length);
};

groundMatchService.prototype.sendEmail = function (playerId, mailId, mailContent, attachList, cb) {
    let playerService = this.app.get("playerService");
    let mailInfo = {};
    let config = dataApi.allData.data["MailText"][mailId];
    mailInfo.recvPlayerId = playerId;
    mailInfo.title = config.Title;
    mailInfo.content = mailContent;
    mailInfo.attachList = attachList;
    playerService.sendEditMail(mailInfo, function (code) {
        logger.debug("groundMatchService sendEditMail finish. code:", code);
        cb(code);
    });
};

//7. 驱赶
groundMatchService.prototype.groundMatchDriveAway = function (playerId, teamIndex, clientRobName, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchDriveAway: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(teamIndex)) {
        logger.error('groundMatchService.groundMatchDriveAway: index from client error !', teamIndex);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    let fieldInfo = player.footballGround.groundMatch.fieldList[teamIndex];
    //没有被占领队伍或者防守队伍时，不能驱赶
    if(!fieldInfo || !fieldInfo.beOccupiedUid || !fieldInfo.teamUid) {
        logger.error('groundMatchService.groundMatchDriveAway: filedInfo error !', fieldInfo);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    /*
    if(fieldInfo.name !== clientRobName) {
        logger.debug("groundMatchDriveAway client data not same: ", fieldInfo, clientRobName);
        return cb(Code.GROUND_MATCH.CS_DATA_NOT_SAME);
    }
    */
    let self = this;
    let gid = this.app.getServerId();
    let session = {frontendId: gid};
    let record, now = TimeUtils.now();
    let rpcMsg = {
        home: player.playerId,
        homeGid: gid,
        homeTeamUid: fieldInfo.teamUid,
        away: fieldInfo.beOccupiedUid,
        awayGid: fieldInfo.beOccupiedGid,
        awayTeamUid: fieldInfo.beOccupiedTeamUid
    };
    let mainGroundLevel = player.footballGround.mainGround.get(playerId).Level;
    let ballFan = player.footballGround.ballFan;
    let result = {code: Code.OK};
    let beReportNum = 0;
    let ownerInfo = {uid: playerId, teamUid: fieldInfo.teamUid, ballFan: ballFan,
        mainGroundLevel: mainGroundLevel, teamIndex: teamIndex};

    let isNeedToSync = false;
    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.DRIVE_AWAY;
    let syncData = {uid: playerId, teamIndex: teamIndex};

    async.waterfall([
        function(callback) {
            self.searchAndLockOwnerField(playerId, teamIndex, function (code) {
                logger.debug("groundMatchDriveAway searchAndLockOwnerField: ", code);
                if(code !== Code.OK) {
                    result.code = code;
                    return callback(code);
                }
                callback();
            });
        },
        function(callback) {
            //保护时间内不可被驱赶
            if(gid === fieldInfo.beOccupiedGid) {
                self.dealWithDriveAwayCheckOccupierProtect(ownerInfo, fieldInfo.beOccupiedUid, function (code) {
                    logger.debug("groundMatchDriveAway dealWithDriveAwayCheckOccupierProtect 111: ", code);
                    result.code = code;
                    if(code === Code.OK) {
                        callback();
                    }else {
                        callback(code);
                    }
                })
            }else {
                self.app.rpc.game.entryRemote.dealWithDriveAwayCheckOccupierProtect(
                    {frontendId: gid, toServerId: fieldInfo.beOccupiedGid},
                    {ownerInfo: ownerInfo, occupierUid: fieldInfo.beOccupiedUid}, function (code) {
                        logger.debug("groundMatchDriveAway dealWithDriveAwayCheckOccupierProtect 222: ", code);
                        result.code = code;
                        if(code === Code.OK) {
                            callback();
                        }else {
                            callback(code);
                        }
                    })
            }
        },
        function(callback) {
            self.app.rpc.battle.battleRemote.pvpGroundMatchBattleReq(session, rpcMsg, function (code, ret) {
                if(code !== Code.OK) {
                    logger.error("rpc pvpGroundMatchBattleReq error: ", code);
                    result.code = code;
                    return callback(code);
                }
                record = {
                    time: now,
                    resId: fieldInfo.resId,
                    uidA: player.playerId,
                    nameA: player.name,
                    faceUrlA: player.faceUrl,
                    scoreA: ret.homeScore,
                    teamUidA: fieldInfo.teamUid,
                    gidA: gid,
                    uidB: fieldInfo.beOccupiedUid,
                    nameB: fieldInfo.name,
                    faceUrlB: fieldInfo.faceUrl,
                    scoreB: ret.awayScore,
                    teamUidB: fieldInfo.occupyTeamUid,
                    gidB: fieldInfo.beOccupiedGid,
                    roomUid: ret.roomUid
                };
                //记录战斗Uid
                result.code = Code.OK;
                result.roomUid = ret.roomUid;
                logger.debug("groundMatchDriveAway pvpGroundMatchBattleReq finish", ret.roomUid);
                if(ret.homeScore > ret.awayScore) {
                    beReportNum = 0;
                    isNeedToSync = true;
                    callback();
                }else {
                    //战败 - 记录
                    if (record.roomUid) {
                        self.addEventRecord(fieldInfo.recordList, record);
                    }
                    player.saveBallGround();
                    //跳出处理流，直接返回
                    callback(Code.OK);
                }
            })
        },
        function (callback) {
            //1. 占领者邮件及数据处理
            if(gid === fieldInfo.beOccupiedGid) {
                self.dealWithDriveAwayOccupier(ownerInfo, fieldInfo.beOccupiedUid, record, function (code, num) {
                    result.code = code;
                    logger.debug("groundMatchDriveAway dealWithDriveAwayOccupier 111", code, num);
                    if(code === Code.OK) {
                        beReportNum = num;
                        callback();
                    }else {
                        callback(code);
                    }
                })
            }else {
                self.app.rpc.game.entryRemote.dealWithDriveAwayOccupier({frontendId: gid, toServerId: fieldInfo.beOccupiedGid}, 
                    {ownerInfo: ownerInfo, occupierUid: fieldInfo.beOccupiedUid, record: record}, function (code, num) {
                        logger.debug("groundMatchDriveAway dealWithDriveAwayOccupier 222", code, num);
                        result.code = code;
                        if(code === Code.OK) {
                            beReportNum = num;
                            callback();
                        }else {
                            callback(code);
                        }
                    })
            }
        },
        function (callback) {
            //2. 发个人邮件及自己训练场数据
            let _dealWithOwnerFunc = function(cb1) {
                //1. 发邮件
                let passTime = Math.floor((now - fieldInfo.occupyStartTime) / 1000);
                if(passTime < 0) {
                    passTime = 0;
                }else if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
                    passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
                }
                let reward = self.calcPerFieldReward(mainGroundLevel || 1,
                    dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"], passTime,
                    ballFan || 0, false, beReportNum);
                //发邮件 - 奖励走邮件
                //您成功驱赶了用户#0#的占领，获得欧元#0#,成功挽回粉丝#0#人，请及时领取附件
                let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_DRIVE_SUCCESS;
                let attachList = [];
                //收益的70%
                if(reward.cash > 0) {
                    reward.cash = Math.floor(reward.cash * 70 / 100);
                    attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: reward.cash});
                }
                if(reward.beliefNum > 0) {
                    reward.beliefNum = Math.floor(reward.beliefNum * 70 / 100);
                    attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
                }
                let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
                mailContent = mailContent.replace("#0#", fieldInfo.name);
                mailContent = mailContent.replace("#0#", reward.cash);
                mailContent = mailContent.replace("#0#", reward.fans);
                self.sendEmail(player.playerId, mailId, mailContent, attachList, function (code) {
                    //2. 发完邮件, 再移除数据
                    fieldInfo.startTime = TimeUtils.now();   //开始自产时间
                    fieldInfo.beOccupiedUid = "";            //占领的玩家uid
                    fieldInfo.beOccupiedGid = "";            //占领的玩家gid
                    fieldInfo.beOccupiedTeamUid = "";        //占领的阵容uid
                    fieldInfo.beOccupiedTeamName = "";       //被占领
                    fieldInfo.name = "";                     //占领玩家的名字
                    fieldInfo.faceUrl = "";                  //占领玩家的头像
                    fieldInfo.formationResId = 0;            //阵型id
                    fieldInfo.attack = 0;                    //进攻值
                    fieldInfo.defend = 0;                    //防守值
                    fieldInfo.atkTactic = 0;                 //占领玩家的进攻战术
                    fieldInfo.defTactic = 0;                 //占领玩家的防守战术
                    fieldInfo.occupyStartTime = 0;           //占领开始时间
                    cb1();
                });
            };
            _dealWithOwnerFunc(function () {
                logger.debug("groundMatchDriveAway _dealWithOwnerFunc");
                if(result.roomUid) {
                    self.addEventRecord(fieldInfo.recordList, record);
                }
                player.saveBallGround();
                callback();
                /* june
                //保存及同步数据到cache datanode
                self.syncGroundToDatanode(playerId, player.footballGround.toJSONforDB(), gid, function (code) {
                    result.code = code;
                    callback();
                })
                */
            });
        },
        function (callback) {
            if(isNeedToSync) {
                self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function (code) {
                    callback();
                })
            }else {
                callback();
            }
        }
    ], function (err) {
        self.unlockOwnerField(playerId, teamIndex, function (code) {
            cb(result.code, result.roomUid);
        });
    });
};

groundMatchService.prototype.dealWithDriveAwayCheckOccupierProtect = function(ownerInfo, occupierUid, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(occupierUid);
    let now = TimeUtils.now();
    //在线
    if(!!player) {
        let fieldList = player.footballGround.groundMatch.occupyFieldList;
        let fieldInfo;
        for (let i = 0, len = fieldList.length; i < len; i++) {
            if (fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                fieldInfo = fieldList[i];
                break;
            }
        }
        logger.debug("dealWithDriveAwayCheckOccupierProtect fieldInfo 1111", fieldInfo);
        if (!fieldInfo) {
            return cb(Code.OK);
        }
        if(now < fieldInfo.protectEndTime) {
            logger.debug("dealWithDriveAwayCheckOccupierProtect the team is protected.");
            return cb(Code.GROUND_MATCH.TEAM_IS_PROTECTED);
        }
        cb(Code.OK);
    }else {
        this.getOfflinePlayerAndDoc(occupierUid, function (player, doc) {
            let fieldList = player.footballGround.groundMatch.occupyFieldList;
            let fieldInfo;
            for(let i=0,len=fieldList.length;i<len;i++) {
                if(fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                    fieldInfo = fieldList[i];
                    break;
                }
            }
            logger.debug("dealWithDriveAwayCheckOccupierProtect fieldInfo 22222", fieldInfo, fieldList, ownerInfo.teamIndex);
            if(!fieldInfo) {
                return cb(Code.OK);
            }
            if(now < fieldInfo.protectEndTime) {
                logger.debug("dealWithDriveAwayCheckOccupierProtect the team is protected offline.");
                return cb(Code.GROUND_MATCH.TEAM_IS_PROTECTED);
            }
            cb(Code.OK);
        });
    }

};

groundMatchService.prototype.dealWithDriveAwayOccupier = function(ownerInfo, occupierUid, record, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(occupierUid);
    let self = this, gid = this.app.getServerId();
    let now = TimeUtils.now();
    logger.debug("dealWithDriveAwayOccupier: ", ownerInfo, occupierUid, record);
    let _dealWithOccupierFunc = function(fieldInfo, cb1) {
        //1. 发邮件
        let passTime = Math.floor((now - fieldInfo.occupyTime) / 1000);
        if(passTime < 0) passTime = 0;
        if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
            passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
        }
        let reward = self.calcPerFieldReward(ownerInfo.mainGroundLevel || 1, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, ownerInfo.ballFan || 0, false, fieldInfo.beReportedList.length);
        //发邮件 - 奖励走邮件
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_DRIVE_AWAY;
        //您在用户#0#球场的占领已被驱赶，您的占领受益造成损失，本次占领共获得#0#欧元，#0#粉丝，请及时领取附件
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        let cash = Math.floor(reward.cash * 30 / 100);
        mailContent = mailContent.replace("#0#", cash);
        mailContent = mailContent.replace("#0#", fieldInfo.beReportedList.length);
        let attachList = [];
        if(cash > 0) {
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: cash});
        }
        if(reward.beliefNum > 0) {
            reward.beliefNum = Math.floor(reward.beliefNum * 30 / 100);
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
        }
        self.sendEmail(occupierUid, mailId, mailContent, attachList, function (code) {
            //2. 发完邮件, 再修改数据
            fieldInfo.resId = 0;               //占领的训练场resId
            fieldInfo.occupyUid = "";          //占领的玩家Uid
            fieldInfo.occupyGid = "";          //占领的玩家的Gid
            fieldInfo.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
            fieldInfo.occupyTeamIndex = 3;
            fieldInfo.occupyFaceUrl = "";
            fieldInfo.name = "";
            fieldInfo.occupyTime = 0;          //占领开始时间
            fieldInfo.ballFan = 0;             //占领时的球迷数
            fieldInfo.mainGroundLevel = 0;     //占领时的球场等级
            //fieldInfo.searchList = [];       //搜索列表 (可能不需要)
            fieldInfo.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
            fieldInfo.beReportedList = [];     //被举报列表
            cb1();
        });
    };

    //在线
    if(!!player) {
        let fieldList = player.footballGround.groundMatch.occupyFieldList;
        let fieldInfo;
        for(let i=0,len=fieldList.length;i<len;i++) {
            if(fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                fieldInfo = fieldList[i];
                break;
            }
        }
        logger.debug("dealWithDriveAwayOccupier fieldInfo 1111", fieldInfo);
        if(!fieldInfo) {
            return cb(Code.OK, 0);
        }
        _dealWithOccupierFunc(fieldInfo, function () {
            if(record.roomUid) {
                self.addEventRecord(fieldInfo.recordList, record);
            }
            player.saveBallGround();
            cb(Code.OK, (!fieldInfo) ? 0 : fieldInfo.beReportedList.length);
            /* june
            self.syncGroundToDatanode(occupierUid, player.footballGround.toJSONforDB(), gid, function (code) {
                cb(code, (!fieldInfo) ? 0 : fieldInfo.beReportedList.length);
            })
            */
        })
    }else {
        //离线
        self.getOfflinePlayerAndDoc(occupierUid, function (player, doc) {
            let fieldList = player.footballGround.groundMatch.occupyFieldList;
            let fieldInfo;
            for(let i=0,len=fieldList.length;i<len;i++) {
                if(fieldList[i].occupyTeamIndex == ownerInfo.teamIndex && fieldList[i].occupyUid === ownerInfo.uid) {
                    fieldInfo = fieldList[i];
                    break;
                }
            }
            logger.debug("dealWithDriveAwayOccupier fieldInfo 22222", fieldInfo, fieldList, ownerInfo.teamIndex);
            if(!fieldInfo) {
                return cb(Code.OK, 0);
            }
            _dealWithOccupierFunc(fieldInfo, function () {
                if(record.roomUid) {
                    self.addEventRecord(fieldInfo.recordList, record);
                }
                playerService.playerDao.updateOne("footballGround", occupierUid, player.footballGround.toJSONforDB(), function () {
                    cb(Code.OK, (!fieldInfo) ? 0 : fieldInfo.beReportedList.length);
                    /* june
                    self.syncGroundToDatanode(occupierUid, player.footballGround.toJSONforDB(), gid, function (code) {
                        cb(code, (!fieldInfo) ? 0 : fieldInfo.beReportedList.length);
                    })
                    */
                });
            });
        });
    }
};

//9. 撤离
groundMatchService.prototype.groundMatchLeaveAway = function (playerId, teamIndex, clientOwnerName, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchLeaveAway: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(teamIndex)) {
        logger.error('groundMatchService.groundMatchLeaveAway: checkTheIndexFromClient error !', teamIndex);
        cb(Code.GROUND_MATCH.PARAM_ERR);
        return;
    }
    let fieldInfo = player.footballGround.groundMatch.occupyFieldList[teamIndex];
    //是否存在被占领者和进攻队伍
    if(!fieldInfo || !fieldInfo.teamUid || !fieldInfo.occupyUid) {
        logger.debug("groundMatchService.groundMatchLeaveAway: no occupy team.", fieldInfo, player.footballGround.groundMatch.occupyFieldList);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    //占领小于30分钟不可撤离
    let now = TimeUtils.now();
    let passTime = Math.floor((now - fieldInfo.occupyTime) / 1000);
    if(passTime < 0) passTime = 0;
    if(passTime < dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchLeftInverval].Param) {
        logger.debug("groundMatchService.groundMatchLeaveAway: too short time to leave away", passTime, fieldInfo.occupyTime, now);
        return cb(Code.GROUND_MATCH.LEFT_TIME_TO_SHORT);
    }
    //先存储必要的占领者数据
    let occupyInfo = {
        uid: playerId,
        gid: this.app.getServerId(),
        mainGroundLevel: fieldInfo.mainGroundLevel,
        ballFan: fieldInfo.ballFan,
        teamUid: fieldInfo.teamUid,
        occupyTime: fieldInfo.occupyTime,
        name: player.name,
        beReportNum : 0
    };
    let ownerInfo = {
        uid: fieldInfo.occupyUid,
        gid: fieldInfo.occupyGid,
        index: fieldInfo.occupyTeamIndex
    };

    if(fieldInfo.name !== clientOwnerName) {
        logger.debug("groundMatchLeaveAway client owner name not same", fieldInfo, clientOwnerName);
        return cb(Code.GROUND_MATCH.CS_DATA_NOT_SAME);
    }

    if(debugConfig.isTraceGroundMatch) {
        let msgObj = {
            fieldInfo: fieldInfo,
            teamIndex: teamIndex,
            clientOwnerName: clientOwnerName
        };
        this.traceLogForDebugger("groundMatchLeaveAway, start occInfo step1", msgObj);
    }

    let self = this;
    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.LEAVE_AWAY;
    let syncData = {uid: ownerInfo.uid, teamIndex: ownerInfo.index};

    async.waterfall([
        function (callback) {
            self.searchAndLockOwnerField(ownerInfo.uid, ownerInfo.index, function (code) {
                if(code !== Code.OK) {
                    callback(code);
                }
                callback()
            });
        },
        function (callback) {
            //占领者处理
            self.dealWithLeaveAwayOccupier(playerId, self.app.getServerId(), teamIndex, passTime, function (code, num) {
                if(code !== Code.OK) {
                    return callback(code);
                }
                occupyInfo.beReportNum = num || 0;
                callback();
            });
        },
        function (callback) {
            //球场拥有者处理 -> 注意这里的占领者数据已被清除
            let myGid = self.app.getServerId();
            let session = {frontendId: myGid, toServerId: ownerInfo.gid};
            let rpcMsg = {
                ownerUid: ownerInfo.uid,
                ownerGid: ownerInfo.gid,
                occupyInfo: occupyInfo
            };
            logger.debug("dealWithLeaveAwayOwner now: ", rpcMsg);
            if(myGid === fieldInfo.occupyGid) {
                self.dealWithLeaveAwayOwner(rpcMsg.ownerUid, rpcMsg.ownerGid, occupyInfo, function () {
                    callback();
                })
            }else {
                self.app.rpc.game.entryRemote.dealWithLeaveAwayOwner(session, rpcMsg, function (code) {
                    if(code !== Code.OK) {
                        //return callback(code);
                    }
                    callback();
                });
            }
        },
        function (callback) {
            self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function (code) {
                callback();
            })
        }
    ], function (err) {
        logger.debug("groundMatchLeaveAway finish !");
        self.unlockOwnerField(ownerInfo.uid, ownerInfo.index, function () {
            if(!!err) {
                return cb(err);
            }
            cb(Code.OK);
        });
    });
};

//注备: 撤离和满时间后自动撤离是一样的处理
groundMatchService.prototype.dealWithLeaveAwayOccupier = function(occupierUid, occupierGid, teamIndex, passTime, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(occupierUid);

    let fieldInfo, syncData, now = TimeUtils.now(), self = this;
    let beReportNum = 0;

    let _dealWithFunc = function(fieldInfo, cb) {
        //1. 发邮件
        let passTime = Math.floor((now - fieldInfo.occupyTime) / 1000);
        if(passTime < 0) passTime = 0;
        if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
            passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
        }
        let reward = self.calcPerFieldReward(fieldInfo.mainGroundLevel || 1, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, fieldInfo.ballFan || 0, false, fieldInfo.beReportedList.length);
        //发邮件 - 奖励走邮件
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_OCCUPY_REWARD;
        //您的#0#在#0#的球场占领满#0#个小时，被举报#0#次，请通过附件领取奖励
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", fieldInfo.teamName);
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        let passHour = Math.floor(passTime / 1800) / 2;
        mailContent = mailContent.replace("#0#", passHour.toFixed(1));
        mailContent = mailContent.replace("#0#", fieldInfo.beReportedList.length);
        let attachList = [];
        if(reward.cash > 0) {
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: reward.cash});
        }
        if(reward.beliefNum > 0) {
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
        }
        self.sendEmail(occupierUid, mailId, mailContent, attachList, function (code) {
            //2. 发完邮件, 再修改数据
            fieldInfo.resId = 0;               //占领的训练场resId
            fieldInfo.occupyUid = "";          //占领的玩家Uid
            fieldInfo.occupyGid = "";          //占领的玩家的Gid
            fieldInfo.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
            fieldInfo.occupyTeamIndex = 3;
            fieldInfo.occupyFaceUrl = "";
            fieldInfo.name = "";
            fieldInfo.occupyTime = 0;          //占领开始时间
            fieldInfo.ballFan = 0;             //占领时的球迷数
            fieldInfo.mainGroundLevel = 0;     //占领时的球场等级
            //fieldInfo.searchList = [];       //搜索列表 (可能不需要)
            fieldInfo.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
            fieldInfo.beReportedList = [];     //被举报列表
            cb(reward.fans);
        });
    };

    async.waterfall([
        function (callback) {
            if(!!player){
                //在线
                fieldInfo = player.footballGround.groundMatch.occupyFieldList[teamIndex];
                if(!fieldInfo || !fieldInfo.teamUid || !fieldInfo.occupyUid) {
                    logger.debug("groundMatchService.dealWithLeaveAwayOccupier: no occupy team.", fieldInfo);
                    return callback(Code.GROUND_MATCH.PARAM_ERR);
                }
                beReportNum = fieldInfo.beReportedList.length;
                _dealWithFunc(fieldInfo, function (fans) {
                    player.footballGround.addBallFans(fans);
                    //syncData = player.footballGround.toJSONforDB();
                    player.saveBallGround();
                    callback(null);
                })
            }else {
                //离线
                self.getOfflinePlayerAndDoc(occupierUid, function (player, doc) {
                    fieldInfo = player.footballGround.groundMatch.occupyFieldList[teamIndex];
                    beReportNum = fieldInfo.beReportedList.length;
                    _dealWithFunc(fieldInfo, function (fans) {
                        player.footballGround.addBallFans(fans);
                        playerService.playerDao.updateOne("footballGround", occupierUid, player.footballGround.toJSONforDB(), function () {
                            //syncData = footballGroundDoc;
                            callback(null);
                        });
                    });
                });
            }
        },
        function (callback) {
            /* june
            //同步数据
            self.syncGroundToDatanode(occupierUid, syncData, occupierGid, function (code) {
                callback(code);
            })
            */
            callback();
        }
    ], function (err) {
            if(err) {
                return cb(err)
            }
            logger.debug("dealWithLeaveAwayOccupier: ", fieldInfo);
            cb(Code.OK, beReportNum);
        }
    )
};

groundMatchService.prototype.dealWithLeaveAwayOwner = function(ownerUid, ownerGid, occupyInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(ownerUid);

    let fieldInfo, syncData, now = TimeUtils.now(), self = this;
    logger.debug("dealWithLeaveAwayOwner ownerUid, ownerGid, occupyInfo: ", ownerUid, ownerGid, occupyInfo);

    let _dealWithFunc = function(fieldInfo, reward, passTime, cb) {
        //1. 发邮件
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_BE_OCCUPIED;
        //您的#0#被用户#0#占领满#0#个小时，您损失#0#欧元，粉丝叛离#0#人
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
        mailContent = mailContent.replace("#0#", occupyInfo.name);
        let passHour = Math.floor(passTime / 1800) / 2;
        mailContent = mailContent.replace("#0#", passHour.toFixed(1));
        mailContent = mailContent.replace("#0#", reward.cash);
        mailContent = mailContent.replace("#0#", reward.fans);
        self.sendEmail(ownerUid, mailId, mailContent, [], function (code) {
            //2. 发完邮件, 再修改数据
            //obj.battleLockTime = 0;                //战斗锁定
            fieldInfo.startTime = TimeUtils.now();   //开始自产时间
            //占领用户信息
            fieldInfo.beOccupiedUid = "";            //占领的玩家uid
            fieldInfo.beOccupiedGid = "";            //占领的玩家gid
            fieldInfo.beOccupiedTeamUid = "";        //占领的阵容uid
            fieldInfo.beOccupiedTeamName = "";       //被占领
            fieldInfo.name = "";                     //占领玩家的名字
            fieldInfo.faceUrl = "";                  //占领玩家的头像
            fieldInfo.formationResId = 0;            //阵型id
            fieldInfo.attack = 0;                    //进攻值
            fieldInfo.defend = 0;                    //防守值
            fieldInfo.atkTactic = 0;                 //占领玩家的进攻战术
            fieldInfo.defTactic = 0;                 //占领玩家的防守战术
            fieldInfo.occupyStartTime = 0;           //占领开始时间
            cb();
        });
    };

    async.waterfall([
            function (callback) {
                if(!!player){
                    //在线
                    let fieldList = player.footballGround.groundMatch.fieldList;
                    for(let i=0; i<fieldList.length; i++) {
                        if(fieldList[i].beOccupiedTeamUid === occupyInfo.teamUid) {
                            fieldInfo = fieldList[i];
                            break;
                        }
                    }
                    if(!fieldInfo || !fieldInfo.beOccupiedTeamUid) {
                        logger.debug("groundMatchService.dealWithLeaveAwayOwner: no occupy team.", fieldInfo);
                        return callback(Code.GROUND_MATCH.PARAM_ERR);
                    }
                    //占领时间
                    let passTime = Math.floor((now - occupyInfo.occupyTime) / 1000);
                    if(passTime < 0) passTime = 0;
                    if(passTime > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
                        passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
                    }
                    let reward = self.calcPerFieldReward(occupyInfo.mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
                        passTime, occupyInfo.ballFan, false, occupyInfo.beReportNum);
                    _dealWithFunc(fieldInfo, reward, passTime, function () {
                        //扣除球迷数
                        player.footballGround.ballFan -= reward.fans;
                        if (player.footballGround.ballFan < 0) player.footballGround.ballFan = 0;
                        syncData = player.footballGround.toJSONforDB();
                        logger.debug("dealWithLeaveAwayOwner online... finish", syncData.groundMatch);
                        player.saveBallGround();
                        callback(null);
                    })
                }else {
                    //离线
                    self.getOfflinePlayerAndDoc(ownerUid, function (player, doc) {
                        let fieldList = player.footballGround.groundMatch.fieldList;
                        logger.debug("dealWithLeaveAwayOwner getOfflinePlayerAndDoc fieldList:", fieldList, player.footballGround.groundMatch);
                        for(let i=0; i<fieldList.length; i++) {
                            if(fieldList[i].beOccupiedTeamUid === occupyInfo.teamUid) {
                                fieldInfo = fieldList[i];
                                break;
                            }
                        }
                        if(!fieldInfo) {
                            fieldInfo = fieldList[0];
                        }
                        //占领时间
                        let passTime = Math.floor((now - occupyInfo.occupyTime) / 1000);
                        if(passTime < 0) passTime = 0;
                        let reward = self.calcPerFieldReward(occupyInfo.mainGroundLevel || 1, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
                            passTime, occupyInfo.ballFan || 0, false, occupyInfo.beReportNum);
                        _dealWithFunc(fieldInfo, reward, passTime, function () {
                            //扣除球迷数
                            player.footballGround.ballFan -= reward.fans;
                            if (player.footballGround.ballFan < 0) player.footballGround.ballFan = 0;
                            let footballGroundDoc = player.footballGround.toJSONforDB();
                            playerService.playerDao.updateOne("footballGround", ownerUid, footballGroundDoc, function () {
                                syncData = footballGroundDoc;
                                logger.debug("dealWithLeaveAwayOwner offline... finish", syncData.groundMatch);
                                callback(null);
                            });
                        });
                    });
                }
            },
            function (callback) {
                if(debugConfig.isTraceGroundMatch) {
                    let msgObj = {
                        fieldList: syncData.groundMatch.fieldList,
                        occupyInfo: occupyInfo
                    };
                    self.traceLogForDebugger("groundMatchLeaveAway, onwer step3", msgObj);
                }
                /* june
                //同步数据
                self.syncGroundToDatanode(ownerUid, syncData, ownerGid, function (code) {
                    callback(code);
                })
                */
                callback();
            }
        ], function (err) {
            if(err) {
                return cb(err)
            }
            logger.debug("dealWithLeaveAwayOwner: ", fieldInfo);
            cb(Code.OK);
        }
    )
};

groundMatchService.prototype.getOfflinePlayerAndDoc = function(playerId, cb) {
    let dbList = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.heros, commonEnum.DB_NAME.teamFormation,
        commonEnum.DB_NAME.trainer, commonEnum.DB_NAME.footballGround];
    let playerService = this.app.get("playerService");
    playerService.playerDao.readPlayerTable(playerId, dbList, function (doc, isNew) {
        let player = new Player(playerId);
        player.initByDB(doc[commonEnum.DB_NAME.player]);
        player.heros.initByDB(doc[commonEnum.DB_NAME.heros]);
        player.teamFormations.initByDB(doc[commonEnum.DB_NAME.teamFormation]);
        player.footballGround.initByDB(doc[commonEnum.DB_NAME.footballGround]);
        player.trainer.initByDB(doc[commonEnum.DB_NAME.trainer]);
        player.checkFix();
        return cb(player, doc);
    });
};

//10. 举报
groundMatchService.prototype.groundMatchReport = function (playerId, ownerUid, teamUid, clientRobName, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchReport: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    //不能举报自己及自己被占领的用户
    if(playerId === ownerUid) {
        logger.debug("groundMatchReport can not report your.");
        return cb(Code.GROUND_MATCH.CANNOT_REPORT_YOURSELF);
    }
    //举报次数限制
    if(player.footballGround.groundMatch.reportNum <= 0) {
        return cb(Code.GROUND_MATCH.REPORT_NUM_FULL);
    }
    //举报时间间隔(半小时)
    let now = TimeUtils.now();
    if((now - player.footballGround.groundMatch.lastReportTime)/1000 <
        dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportInverval]) {
        return cb(Code.GROUND_MATCH.REPORT_TIME_INTERVAL_SHORT);
    }
    //获取举报用户
    let searchList = player.footballGround.groundMatch.searchList;
    let ownerGid, ownerTeamIndex;
    for(let i=0, len=searchList.length; i<len; i++) {
        if(searchList[i].ownerUid === ownerUid && teamUid === searchList[i].occupyTeamUid) {
            ownerGid = searchList[i].ownerGid;
            ownerTeamIndex = searchList[i].teamIndex;
        }
    }
    if(!ownerGid) {
        logger.debug("groundMatchReport not find ownerGid, param error.");
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }

    //举报用户
    let self = this;
    let myGid = self.app.getServerId();
    let session = {frontendId: myGid, toServerId: ownerGid};

    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.REPORT;
    let syncData = {uid: ownerUid, teamIndex: ownerTeamIndex};

    self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: ownerUid,
        collectionNames: [commonEnum.DB_NAME.player, commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
    /* june */
    // self.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache({frontendId: myGid}, {playerId: ownerUid, gid: null,
    //     collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0}, function(code, playerGid, playerDoc) {
            let groundInfo = playerDoc[commonEnum.DB_NAME.footballGround];
            let beReportInfo = {};
            let ownerInfo = {uid: ownerUid, gid: ownerGid, name: playerDoc[commonEnum.DB_NAME.player].name};
            let resId = 0;
            let occupyStartTime = 0;
            for(let i=0; i<3; i++) {
                if(teamUid === groundInfo.groundMatch.fieldList[i].beOccupiedTeamUid) {
                    beReportInfo.uid = groundInfo.groundMatch.fieldList[i].beOccupiedUid;
                    beReportInfo.gid = groundInfo.groundMatch.fieldList[i].beOccupiedGid;
                    beReportInfo.name = groundInfo.groundMatch.fieldList[i].name;
                    beReportInfo.teamUid = groundInfo.groundMatch.fieldList[i].beOccupiedTeamUid;
                    ownerInfo.teamIndex = i;
                    resId = groundInfo.groundMatch.fieldList[i].resId;
                    occupyStartTime = groundInfo.groundMatch.fieldList[i].occupyStartTime;
                    break;
                }
            }
            if(!beReportInfo.uid) {
                logger.debug("groundMatchService.groundMatchReport: cannot find uid", teamUid, ownerUid, groundInfo.groundMatch.fieldList);
                return cb(Code.GROUND_MATCH.PARAM_ERR);
            }

            /*
            if(beReportInfo.name !== clientRobName) {
                logger.debug("groundMatchReport client data not same.", beReportInfo, clientRobName);
                return cb(Code.GROUND_MATCH.CS_DATA_NOT_SAME);
            }*/

            let myGid = self.app.getServerId();
            let reporterInfo = {uid: player.playerId, gid: myGid, name: player.name};
            let rpcMsg = {beReportInfo: beReportInfo, reporterInfo: reporterInfo, ownerInfo: ownerInfo};
            if(myGid === beReportInfo.gid) {
                //1. 处理被举报用户
                self.dealWithReportDef(beReportInfo, reporterInfo, ownerInfo, function (code1, reportList) {
                    if(code1 !== Code.OK) {
                        return cb(code1);
                    }
                    syncData.reportList = reportList;
                    //2. 处理举报用户
                    self.dealWithReportAtk(player, ownerInfo.name, beReportInfo.name, function (code2) {
                        if(code2 !== Code.OK) {
                            return cb(code2);
                        }
                        //3. 处理拥有者
                        if(myGid === ownerInfo.gid) {
                            self.dealWithReportOwner(beReportInfo, reporterInfo, ownerInfo, function (code3) {
                                self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {
                                    cb(code3);
                                })
                            })
                        }else {
                            let ownerSession = {frontendId: myGid, toServerId: ownerInfo.gid};
                            self.app.rpc.game.entryRemote.dealWithReportOwner(ownerSession, rpcMsg, function (code3) {
                                self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {
                                    cb(code3);
                                })
                            })
                        }
                    });
                });
            }else {
                //1. 处理被举报用户
                let session = {frontendId: myGid, toServerId: beReportInfo.gid};
                self.app.rpc.game.entryRemote.dealWithReportDef(session, rpcMsg, function (code1, reportList) {
                    if(code1 !== Code.OK) {
                        return cb(code1);
                    }
                    syncData.reportList = reportList;
                    //2. 处理举报用户
                    self.dealWithReportAtk(player, ownerInfo.name, beReportInfo.name,function (code2) {
                        if(code2 !== Code.OK) {
                            return cb(code2);
                        }
                        //3. 处理拥有者
                        if(myGid === ownerInfo.gid) {
                            self.dealWithReportOwner(beReportInfo, reporterInfo, ownerInfo, function (code3) {
                                self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {
                                    cb(code3);
                                })
                            })
                        }else {
                            let ownerSession = {frontendId: myGid, toServerId: ownerInfo.gid};
                            self.app.rpc.game.entryRemote.dealWithReportOwner(ownerSession, rpcMsg, function (code3) {
                                self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function () {
                                    cb(code3);
                                })
                            })
                        }
                    });
                });
            }
        });
};

// 处理被举报用户
groundMatchService.prototype.dealWithReportDef = function(beReportInfo, reporterInfo, ownerInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(beReportInfo.uid);
    let self = this;
    
    let _sendMail = function () {
        //发邮件
        //有一位热心的球友向用户【#0#】举报了您占领球场的行为，去破案还是及时止损？
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_REPORT_OCCUPIER;
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        mailContent = mailContent.replace("#0#", ownerInfo.name);
        self.sendEmail(beReportInfo.uid, mailId, mailContent, [], function (code) {});
    };

    if(!!player){
        //1. 在线
        let occupyFieldList = player.footballGround.groundMatch.occupyFieldList;
        let fieldInfo;
        for(let i=0;i<3;i++) {
            if(occupyFieldList[i].teamUid === beReportInfo.teamUid) {
                fieldInfo = occupyFieldList[i];
                break;
            }
        }
        if(!fieldInfo) {
            logger.debug("groundMatchService.dealWithReportDef 111: cannot find beReportInfo", beReportInfo);
            return cb(Code.GROUND_MATCH.PARAM_ERR);
        }
        if(fieldInfo.beReportedList.length >= 3) {
            return cb(Code.GROUND_MATCH.REPORT_NUM_OVER);
        }

        let now = TimeUtils.now();
        //举报收益必须大于10W
        let mainGroundLevel = fieldInfo.mainGroundLevel || 1;
        let ballFan = fieldInfo.ballFan || 0;
        let passTime = Math.floor((now - fieldInfo.occupyTime)/ 1000);
        logger.debug("dealWithReportDef fieldInfo: ", fieldInfo);
        let reward = self.calcPerFieldReward(mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, ballFan, false, fieldInfo.beReportedList.length);
        if(reward.cash < dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportReward)) {
            return cb(Code.GROUND_MATCH.REPORT_REWARD_NOT_ENOUGH);
        }

        //记录举报者
        fieldInfo.beReportedList.push({uid: reporterInfo.uid, gid: reporterInfo.gid, name: reporterInfo.name, time: TimeUtils.now()});
        logger.debug("dealWithReportDef 111 fieldInfo: ", fieldInfo, reporterInfo, beReportInfo);
        _sendMail();
        player.saveBallGround();
        cb(Code.OK, fieldInfo.beReportedList);
        /* june
        //同步到cache
        self.syncGroundToDatanode(beReportInfo.uid, player.footballGround.toJSONforDB(), beReportInfo.gid,
            function (code) {
                cb(Code.OK);
            })
            */
    }else {
        //2. 离线
        let session = {frontendId: self.app.getServerId(), toServerId: beReportInfo.gid};
        self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: beReportInfo.uid,
            collectionNames: [commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
        /* june */
        // this.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache(session, {playerId: beReportInfo.uid, gid: beReportInfo.gid,
        //         collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0},
        //     function (code, playerGid, playerDoc) {
                let occupyFieldList = playerDoc[commonEnum.DB_NAME.footballGround].groundMatch.occupyFieldList;
                let fieldInfo;
                for(let i=0;i<3;i++) {
                    if(occupyFieldList[i].teamUid === beReportInfo.teamUid) {
                        fieldInfo = occupyFieldList[i];
                        break;
                    }
                }
                if(!fieldInfo) {
                    logger.debug("groundMatchService.dealWithReportDef 222: cannot find beReportInfo", beReportInfo);
                    return cb(Code.GROUND_MATCH.PARAM_ERR);
                }
                if(fieldInfo.beReportedList.length >= 3) {
                    return cb(Code.GROUND_MATCH.REPORT_NUM_OVER);
                }

                let now = TimeUtils.now();
                //举报收益必须大于10W
                let mainGroundLevel = fieldInfo.mainGroundLevel || 1;
                let ballFan = fieldInfo.ballFan || 0;
                let passTime = Math.floor((now - fieldInfo.occupyTime)/ 1000);
                let resId = fieldInfo.resId || 1;
                let reward = self.calcPerFieldReward(mainGroundLevel, dataApi.allData.data["GroundMatchField"][resId]["Train"],
                    passTime, ballFan, false, fieldInfo.beReportedList.length);
                if(reward.cash < dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportReward)) {
                    return cb(Code.GROUND_MATCH.REPORT_REWARD_NOT_ENOUGH);
                }

                //记录举报者
                fieldInfo.beReportedList.push({uid: reporterInfo.uid, gid: reporterInfo.gid, name: reporterInfo.name, time: TimeUtils.now()});
                _sendMail();
                logger.debug("dealWithReportDef 222 fieldInfo: ", fieldInfo, reporterInfo, beReportInfo);
                //保存数据
                playerService.playerDao.updateOne(commonEnum.DB_NAME.footballGround, beReportInfo.uid,
                    {groundMatch: playerDoc[commonEnum.DB_NAME.footballGround].groundMatch}, function () {
                        cb(Code.OK, fieldInfo.beReportedList);
                    /*
                    //同步到cache
                    self.syncGroundToDatanode(beReportInfo.uid, playerDoc[commonEnum.DB_NAME.footballGround], beReportInfo.gid,
                        function (code) {
                        cb(Code.OK);
                    })
                    */
                })
        })
    }
};

// 处理用户
groundMatchService.prototype.dealWithReportAtk = function(player, ownerName, beReporterName, cb) {
    //举报次数增加
    if(player.footballGround.groundMatch.reportNum < 1) {
        logger.error("groundMatchService.dealWithReportAtk: reportNum: ",player.footballGround.groundMatch.reportNum);
        return cb(Code.GROUND_MATCH.CANNOT_REPORT_YOURSELF);
    }
    player.footballGround.groundMatch.reportNum -= 1;
    player.footballGround.groundMatch.lastReportTime = TimeUtils.now();
    player.saveBallGround();

    let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_REPORT_REPORTER;
    //您成功向【#0#】举报了【#0#】对球场的占领，并偷偷获得了【#0#】欧元的好处费/手动微笑。
    let cash = dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchReportReward);
    let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
    mailContent = mailContent.replace("#0#", ownerName);
    mailContent = mailContent.replace("#0#", beReporterName);
    mailContent = mailContent.replace("#0#", cash);
    let attachList = [];
    if(cash > 0) {
        attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: cash});
    }
    this.sendEmail(player.playerId, mailId, mailContent, attachList, function (code) {
        cb(Code.OK);
    });
};

//处理举报球场拥有者
groundMatchService.prototype.dealWithReportOwner = function(beReportInfo, reporterInfo, ownerInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(ownerInfo.uid);
    let self = this;

    let _sendMail = function (resId) {
        //发邮件
        //热心球友【#0#】向您举报，您的【#0#】被用户【#0#】占领，家都被人占了，还不回去看看？
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_REPORT_OWNER;
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        mailContent = mailContent.replace("#0#", reporterInfo.name);
        let filedConfig = dataApi.allData.data["GroundMatchField"][resId];
        mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
        mailContent = mailContent.replace("#0#", beReportInfo.name);
        self.sendEmail(ownerInfo.uid, mailId, mailContent, [], function (code) {});
    };

    if(!!player){
        //1. 在线
        let fieldInfo = player.footballGround.groundMatch.fieldList[ownerInfo.teamIndex];
        if(!fieldInfo) {
            logger.debug("groundMatchService.dealWithReportOwner 111: cannot find owner", ownerInfo, reporterInfo, beReportInfo);
            return cb(Code.GROUND_MATCH.PARAM_ERR);
        }
        //记录举报者
        logger.debug("dealWithReportOwner 111 : ", fieldInfo, reporterInfo, beReportInfo, ownerInfo);
        //发邮件
        _sendMail(fieldInfo.resId);
        cb(Code.OK);
    }else {
        //2. 离线
        let session = {frontendId: self.app.getServerId(), toServerId: beReportInfo.gid};
        self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: beReportInfo.uid,
            collectionNames: [commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
        // let session = {frontendId: self.app.getServerId()};
        // this.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache(session, {playerId: beReportInfo.uid, gid: beReportInfo.gid,
        //         collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0},
        //     function (code, playerGid, playerDoc) {
                if(!playerDoc[commonEnum.DB_NAME.footballGround]) {
                    logger.debug("dealWithReportOwner getOtherPlayerDocByCache fail.", beReportInfo, ownerInfo);
                    return cb(Code.GROUND_MATCH.PARAM_ERR);
                }
                let fieldInfo = playerDoc[commonEnum.DB_NAME.footballGround].groundMatch.fieldList[ownerInfo.teamIndex];
                if(!fieldInfo) {
                    logger.debug("groundMatchService.dealWithReportOwner 222: cannot find owner", ownerInfo, reporterInfo, beReportInfo);
                    return cb(Code.GROUND_MATCH.PARAM_ERR);
                }
                //记录举报者
                logger.debug("dealWithReportOwner 222 : ", fieldInfo, reporterInfo, beReportInfo, ownerInfo);
                //发邮件
                _sendMail(fieldInfo.resId);
                cb(Code.OK);
            })
    }
};

groundMatchService.prototype.getMyGroundMatchCash = function (playerId, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.getMyGroundMatchCash: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    let cash = player.footballGround.calcOwnFieldTotalCash();
    if(cash > 0) {
        //自产时间重新计算
        let fieldList = player.footballGround.groundMatch.fieldList;
        for(let i=0; i<fieldList.length; i++) {
            if(fieldList[i].startTime >= 0 && fieldList[i].resId > 0) {
                fieldList[i].startTime = TimeUtils.now();
            }
        }
        player.saveBallGround();
        player.addResource(commonEnum.PLAY_INFO.cash, cash);
        player.updatePlayer();
        player.save();
    }
    cb(Code.OK);
};


// 排行榜: 1. 资产排行榜  2. 分级球场排行榜
groundMatchService.prototype.getGroundMatchToplist = function (playerId, type, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.getGroundMatchToplist: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }

    //校验参数
    let isTypeRight = false;
    for(let i in commonEnum.GROUND_MATCH_TOPLIST_TYPE) {
        if(type === commonEnum.GROUND_MATCH_TOPLIST_TYPE[i]) {
            isTypeRight = true;
            break;
        }
    }
    if(!isTypeRight) {
        logger.error("groundMatchService.getGroundMatchToplist: client param type error: ", type);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }

    //取排名数据
    this.app.rpc.datanode.dataNodeRemote.getGroundMatchToplist({frontendId: this.app.getServerId()},
        {type: type, playerId: playerId}, function (code, toplist, myRank) {
        return cb(code, toplist, myRank, type);
    })

};

//使用保护道具
groundMatchService.prototype.groundMatchProtect = function (playerId, itemId, teamIndex, teamType, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchProtect: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(teamIndex) || (teamType !== commonEnum.FORMATION_TYPE.GROUND_ATK
        && teamType !== commonEnum.FORMATION_TYPE.GROUND_DEF)) {
        logger.error("groundMatchService.groundMatchProtect: param err 1 ", teamIndex, teamType);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    let fieldList;
    if(teamType === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        fieldList = player.footballGround.groundMatch.fieldList;
    }else {
        fieldList = player.footballGround.groundMatch.occupyFieldList;
    }
    let fieldInfo = fieldList[teamIndex];
    if(!fieldInfo) {
        logger.error("groundMatchService.groundMatchProtect: param err 2 ", teamIndex, teamType);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    //配置表
    let itemConfig = dataApi.allData.data["Item"][itemId];
    if(!itemConfig) {
        logger.error("groundMatchService.groundMatchProtect: param err 3 ", itemId);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    //道具类型校验
    if(itemConfig.Type !== 5) {
        logger.error("groundMatchService.groundMatchProtect: param err 4 ", itemId);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    let now = TimeUtils.now();
    //1. 是否在系统保护中 (任何时间段都可以使用保护文书, 效果直接覆盖)
    let protectInfo = this.calcFieldInProtectLeftTime(fieldInfo.protectType, fieldInfo.protectEndTime, now);
    //使用道具
    let itemUid = player.item.getItemUidByResId(itemId);
    if(!itemUid) {
        logger.debug("groundMatchService.groundMatchProtect: player do not have the item", itemUid);
        return cb(Code.GROUND_MATCH.DO_NOT_HAVE_PROTECT_ITEM);
    }
    //删除道具
    player.bag.removeItem(itemUid, 1);
    fieldInfo.protectEndTime = now + itemConfig.ItemParameters * 1000;
    fieldInfo.protectType = itemId;
    player.saveBag();
    player.saveBallGround();
    player.updateBag();
    //同步数据到cache
    logger.debug("player.footballGround.toJSONforDB(): ", player.footballGround.toJSONforDB().groundMatch.fieldList);

    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.USE_PROTECT;
    let syncData = {};
    if(teamType === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        syncData.uid = playerId;
        syncData.teamIndex = teamIndex;
    }else {
        syncData.uid = fieldInfo.occupyUid;
        syncData.teamIndex = fieldInfo.occupyTeamIndex;
    }
    syncData.teamType = teamType;
    syncData.protectType = fieldInfo.protectType;
    syncData.protectEndTime = fieldInfo.protectEndTime;
    this.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function (code) {
        cb(Code.OK);
    });
    /* june
    this.syncGroundToDatanode(playerId, player.footballGround.toJSONforDB(), this.app.getServerId(), function () {
        cb(Code.OK);
    });
    */
};

groundMatchService.prototype.calcFieldInProtectLeftTime = function (protectType, protectEndTime, now) {
    //type: 0. 无保护 1. 系统保护 其他: 保护物品id
    logger.debug("calcFieldInProtectLeftTime :", protectType, protectEndTime, now);
    if(protectEndTime === 0) {
        return {type: 0, leftTime: 0};
    }
    if(now <= protectEndTime) {
        return {type: protectType, leftTime: Math.floor((protectEndTime - now) / 1000)};
    }else {
        return {type: 0, leftTime: 0};
    }
};

groundMatchService.prototype.checkGroundMatchTimeOver = function (player, cb) {
    let self = this;
    async.waterfall([
        function(callback) {
            //检查我的训练场
            self.dealWithMyFieldTimeOver(player, function () {
                callback()
            });
        },
        function(callback) {
            //检查占领
            self.dealWithMyOccupyTimeOver(player, function () {
                callback()
            });
        }
    ], function (err) {
        //检查是否可以刷新举报次数
        if(TimeUtils.isFreshTimeByHour(dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRefreshHour),
            player.footballGround.groundMatch.reportFreshTime)) {
            player.footballGround.groundMatch.reportNum = 10;
            player.footballGround.groundMatch.reportFreshTime = TimeUtils.now();
        }
        cb();
    });
};

//处理自己的训练场被他人占领到期
groundMatchService.prototype.dealWithMyFieldTimeOver = function (player, cb) {
    let fieldList = player.footballGround.groundMatch.fieldList;//自己球场列表
    let now = TimeUtils.now();
    let removeList = [];
    let self = this;
    let mainGroundLevel = player.footballGround.mainGround.get(player.playerId).Level;
    let ballFan = player.footballGround.ballFan;
    let myGid = this.app.getServerId();

    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.OCCUPY_TIME_OVER;
    let syncData = {uid: player.playerId};
    let isDataChange = false;

    async.waterfall([
        function (callback) {
            // logger.debug("dealWithMyFieldTimeOver 22222 fieldList: ", fieldList);
            let index = 0;
            async.eachSeries(fieldList, function (fieldInfo, cb1) {
                // logger.debug("dealWithMyFieldTimeOver 11111 fieldInfo: ", fieldInfo);
                if(fieldInfo.beOccupiedUid && fieldInfo.beOccupiedTeamUid) {//占领玩家uid和占领队伍uid
                    let passTime = Math.floor((now - fieldInfo.occupyStartTime) / 1000);
                    //判断是否达到占领时间
                    if(passTime < dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
                        index++;
                        return cb1();
                    }
                    // logger.debug("dealWithMyFieldTimeOver 3333 fieldInfo:", fieldInfo);
                    isDataChange = true;
                    syncData.teamIndex = index;
                    //待处理的占领队伍数据
                    removeList.push({uid: fieldInfo.beOccupiedUid, gid: fieldInfo.beOccupiedGid,
                        teamUid: fieldInfo.beOccupiedTeamUid, resId: fieldInfo.resId});
                    //发邮件
                    passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
                    let reward = self.calcPerFieldReward(mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
                        passTime, ballFan, false);
                    //您的#0#被用户#0#占领满#0#个小时，您损失#0#欧元，粉丝叛离#0#人
                    let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_BE_OCCUPIED;
                    let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
                    let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
                    mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
                    mailContent = mailContent.replace("#0#", fieldInfo.name);
                    let passHour = Math.floor(passTime / 1800) / 2;
                    mailContent = mailContent.replace("#0#", passHour.toFixed(1));
                    mailContent = mailContent.replace("#0#", reward.cash);
                    mailContent = mailContent.replace("#0#", reward.fans);
                    if(reward.fans > 0) {
                        player.footballGround.addBallFans(0-reward.fans);
                    }
                    syncData.ballFan = player.footballGround.ballFan;
                    self.sendEmail(player.playerId, mailId, mailContent, [], function (code) {
                        //2. 发完邮件, 再移除数据
                        fieldInfo.startTime = TimeUtils.now();   //开始自产时间
                        fieldInfo.beOccupiedUid = "";            //占领的玩家uid
                        fieldInfo.beOccupiedGid = "";            //占领的玩家gid
                        fieldInfo.beOccupiedTeamUid = "";        //占领的阵容uid
                        fieldInfo.beOccupiedTeamName = "";       //被占领
                        fieldInfo.name = "";                     //占领玩家的名字
                        fieldInfo.faceUrl = "";                  //占领玩家的头像
                        fieldInfo.formationResId = 0;            //阵型id
                        fieldInfo.attack = 0;                    //进攻值
                        fieldInfo.defend = 0;                    //防守值
                        fieldInfo.atkTactic = 0;                 //占领玩家的进攻战术
                        fieldInfo.defTactic = 0;                 //占领玩家的防守战术
                        fieldInfo.occupyStartTime = 0;           //占领开始时间
                        index++;
                        cb1();
                    });
                }else {
                    index++;
                    cb1();
                }
            }, function (err) {
                if(isDataChange) {
                    player.saveBallGround();
                    /* june
                    self.syncGroundToDatanode(player.playerId, player.footballGround.toJSONforDB(), myGid, function () {
                        callback()
                    })
                    */
                }
                /* june
                else {
                    callback();
                }*/
                callback();
            })
        },
        function (callback) {
            //处理占领者
            let myGid = self.app.getServerId();
            //去重
            let tmpMap = new Map();
            for(let i=0; i< removeList.length; i++) {
                if(tmpMap.has(removeList[i].uid)) {
                    removeList.splice(i, 1);
                    i--;
                }else {
                    tmpMap.set(removeList[i].uid, 1);
                }
            }
            logger.debug("after reduce removeList: ", removeList);
            async.eachSeries(removeList, function (occupyInfo, cb1) {
                if(myGid === occupyInfo.gid) {
                    self.dealWithCheckTimeOverOccupier(occupyInfo, function (code) {
                        cb1();
                    })
                }
                else {
                    //发送到对应gid去处理
                    self.app.rpc.game.entryRemote.dealWithCheckTimeOverOccupier({frontendId: myGid, toServerId: occupyInfo.gid},
                        {occupyInfo: occupyInfo}, function () {
                            cb1();
                    })
                }
            }, function(err) {
                callback();
            });
        }
    ], function (err) {
        logger.debug("dealWithMyFieldTimeOver finish err: ", err);
        //数据变更同步 -> 清除球场占领者数据, 个人球场保护时间清除
        if(isDataChange) {
            self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function (code) {
                cb()
            })
        }else {
            cb();
        }
    });
};

groundMatchService.prototype.dealWithCheckTimeOverOccupier = function (occupyInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(occupyInfo.uid);
    let self = this;
    let _dealWithFunc = function(fieldInfo, cb1) {
        //1. 发邮件
        let passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
        let reward = self.calcPerFieldReward(fieldInfo.mainGroundLevel || 1, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, fieldInfo.ballFan || 0, false);
        //发邮件 - 奖励走邮件
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_OCCUPY_REWARD;
        //您的#0#在#0#的球场占领满#0#个小时，被举报#0#次，请通过附件领取奖励
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", fieldInfo.teamName);
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        let passHour = Math.floor(passTime / 1800) / 2;
        mailContent = mailContent.replace("#0#", passHour.toFixed(1));
        mailContent = mailContent.replace("#0#", fieldInfo.beReportedList.length);
        let attachList = [];
        if (reward.cash > 0) {
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: reward.cash});
        }
        if (reward.beliefNum > 0) {
            attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
        }
        self.sendEmail(occupyInfo.uid, mailId, mailContent, attachList, function (code) {
            //2. 发完邮件, 再修改数据
            fieldInfo.resId = 0;               //占领的训练场resId
            fieldInfo.occupyUid = "";          //占领的玩家Uid
            fieldInfo.occupyGid = "";          //占领的玩家的Gid
            fieldInfo.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
            fieldInfo.occupyTeamIndex = 3;
            fieldInfo.occupyFaceUrl = "";
            fieldInfo.name = "";
            fieldInfo.occupyTime = 0;          //占领开始时间
            fieldInfo.ballFan = 0;             //占领时的球迷数
            fieldInfo.mainGroundLevel = 0;     //占领时的球场等级
            //fieldInfo.searchList = [];       //搜索列表 (可能不需要)
            fieldInfo.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
            fieldInfo.beReportedList = [];     //被举报列表
            cb1(reward.fans);
        });
    };
    if(!!player){
        //在线
        let fieldList = player.footballGround.groundMatch.occupyFieldList;
        let fieldInfo;
        for(let i=0,len=fieldList.length;i<len;i++) {
            if(fieldList[i].teamUid === occupyInfo.teamUid) {
                fieldInfo = fieldList[i];
                break;
            }
        }
        if(!fieldInfo) {
            logger.error("dealWithCheckTimeOverOccupier find fieldInfo err1: ", fieldList, occupyInfo);
            return cb();
        }
        logger.debug("dealWithCheckTimeOverOccupier online fieldInfo: ", fieldInfo);
        if(!fieldInfo.occupyUid || fieldInfo.occupyTeamIndex == 3) {
            logger.debug("dealWithCheckTimeOverOccupier online no occupy user to deal.");
            return cb();
        }
        _dealWithFunc(fieldInfo, function (fans) {
            player.footballGround.addBallFans(fans);
            player.saveBallGround();
            cb();
            /* june
            self.syncGroundToDatanode(occupyInfo.uid, player.footballGround.toJSONforDB(), occupyInfo.gid, function () {
                player.saveBallGround();
                cb();
            });
            */
        });
    }else {
        //离线
        let session = {frontendId: self.app.getServerId(), toServerId: occupyInfo.gid};
        self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: occupyInfo.uid,
            collectionNames: [commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
        /* june */
        // let session = {frontendId: this.app.getServerId()};
        // this.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache(session, {playerId: occupyInfo.uid, gid: occupyInfo.gid,
        //     collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0},
        //     function (code, playerGid, playerDoc) {
                let occupyFieldList = playerDoc[commonEnum.DB_NAME.footballGround].groundMatch.occupyFieldList;
                let fieldInfo;
                for(let i=0;i<3;i++) {
                    if(occupyFieldList[i].teamUid === occupyInfo.teamUid) {
                        fieldInfo = occupyFieldList[i];
                        break;
                    }
                }
                if(!fieldInfo) {
                    logger.error("dealWithCheckTimeOverOccupier find fieldInfo err2: ", occupyFieldList, occupyInfo);
                    return cb();
                }
                logger.debug("dealWithCheckTimeOverOccupier offline fieldInfo: ", fieldInfo);
                if(!fieldInfo.occupyUid || fieldInfo.occupyTeamIndex == 3) {
                    logger.debug("dealWithCheckTimeOverOccupier offline no occupy user to deal.");
                    return cb();
                }
                _dealWithFunc(fieldInfo, function (fans) {
                    playerDoc[commonEnum.DB_NAME.footballGround].ballFan += fans;
                    //入库
                    self.app.rpc.game.entryRemote.saveGroundData({frontendId: self.app.getServerId(), toServerId: occupyInfo.gid},
                        {playerId: occupyInfo.uid, doc: playerDoc[commonEnum.DB_NAME.footballGround]}, function (code) {
                            logger.debug("save code2: ", code);
                            cb();
                        })
                    /* june
                    self.syncGroundToDatanode(occupyInfo.uid, playerDoc[commonEnum.DB_NAME.footballGround], occupyInfo.gid, function () {
                        //入库
                        self.app.rpc.game.entryRemote.saveGroundData({frontendId: self.app.getServerId(), toServerId: occupyInfo.gid},
                            {playerId: occupyInfo.uid, doc: playerDoc[commonEnum.DB_NAME.footballGround]}, function (code) {
                                logger.debug("save code2: ", code);
                                cb();
                            })
                    })
                    */
                });
            });
    }
};

//处理我的占领 - 时间到期
groundMatchService.prototype.dealWithMyOccupyTimeOver = function (player, cb) {
    let fieldList = player.footballGround.groundMatch.occupyFieldList;
    let now = TimeUtils.now();
    let removeList = [];
    let self = this;
    //let mainGroundLevel = player.footballGround.mainGround.get(player.playerId).Level;
    let ballFan = player.footballGround.ballFan;
    let myGid = this.app.getServerId();
    async.waterfall([
        function (callback) {
            let isDataChange = false;
            //处理自己已占领的
            async.eachSeries(fieldList, function (fieldInfo, cb1) {
                logger.debug("dealWithMyOccupyTimeOver fieldInfo: ", fieldInfo);
                if(fieldInfo.occupyUid) {
                    let passTime = Math.floor((now - fieldInfo.occupyTime) / 1000);
                    logger.debug("dealWithMyOccupyTimeOver passTime: ", passTime);
                    //未到期
                    if(passTime < dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param) {
                        return cb1();
                    }
                    isDataChange = true;
                    //待处理的占领队伍数据
                    removeList.push({uid: fieldInfo.occupyUid, gid: fieldInfo.occupyGid, teamIndex: fieldInfo.occupyTeamIndex,
                        resId: fieldInfo.resId, robUid: player.uid, robTeamUid: fieldInfo.teamUid});
                    //发邮件
                    passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
                    let mainGroundLevel = fieldInfo.mainGroundLevel;
                    let reward = self.calcPerFieldReward(mainGroundLevel, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
                        passTime, ballFan, false);
                    //您的#0#在#0#的球场占领满#0#个小时，被举报#0#次，请通过附件领取奖励
                    let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_OCCUPY_REWARD;
                    let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
                    let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
                    mailContent = mailContent.replace("#0#", fieldInfo.teamName);
                    mailContent = mailContent.replace("#0#", fieldInfo.name);
                    let passHour = Math.floor(passTime / 1800) / 2;
                    mailContent = mailContent.replace("#0#", passHour.toFixed(1));
                    mailContent = mailContent.replace("#0#", fieldInfo.beReportedList.length);
                    let attachList = [];
                    if (reward.cash > 0) {
                        attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 1, Num: reward.cash});
                    }
                    if (reward.beliefNum > 0) {
                        attachList.push({ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, ResId: 16, Num: reward.beliefNum});
                    }
                    player.footballGround.addBallFans(reward.fans);
                    logger.debug("dealWithMyOccupyTimeOver removeList: ", removeList);
                    self.sendEmail(player.playerId, mailId, mailContent, attachList, function (code) {
                        //2. 发完邮件, 再修改数据
                        fieldInfo.resId = 0;               //占领的训练场resId
                        fieldInfo.occupyUid = "";          //占领的玩家Uid
                        fieldInfo.occupyGid = "";          //占领的玩家的Gid
                        fieldInfo.teamName = "";
                        fieldInfo.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
                        fieldInfo.occupyTeamIndex = 3;
                        fieldInfo.occupyFaceUrl = "";
                        fieldInfo.name = "";
                        fieldInfo.occupyTime = 0;          //占领开始时间
                        fieldInfo.ballFan = 0;             //占领时的球迷数
                        fieldInfo.mainGroundLevel = 0;     //占领时的球场等级
                        //fieldInfo.searchList = [];       //搜索列表 (可能不需要)
                        fieldInfo.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
                        fieldInfo.beReportedList = [];     //被举报列表
                        cb1();
                    });
                }else {
                    cb1();
                }
            }, function (err) {
                if(isDataChange) {
                    player.saveBallGround();
                    /* june
                    self.syncGroundToDatanode(player.playerId, player.footballGround.toJSONforDB(), myGid, function () {
                        callback()
                    })
                    */
                }
                callback();
                /* june
                else {
                    callback();
                }
                */
            })
        },
        function (callback) {
            //处理被占领者 - 拥有者
            let myGid = self.app.getServerId();
            async.eachSeries(removeList, function (ownerInfo, cb1) {
                if(myGid === ownerInfo.gid) {
                    self.dealWithCheckTimeOverOwner(ownerInfo, function (code) {
                        cb1();
                    })
                }
                else {
                    //发送到对应gid去处理
                    self.app.rpc.game.entryRemote.dealWithCheckTimeOverOwner({frontendId: myGid, toServerId: ownerInfo.gid},
                        {ownerInfo: ownerInfo}, function () {
                            cb1();
                    })
                }
            }, function (err) {
                callback();
            });
        }
    ], function (err) {
        logger.debug("dealWithMyFieldTimeOver finish err: ", err);
        cb();
    });
};

groundMatchService.prototype.dealWithCheckTimeOverOwner = function (ownerInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(ownerInfo.uid);
    let self = this;
    let syncType = commonEnum.GROUND_MATCH_SYNC_TYPE.OCCUPY_TIME_OVER;
    let syncData = {uid: ownerInfo.uid, teamIndex: ownerInfo.teamIndex};
    let _dealWithFunc = function(fieldInfo, cb1) {
        logger.debug("dealWithCheckTimeOverOwner fieldInfo 222: ", fieldInfo);
        //1. 发邮件
        let passTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param;
        let reward = self.calcPerFieldReward(fieldInfo.mainGroundLevel || 1, dataApi.allData.data["GroundMatchField"][fieldInfo.resId]["Train"],
            passTime, fieldInfo.ballFan || 0, false);
        //发邮件 - 奖励走邮件
        //您的#0#被用户#0#占领满#0#个小时，您损失#0#欧元，粉丝叛离#0#人
        let mailId = commonEnum.MAIL_TRANSLATE_CONTENT.GROUND_MATCH_BE_OCCUPIED;
        let mailContent = dataApi.allData.data["MailText"][mailId].Result1;
        let filedConfig = dataApi.allData.data["GroundMatchField"][fieldInfo.resId];
        mailContent = mailContent.replace("#0#", commonEnum.GROUND_MATCH_FIELD_DETAIL_NAME[filedConfig.Train]);
        mailContent = mailContent.replace("#0#", fieldInfo.name);
        let passHour = Math.floor(passTime / 1800) / 2;
        mailContent = mailContent.replace("#0#", passHour.toFixed(1));
        mailContent = mailContent.replace("#0#", reward.cash);
        mailContent = mailContent.replace("#0#", reward.fans);
        self.sendEmail(ownerInfo.uid, mailId, mailContent, [], function (code) {
            //2. 发完邮件, 再移除数据
            fieldInfo.startTime = TimeUtils.now();   //开始自产时间
            fieldInfo.beOccupiedUid = "";            //占领的玩家uid
            fieldInfo.beOccupiedGid = "";            //占领的玩家gid
            fieldInfo.beOccupiedTeamUid = "";        //占领的阵容uid
            fieldInfo.beOccupiedTeamName = "";       //被占领
            fieldInfo.name = "";                     //占领玩家的名字
            fieldInfo.faceUrl = "";                  //占领玩家的头像
            fieldInfo.formationResId = 0;            //阵型id
            fieldInfo.attack = 0;                    //进攻值
            fieldInfo.defend = 0;                    //防守值
            fieldInfo.atkTactic = 0;                 //占领玩家的进攻战术
            fieldInfo.defTactic = 0;                 //占领玩家的防守战术
            fieldInfo.occupyStartTime = 0;           //占领开始时间
            cb1(reward.fans);
        });
    };
    if(!!player){
        //在线
        let fieldList = player.footballGround.groundMatch.fieldList;
        let fieldInfo;
        for(let i=0,len=fieldList.length;i<len;i++) {
            if(i == ownerInfo.teamIndex) {
                fieldInfo = fieldList[i];
                break;
            }
        }
        if(!fieldInfo) {
            logger.error("dealWithCheckTimeOverOwner find fieldInfo err1: ", fieldList, ownerInfo);
            return cb();
        }
        logger.debug("dealWithCheckTimeOverOwner fieldInfo 111: ", fieldInfo);
        if(!fieldInfo.beOccupiedUid) {
            logger.debug("already send online.");
            return cb();
        }
        //异常数据保护
        if(fieldInfo.beOccupiedUid !== ownerInfo.robUid && fieldInfo.beOccupiedTeamUid !== ownerInfo.robTeamUid) {
            logger.debug("the data is not right. occupy data wrong.", fieldInfo.beOccupiedUid, ownerInfo.robUid,
                fieldInfo.beOccupiedTeamUid, ownerInfo.robTeamUid);
            return cb();
        }
        _dealWithFunc(fieldInfo, function (fans) {
            player.footballGround.addBallFans(0-fans);
            player.saveBallGround();
            syncData.ballFan = player.footballGround.ballFan;
            self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function(code) {
                cb();
            });
            /* june
            self.syncGroundToDatanode(ownerInfo.uid, player.footballGround.toJSONforDB(), ownerInfo.gid, function () {
                cb();
            });
            */
        });
    }else {
        //离线
        let session = {frontendId: self.app.getServerId(), toServerId: ownerInfo.gid};
        self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, {playerId: ownerInfo.uid,
            collectionNames: [commonEnum.DB_NAME.footballGround]}, function(code, playerDoc) {
        /* june */
        // let session = {frontendId: this.app.getServerId()};
        // this.app.rpc.datanode.dataNodeRemote.getOtherPlayerDocByCache(session, {playerId: ownerInfo.uid, gid: ownerInfo.gid,
        //         collectionNames: [commonEnum.DB_NAME.footballGround], syncDelayTime: 0},
        //     function (code, playerGid, playerDoc) {
                let fieldList = playerDoc[commonEnum.DB_NAME.footballGround].groundMatch.fieldList;
                let fieldInfo;
                for(let i=0;i<3;i++) {
                    if(i == ownerInfo.teamIndex) {
                        fieldInfo = fieldList[i];
                        break;
                    }
                }
                if(!fieldInfo) {
                    logger.error("dealWithCheckTimeOverOccupier find fieldInfo err2: ", fieldList, ownerInfo);
                    return cb();
                }
                logger.debug("dealWithCheckTimeOverOwner fieldInfo 333: ", fieldInfo);
                if(!fieldInfo.beOccupiedUid) {
                    logger.debug("already send offline.");
                    return cb();
                }
                //异常数据保护
                if(fieldInfo.beOccupiedUid !== ownerInfo.robUid && fieldInfo.beOccupiedTeamUid !== ownerInfo.robTeamUid) {
                    logger.debug("the data is not right. occupy data wrong.", fieldInfo.beOccupiedUid, ownerInfo.robUid,
                        fieldInfo.beOccupiedTeamUid, ownerInfo.robTeamUid);
                    return cb();
                }
                _dealWithFunc(fieldInfo, function (fans) {
                    playerDoc[commonEnum.DB_NAME.footballGround].ballFan -= fans;
                    if(playerDoc[commonEnum.DB_NAME.footballGround].ballFan < 0) playerDoc[commonEnum.DB_NAME.footballGround].ballFan = 0;
                    syncData.ballFan = playerDoc[commonEnum.DB_NAME.footballGround].ballFan;
                    self.syncGroundMatchDataToDatanode(syncData.uid, syncData.teamIndex, syncType, syncData, function(code) {
                        //入库
                        self.app.rpc.game.entryRemote.saveGroundData({frontendId: self.app.getServerId(), toServerId: ownerInfo.gid},
                            {playerId: ownerInfo.uid, doc: playerDoc[commonEnum.DB_NAME.footballGround]}, function (code) {
                                logger.debug("save code1: ", code);
                                cb();
                            })
                    });
                    /* june
                    self.syncGroundToDatanode(ownerInfo.uid, playerDoc[commonEnum.DB_NAME.footballGround], ownerInfo.gid, function () {

                    });
                    */
                });
            });
    }
};

groundMatchService.prototype.groundMatchGetRecord = function (playerId, teamIndex, teamType, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('groundMatchService.groundMatchGetRecord: player not exist !', playerId);
        cb(Code.FAIL);
        return;
    }
    if(!this.checkTheIndexFromClient(teamIndex) || (teamType !== commonEnum.FORMATION_TYPE.GROUND_ATK
        && teamType !== commonEnum.FORMATION_TYPE.GROUND_DEF)) {
        logger.error("groundMatchService.groundMatchGetRecord: param err 1 ", teamIndex, teamType);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }
    let fieldList;
    if(teamType === commonEnum.FORMATION_TYPE.GROUND_DEF) {
        fieldList = player.footballGround.groundMatch.fieldList;
    }else {
        fieldList = player.footballGround.groundMatch.occupyFieldList;
    }
    let fieldInfo = fieldList[teamIndex];
    if(!fieldInfo) {
        logger.error("groundMatchService.groundMatchGetRecord: param err 2 ", teamIndex, teamType);
        return cb(Code.GROUND_MATCH.PARAM_ERR);
    }

    logger.debug("groundMatchGetRecord: ", fieldInfo);
    for(let i=0, len=fieldInfo.recordList.length; i<len; i++) {
        if(!fieldInfo.recordList[i].nameB) {
            fieldInfo.recordList[i].nameB = "";
        }
    }
    return cb(Code.OK, fieldInfo.recordList);
};

groundMatchService.prototype.searchAndLockOwnerField = function (ownerUid, teamIndex, cb) {
    let gid = this.app.getServerId();
    let self = this;
    //到datanode online上去请求全局锁信息
    self.app.rpc.datanode.dataNodeRemote.searchAndLockOwnerField({frontendId: gid}, {ownerUid: ownerUid, teamIndex: teamIndex}, function (code) {
        cb(code);
    })
};

groundMatchService.prototype.unlockOwnerField = function (ownerUid, teamIndex, cb) {
    let gid = this.app.getServerId();
    let self = this;
    //到datanode online上去请求全局锁信息
    self.app.rpc.datanode.dataNodeRemote.unlockOwnerField({frontendId: gid}, {ownerUid: ownerUid, teamIndex: teamIndex}, function (code) {
        cb(code);
    })
};

groundMatchService.prototype.checkOwnerAndSearchData = function (ownerUid, teamIndex, ownerGid, robInfo, cb) {
    let gid = this.app.getServerId();
    let self = this;
    if(gid === ownerGid) {
        self.dealWithCheckOwnerInfo(ownerUid, teamIndex, robInfo, function (code) {
            cb(code);
        })
    }else {
        self.app.rpc.game.entryRemote.dealWithCheckOwnerInfo({frontendId: gid, toServerId: ownerGid},
            {ownerUid: ownerUid, teamIndex: teamIndex, robInfo: robInfo}, function (code) {
                cb(code);
            })
    }
};

groundMatchService.prototype.dealWithCheckOwnerInfo = function (ownerUid, teamIndex, robInfo, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(ownerUid);
    robInfo.uid = robInfo.uid || "";
    robInfo.teamUid = robInfo.teamUid || "";
    if(!!player){
        //in cache
        let fieldInfo = player.footballGround.groundMatch.fieldList[teamIndex];
        if(!fieldInfo) {
            logger.debug("dealWithCheckOwnerInfo online fieldInfo is null");
            return cb(Code.GROUND_MATCH.FIELD_IS_LOCK);
        }
        if(fieldInfo.beOccupiedUid !== robInfo.uid || fieldInfo.beOccupiedTeamUid !== robInfo.teamUid) {
            logger.debug("dealWithCheckOwnerInfo online check fail.", fieldInfo, robInfo);
            return cb(Code.GROUND_MATCH.FIELD_IS_LOCK);
        }
        cb(Code.OK);
    }else {
        //out cache
        this.getOfflinePlayerAndDoc(ownerUid, function (player, doc) {
            let fieldInfo = player.footballGround.groundMatch.fieldList[teamIndex];
            if(!fieldInfo) {
                logger.debug("dealWithCheckOwnerInfo offline fieldInfo is null");
                return cb(Code.GROUND_MATCH.FIELD_IS_LOCK);
            }
            if(fieldInfo.beOccupiedUid !== robInfo.uid || fieldInfo.beOccupiedTeamUid !== robInfo.teamUid) {
                logger.debug("dealWithCheckOwnerInfo offline check fail.", fieldInfo, robInfo);
                return cb(Code.GROUND_MATCH.FIELD_IS_LOCK);
            }
            cb(Code.OK);
        });
    }
};




//根据事件类型同步对应的数据
groundMatchService.prototype.syncGroundMatchDataToDatanode = function (ownerUid, teamIndex, syncType, syncData, cb) {
    let session = {frontendId: this.app.getServerId()};
    let msg = {uid: ownerUid, teamIndex: teamIndex, syncType: syncType, syncData: syncData};
    this.app.rpc.datanode.dataNodeRemote.syncGroundMatchDataToDatanode(session, msg, function (code) {
        cb(code);
    });
};


/*
//收集球场拥有者数据需同步数据
groundMatchService.prototype.makeSyncFieldObj = function (resId, ) {

};

groundMatchService.prototype.syncFieldInfoToDatanode = function(playerId, teamIndex, fieldInfo, cb) {
    let session = {frontendId: this.app.getServerId()};
    let msg = {uid: playerId, teamIndex: teamIndex, fieldInfo: fieldInfo};
    this.app.rpc.datanode.dataNodeRemote.syncFieldInfoToDatanode(session, msg, function (code) {
        cb(code);
    });
};

groundMatchService.prototype.syncClearOccupyInfo = function(playerId, teamIndex, cb) {
    let session = {frontendId: this.app.getServerId()};
    let msg = {uid: playerId, teamIndex: teamIndex};
    this.app.rpc.datanode.dataNodeRemote.syncClearOccupyInfo(session, msg, function (code) {
        cb(code);
    });
};

groundMatchService.prototype.makeSyncOccupyObj = function (robInfo, ownerInfo, teamIndex, occupyInfo) {

};
*/
