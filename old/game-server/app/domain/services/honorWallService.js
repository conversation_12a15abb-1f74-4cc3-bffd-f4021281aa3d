/**
 * Idea and Persist
 * Created by <PERSON> on 2020/7/1.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var calcUtils = require('../../util/calc');

var async = require('async');
var dataApi = require('../../util/dataApi');
var Constant = require("../../../../shared/constant");
var commonEnum = require('../../../../shared/enum');
var debugConfig = require('../../../config/debugConfig');
var clusterConfig = require('../../../config/cluster');

module.exports.create = function(app, dbclient)
{
    return new honorWallService(app, dbclient);
};
//荣誉墙
let honorWallService = function(app, dbclient, cb)
{
    EventEmitter.call(this);
    this.app = app;
};

util.inherits(honorWallService, EventEmitter);


//返回客户端信息
honorWallService.prototype.getHonorWallInfoTOClient = function (playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.error('honorWallService.getHonorWallInfoTOClient: player not exist !', playerId);
        cb(Code.FAIL, "", 0, {}, {}, {}, {}, {}, []);
        return;
    }
    let uid = msg.uid;
    //自己的数据
    if(!!uid && uid === playerId)
    {
        let peakMatchData = player.honorWall.getPeakHonorInfo();
        let chairmanMatchData = player.honorWall.getChairmanHonorInfo();
        let beliefMatchData = player.honorWall.getBeliefHonorInfo();
        let leagueData = player.honorWall.getLeagueHonorInfo();
        let dqCupMatchData = player.honorWall.getDQCupHonorInfo();
        let taskList = player.honorWall.getHonorTaskList();
        cb(Code.OK, player.honorWall.title, player.honorWall.integral, peakMatchData, chairmanMatchData, beliefMatchData, leagueData, dqCupMatchData, taskList, player.name);
        return;
    }
    //别人的数据
    let findDbName = new Array(commonEnum.DB_NAME.player, commonEnum.DB_NAME.honorWall);
    playerService.getOtherPlayer(uid, findDbName, function (otherPlayer) {
        if (!otherPlayer) {
            logger.error("getHonorWallInfoTOClient: getOtherPlayer failed", playerId);
            cb(Code.FAIL, "", 0, {}, {}, {}, {}, {}, []);
        }
        let peakMatchData = otherPlayer.honorWall.getPeakHonorInfo();
        let chairmanMatchData = otherPlayer.honorWall.getChairmanHonorInfo();
        let beliefMatchData = otherPlayer.honorWall.getBeliefHonorInfo();
        let leagueData = otherPlayer.honorWall.getLeagueHonorInfo();
        let dqCupMatchData = otherPlayer.honorWall.getDQCupHonorInfo();
        let taskList = otherPlayer.honorWall.getHonorTaskList();
        cb(Code.OK, otherPlayer.honorWall.title, otherPlayer.honorWall.integral, peakMatchData, chairmanMatchData, beliefMatchData, leagueData, dqCupMatchData, taskList, otherPlayer.name);
        return;
    });
};
//领取任务奖励
honorWallService.prototype.getHonorTaskReward = function (playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if (!player) {
        logger.debug('honorWallService getHonorTaskReward player not exist !');
        cb(Code.FAIL, 0);
        return;
    }

    let resId = msg.resId;
    logger.debug("----------- getHonorTaskReward ------------------");
    let ret = player.honorWall.getHonorTaskReward(resId);
    if (ret !== Code.OK) {
        cb(ret, resId);
        return;
    }

    player.save();
    player.saveHonorWall();
    player.saveItem();
    player.saveBag();
    player.updateBag();
    cb(Code.OK, resId);
};

//更新荣誉墙参加次数
honorWallService.prototype.updateHonorWallJoinNum = function (msg, cb) {
    let playerList = msg.playerList;
    let type = msg.type;
    let self = this;
    async.eachSeries(playerList, function (obj, callback) {
        let playerId = obj.uid;
        let playerService = self.app.get("playerService");
        let player = playerService.getPlayer(playerId);
        if(!!player && player.isOnline(player))//在线
        {
            player.honorWall.updateHonorWallJoinNum(type);
            player.saveHonorWall();
            callback(null);
        }
        else//离线
        {
            let findDbName = new Array(commonEnum.DB_NAME.player, commonEnum.DB_NAME.honorWall);
            playerService.getOtherPlayer(playerId, findDbName, function (otherPlayer) {
                if (!otherPlayer) {
                    logger.error("updateHonorWallJoinNum: getOtherPlayer failed", playerId);
                    callback(null);
                    return;
                }
                otherPlayer.honorWall.updateHonorWallJoinNum(type);
                playerService.playerDao.updateOne("honorWall", playerId, otherPlayer.honorWall.toJSONforDB(), function (err) {
                    callback(null);
                });
            });
        }
    }, function (err) {
        cb(Code.OK);
    });
};

//更新荣誉数据
honorWallService.prototype.updateHonorWallData = function (playerList, type, cb) {
    let self = this;
    async.eachSeries(playerList, function (data, callback) {
        let playerId = data.uid;
        let playerService = self.app.get("playerService");
        let player = playerService.getPlayer(playerId);
        if(!!player && player.isOnline())//在线
        {
            switch (type) {
                case commonEnum.HONOR_WALL_TYPE.PEAK:
                    player.honorWall.updatePeakHonorRank(data.seasonId, data.rank);
                    break;
                case commonEnum.HONOR_WALL_TYPE.CHAIRMAN:
                    player.honorWall.updateChairmanHonorRank(data.seasonId, data.beliefId, data.rank, data.pos);
                    break;
                case commonEnum.HONOR_WALL_TYPE.BELIEF:
                    player.honorWall.updateBeliefHonorRank(data.seasonId, data.win, data.live, data.attack, data.defend);
                    break;
                case commonEnum.HONOR_WALL_TYPE.LEAGUE:
                    player.honorWall.updateLeagueHonorRank(data.seasonId, data.typeId, data.rank);
                    break;
                case commonEnum.HONOR_WALL_TYPE.DQCUP:
                    player.honorWall.updateDQCupHonorRank(data.seasonId, data.type, data.rank);
                    break;
            }
            //检查参赛次数是否错误
            player.honorWall.checkJoinNumAndRepair();
            player.saveHonorWall();
            callback(null);
        }
        else//离线
        {
            let findDbName = new Array(commonEnum.DB_NAME.player, commonEnum.DB_NAME.honorWall);
            playerService.getOtherPlayer(playerId, findDbName, function (otherPlayer) {
                if (!otherPlayer) {
                    logger.error("updateHonorWallJoinNum: getOtherPlayer failed", playerId, type);
                    callback(null);
                    return;
                }
                switch (type) {
                    case commonEnum.HONOR_WALL_TYPE.PEAK:
                        otherPlayer.honorWall.updatePeakHonorRank(data.seasonId, data.rank);
                        break;
                    case commonEnum.HONOR_WALL_TYPE.CHAIRMAN:
                        otherPlayer.honorWall.updateChairmanHonorRank(data.seasonId, data.beliefId, data.rank, data.pos);
                        break;
                    case commonEnum.HONOR_WALL_TYPE.BELIEF:
                        otherPlayer.honorWall.updateBeliefHonorRank(data.seasonId, data.win, data.live, data.attack, data.defend);
                        break;
                    case commonEnum.HONOR_WALL_TYPE.LEAGUE:
                        otherPlayer.honorWall.updateLeagueHonorRank(data.seasonId, data.typeId, data.rank);
                        break;
                    case commonEnum.HONOR_WALL_TYPE.DQCUP:
                        otherPlayer.honorWall.updateDQCupHonorRank(data.seasonId, data.type, data.rank);
                        break;
                }
                //检查参赛次数是否错误
                otherPlayer.honorWall.checkJoinNumAndRepair();
                playerService.playerDao.updateOne("honorWall", playerId, otherPlayer.honorWall.toJSONforDB(), function (err) {
                    callback(null);
                });
            });
        }
    }, function (err) {
        cb(Code.OK);
    });
};


