/**
 * Idea and Persist
 * Created by June on 2016/11/16.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');

module.exports.create = function(app, dbclient){
    return new TestService(app, dbclient);
};

var TestService = function(app, dbclient){
    EventEmitter.call(this);
    this.app = app;
    this.db = dbclient;
};

util.inherits(TestService, EventEmitter);