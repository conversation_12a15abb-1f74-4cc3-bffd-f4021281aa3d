/**
 * Created by shine on 2015/4/15.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var async = require('async');
var Code = require('../../../shared/code');
var utils = require('../util/utils');

module.exports.create = function(db){
	return new MatchDao(db);
};

var MatchDao = function(db){
	this.db = db;
};

//相关数据库Table
//Note: 联赛系统数据是与每个联赛一起更新，只会更新内容，不会增加数量
var CollectionNames = [
	"leagueFsm",        //状态机数据
];

MatchDao.prototype.getLeagueSeasonId = function(callback)
{
	var self = this;
	self.findOneLimitOne("leagueFsm", function(err, doc){
		if(!!err){
			logger.error("load name: %s failed! seasonId: %s, err: %s", "league", seasonId, err);
			callback(err);
			return;
		}
		//logger.error("getSeasonId_________________", doc);
		if (!doc)
		{
			callback(null, doc);
			return;
		}
		callback(null, doc.seasonId);
	});
};

//查询该赛季下所有的数据信息
MatchDao.prototype.readWholeSeasonFsm = function(uid, callback){
	var self = this;
	var res = {};
	var isNew = 0;
	var index = 0;
	var length = CollectionNames.length;

	async.whilst(
		function() {return index < length;},
		function(cb){
			var name = CollectionNames[index++];
			self.findOrCreate(name, uid, function(err, doc, isInsert){
				if(!!err){
					logger.error("load name: %s failed! seasonId: %s, err: %s", name, uid, err);
					cb(err);
					return;
				}
				if(name == "leagueFsm" && isInsert){
					isNew = 1;
				}
				res[name] = doc;
				cb();
			});
		},
		function(err){
			if(!!err){
				return callback(null);
			}
			callback(res, isNew);
		}
	);
};

var NoFsmCollectionNames = [
	"community", 		//社区赛
	"normal", 			//常规赛
	"knockout", 		//淘汰赛
	"profession", 		//专业赛
	"enrollTime",       //玩家报名时间
];

//查询该赛季下所有的数据信息
MatchDao.prototype.readWholeSeasonNoFsm = function(seasonId, callback){
	var self = this;
	var res = {};
	var isNew = 0;
	var index = 0;
	var length = NoFsmCollectionNames.length;
	async.whilst(
		function() {return index < length;},
		function(cb){
			let name = NoFsmCollectionNames[index++];
			self.findOrCreate(name, seasonId, function(err, doc, isInsert){
				if(!!err){
					logger.error("load name: %s failed! seasonId: %s, err: %s", name, seasonId, err);
					cb(err);
					return;
				}
				if (name == "community" && isInsert)
				{
					isNew = 1;
				}
				res[name] = doc;
				cb();
			});
		},
		function(err){
			if(!!err){
				return callback(null);
			}
			callback(res, isNew);
		}
	);
};

//查询联赛各个赛季冠军
MatchDao.prototype.getLeagueChampion = function(seasonId, callback){
	var self = this;
	let ChampionMap = new Map();
	let getPlayerList = [];
	var res = {};
	var index = 4;
	if(seasonId <= 3)
	{
		index = 1;
	}
	this.db.collection("profession", function(error, collection){
		let finalRank;
		async.whilst(
			function() {return index < seasonId;},
			function(cb){
				let tmpList = [];
				let professionTypeMax = 5;
				collection.findOne({uid: index}, function (err, doc) {
					if(!!err || !doc)
					{
						logger.error("findOne getLeagueChampion err, errMsg:", index, err);
						index++;
						return cb(null);
					}
					finalRank = doc.finalRank;
					for(let i in finalRank)
					{
						if("robot_" !== finalRank[i].rankObjList[0].playerUid.substr(0, 6))
						{
							if(professionTypeMax > 0)
							{
								res.typeId = professionTypeMax--;//得到所在联赛级别
							}
							else
							{
								res.typeId = finalRank[i].groupId;//得到所在联赛级别
							}
							res.championUid = finalRank[i].rankObjList[0].playerUid;
							tmpList.push(utils.deepCopy(res));
							getPlayerList.push(res.championUid);
						}
					}
					if(seasonId <= 3)
					{
						ChampionMap.set(index, utils.cloneArray(tmpList));
					}
					else
					{
						ChampionMap.set(index - 3, utils.cloneArray(tmpList));//减3因为线上是从第4赛季才开始算正式开始，前3个赛季都是季前赛
					}

					index++;
					cb(null);
				})
			},
			function(err){
				if(!!err){
					return callback(null);
				}
				callback(ChampionMap, getPlayerList);
			}
		);
	});
};

var matchCollectionName = [
	"matchRank",        //匹配赛数据(不能存排名，只存挑战次数和刷新时间)
];

MatchDao.prototype.readWholeMatch = function(uid, cb){
	var self = this;
	var res = {};
	var index = 0;
	var length = matchCollectionName.length;
	async.whilst(
		function() {return index < length;},
		function(callback){
			var name = matchCollectionName[index++];
			self.findOrCreate(name, uid, function(err, doc){
				if(!!err){
					logger.error("load name: %s failed! err: %s", name, uid, err);
					callback(err);
					return;
				}
				res[name] = doc;
				callback();
			});
		},
		function(err){
			if(!!err){
				return cb(null);
			}
			cb(res);
		}
	);
};

MatchDao.prototype.updateMatchRank = function(playerId, obj, cb){
	let collection = this.db.collection("matchRank");
	collection.findOne({
		uid: playerId
	}, function(err, doc){
		if(!!err){
			logger.error("findOne Collections[%s] err, errMsg:", playerId, err);
			cb(err);
			return;
		}
		if(doc){
			collection.updateOne({uid: playerId}, {
				$set: { fightTimes: obj.fightTimes, passiveTimes: obj.passiveTimes, buyTimes: obj.buyTimes,lastUpdateTimes: obj.lastUpdateTimes, weekFansRank: obj.weekFansRank}
			   },{upsert:true, w: 1}).then(function(result) {
					   return cb(null);
			   });
		}else{
			collection.insertOne({
				uid: playerId,
				fightTimes: obj.fightTimes,
				passiveTimes: obj.passiveTimes,
				buyTimes: obj.buyTimes,
				lastUpdateTimes: obj.lastUpdateTimes,
				weekFansRank: obj.weekFansRank
			}, {w: 1}, function(err, result){
				if(!!err){
					logger.error("insertOne Collections[%s] err, errMsg:", playerId, err);
					cb(err);
					return;
				}
				cb(null);
			});
		}
	});
};

MatchDao.prototype.findOrCreate = function(collectionName, uid, cb){
	this.db.collection(collectionName, function(error, collection){
		collection.findOne({
			uid: uid
		}, function(err, doc){
			if(!!err){
				logger.error("findOne Collections[%s] err, errMsg:", uid, err);
				cb(err);
				return;
			}
			if(doc){
				return cb(null, doc);
			}else{
				collection.insertOne({
					uid: uid
				}, {w: 1}, function(err, result){
					if(!!err){
						logger.error("insertOne Collections[%s] err, errMsg:", uid, err);
						cb(err);
						return;
					}
					cb(null, {uid: uid}, true);
				});
			}
		});
	});
};

MatchDao.prototype.find = function(collectionName, seasonId, cb)
{
	this.db.collection(collectionName, function(error, collection){
		collection.findOne({
			uid: seasonId
		}, function(err, doc){
			if(!!err){
				logger.error("findOne Collections[%s] err, errMsg:", seasonId, err);
				cb(err);
				return;
			}
			return cb(null, doc);
		});
	});
};

MatchDao.prototype.findOneLimitOne = function(collectionName, cb){
	this.db.collection(collectionName, function(error, collection){
		collection.findOne({"uid": 1}, function(err, doc){
			if(!!err){
				logger.error("findOne Collections[%s] err, errMsg:", seasonId, err);
				cb(err);
				return;
			}
			return cb(null, doc);
		});
	});
};

var matchWeekTimeCollection = [
	"weekTime",
];

MatchDao.prototype.updateWeekTimeCollection = function(obj, cb)
{
	var self = this;
	let collectionName = matchWeekTimeCollection[0];
	self.db.collection(collectionName, function (err, collection) {
		if(!!err){
			logger.error("update WeekTimeCollection err, errMsg:", err);
			cb(err);
			return;
		}
		if(collection)
		{
			collection.updateOne({id: 1}, {$set: {id: 1, weekNum: obj}},{upsert:true, w: 1}).then(function(result) {
				return cb(null);
			});
		}
		else
		{
			collection.insertOne({id: 1}, {id: 1, weekNum: obj}, function(err, result){
				if(!!err){
					logger.error("insertOne WeekTimeCollection err, errMsg:", err);
					cb(err);
					return;
				}
				cb(null, {uid: uid}, true);
			});
		}
	});
};

MatchDao.prototype.loadWeekTimeCollection = function(cb)
{
	var self = this;
	var config = {};
	config.name = matchWeekTimeCollection[0];
	var collection = self.db.collection(config.name);
	collection.findOne({id: 1}, function(err, result){
		if(!!err){
			logger.error("findOne Collections[%s] err, errMsg:", err);
			cb(err);
			return;
		}
		return cb(null, result);
	});

};

MatchDao.prototype.findByPlayerId = function(playerId, cb) {
	let collection = this.db.collection("account");
	collection.findOne({uid: playerId}, function (err, doc) {
		if(!!err){
			logger.debug("findByPlayerId get account fail, playerId:", playerId);
			return cb(Code.FAIL);
		}
		if(!doc){
			// logger.info("findByPlayerId: playerId", playerId, doc);
			return cb(Code.OK, null);
		}
		return cb(Code.OK, doc);
	});
};


var clearCollections = [
	"leagueFsm",        //状态机数据
	"community", 		//社区赛
	"normal", 			//常规赛
	"knockout", 		//淘汰赛
	"profession", 		//专业赛
	"enrollTime",
];

MatchDao.prototype.clearDB = function(callback)
{
	var self = this;
	var index = 0;
	var length = clearCollections.length;
	
	async.whilst(
		function() {return index < length;},
		function(cb){
			var name = clearCollections[index++];
			self.drop(name, function(err){
				if(!!err){
					logger.error("drop name: %s failed! seasonId: %s, err: %s", name, err);
					cb(err);
					return;
				}

				cb();
			});
		},
		function(err){
			if(!!err){
				return callback(err);
			}
			callback(null);
		}
	);
};

MatchDao.prototype.drop = function(collectionName, cb)
{
	this.db.collection(collectionName, function(error, collection){
		collection.drop(function(err, doc){
			if(!!err){
				logger.error("drop Collections[%s] err, errMsg:", err);
				cb(err);
				return;
			}
			//logger.info("drop collections success!", collectionName);
			return cb(null);
		});
	});
};
//保存联赛数据
MatchDao.prototype.saveLeagueData = function (leagueFsm, cb) {
	let self = this;
	var uid = leagueFsm.getId();
	async.waterfall([
		function (callback) {
			val = {uid: uid, e: leagueFsm};
			self.saveLeagueDataToDB("leagueFsm", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "leagueFsm"', error);
				}
				else
				{
					logger.debug("save leagueFsm data finish", uid);
				}
				callback(null);
			});
		},
		function (callback) {
			val = {uid: leagueFsm.league.uid, e: leagueFsm.enrollTime};
			self.saveLeagueDataToDB("enrollTime", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "EnrollTime"', error);
				}
				else
				{
					logger.debug("save EnrollTime data finish", uid );
				}
				callback(null);
			});
		},
		function (callback) {
			val = {uid: leagueFsm.league.community.uid, e: leagueFsm.league.community};
			self.saveLeagueDataToDB("community", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "Community"', error);
				}
				else
				{
					logger.debug("save Community data finish", uid );
				}
				callback(null);
			});
		},
		function (callback) {
			val = {uid: leagueFsm.league.knockout.uid, e: leagueFsm.league.knockout};
			self.saveLeagueDataToDB("knockout", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "Knockout"', error);
				}
				else
				{
					logger.debug("save Knockout data finish", uid );
				}
				callback(null);
			});
		},
		function (callback) {
			val = {uid: leagueFsm.league.normal.uid, e: leagueFsm.league.normal};
			self.saveLeagueDataToDB("normal", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "Normal"', error);
				}
				else
				{
					logger.debug("save Normal data finish", uid );
				}
				callback(null);
			});
		},
		function (callback) {
			val = {uid: leagueFsm.league.profession.uid, e: leagueFsm.league.profession};
			self.saveLeagueDataToDB("profession", val, function (error) {
				if (!!error) {
					logger.debug('save data err, collectionName: "Profession"', error);
				}
				else
				{
					logger.debug("save Profession data finish", uid );
				}
				callback(null);
			});
		},
	],function(error) {
		if(error)
		{
			cb();
		}
		cb();
	});
};

//写入DB
MatchDao.prototype.saveLeagueDataToDB = function (collectionName, val, callback)
{
	this.db.collection(collectionName, function (error, collection) {
		if (!!error) {
			logger.debug('save data err, collectionName:',collectionName, error);
			callback(error);
			return;
		}
		collection.updateOne({
			uid: val.uid
		}, {
			$set: val.e.toJSONforDB()
		}, {w: 1}, function(err, result){
			if(!!err){
				return callback(err);
			}
			logger.info("collection: %s, save result: %j", collectionName, result, val.uid, val.e.toJSONforDB());
			callback(null);
		});
	});
};

MatchDao.prototype.updateDqMatchInfoToDB = function (data, cb) {
	let self = this;
	self.db.collection("dqMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("updateDqMatchInfoToDB dqMatch findOne err:", err);
				return cb(err);
			}
			if(doc) {
				//update
				collection.updateOne({
					uid: 1
				},{
					$set: {matchInfo: data}
				}, function (err) {
					if(!!err){
						logger.error('updateDqMatchInfoToDB updateOne err', err);
						return cb(err);
					}
					logger.debug('updateDqMatchInfoToDB update success.');
					return cb(null);
				});
			}
			else {
				collection.insertOne({uid: 1, matchInfo: data}, {w: 1}, function(err, result){
					if(!!err){
						logger.error("updateDqMatchInfoToDB insertOne err:", err);
						return cb(err);
					}
					cb(null);
				});
			}
		})
	})
};

MatchDao.prototype.findDqMatchInfo = function (cb) {
	let self = this;
	self.db.collection("dqMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("findDqMatchInfo findOne err:", err);
				return cb(err);
			}
			if(doc) {
				return cb(null, doc);
			}
			cb(null);
		});
	});
};

MatchDao.prototype.updateChMatchInfoToDB = function (data, cb) {
	let self = this;
	self.db.collection("chMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("updateChMatchInfoToDB chMatch findOne err:", err);
				return cb(err);
			}
			if(doc) {
				//update
				collection.updateOne({
					uid: 1
				},{
					$set: {matchInfo: data}
				}, function (err) {
					if(!!err){
						logger.error('updateChMatchInfoToDB updateOne err', err);
						return cb(err);
					}
					logger.debug('updateChMatchInfoToDB update success.');
					return cb(null);
				});
			}
			else {
				collection.insertOne({uid: 1, matchInfo: data}, {w: 1}, function(err, result){
					if(!!err){
						logger.error("updateChMatchInfoToDB insertOne err:", err);
						return cb(err);
					}
					cb(null);
				});
			}
		})
	})
};

MatchDao.prototype.findChMatchInfo = function (cb) {
	let self = this;
	self.db.collection("chMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("findChMatchInfo findOne err:", err);
				return cb(err);
			}
			if(doc) {
				return cb(null, doc);
			}
			cb(null);
		});
	});
};

MatchDao.prototype.updatePkMatchInfoToDB = function (data, cb) {
	let self = this;
	self.db.collection("pkMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("updatePkMatchInfoToDB pkMatch findOne err:", err);
				return cb(err);
			}
			if(doc) {
				//update
				collection.updateOne({
					uid: 1
				},{
					$set: {matchInfo: data}
				}, function (err) {
					if(!!err){
						logger.error('updatePkMatchInfoToDB updateOne err', err);
						return cb(err);
					}
					logger.debug('updatePkMatchInfoToDB update success.');
					return cb(null);
				});
			}
			else {
				collection.insertOne({uid: 1, matchInfo: data}, {w: 1}, function(err, result){
					if(!!err){
						logger.error("updatePkMatchInfoToDB insertOne err:", err);
						return cb(err);
					}
					cb(null);
				});
			}
		})
	})
};

MatchDao.prototype.findPkMatchInfo = function (cb) {
	let self = this;
	self.db.collection("pkMatch", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("findPkMatchInfo findOne err:", err);
				return cb(err);
			}
			if(doc) {
				return cb(null, doc);
			}
			cb(null);
		});
	});
};

MatchDao.prototype.updateWorldBossInfoToDB = function (data, cb) {
	let self = this;
	self.db.collection("worldBoss", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("updateWorldBossInfoToDB worldBoss findOne err:", err);
				return cb(err);
			}
			if(doc) {
				//update
				collection.updateOne({
					uid: 1
				},{
					$set: {worldBossInfo: data}
				}, function (err) {
					if(!!err){
						logger.error('updateWorldBossInfoToDB updateOne err', err);
						return cb(err);
					}
					logger.debug('updateWorldBossInfoToDB update success.');
					return cb(null);
				});
			}
			else {
				collection.insertOne({uid: 1, worldBossInfo: data}, {w: 1}, function(err, result){
					if(!!err){
						logger.error("updateWorldBossInfoToDB insertOne err:", err);
						return cb(err);
					}
					cb(null);
				});
			}
		})
	})
};

MatchDao.prototype.findWorldBossInfo = function (cb) {
	let self = this;
	self.db.collection("worldBoss", function (error, collection) {
		collection.findOne({uid: 1}, function (err, doc) {
			if(!!err) {
				logger.error("findWorldBossInfo findOne err:", err);
				return cb(err);
			}
			if(doc) {
				return cb(null, doc);
			}
			cb(null);
		});
	});
};

MatchDao.prototype.updateDqMatchRecord = function (record, cb) {
	let self = this;
	self.db.collection("dqMatch", function (error, collection) {
		collection.findOne({uid: 2}, function (err, doc) {
			if(!!err) {
				logger.error("updateDqMatchRecord findOne err:", err);
				return cb(err);
			}
			if(doc) {
				if(!doc.recordList) {
					doc.recordList = [];
					doc.recordList.push(record);
				}else if(doc.recordList.length >= 10){
					doc.recordList.shift();
					doc.recordList.push(record);
				}
				collection.updateOne({
					uid: 2
				},{
					$set: {recordList: doc.recordList}
				}, function (err) {
					if(!!err){
						logger.error('updateDqMatchRecord updateOne err', err);
						return cb(err);
					}
					logger.debug('updateDqMatchRecord update success.');
					return cb(null);
				});
			}else {
				doc = {};
				doc.uid = 2;
				doc.recordList = [];
				doc.recordList.push(record);
				collection.insertOne(doc, {w: 1}, function (err, result) {
					if(!!err){
						logger.error('updateDqMatchRecord insertOne err', err);
						return cb(err);
					}
					return cb(null);
				});
			}
		});
	});
};

MatchDao.prototype.updateChMatchRecord = function (beliefId, record, cb) {
	let self = this;
	self.db.collection("chMatch", function (error, collection) {
		collection.findOne({uid: beliefId}, function (err, doc) {
			if(!!err) {
				logger.error("updateDqMatchRecord findOne err:", err);
				return cb(err);
			}
			if(doc) {
				if(!doc.recordList) {
					doc.recordList = [];
					doc.recordList = record;
				}//else if(doc.recordList.length >= 10){
				// 	doc.recordList.shift();
				// 	doc.recordList.push(record);
				// }
				collection.updateOne({
					uid: beliefId
				},{
					$set: doc
				}, function (err) {
					if(!!err){
						logger.error('updateDqMatchRecord updateOne err', err);
						return cb(err);
					}
					logger.debug('updateDqMatchRecord update success.');
					return cb(null);
				});
			}else {
				doc = {};
				doc.uid = beliefId;
				doc.recordList = [];
				doc.recordList = record;
				collection.insertOne(doc, {w: 1}, function (err, result) {
					if(!!err){
						logger.error('updateDqMatchRecord insertOne err', err);
						return cb(err);
					}
					return cb(null);
				});
			}
		});
	});
};

MatchDao.prototype.updatePkMatchRecord = function (record, cb) {
	let self = this;
	self.db.collection("pkMatch", function (error, collection) {
		collection.findOne({uid: 2}, function (err, doc) {
			if(!!err) {
				logger.error("updateDqMatchRecord findOne err:", err);
				return cb(err);
			}
			if(doc) {
				if(!doc.recordList) {
					doc.recordList = [];
					doc.recordList.push(record);
				}else if(doc.recordList.length >= 10){
					doc.recordList.shift();
					doc.recordList.push(record);
				}
				collection.updateOne({
					uid: 2
				},{
					$set: {recordList: doc.recordList}
				}, function (err) {
					if(!!err){
						logger.error('updateDqMatchRecord updateOne err', err);
						return cb(err);
					}
					logger.debug('updateDqMatchRecord update success.');
					return cb(null);
				});
			}else {
				doc = {};
				doc.uid = 2;
				doc.recordList = [];
				doc.recordList.push(record);
				collection.insertOne(doc, {w: 1}, function (err, result) {
					if(!!err){
						logger.error('updateDqMatchRecord insertOne err', err);
						return cb(err);
					}
					return cb(null);
				});
			}
		});
	});
};

MatchDao.prototype.getAccountInfoLimit = function (limitNum, cb) {
	let self = this;
	self.db.collection("account", function (error, collection) {
		collection.find({}, {limit: limitNum}).toArray(function (err, doc) {
			if(!!err) {
				logger.error("updateDqMatchRecord findOne err:", err);
				return cb(err);
			}
			if(doc) {
				logger.debug("getAccountInfoLimit num: ", limitNum, doc.length);
				cb(doc);
				/*
				if(!doc.recordList) {
					doc.recordList = [];
					doc.recordList.push(record);
				}else if(doc.recordList.length >= 10){
					doc.recordList.shift();
					doc.recordList.push(record);
				}
				collection.updateOne({
					uid: 2
				},{
					$set: {recordList: doc.recordList}
				}, function (err) {
					if(!!err){
						logger.error('updateDqMatchRecord updateOne err', err);
						return cb(err);
					}
					logger.debug('updateDqMatchRecord update success.');
					return cb(null);
				});
			}else {
				doc = {};
				doc.uid = 2;
				doc.recordList = [];
				doc.recordList.push(record);
				collection.insertOne(doc, {w: 1}, function (err, result) {
					if(!!err){
						logger.error('updateDqMatchRecord insertOne err', err);
						return cb(err);
					}
					return cb(null);
				});
				*/
			}
		});
	});
};
