/**
 * Created by aaa on 2015/5/12.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var Code = require('../../../shared/code');
var Utils = require('../util/utils');
var async = require('async');
var debugConfig = require('../../config/debugConfig');
//var ObjectId = require('mongodb').ObjectId;


module.exports.create = function(db){
    return new accountDao(db);
};

var accountDao = function(db){
    this.db = db;
};

//外部接口函数

//account表数据结构说明
//oid: openId[平台账号], cl:channel[渠道ID], pf:platform[平台ID], nm:name[玩家名字]

//获取Account表最大个数
accountDao.prototype.getAccountMaxNum = function(msg, cb) {
    let collection = this.db.collection("account");
    collection.find({}).count(function (err, result) {
        if(!!err)
        {
            logger.warn('getAccountMaxNum. err', err);
            return cb(err, result);
        }
        return cb(null, result);
    })
};

//获取某个game_server_id的玩家数量
accountDao.prototype.getAccountNumByGameServerId = function(gid, cb) {
    let collection = this.db.collection("account");
    collection.find({gid : gid}).count(function (err, result) {
        if(!!err)
        {
            logger.warn('getAccountMaxNum. err: ', err);
            return cb(err, result);
        }
        return cb(null, result);
    })
};

accountDao.prototype.findOrCreate = function(msg, cb) {
    this.db.collection("account", function (err, collection) {
        collection.findOne({
            oid: msg.openId
        }, function(err, doc) {
            if(!!err){
                logger.debug('get account fail, openId:',msg.openId);
                return cb(Code.FAIL);
            }
            if(doc){
                logger.debug('find the account by openId:', msg.openId);
                return cb(Code.OK, doc);
            }else{
                //new uid
                msg.uid = Utils.syncCreateUid();
                collection.insertOne({
                    oid: msg.openId,
                    uid: msg.uid,
                    gid: msg.gid,
                    name: msg.name,
                    level: 0,
                    fansCount: 0,
                    actualStrength: 0
                }, {w: 1}, function(err){
                    if(!!err){
                        logger.debug('insert new account fail, openId:', msg.openId);
                        return cb(Code.FAIL);
                    }
                    cb(Code.OK, msg);
                });
            }
        });
    });
};

accountDao.prototype.findByPlayerId = function(playerId, cb) {
    let collection = this.db.collection("account");
    collection.findOne({uid: playerId}, function (err, doc) {
        if(!!err){
            logger.debug("findByPlayerId get account fail, playerId:", playerId);
            return cb(Code.FAIL);
        }
        if(!doc){
            //logger.info("findByPlayerId: playerId", playerId);
            return cb(Code.OK, null);
        }
        return cb(Code.OK, doc);
    });
};

accountDao.prototype.getAccountGid = function(openId, cb) {
    let collection = this.db.collection("account");
    collection.findOne({oid: openId}, function (err, doc) {
        if(!!err) {
            logger.debug("getAccountGid fail, openId:", openId);
            return cb(Code.FAIL);
        }
        if(!doc){
            return cb(Code.OK, "");
        }
        return cb(Code.OK, doc.gid);
    })
};

accountDao.prototype.getTouristAccount = function(msg, cb) {
    var self = this;
    this.db.collection("account", function(err, collection) {
        //msg.username null insert
        logger.debug('------ getTouristAccount -------');
        if(msg.username == null) {
            collection.insertOne({
                sid: msg.serverId,
                platform: msg.platform
            }, {w: 1}, function(err, result){
                if(!!err){
                    logger.debug('insert new account fail, username:',msg.username);
                    return cb(null, Code.FAIL);
                }
                msg._id = result.insertedId;
                cb(null, Code.OK, msg);
            });
        }
        else {
            var id = msg.username.split("_")[0];
            if(!id) {
                logger.debug('getTouristAccount username err: ',msg.username);
                return cb(null, Code.FAIL);
            }
            var objectId = new ObjectId(id);
            collection.findOne({
                _id: objectId
            }, {
                fields : {
                    _id: 1,
                    sid: 1,
                    platform: 1
                }
            }, function(err, doc) {
                if(!!err){
                    logger.debug('get account fail, username:',msg.username);
                    return cb(null, Code.FAIL);
                }
                if(doc){
                    logger.debug('getTouristAccount doc: ',doc);
                    return cb(null, Code.OK, doc);
                }else{
                    logger.debug('getTouristAccount account not exist !, playerId: ', msg.username);
                    return cb(null, Code.ENTRY.FA_USER_NOT_EXIST);
                }
            });
        }
    });
};

accountDao.prototype.getAllAccount = function(cb)
{
	let collection = this.db.collection("account");
    if(debugConfig.isDevEnv) {
        collection.find().limit(3000).toArray(function(err,  doc) {
            if (!!err)
            {
                logger.debug('accountDao all player account err', err);
                return cb(err);
            }
            return cb(null, doc);
        });
    }else {
        collection.find().toArray(function(err,  doc) {
            if (!!err)
            {
                logger.debug('accountDao all player account err', err);
                return cb(err);
            }
            return cb(null, doc);
        });
    }
};

accountDao.prototype.updateAccountByPlayerId = function(playerId, obj, cb){
	let collection = this.db.collection("account");
	collection.updateOne({uid: playerId}, {
            $set: {
                uid: obj.uid,
                oid: obj.oid,
                cl: obj.cl,
                pf: obj.pf,
                nm: obj.nm,
                gid: obj.gid,
                name: obj.name,
                level: obj.level,
                fansCount: obj.fansCount,
                actualStrength: obj.actualStrength,
                groundOpenStatus: obj.groundOpenStatus,
            }
    },{upsert:true, w: 1}).then(function(result) {
			return cb(null);
    });
};

accountDao.prototype.getAccountByName = function (name, cb) {
    let collection = this.db.collection("account");
    collection.findOne({name: name}, function(err,  doc) {
        if (!!err) {
            logger.debug('accountDao getAccountByName err', err);
            return cb(err);
        }
        return cb(null, doc);
    });
};

accountDao.prototype.getPlayerListAS = function(playerList, cb)
{
    let list = [];
    let collection = this.db.collection("account");
    async.eachSeries(playerList, function (playerId, callback) {
        collection.findOne({uid: playerId}, function (err, doc) {
            if (!!err) {
                logger.debug('accountDao getPlayerListActualStrength err', playerId, err);
                return callback(null);
            }
            list.push({uid: playerId, gid: doc.gid, actualStrength: doc.actualStrength});
            callback(null);
        });
    }, function (err) {
        cb(list);
    });
};