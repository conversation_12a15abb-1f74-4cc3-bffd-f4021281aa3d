/**
 * Created by shine on 2015/3/20.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var Player = require('../domain/entities/player');

var collectionName = "loginActivity";

module.exports.create = function(db){
	return new GMDao(db);
};

var GMDao = function(db){
	this.db = db;
};

GMDao.prototype.insertLoginActivity = function(log, from) {
	this.db.collection('loginActivity', function (error, collection) {
		if (!!error) {
			return;
		}
		collection.insertOne(log, function (error, document){
			if(error){
				logger.error("insertLoginActivity failed! err: %s", error);
			}
		});
	});
};

GMDao.prototype.insertRegisterActivity = function(log, from) {
	this.db.collection('registerActivity', function (error, collection) {
		if (!!error) {
			return;
		}

		collection.insertOne(log, function (error, document){
			if(error){
				logger.error("insertRegisterActivity failed! err: %s", error);
			}
		});
	});
};

