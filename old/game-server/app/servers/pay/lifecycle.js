let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let clusterConfig = require('../../../config/cluster');
let dataApi = require('../../util/dataApi');

//进程启动前的处理函数
module.exports.beforeStartup = function(app, cb) {
    // do some operations before application start up
    logger.debug("pay Server beforeStartup now!");
    dataApi.allData.readWholeConfig(function (err) {
        cb();
    });
};

//进程启动后的处理函数
module.exports.afterStartup = function(app, cb) {
    // do some operations after application start up
    logger.debug("pay Server afterStartup now!");
    cb();
};

//进程关闭前的处理函数
module.exports.beforeShutdown = function(app, cb) {
    // do some operations before application shutdown down
    logger.debug("pay Server beforeShutdown now!");
    //保存未发完邮件数据
    let payService = app.get("payService");
    payService.payDao.updateUnfinishedMail(payService.sendMailObj, function () {
        cb();
    });
};

//所有服务器启动后的处理函数
module.exports.afterStartAll = function(app) {
    // do some operations after all applications start up
    app.get("payService").initWhenAllServerStart();
    //检查是否有未发完全服邮件， 如有继续发
    let payService = app.get("payService");
    payService.payDao.readUnfinishedMail(function (err, doc) {
        payService.sendMailObj = doc;
       if(!!doc)
       {
           let session = {frontendId: app.getServerId()};
           app.rpc.datanode.dataNodeRemote.sendUrlSystemMailByUidList(session, doc, function (code) {
           });
       }
    });

    logger.debug("pay Server afterStartAll now!");
};
