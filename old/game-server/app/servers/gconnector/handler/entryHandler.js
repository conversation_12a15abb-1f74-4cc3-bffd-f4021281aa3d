let Code = require('../../../../../shared/code');
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../../shared/enum');
let routeUtil = require('../../../util/routeUtil');

let SESSION_NEW = 0;
let SESSION_INIT = 1;

module.exports = function(app) {
  return new Handler(app);
};

var Handler = function(app) {
  this.app = app;
};

//进入游戏入口
Handler.prototype.login = function(msg, session, next) {
	let self = this;
	if(session.get("state")){
		logger.warn("invalid session state, state:", state);
		next(null, {code: Code.FAIL});
		return;
	}
	session.on('closed', onUserLeave.bind(null, self.app));
	session.set("state", SESSION_INIT);

	//to do 全局状态管理移交到online服上去开发
	async.waterfall([
		function(callback){
            //服务器id路由
			msg.gid = routeUtil.getRouterGameId(session, self.app);
			//登录验证
			self.app.rpc.auth.authRemote.auth(session, msg, function (error, code, uid) {
				logger.debug('----- authRemote login return: -----', error, code, uid);
				if(code !== Code.OK) {
					callback(code, {code: code});
					return;
				}
				if(!session.get("state")) {
					logger.warn("before finish auth, session had closed!");
					callback(Code.FAIL);
					return;
				}
				msg.playerId = uid;
				msg.loginState = commonEnum.LoginState.AUTH;
				callback(null);
			});
		},
		/* 这一步没有意义，减少rpc消耗，去掉
		function(callback){
			//online服务器初始化状态
			self.app.rpc.datanode.dataNodeRemote.syncLoginStateAfterAuth(session, msg, function (error, code) {
				if(code !== Code.OK) 
				{
					logger.info("syncLoginStateAfterAuth", code);
					callback(code, {code: code});
					return;
				}
				callback(null);
			});
		},
		*/
		function(callback){
			let sessions = self.app.get('sessionService').getByUid(msg.playerId);
			if(!sessions){
				//logger.info("waterfall __session not exist", msg.playerId);
				callback(null);
				return;
			}
			logger.info("waterfall __session exist, to be kick !", msg.playerId);
			sessions = sessions.map(function(session){
				return session.id;
			});
			pushUserMessage(self.app, "onKick", {reason: "login again " + msg.playerId + msg.openId}, sessions, function() {
				self.app.get('sessionService').kick(msg.playerId, callback);
			});
		},
		function(callback){
			session.bind(msg.playerId, callback);
			//statisticLoginInfo(self.app);
		},
		function(callback){
			msg.ip = session.__session__.__socket__.remoteAddress.ip.substring(7);
			msg.frontendId = self.app.getServerId();
			msg.sessionId = session.id;
			self.app.rpc.game.entryRemote.login(session, msg, function(error, res){
				if(error){
					callback(error);
					return;
				}
				//logger.info("waterfall login", msg.playerId);
				callback(null, res);
			});
		},
		function(res, callback){
			if(res.isNew === 1) {
				//新用户数据同步到gate服务器
				self.app.rpc.gate.gateRemote.newAccountLogin(session, msg, function(err){
					if(!!err){
						callback(err);
						return;
					}
					//logger.info("waterfall newAccountLogin", msg.playerId);
					callback(null, res);
				});
			}
			else {
				//logger.info("waterfall old", msg.playerId);
				callback(null, res);
			}
		},
		function(res, callback){
			//再次同步登陆成功状态到online服务器
			msg.loginState = commonEnum.LoginState.LOGIN;
			self.app.rpc.datanode.dataNodeRemote.syncLoginStateAfterAuth(session, msg, function (error, code) {
				if(code !== Code.OK) {
					callback(code, res);
					return;
				}
				//logger.info("waterfall syncLoginStateAfterAuth", msg.playerId);
				callback(null, res);
			});
		},
	], function(error, res){
		if(error){
			session.set("state", SESSION_NEW);
			next(null, res || {code: Code.FAIL});
			return;
		}
		next(null, res);
	});
};

/*
---------------------------------------------------------------------
 */
var onUserLeave = function (app, session, reason) {
	if(!session || !session.uid) {
		return;
	}

	//logger.debug("user leave! session id: %d, uid=%s, reason: %s", session.id, session.uid, reason);
	session.set("state", SESSION_NEW);
	let msg = {};
	msg.playerId = session.uid;
	msg.gid = routeUtil.getRouterGameId(session, app);
	async.waterfall([
		function(callback){
			app.rpc.game.entryRemote.leave(session, msg, function(err){
				if (!!err)
				{
					logger.error("onUserLeave: error", msg.playerId, err);
					callback(err);
					return;
				}
				//statisticLoginInfo(app);
				callback(null);
			});
		},
		function(callback){
			//通知online删除用户在线数据
			app.rpc.datanode.dataNodeRemote.useLeave(session, msg, function(err, code) {
				if (!!err)
				{
					logger.error("onUserLeave:  rpc dataNode useLeave error", msg.playerId, err);
					callback(err);
					return;
				}

				if(code !== Code.OK) 
				{
					logger.info("syncLoginStateAfterAuth", code);
					callback(code);
					return;
				}
				callback(null);
			});
		}
	], function(err){
		if(!!err){
			logger.error("onUserLeave failed! ", msg.playerId, err);
			//return;
		}
		//logger.info("onUserLeave is ok!", msg.playerId);
		//return;
	});
};

var pushUserMessage = function(app, route, msg, sessionIds, cb) {
	var connector = app.components.__connector__;
	connector.send(null, route, msg, sessionIds, {isPush: true}, cb);
};

var statisticLoginInfo = function(app){
	var connection = app.components.__connection__;
	logger.info("statistic login info: %j", connection.getStatisticsInfo());
};
