var Code = require('../../../../../shared/code');
var logger = require('pomelo-logger').getLogger("pomelo", __filename);

module.exports = function(app) {
    return new Remote(app);
};

var Remote = function(app) {
    this.app = app;
};

Remote.prototype.recordStatisLog = function (msg, cb) {
    this.app.get("slogService").recordStatisLog(msg.playerId, msg.behaviorType, msg.valueArray, msg.moreInfo, function (code) {
        cb(code);
    });
};

/*
Remote.prototype.rechargeCreateOrder = function (msg, cb) {
    this.app.get("payService").rechargeCreateOrder(msg, function (code, orderId) {
        cb(code, orderId);
    });
};

Remote.prototype.addRenameCardOrder = function (msg, cb) {
    this.app.get("payService").addRenameCardOrder(msg, function (code, isInsert) {
        cb(code, isInsert);
    });
};
*/
