/**
 * Created by scott on 2019/05/07.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var pomelo = require('pomelo');

module.exports = function(){
	return new Filter();
};

var Filter = function(){

};

Filter.prototype.before = function(msg, session, next){
	if(!pomelo.app.get('isReady')){
		logger.error("league server is not ready. msg=" + JSON.stringify(msg));
		next("league server not ready");
		return;
	}
	next(null);
};