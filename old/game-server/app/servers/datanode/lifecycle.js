let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let clusterConfig = require('../../../config/cluster');
let debugConfig = require('../../../config/debugConfig');
let dataApi = require('../../util/dataApi');

//进程启动前的处理函数
module.exports.beforeStartup = function(app, cb) {
    // do some operations before application start up
    logger.debug("dataNodeService beforeStartup now!");
    dataApi.allData.readWholeConfig(function (err) {
        cb();
    });
};

//进程启动后的处理函数
module.exports.afterStartup = function(app, cb) {
    // do some operations after application start up
    logger.debug("dataNodeService afterStartup now!");
    cb();
};

//进程关闭前的处理函数
module.exports.beforeShutdown = function(app, cb) {
    // do some operations before application shutdown down
    logger.debug("dataNodeService beforeShutdown now!");
    //关闭前看下有没有配置表还没上传数据库
    app.get("configService").saveAllConfig(function(err) {
		cb();
	});
};

//所有服务器启动后的处理函数
module.exports.afterStartAll = function(app) {
    // do some operations after all applications start up
    //app.get("playerMiddleEastCupService").playerService = app.get("playerService");
    app.get("dataNodeService").init();
    app.get("configService").init();
    app.get("beliefService").beliefServiceInit();
    //june hide
    if(!debugConfig.groundMatchHide) {
        app.get("onlineService").loadPlayerUserCache();
    }
    //app.get("associationService").init();
    logger.debug("dataNodeService afterStartAll now!");
};
