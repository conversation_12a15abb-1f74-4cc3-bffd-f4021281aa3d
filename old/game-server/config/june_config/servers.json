{"development": {"gate": [{"id": "gate-outerTest-1", "host": "*************", "clientPort": 11001, "port": 11002, "frontend": true}], "auth": [{"id": "auth-outerTest-1", "host": "*************", "port": 11003}], "datanode": [{"id": "datanode-outerTest-1", "host": "*************", "port": 11004}], "match": [{"id": "match-outerTest-1", "host": "*************", "port": 11005}], "league": [{"id": "league-outerTest-1", "host": "*************", "port": 11006}], "brecord": [{"id": "brecord-outerTest-1", "host": "*************", "port": 18001}], "pay": [{"id": "pay-outerTest-1", "host": "*************", "port": 19001}], "slog": [{"id": "slog-outerTest-1", "host": "*************", "port": 20001}], "gconnector": [{"id": "gconnector-outerTest-1", "host": "*************", "port": 13001, "clientPort": 12001, "frontend": true}, {"id": "gconnector-outerTest-2", "host": "*************", "port": 13002, "clientPort": 12002, "frontend": true}], "bconnector": [{"id": "bconnector-outerTest-1", "host": "*************", "port": 15001, "clientPort": 14001, "frontend": true}, {"id": "bconnector-outerTest-2", "host": "*************", "port": 15002, "clientPort": 14002, "frontend": true}], "game": [{"id": "game-outerTest-1", "host": "*************", "port": 16001}, {"id": "game-outerTest-2", "host": "*************", "port": 16002}], "battle": [{"id": "battle-outerTest-1", "host": "*************", "port": 17001}, {"id": "battle-outerTest-2", "host": "*************", "port": 17002}], "zmonitor": [{"id": "zmonitor-outerTest-1", "host": "*************", "port": 21001}, {"id": "zmonitor-outerTest-2", "host": "*************", "port": 21002}]}, "production": {"gate": [{"id": "gate-outerTest-1", "host": "*************", "clientPort": 11001, "port": 11002, "frontend": true}], "auth": [{"id": "auth-outerTest-1", "host": "*************", "port": 11003}], "datanode": [{"id": "datanode-outerTest-1", "host": "*************", "port": 11004}], "match": [{"id": "match-outerTest-1", "host": "*************", "port": 11005}], "league": [{"id": "league-outerTest-1", "host": "*************", "port": 11006}], "brecord": [{"id": "brecord-outerTest-1", "host": "*************", "port": 18001}], "pay": [{"id": "pay-outerTest-1", "host": "*************", "port": 19001}], "slog": [{"id": "slog-outerTest-1", "host": "*************", "port": 20001}], "gconnector": [{"id": "gconnector-outerTest-1", "host": "*************", "port": 13001, "clientPort": 12001, "frontend": true}, {"id": "gconnector-outerTest-2", "host": "*************", "port": 13002, "clientPort": 12002, "frontend": true}], "bconnector": [{"id": "bconnector-outerTest-1", "host": "*************", "port": 15001, "clientPort": 14001, "frontend": true}, {"id": "bconnector-outerTest-2", "host": "*************", "port": 15002, "clientPort": 14002, "frontend": true}], "game": [{"id": "game-outerTest-1", "host": "*************", "port": 16001}, {"id": "game-outerTest-2", "host": "*************", "port": 16002}], "battle": [{"id": "battle-outerTest-1", "host": "*************", "port": 17001}, {"id": "battle-outerTest-2", "host": "*************", "port": 17002}], "zmonitor": [{"id": "zmonitor-outerTest-1", "host": "*************", "port": 21001}, {"id": "zmonitor-outerTest-2", "host": "*************", "port": 21002}]}}