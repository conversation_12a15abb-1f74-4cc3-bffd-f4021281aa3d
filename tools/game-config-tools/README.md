# 配置库脚本工具

这个目录包含了配置库的核心脚本工具，用于处理游戏配置文件的迁移、生成和验证。

## 📋 脚本列表

### 🚀 主要脚本

#### `setup-configs.js` - 一键设置
**推荐使用** - 完整的配置库设置流程
```bash
npm run config:setup
```
**功能**：
- 📦 配置迁移（从old项目复制JSON文件）
- 🔧 接口生成（生成TypeScript接口定义）
- 🔍 配置验证（检查数据完整性）

---

### 🔧 单独脚本

#### `migrate-configs.js` - 配置迁移
```bash
npm run config:migrate
```
**功能**：
- 从 `old/shared/config/data/` 复制JSON配置文件
- 标准化字段名（Id→id, Name→name等）
- 保存到 `libs/game-config/src/data/`
- 保持原始文件名不变

#### `generate-interfaces.js` - 接口生成
```bash
npm run config:generate
```
**功能**：
- 分析JSON结构自动生成TypeScript接口
- 字段名驼峰化（ItemID→itemId）
- 应用项目命名规范（footballer→hero, association→guild）
- 生成字段映射和元数据
- 文件重命名（footballer.interface.ts→hero.interface.ts）

#### `validate-configs.js` - 配置验证
```bash
npm run config:validate
```
**功能**：
- 检查JSON格式正确性
- 验证数据结构完整性
- 检查字段类型一致性
- 验证必需字段存在性

## 🎯 使用建议

### 首次设置
```bash
# 一键完成所有设置
npm run config:setup
```

### 日常开发
```bash
# 只重新生成接口（当JSON数据更新时）
npm run config:generate

# 只验证配置（检查数据完整性）
npm run config:validate
```

### 重新迁移
```bash
# 重新从old项目迁移配置
npm run config:migrate
npm run config:generate
```

## 📁 目录结构

```
tools/config-tools
# 执行脚本（当前目录）
├── setup-configs.js      # 🚀 一键设置
├── migrate-configs.js    # 📦 配置迁移
├── generate-interfaces.js # 🔧 接口生成
└── validate-configs.js   # 🔍 配置验证
libs/      # 核心工具类
    ├── config-migrator.js     # 迁移工具
    ├── interface-generator.js # 接口生成工具
    └── config-validator.js    # 验证工具
```

## ✨ 特性

- **🎯 一目了然**：每个脚本功能明确，注释详细
- **🔄 自动化**：一键完成复杂的配置处理流程
- **🛡️ 类型安全**：生成完整的TypeScript接口定义
- **📊 详细报告**：每个步骤都有详细的执行报告
- **🔧 可扩展**：模块化设计，易于扩展新功能

## 🚨 注意事项

1. **数据文件名保持不变**：迁移后的JSON文件保持原始名称（如Footballer.json），确保数据加载正常
2. **接口文件名规范化**：接口文件名应用项目命名规范（如hero.interface.ts）
3. **字段映射**：生成的接口包含完整的字段映射，支持新旧字段名转换
4. **执行顺序**：建议按照 迁移→生成→验证 的顺序执行，或直接使用setup脚本

## 📞 问题反馈

如果在使用过程中遇到问题，请检查：
1. 是否在项目根目录执行脚本
2. old项目目录是否存在且包含配置文件
3. 查看控制台输出的详细错误信息
