/**
 * 配置迁移工具
 * 从old项目复制配置文件并进行标准化处理
 * 使用项目根目录的node_modules
 */

const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');
const chalk = require('chalk');

class ConfigMigrator {
  constructor() {
    this.sourceDir = path.resolve('old/shared/config/data');
    this.targetDir = path.resolve('libs/game-config/src/data');
    
    // 字段映射规则
    this.commonFieldMappings = {
      'ID': 'id',
      'Id': 'id',
      'NAME': 'name',
      'Name': 'name',
      'DESC': 'description',
      'Desc': 'description',
      'DESCRIPTION': 'description'
    };
  }

  /**
   * 迁移所有配置文件
   */
  async migrateAll() {
    console.log(chalk.blue('🚀 开始迁移配置文件...'));
    
    try {
      // 确保目标目录存在
      await fs.ensureDir(this.targetDir);
      
      // 查找所有JSON文件 (使用相对路径避免Windows路径问题)
      const pattern = 'old/shared/config/data/*.json';
      const files = glob.sync(pattern);
      
      if (files.length === 0) {
        console.log(chalk.yellow('⚠️  未找到配置文件，请检查源目录路径'));
        return { success: false, message: '未找到配置文件' };
      }
      
      const report = {
        totalFiles: files.length,
        successFiles: 0,
        failedFiles: [],
        standardizedFields: new Set(),
        processedTables: []
      };
      
      // 逐个处理文件
      for (const file of files) {
        try {
          const tableName = path.basename(file, '.json');
          console.log(chalk.gray(`处理: ${tableName}`));
          
          const result = await this.migrateConfig(file, tableName);
          report.successFiles++;
          report.processedTables.push(tableName);
          
          // 记录标准化的字段
          result.standardizedFields.forEach(field => 
            report.standardizedFields.add(field)
          );
          
        } catch (error) {
          report.failedFiles.push({
            file: path.basename(file),
            error: error.message
          });
          console.log(chalk.red(`❌ ${path.basename(file)}: ${error.message}`));
        }
      }
      
      // 输出报告
      this.printMigrationReport(report);
      
      return {
        success: report.failedFiles.length === 0,
        report
      };
      
    } catch (error) {
      console.log(chalk.red('❌ 迁移过程中发生错误:'), error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 迁移单个配置文件
   */
  async migrateConfig(sourceFile, tableName) {
    // 读取原始JSON
    const originalData = await fs.readJson(sourceFile);
    
    if (!Array.isArray(originalData)) {
      throw new Error(`配置文件格式错误，期望数组格式: ${tableName}`);
    }
    
    if (originalData.length === 0) {
      console.log(chalk.yellow(`⚠️  配置文件为空: ${tableName}`));
    }
    
    // 字段标准化
    const { standardizedData, standardizedFields } = this.standardizeFields(originalData, tableName);
    
    // 数据验证
    this.validateConfigData(standardizedData, tableName);
    
    // 保存到目标位置
    const targetFile = path.join(this.targetDir, `${tableName}.json`);
    await fs.writeJson(targetFile, standardizedData, { spaces: 2 });
    
    console.log(chalk.green(`✅ ${tableName} (${standardizedData.length} 条记录)`));
    
    return { standardizedFields };
  }

  /**
   * 字段标准化处理
   */
  standardizeFields(data, tableName) {
    const fieldMappings = {
      ...this.commonFieldMappings
    };
    
    const standardizedFields = new Set();
    
    const standardizedData = data.map(item => {
      const standardized = {};
      
      for (const [key, value] of Object.entries(item)) {
        const standardKey = fieldMappings[key] || key;
        
        if (standardKey !== key) {
          standardizedFields.add(`${key} -> ${standardKey}`);
        }
        
        standardized[standardKey] = value;
      }
      
      return standardized;
    });
    
    return { standardizedData, standardizedFields };
  }

  /**
   * 数据验证
   */
  validateConfigData(data, tableName) {
    if (!Array.isArray(data) || data.length === 0) {
      return; // 空数据不验证
    }
    
    const sample = data[0];
    
    // 检查是否有ID字段
    const hasId = 'id' in sample || 'heroId' in sample || 'itemId' in sample || 'skillId' in sample;
    if (!hasId) {
      console.log(chalk.yellow(`⚠️  ${tableName}: 未找到ID字段`));
    }
    
    // 检查ID唯一性
    if (hasId) {
      const idField = Object.keys(sample).find(key => key.includes('id') || key.includes('Id'));
      const ids = data.map(item => item[idField]).filter(id => id != null);
      const uniqueIds = new Set(ids);
      
      if (ids.length !== uniqueIds.size) {
        console.log(chalk.yellow(`⚠️  ${tableName}: 发现重复ID`));
      }
    }
    
    // 检查必要字段
    const requiredFields = ['name', 'heroName', 'itemName', 'skillName'].filter(field => field in sample);
    for (const field of requiredFields) {
      const emptyCount = data.filter(item => !item[field] || item[field] === '').length;
      if (emptyCount > 0) {
        console.log(chalk.yellow(`⚠️  ${tableName}: ${emptyCount} 条记录的 ${field} 字段为空`));
      }
    }
  }

  /**
   * 打印迁移报告
   */
  printMigrationReport(report) {
    console.log('\n' + chalk.blue('📊 迁移报告'));
    console.log(chalk.gray('='.repeat(50)));
    
    console.log(`总文件数: ${chalk.cyan(report.totalFiles)}`);
    console.log(`成功迁移: ${chalk.green(report.successFiles)}`);
    console.log(`失败数量: ${chalk.red(report.failedFiles.length)}`);
    
    if (report.standardizedFields.size > 0) {
      console.log(`\n${chalk.blue('字段标准化:')}`);
      Array.from(report.standardizedFields).forEach(field => {
        console.log(`  ${chalk.gray(field)}`);
      });
    }
    
    if (report.failedFiles.length > 0) {
      console.log(`\n${chalk.red('失败文件:')}`);
      report.failedFiles.forEach(failed => {
        console.log(`  ${chalk.red(failed.file)}: ${failed.error}`);
      });
    }
    
    if (report.processedTables.length > 0) {
      console.log(`\n${chalk.blue('已处理的配置表:')}`);
      report.processedTables.forEach(table => {
        console.log(`  ${chalk.green('✓')} ${table}`);
      });
    }
    
    console.log(chalk.gray('='.repeat(50)));
    
    if (report.failedFiles.length === 0) {
      console.log(chalk.green('🎉 所有配置文件迁移成功！'));
    } else {
      console.log(chalk.yellow('⚠️  部分文件迁移失败，请检查错误信息'));
    }
  }

  /**
   * 获取迁移统计信息
   */
  async getStats() {
    try {
      const sourceFiles = glob.sync('old/shared/config/data/*.json');
      const targetFiles = glob.sync('libs/game-config/src/data/*.json');
      
      return {
        sourceCount: sourceFiles.length,
        targetCount: targetFiles.length,
        sourceTables: sourceFiles.map(f => path.basename(f, '.json')),
        targetTables: targetFiles.map(f => path.basename(f, '.json'))
      };
    } catch (error) {
      return {
        sourceCount: 0,
        targetCount: 0,
        sourceTables: [],
        targetTables: [],
        error: error.message
      };
    }
  }
}

module.exports = ConfigMigrator;
