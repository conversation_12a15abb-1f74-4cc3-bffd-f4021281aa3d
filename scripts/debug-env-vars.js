#!/usr/bin/env node

/**
 * 环境变量调试脚本
 * 检查环境变量是否正确加载
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 环境变量调试');
console.log('='.repeat(50));

// 1. 检查当前工作目录
console.log(`📁 当前工作目录: ${process.cwd()}`);

// 2. 检查 NODE_ENV
console.log(`🌍 NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);

// 3. 检查密码相关环境变量
console.log('\n🔑 密码策略环境变量:');
console.log(`PASSWORD_SALT_ROUNDS: "${process.env.PASSWORD_SALT_ROUNDS}"`);
console.log(`PASSWORD_MAX_AGE: "${process.env.PASSWORD_MAX_AGE}"`);
console.log(`PASSWORD_MIN_LENGTH: "${process.env.PASSWORD_MIN_LENGTH}"`);

// 4. 检查环境文件是否存在
console.log('\n📄 环境文件检查:');
const envFiles = [
  '.env',
  '.env.development',
  '.env.test',
  'apps/auth/.env.development',
  'apps/auth/.env.test',
  'apps/auth/.env.security.development',
  'apps/auth/.env.local'
];

envFiles.forEach(file => {
  const exists = fs.existsSync(path.join(process.cwd(), file));
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
});

// 5. 检查可能的配置冲突
console.log('\n⚠️  可能的配置冲突:');
if (process.env.PASSWORD_MAX_AGE === '0') {
  console.log('  ❌ PASSWORD_MAX_AGE 设置为 0，这会导致验证失败');
}
if (parseInt(process.env.PASSWORD_SALT_ROUNDS) < 10) {
  console.log('  ❌ PASSWORD_SALT_ROUNDS 小于 10，这会导致验证失败');
}

console.log('\n✅ 环境变量调试完成');
