#!/usr/bin/env node

/**
 * 简化的区服部署脚本（跨平台）
 * 使用Node.js实现，支持Windows和Linux
 * 
 * 使用方式：
 * node scripts/deploy-server-simple.js server001
 * node scripts/deploy-server-simple.js server001 character,hero
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

class ServerDeployer {
  constructor() {
    this.colors = {
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m'
    };
    
    this.defaultServices = ['character', 'hero', 'economy', 'social', 'activity', 'match'];
  }

  log(level, message) {
    const timestamp = new Date().toISOString();
    const color = this.colors[level] || this.colors.reset;
    console.log(`${color}[${level.toUpperCase()}]${this.colors.reset} ${message}`);
  }

  logInfo(message) { this.log('blue', message); }
  logSuccess(message) { this.log('green', message); }
  logWarning(message) { this.log('yellow', message); }
  logError(message) { this.log('red', message); }

  /**
   * 检查必要的依赖
   */
  checkDependencies() {
    this.logInfo('🔍 检查依赖工具...');
    
    const requiredTools = ['node', 'docker', 'docker-compose'];
    const missingTools = [];
    
    for (const tool of requiredTools) {
      try {
        execSync(`${tool} --version`, { stdio: 'ignore' });
      } catch (error) {
        missingTools.push(tool);
      }
    }
    
    if (missingTools.length > 0) {
      this.logError(`缺少必要工具: ${missingTools.join(', ')}`);
      this.logInfo('请安装缺少的工具后重试');
      process.exit(1);
    }
    
    this.logSuccess('依赖工具检查通过');
  }

  /**
   * 生成配置文件
   */
  generateConfigs(serverId, services) {
    this.logInfo('⚙️ 生成配置文件...');
    
    // 确保配置目录存在
    const configDir = path.join('config', serverId);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    
    // 为每个服务生成配置
    for (const service of services) {
      this.logInfo(`生成 ${service} 服务配置...`);
      
      try {
        const command = `node tools/config-generator/server-config-generator.js env --server=${serverId} --service=${service} --output=config/${serverId}/.env.${service}`;
        execSync(command, { stdio: 'inherit' });
      } catch (error) {
        this.logError(`生成 ${service} 配置失败: ${error.message}`);
        process.exit(1);
      }
    }
    
    this.logSuccess('配置文件生成完成');
  }

  /**
   * 生成Docker Compose文件
   */
  generateDockerCompose(serverId, services) {
    this.logInfo('🐳 生成Docker Compose配置...');
    
    try {
      const command = `node tools/config-generator/server-config-generator.js docker-compose-full --server=${serverId} --services=${services.join(' ')} --output=docker-compose.${serverId}.yml`;
      execSync(command, { stdio: 'inherit' });
      this.logSuccess(`Docker Compose配置生成完成: docker-compose.${serverId}.yml`);
    } catch (error) {
      this.logError(`生成Docker Compose配置失败: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 初始化数据库（可选）
   */
  initDatabases(serverId) {
    this.logInfo('🗄️ 生成数据库初始化脚本...');
    
    try {
      const command = `node tools/config-generator/server-config-generator.js database-init --server=${serverId}`;
      execSync(command, { stdio: 'inherit' });
      this.logSuccess('数据库初始化脚本生成完成');
      this.logWarning('请手动执行数据库初始化脚本（如果需要）');
    } catch (error) {
      this.logWarning(`数据库初始化脚本生成失败: ${error.message}`);
    }
  }

  /**
   * 部署服务实例
   */
  deployServices(serverId) {
    this.logInfo('🚀 部署服务实例...');
    
    const composeFile = `docker-compose.${serverId}.yml`;
    
    if (!fs.existsSync(composeFile)) {
      this.logError(`Docker Compose配置文件不存在: ${composeFile}`);
      process.exit(1);
    }
    
    try {
      // 启动服务
      this.logInfo('启动服务容器...');
      execSync(`docker-compose -f ${composeFile} up -d`, { stdio: 'inherit' });
      this.logSuccess('服务实例部署完成');
    } catch (error) {
      this.logError(`服务部署失败: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 等待服务就绪
   */
  async waitForServices(serverId, services) {
    this.logInfo('⏳ 等待服务就绪...');
    
    const maxWait = 300; // 最大等待5分钟
    const checkInterval = 10; // 每10秒检查一次
    
    const serviceConfigs = {
      character: { port: 3002, instances: 2 },
      hero: { port: 3003, instances: 2 },
      economy: { port: 3004, instances: 1 },
      social: { port: 3005, instances: 1 },
      activity: { port: 3006, instances: 1 },
      match: { port: 3007, instances: 2 },
    };
    
    for (const service of services) {
      const config = serviceConfigs[service];
      if (!config) continue;
      
      for (let i = 1; i <= config.instances; i++) {
        const instanceName = `${service}-${serverId}-${i}`;
        const port = config.port + (i - 1) * 10;
        
        this.logInfo(`等待 ${instanceName} 启动...`);
        
        let waitTime = 0;
        let isReady = false;
        
        while (waitTime < maxWait && !isReady) {
          try {
            // 使用curl检查健康状态（如果可用）
            execSync(`curl -f -s http://localhost:${port}/health/simple`, { stdio: 'ignore' });
            this.logSuccess(`${instanceName} 已就绪`);
            isReady = true;
          } catch (error) {
            // 健康检查失败，继续等待
            await this.sleep(checkInterval * 1000);
            waitTime += checkInterval;
          }
        }
        
        if (!isReady) {
          this.logWarning(`${instanceName} 启动超时，但继续部署...`);
        }
      }
    }
    
    this.logSuccess('服务就绪检查完成');
  }

  /**
   * 验证部署
   */
  verifyDeployment(serverId) {
    this.logInfo('✅ 验证部署...');
    
    const composeFile = `docker-compose.${serverId}.yml`;
    
    try {
      // 检查容器状态
      this.logInfo('检查容器状态...');
      execSync(`docker-compose -f ${composeFile} ps`, { stdio: 'inherit' });
      this.logSuccess('部署验证完成');
    } catch (error) {
      this.logWarning(`部署验证失败: ${error.message}`);
    }
  }

  /**
   * 输出部署信息
   */
  printDeploymentInfo(serverId, services) {
    this.logSuccess(`🎉 区服 ${serverId} 部署完成！`);
    console.log();
    this.logInfo('📋 部署信息:');
    this.logInfo(`  区服ID: ${serverId}`);
    this.logInfo(`  部署服务: ${services.join(', ')}`);
    this.logInfo(`  配置目录: config/${serverId}/`);
    this.logInfo(`  Docker Compose: docker-compose.${serverId}.yml`);
    console.log();
    this.logInfo('🔧 管理命令:');
    this.logInfo(`  查看状态: docker-compose -f docker-compose.${serverId}.yml ps`);
    this.logInfo(`  查看日志: docker-compose -f docker-compose.${serverId}.yml logs -f`);
    this.logInfo(`  停止服务: docker-compose -f docker-compose.${serverId}.yml down`);
    this.logInfo(`  重启服务: docker-compose -f docker-compose.${serverId}.yml restart`);
    console.log();
    this.logInfo('🏥 健康检查:');
    
    const serviceConfigs = {
      character: { port: 3002, instances: 2 },
      hero: { port: 3003, instances: 2 },
      economy: { port: 3004, instances: 1 },
      social: { port: 3005, instances: 1 },
      activity: { port: 3006, instances: 1 },
      match: { port: 3007, instances: 2 },
    };
    
    services.forEach(service => {
      const config = serviceConfigs[service];
      if (!config) return;
      
      for (let i = 1; i <= config.instances; i++) {
        const port = config.port + (i - 1) * 10;
        this.logInfo(`  ${service}-${serverId}-${i}: http://localhost:${port}/health`);
      }
    });
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 主部署流程
   */
  async deploy(serverId, services = this.defaultServices) {
    this.logInfo(`🚀 开始部署区服: ${serverId}`);
    this.logInfo(`📋 部署服务: ${services.join(', ')}`);
    
    try {
      this.checkDependencies();
      this.generateConfigs(serverId, services);
      this.generateDockerCompose(serverId, services);
      this.initDatabases(serverId);
      this.deployServices(serverId);
      await this.waitForServices(serverId, services);
      this.verifyDeployment(serverId);
      this.printDeploymentInfo(serverId, services);
      
      this.logSuccess('区服部署流程完成！');
    } catch (error) {
      this.logError(`部署过程中发生错误: ${error.message}`);
      process.exit(1);
    }
  }
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('使用方式: node scripts/deploy-server-simple.js <server_id> [service1,service2,...]');
    console.log('示例: node scripts/deploy-server-simple.js server001');
    console.log('示例: node scripts/deploy-server-simple.js server001 character,hero,economy');
    process.exit(1);
  }
  
  const serverId = args[0];
  const servicesInput = args[1];
  
  let services;
  if (servicesInput) {
    services = servicesInput.split(',').map(s => s.trim());
  } else {
    services = ['character', 'hero', 'economy', 'social', 'activity', 'match'];
  }
  
  const deployer = new ServerDeployer();
  await deployer.deploy(serverId, services);
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = { ServerDeployer };
