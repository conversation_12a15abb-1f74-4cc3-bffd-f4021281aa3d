/**
 * 抽奖系统综合测试脚本
 * 基于match服务的成功测试模式
 * 
 * 支持运行特定服务的测试：
 * - node scripts/test-lottery-systems.js                    # 运行所有抽奖系统测试
 * - node scripts/test-lottery-systems.js hero               # 只运行Hero服务测试
 * - node scripts/test-lottery-systems.js economy            # 只运行Economy服务测试
 * - node scripts/test-lottery-systems.js activity           # 只运行Activity服务测试
 * - node scripts/test-lottery-systems.js hero,economy       # 运行多个指定服务
 *
 * 测试目标：
 * 1. Hero服务：球探系统的完整流程（探索、搜索、签约、体力购买、RP兑换）
 * 2. Economy服务：传统抽奖系统（金币抽奖、代币抽奖、单抽、十连抽）
 * 3. Activity服务：活动抽奖系统（最佳11人、老虎机、拉霸、周末返场）
 */

const { spawn } = require('child_process');
const chalk = require('chalk');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
const targetServices = args.length > 0 ? args[0].split(',').map(s => s.trim().toLowerCase()) : [];
const existingCharacterId = args.length > 1 ? args[1] : null;

// 可用的测试服务
const AVAILABLE_SERVICES = {
  hero: { 
    name: 'Hero服务（球探系统）', 
    script: 'apps/hero/scripts/test-hero-system.js',
    module: 'scout'
  },
  economy: { 
    name: 'Economy服务（传统抽奖）', 
    script: 'apps/economy/scripts/test-economy-system.js',
    module: 'lottery'
  },
  activity: { 
    name: 'Activity服务（活动抽奖）', 
    script: 'apps/activity/scripts/test-activity-system.js',
    module: 'lottery'
  }
};

/**
 * 抽奖系统综合测试类
 */
class LotterySystemsTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(chalk.blue('\n📖 抽奖系统综合测试脚本使用说明'));
    console.log(chalk.gray('====================================='));
    console.log(chalk.white('运行所有抽奖系统测试:'));
    console.log(chalk.green('  node scripts/test-lottery-systems.js'));
    console.log(chalk.white('\n运行特定服务测试:'));

    Object.entries(AVAILABLE_SERVICES).forEach(([key, service]) => {
      console.log(chalk.green(`  node scripts/test-lottery-systems.js ${key.padEnd(12)} # ${service.name}测试`));
    });

    console.log(chalk.white('\n运行多个服务测试:'));
    console.log(chalk.green('  node scripts/test-lottery-systems.js hero,economy'));
    console.log(chalk.white('\n使用现有角色测试:'));
    console.log(chalk.green('  node scripts/test-lottery-systems.js hero char_1752805335899_wm3tgjcxp'));
    console.log(chalk.gray('  (建议先运行各服务的 quick-test-setup.js 准备测试数据)'));
    console.log(chalk.gray('\n可用服务: ' + Object.keys(AVAILABLE_SERVICES).join(', ')));
  }

  /**
   * 验证目标服务
   */
  validateTargetServices() {
    if (targetServices.length === 0) {
      return { valid: true, services: Object.keys(AVAILABLE_SERVICES) }; // 运行所有服务
    }

    const invalidServices = targetServices.filter(service => !AVAILABLE_SERVICES[service]);

    if (invalidServices.length > 0) {
      console.log(chalk.red(`❌ 无效的测试服务: ${invalidServices.join(', ')}`));
      this.showHelp();
      return { valid: false };
    }

    return { valid: true, services: targetServices };
  }

  /**
   * 运行单个服务测试
   */
  async runServiceTest(serviceKey, serviceInfo) {
    return new Promise((resolve) => {
      console.log(chalk.cyan(`\n🔧 开始 ${serviceInfo.name} 测试...`));

      const args = [serviceInfo.module];
      if (existingCharacterId) {
        args.push(existingCharacterId);
      }

      const testProcess = spawn('node', [serviceInfo.script, ...args], {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });

      testProcess.on('close', (code) => {
        if (code === 0) {
          console.log(chalk.green(`✅ ${serviceInfo.name} 测试完成`));
          this.testResults.push({ service: serviceKey, success: true });
        } else {
          console.log(chalk.red(`❌ ${serviceInfo.name} 测试失败 (退出码: ${code})`));
          this.testResults.push({ service: serviceKey, success: false, exitCode: code });
        }
        resolve();
      });

      testProcess.on('error', (error) => {
        console.log(chalk.red(`❌ ${serviceInfo.name} 测试异常: ${error.message}`));
        this.testResults.push({ service: serviceKey, success: false, error: error.message });
        resolve();
      });
    });
  }

  /**
   * 运行测试（支持选择性运行服务）
   */
  async runTests() {
    try {
      // 验证目标服务
      const validation = this.validateTargetServices();
      if (!validation.valid) {
        return;
      }

      const testServices = validation.services.map(serviceKey => ({
        key: serviceKey,
        ...AVAILABLE_SERVICES[serviceKey]
      }));

      console.log(chalk.blue('🚀 开始抽奖系统综合测试'));

      if (targetServices.length > 0) {
        console.log(chalk.yellow('🎯 目标测试服务:'), testServices.map(s => s.name).join(', '));
      } else {
        console.log(chalk.yellow('🎯 运行模式: 全部抽奖系统测试'));
      }

      if (existingCharacterId) {
        console.log(chalk.gray('🔄 使用现有角色:'), existingCharacterId);
      }

      // 运行选定的服务测试
      for (const serviceInfo of testServices) {
        await this.runServiceTest(serviceInfo.key, serviceInfo);
      }

      // 测试结果汇总
      console.log(chalk.blue('\n=== 抽奖系统测试结果汇总 ==='));
      const successCount = this.testResults.filter(r => r.success).length;
      const totalCount = this.testResults.length;

      console.log(chalk.white(`总测试服务: ${totalCount}`));
      console.log(chalk.green(`成功服务: ${successCount}`));
      console.log(chalk.red(`失败服务: ${totalCount - successCount}`));

      // 显示详细结果
      this.testResults.forEach(result => {
        const serviceInfo = AVAILABLE_SERVICES[result.service];
        if (result.success) {
          console.log(chalk.green(`  ✅ ${serviceInfo.name}`));
        } else {
          console.log(chalk.red(`  ❌ ${serviceInfo.name} - ${result.error || `退出码: ${result.exitCode}`}`));
        }
      });

      if (successCount === totalCount) {
        console.log(chalk.green('\n🎉 所有抽奖系统测试完成！系统运行正常'));
      } else {
        console.log(chalk.yellow(`\n⚠️ 部分抽奖系统测试失败，请检查失败的服务`));
      }

      console.log(chalk.blue('\n📋 测试建议:'));
      console.log(chalk.gray('1. 如果测试失败，请先检查对应的微服务是否正常启动'));
      console.log(chalk.gray('2. 建议先运行各服务的 quick-test-setup.js 准备充足的测试数据'));
      console.log(chalk.gray('3. 可以单独运行失败的服务测试进行详细调试'));

    } catch (error) {
      console.log(chalk.red(`\n❌ 测试执行失败: ${error.message}`));
      throw error;
    }
  }
}

// 运行测试
if (require.main === module) {
  // 检查是否请求帮助
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    const tester = new LotterySystemsTester();
    tester.showHelp();
    process.exit(0);
  }

  const tester = new LotterySystemsTester();
  tester.runTests().catch((error) => {
    console.error(chalk.red('测试执行失败:'), error);
    process.exit(1);
  });
}

module.exports = LotterySystemsTester;
