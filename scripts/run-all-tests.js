#!/usr/bin/env node

/**
 * 综合测试执行脚本
 * 按顺序执行所有类型的测试并生成详细报告
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const ServiceManager = require('./service-manager');

class TestRunner {
  constructor() {
    this.config = {
      testTypes: [
        { name: 'health', command: 'npm run health', description: '健康检查' },
        { name: 'api', command: 'npm run test:api', description: 'API功能测试' },
        { name: 'websocket', command: 'npm run test:websocket', description: 'WebSocket测试' },
        { name: 'security', command: 'npm run test:security', description: '安全测试' },
        { name: 'load', command: 'npm run test:load', description: '负载测试' },
        { name: 'stress', command: 'npm run test:stress', description: '压力测试' },
        { name: 'concurrent', command: 'npm run test:concurrent', description: '并发测试' }
      ],
      outputDir: path.join(__dirname, '../test-results'),
      logDir: path.join(__dirname, '../test-logs'),
      timeout: 300000 // 5分钟超时
    };
    
    this.results = {
      startTime: null,
      endTime: null,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      testResults: [],
      summary: {}
    };
    
    this.ensureDirectories();
    this.serviceManager = new ServiceManager();
  }

  // 确保目录存在
  ensureDirectories() {
    [this.config.outputDir, this.config.logDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  // 日志输出
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      start: '🚀',
      finish: '🏁'
    }[type] || '📋';
    
    const logMessage = `[${timestamp}] ${prefix} ${message}`;
    console.log(logMessage);
    
    // 写入日志文件
    const logFile = path.join(this.config.logDir, 'test-runner.log');
    fs.appendFileSync(logFile, logMessage + '\n');
  }

  // 执行单个测试
  async runSingleTest(testConfig) {
    this.log(`开始执行 ${testConfig.description}...`, 'start');
    
    const testResult = {
      name: testConfig.name,
      description: testConfig.description,
      command: testConfig.command,
      startTime: Date.now(),
      endTime: null,
      duration: 0,
      exitCode: null,
      passed: false,
      output: '',
      error: '',
      logFile: null
    };
    
    return new Promise((resolve) => {
      const logFile = path.join(this.config.logDir, `${testConfig.name}-test.log`);
      testResult.logFile = logFile;
      
      const logStream = fs.createWriteStream(logFile);
      
      // 解析命令
      const [command, ...args] = testConfig.command.split(' ');
      
      const childProcess = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        cwd: path.join(__dirname, '..')
      });
      
      let stdout = '';
      let stderr = '';
      
      // 收集输出
      childProcess.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        logStream.write(output);
        
        // 实时显示重要信息
        if (output.includes('✅') || output.includes('❌') || output.includes('通过') || output.includes('失败')) {
          process.stdout.write(output);
        }
      });
      
      childProcess.stderr.on('data', (data) => {
        const error = data.toString();
        stderr += error;
        logStream.write(`STDERR: ${error}`);
      });
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.log(`测试 ${testConfig.name} 超时，正在终止...`, 'warning');
        childProcess.kill('SIGTERM');
        
        setTimeout(() => {
          childProcess.kill('SIGKILL');
        }, 5000);
      }, this.config.timeout);
      
      childProcess.on('close', (code) => {
        clearTimeout(timeout);
        logStream.end();
        
        testResult.endTime = Date.now();
        testResult.duration = testResult.endTime - testResult.startTime;
        testResult.exitCode = code;
        testResult.passed = code === 0;
        testResult.output = stdout;
        testResult.error = stderr;
        
        this.results.totalTests++;
        
        if (testResult.passed) {
          this.results.passedTests++;
          this.log(`${testConfig.description} 通过 (${(testResult.duration / 1000).toFixed(2)}s)`, 'success');
        } else {
          this.results.failedTests++;
          this.log(`${testConfig.description} 失败 (退出码: ${code})`, 'error');
        }
        
        this.results.testResults.push(testResult);
        resolve(testResult);
      });
      
      childProcess.on('error', (error) => {
        clearTimeout(timeout);
        logStream.end();
        
        testResult.endTime = Date.now();
        testResult.duration = testResult.endTime - testResult.startTime;
        testResult.exitCode = -1;
        testResult.passed = false;
        testResult.error = error.message;
        
        this.results.totalTests++;
        this.results.failedTests++;
        
        this.log(`${testConfig.description} 执行失败: ${error.message}`, 'error');
        
        this.results.testResults.push(testResult);
        resolve(testResult);
      });
    });
  }

  // 清理旧进程并检查服务状态
  async setupServices() {
    this.log('设置测试环境...', 'start');

    // 只清理旧进程，不启动新服务
    await this.serviceManager.cleanupOldProcesses();

    this.log('请确保以下服务已手动启动:', 'warning');
    this.log('  - 认证服务: npm run start:auth (端口 3001)', 'warning');
    this.log('  - 网关服务: npm run start:gateway (端口 3000)', 'warning');
    this.log('等待5秒后开始健康检查...', 'start');

    // 等待用户启动服务
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 健康检查
    const axios = require('axios');
    const services = [
      { name: 'Auth服务', url: 'http://localhost:3001/api/health' },
      { name: 'Gateway服务', url: 'http://localhost:3000/health' }
    ];

    const healthResults = {};

    for (const service of services) {
      try {
        const response = await axios.get(service.url, { timeout: 5000 });
        healthResults[service.name] = true;
        this.log(`${service.name} 健康检查通过`, 'success');
      } catch (error) {
        healthResults[service.name] = false;
        this.log(`${service.name} 健康检查失败: ${error.message}`, 'error');
      }
    }

    const allHealthy = Object.values(healthResults).every(h => h);
    if (!allHealthy) {
      this.log('部分服务不可用，请检查服务是否正确启动', 'error');
      this.log('测试将继续执行，但可能会失败', 'warning');
    }

    return allHealthy;
  }

  // 清理服务（仅清理测试产生的数据，不停止服务）
  async cleanupServices() {
    this.log('清理测试环境...', 'start');
    this.log('注意: 服务将继续运行，仅清理测试数据', 'info');
    // 这里可以添加清理测试数据的逻辑，比如删除测试用户等
  }

  // 生成测试报告
  generateReport() {
    const totalDuration = this.results.endTime - this.results.startTime;
    const passRate = (this.results.passedTests / this.results.totalTests * 100).toFixed(2);
    
    const report = {
      summary: {
        startTime: new Date(this.results.startTime).toISOString(),
        endTime: new Date(this.results.endTime).toISOString(),
        totalDuration: `${(totalDuration / 1000).toFixed(2)}s`,
        totalTests: this.results.totalTests,
        passedTests: this.results.passedTests,
        failedTests: this.results.failedTests,
        skippedTests: this.results.skippedTests,
        passRate: `${passRate}%`
      },
      testResults: this.results.testResults.map(result => ({
        name: result.name,
        description: result.description,
        passed: result.passed,
        duration: `${(result.duration / 1000).toFixed(2)}s`,
        exitCode: result.exitCode,
        logFile: result.logFile
      })),
      recommendations: this.generateRecommendations()
    };
    
    // 保存JSON报告
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(this.config.outputDir, `test-report-${timestamp}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    // 生成HTML报告
    this.generateHtmlReport(report, timestamp);
    
    return report;
  }

  // 生成建议
  generateRecommendations() {
    const recommendations = [];
    
    const failedTests = this.results.testResults.filter(r => !r.passed);
    
    if (failedTests.length > 0) {
      recommendations.push({
        type: 'error',
        title: '失败的测试需要修复',
        description: `有 ${failedTests.length} 个测试失败，需要检查和修复`,
        tests: failedTests.map(t => t.name)
      });
    }
    
    const slowTests = this.results.testResults.filter(r => r.duration > 60000); // 超过1分钟
    if (slowTests.length > 0) {
      recommendations.push({
        type: 'warning',
        title: '测试执行时间过长',
        description: '以下测试执行时间超过1分钟，可能需要优化',
        tests: slowTests.map(t => ({ name: t.name, duration: `${(t.duration / 1000).toFixed(2)}s` }))
      });
    }
    
    const passRate = (this.results.passedTests / this.results.totalTests * 100);
    if (passRate < 80) {
      recommendations.push({
        type: 'error',
        title: '测试通过率过低',
        description: `当前通过率为 ${passRate.toFixed(2)}%，建议通过率应达到80%以上`
      });
    }
    
    return recommendations;
  }

  // 生成HTML报告
  generateHtmlReport(report, timestamp) {
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - ${timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .warning { color: #ffc107; }
        .test-results { margin-bottom: 30px; }
        .test-item { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #ddd; }
        .test-item.passed { border-left-color: #28a745; }
        .test-item.failed { border-left-color: #dc3545; }
        .test-name { font-weight: bold; margin-bottom: 5px; }
        .test-details { font-size: 14px; color: #666; }
        .recommendations { background: #fff3cd; padding: 20px; border-radius: 6px; border: 1px solid #ffeaa7; }
        .recommendation { margin: 10px 0; padding: 10px; background: white; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 足球经理游戏服务器测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="value">${report.summary.totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>通过测试</h3>
                <div class="value passed">${report.summary.passedTests}</div>
            </div>
            <div class="summary-card">
                <h3>失败测试</h3>
                <div class="value failed">${report.summary.failedTests}</div>
            </div>
            <div class="summary-card">
                <h3>通过率</h3>
                <div class="value ${parseFloat(report.summary.passRate) >= 80 ? 'passed' : 'failed'}">${report.summary.passRate}</div>
            </div>
            <div class="summary-card">
                <h3>总耗时</h3>
                <div class="value">${report.summary.totalDuration}</div>
            </div>
        </div>
        
        <div class="test-results">
            <h2>📋 详细测试结果</h2>
            ${report.testResults.map(test => `
                <div class="test-item ${test.passed ? 'passed' : 'failed'}">
                    <div class="test-name">${test.passed ? '✅' : '❌'} ${test.description}</div>
                    <div class="test-details">
                        耗时: ${test.duration} | 退出码: ${test.exitCode} | 日志: ${path.basename(test.logFile)}
                    </div>
                </div>
            `).join('')}
        </div>
        
        ${report.recommendations.length > 0 ? `
        <div class="recommendations">
            <h2>💡 建议和改进</h2>
            ${report.recommendations.map(rec => `
                <div class="recommendation">
                    <strong>${rec.title}</strong><br>
                    ${rec.description}
                    ${rec.tests ? `<br><small>涉及测试: ${Array.isArray(rec.tests[0]) ? rec.tests.map(t => typeof t === 'object' ? `${t.name} (${t.duration})` : t).join(', ') : rec.tests.join(', ')}</small>` : ''}
                </div>
            `).join('')}
        </div>
        ` : ''}
    </div>
</body>
</html>`;
    
    const htmlFile = path.join(this.config.outputDir, `test-report-${timestamp}.html`);
    fs.writeFileSync(htmlFile, htmlContent);
    this.log(`HTML报告已生成: ${htmlFile}`, 'success');
  }

  // 打印控制台报告
  printConsoleReport(report) {
    console.log('\n' + '='.repeat(80));
    console.log('🧪 综合测试报告');
    console.log('='.repeat(80));
    
    console.log(`📊 测试概览:`);
    console.log(`   开始时间: ${report.summary.startTime}`);
    console.log(`   结束时间: ${report.summary.endTime}`);
    console.log(`   总耗时: ${report.summary.totalDuration}`);
    console.log(`   总测试数: ${report.summary.totalTests}`);
    console.log(`   通过测试: ${report.summary.passedTests}`);
    console.log(`   失败测试: ${report.summary.failedTests}`);
    console.log(`   通过率: ${report.summary.passRate}`);
    
    console.log(`\n📋 详细结果:`);
    report.testResults.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`   ${status} ${test.description} (${test.duration})`);
    });
    
    if (report.recommendations.length > 0) {
      console.log(`\n💡 建议:`);
      report.recommendations.forEach(rec => {
        console.log(`   - ${rec.title}: ${rec.description}`);
      });
    }
    
    console.log('='.repeat(80));
  }

  // 运行所有测试
  async run() {
    this.log('开始执行综合测试套件...', 'start');
    this.results.startTime = Date.now();
    
    try {
      // 设置测试环境
      const servicesReady = await this.setupServices();

      if (!servicesReady) {
        this.log('服务启动失败，无法执行测试', 'error');
        process.exit(1);
      }
      
      // 执行所有测试
      for (const testConfig of this.config.testTypes) {
        await this.runSingleTest(testConfig);
        
        // 测试间短暂休息
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      this.results.endTime = Date.now();
      
      // 生成报告
      const report = this.generateReport();
      this.printConsoleReport(report);
      
      this.log('综合测试执行完成！', 'finish');

      // 清理测试环境
      await this.cleanupServices();

      // 根据结果设置退出码
      const exitCode = this.results.failedTests > 0 ? 1 : 0;
      process.exit(exitCode);

    } catch (error) {
      this.log(`测试执行失败: ${error.message}`, 'error');

      // 确保清理环境
      try {
        await this.cleanupServices();
      } catch (cleanupError) {
        this.log(`清理环境失败: ${cleanupError.message}`, 'warning');
      }

      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const runner = new TestRunner();
  await runner.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = TestRunner;
