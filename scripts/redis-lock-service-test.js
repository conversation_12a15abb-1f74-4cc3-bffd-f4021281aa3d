#!/usr/bin/env node

/**
 * Redis 分布式锁服务专项测试
 * 
 * 测试范围：
 * 1. RedisLockService - 分布式锁基础功能
 * 2. 锁竞争和并发控制
 * 3. 锁超时和续期机制
 * 4. 死锁检测和恢复
 */

const axios = require('axios');
const io = require('socket.io-client');

// 测试配置
const config = {
  gateway: {
    baseUrl: 'http://127.0.0.1:3000',
  },
  timeout: 15000,
};

// 颜色输出函数
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class RedisLockServiceTester {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: [],
      details: {}
    };
  }

  async initialize() {
    log('🚀 初始化Redis分布式锁服务测试...', 'cyan');
    
    try {
      // 检查网关服务
      const response = await axios.get(`${config.gateway.baseUrl}/health`, {
        timeout: config.timeout
      });
      
      if (response.status === 200) {
        log('✅ 网关服务连接成功', 'green');
        return true;
      } else {
        throw new Error(`网关服务状态异常: ${response.status}`);
      }
    } catch (error) {
      log(`❌ 网关服务连接失败: ${error.message}`, 'red');
      return false;
    }
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    log(`\n🧪 执行测试: ${testName}`, 'blue');
    
    try {
      const startTime = Date.now();
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      this.testResults.passed++;
      this.testResults.details[testName] = { ...result, duration, status: 'passed' };
      log(`  ✅ ${testName} - 通过 (${duration}ms)`, 'green');
      return result;
    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push({ test: testName, error: error.message });
      this.testResults.details[testName] = { duration: 0, status: 'failed', error: error.message };
      log(`  ❌ ${testName} - 失败: ${error.message}`, 'red');
      throw error;
    }
  }

  // ==================== 分布式锁基础测试 ====================

  async testBasicLockOperations() {
    return await this.runTest('分布式锁基础操作', async () => {
      // 通过WebSocket连接测试分布式锁功能
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          socket.disconnect();
          reject(new Error('锁测试超时'));
        }, 15000); // 增加超时时间到15秒

        const socket = io(config.gateway.baseUrl, {
          transports: ['websocket']
        });

        let connected = false;
        let lockAcquired = false;
        let lockReleased = false;

        socket.on('connect', () => {
          connected = true;
          log('    WebSocket连接成功', 'green');
          
          // 模拟获取分布式锁
          socket.emit('acquire_lock', {
            lockKey: 'test-lock-basic',
            ttl: 5000,
            clientId: 'test-client-1'
          });
        });

        socket.on('lock_acquired', (data) => {
          lockAcquired = true;
          log(`    获取锁成功: ${data.lockKey}`, 'green');
          
          // 释放锁
          socket.emit('release_lock', {
            lockKey: 'test-lock-basic',
            lockId: data.lockId,
            clientId: 'test-client-1'
          });
        });

        socket.on('lock_released', (data) => {
          lockReleased = true;
          log(`    释放锁成功: ${data.lockKey}`, 'green');

          clearTimeout(timeout);
          socket.disconnect();

          resolve({
            connected,
            lockAcquired,
            lockReleased,
            basicLockWorking: connected && lockAcquired && lockReleased
          });
        });

        socket.on('lock_failed', (data) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`锁操作失败: ${data.reason}`));
        });

        socket.on('lock_release_failed', (data) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`锁释放失败: ${data.reason}`));
        });

        socket.on('lock_error', (data) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`锁错误: ${data.error}`));
        });

        socket.on('connect_error', (error) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`WebSocket连接错误: ${error.message}`));
        });
      });
    });
  }

  async testLockConcurrency() {
    return await this.runTest('锁并发竞争测试', async () => {
      // 创建多个WebSocket连接模拟并发锁竞争
      const clientCount = 3;
      const lockKey = 'test-lock-concurrent';
      
      const promises = [];
      
      for (let i = 0; i < clientCount; i++) {
        promises.push(this.createLockClient(i + 1, lockKey));
      }

      const results = await Promise.all(promises);
      
      // 分析结果
      const successfulAcquisitions = results.filter(r => r.lockAcquired).length;
      const allConnected = results.every(r => r.connected);
      
      // 应该只有一个客户端成功获取锁
      if (successfulAcquisitions !== 1) {
        throw new Error(`锁竞争失败: ${successfulAcquisitions}个客户端获得锁，应该只有1个`);
      }

      return {
        clientCount,
        allConnected,
        successfulAcquisitions,
        lockExclusivity: successfulAcquisitions === 1,
        concurrencyTestPassed: allConnected && successfulAcquisitions === 1
      };
    });
  }

  async createLockClient(clientId, lockKey) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        socket.disconnect();
        resolve({
          clientId,
          connected: false,
          lockAcquired: false,
          timeout: true
        });
      }, 8000);

      const socket = io(config.gateway.baseUrl, {
        transports: ['websocket']
      });

      let connected = false;
      let lockAcquired = false;

      socket.on('connect', () => {
        connected = true;
        
        // 尝试获取锁
        socket.emit('acquire_lock', {
          lockKey,
          ttl: 3000,
          clientId: `client-${clientId}`
        });
      });

      socket.on('lock_acquired', (data) => {
        lockAcquired = true;
        log(`    客户端${clientId}获取锁成功`, 'green');
        
        // 持有锁一段时间后释放
        setTimeout(() => {
          socket.emit('release_lock', {
            lockKey,
            lockId: data.lockId,
            clientId: `client-${clientId}`
          });
        }, 1000);
      });

      socket.on('lock_released', () => {
        clearTimeout(timeout);
        socket.disconnect();
        
        resolve({
          clientId,
          connected,
          lockAcquired,
          timeout: false
        });
      });

      socket.on('lock_failed', () => {
        // 获取锁失败是正常的（其他客户端已获取）
        clearTimeout(timeout);
        socket.disconnect();
        
        resolve({
          clientId,
          connected,
          lockAcquired: false,
          timeout: false
        });
      });

      socket.on('connect_error', () => {
        clearTimeout(timeout);
        resolve({
          clientId,
          connected: false,
          lockAcquired: false,
          timeout: false
        });
      });
    });
  }

  async testLockTimeout() {
    return await this.runTest('锁超时机制测试', async () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          socket.disconnect();
          reject(new Error('锁超时测试超时'));
        }, 15000);

        const socket = io(config.gateway.baseUrl, {
          transports: ['websocket']
        });

        let connected = false;
        let lockAcquired = false;
        let lockExpired = false;

        socket.on('connect', () => {
          connected = true;
          
          // 获取一个短TTL的锁
          socket.emit('acquire_lock', {
            lockKey: 'test-lock-timeout',
            ttl: 2000, // 2秒TTL
            clientId: 'timeout-test-client'
          });
        });

        socket.on('lock_acquired', (data) => {
          if (data.clientId === 'timeout-test-client') {
            lockAcquired = true;
            log(`    获取短TTL锁成功，等待过期...`, 'yellow');
            // 不主动释放锁，等待自动过期
          } else if (data.clientId === 'timeout-test-client-2') {
            // 第二次获取锁成功，说明第一个锁已过期
            lockExpired = true;
            log(`    第二次获取锁成功，证明第一个锁已过期`, 'green');

            clearTimeout(timeout);
            socket.disconnect();

            resolve({
              connected,
              lockAcquired,
              lockExpired,
              timeoutMechanismWorking: lockAcquired && lockExpired
            });
          }
        });

        socket.on('lock_expired', (data) => {
          lockExpired = true;
          log(`    锁自动过期: ${data.lockKey}`, 'green');

          clearTimeout(timeout);
          socket.disconnect();

          resolve({
            connected,
            lockAcquired,
            lockExpired,
            timeoutMechanismWorking: lockAcquired && lockExpired
          });
        });

        // 如果没有lock_expired事件，我们等待一段时间后检查
        setTimeout(() => {
          if (lockAcquired && !lockExpired) {
            // 尝试再次获取同一个锁，如果成功说明之前的锁已过期
            socket.emit('acquire_lock', {
              lockKey: 'test-lock-timeout',
              ttl: 1000,
              clientId: 'timeout-test-client-2'
            });
          }
        }, 3000);

        socket.on('connect_error', (error) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`WebSocket连接错误: ${error.message}`));
        });
      });
    });
  }

  async testLockReentrancy() {
    return await this.runTest('可重入锁测试', async () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          socket.disconnect();
          reject(new Error('可重入锁测试超时'));
        }, 10000);

        const socket = io(config.gateway.baseUrl, {
          transports: ['websocket']
        });

        let connected = false;
        let firstLockAcquired = false;
        let secondLockAcquired = false;
        let lockReleased = false;
        let firstLockId = null; // 保存第一次获取的lockId

        socket.on('connect', () => {
          connected = true;

          // 第一次获取可重入锁
          socket.emit('acquire_lock', {
            lockKey: 'test-lock-reentrant',
            ttl: 5000,
            clientId: 'reentrant-test-client',
            reentrant: true
          });
        });

        socket.on('lock_acquired', (data) => {
          if (!firstLockAcquired) {
            firstLockAcquired = true;
            firstLockId = data.lockId; // 保存第一次获取的lockId
            log(`    第一次获取锁成功，lockId: ${firstLockId}`, 'green');

            // 同一客户端再次获取同一个锁（可重入）
            socket.emit('acquire_lock', {
              lockKey: 'test-lock-reentrant',
              ttl: 5000,
              clientId: 'reentrant-test-client',
              reentrant: true
            });
          } else {
            secondLockAcquired = true;
            log(`    可重入锁获取成功，lockId: ${data.lockId}`, 'green');

            // 释放可重入锁（使用当前的lockId）
            socket.emit('release_lock', {
              lockKey: 'test-lock-reentrant',
              lockId: data.lockId,
              clientId: 'reentrant-test-client',
              reentrant: true
            });
          }
        });

        socket.on('lock_released', () => {
          lockReleased = true;
          log(`    可重入锁释放成功`, 'green');

          clearTimeout(timeout);
          socket.disconnect();

          resolve({
            connected,
            firstLockAcquired,
            secondLockAcquired,
            lockReleased,
            reentrancyWorking: firstLockAcquired && secondLockAcquired && lockReleased
          });
        });

        socket.on('lock_failed', (data) => {
          if (firstLockAcquired && !secondLockAcquired) {
            // 如果第二次获取失败，说明不支持可重入，这也是可以接受的
            log(`    不支持可重入锁（这是可以接受的）`, 'yellow');

            // 使用第一次获取的真实lockId释放锁
            socket.emit('release_lock', {
              lockKey: 'test-lock-reentrant',
              lockId: firstLockId,
              clientId: 'reentrant-test-client',
              reentrant: true
            });
          } else {
            clearTimeout(timeout);
            socket.disconnect();
            reject(new Error(`锁操作失败: ${data.reason}`));
          }
        });

        socket.on('connect_error', (error) => {
          clearTimeout(timeout);
          socket.disconnect();
          reject(new Error(`WebSocket连接错误: ${error.message}`));
        });
      });
    });
  }

  // ==================== 主测试流程 ====================

  async runAllTests() {
    log('🎯 开始Redis分布式锁服务专项测试\n', 'cyan');
    log('📋 测试计划:', 'cyan');
    log('  1. 分布式锁基础操作 - 获取/释放锁', 'blue');
    log('  2. 锁并发竞争测试 - 多客户端锁竞争', 'blue');
    log('  3. 锁超时机制测试 - TTL自动过期', 'blue');
    log('  4. 可重入锁测试 - 同一客户端重复获取', 'blue');
    log('', 'white');

    try {
      // 基础锁操作测试
      await this.testBasicLockOperations();
      
      // 并发竞争测试
      await this.testLockConcurrency();
      
      // 超时机制测试
      await this.testLockTimeout();
      
      // 可重入锁测试
      await this.testLockReentrancy();

    } catch (error) {
      log(`\n💥 测试过程中发生错误: ${error.message}`, 'red');
    }

    // 输出测试结果
    this.printTestResults();
  }

  printTestResults() {
    log('\n📊 Redis分布式锁服务测试结果汇总', 'cyan');
    log('='.repeat(60), 'cyan');
    
    log(`总测试数: ${this.testResults.total}`, 'blue');
    log(`通过: ${this.testResults.passed}`, 'green');
    log(`失败: ${this.testResults.failed}`, 'red');
    
    const successRate = this.testResults.total > 0 ? 
      ((this.testResults.passed / this.testResults.total) * 100).toFixed(2) : 0;
    log(`成功率: ${successRate}%`, successRate >= 75 ? 'green' : 'yellow');

    // 详细测试结果
    log('\n📋 详细测试结果:', 'cyan');
    Object.entries(this.testResults.details).forEach(([testName, details]) => {
      const statusColor = details.status === 'passed' ? 'green' : 'red';
      const statusIcon = details.status === 'passed' ? '✅' : '❌';
      
      log(`  ${statusIcon} ${testName} (${details.duration}ms)`, statusColor);
      
      if (details.status === 'passed') {
        // 显示关键指标
        if (details.basicLockWorking !== undefined) {
          log(`    基础锁功能: ${details.basicLockWorking ? '正常' : '异常'}`, 
              details.basicLockWorking ? 'green' : 'red');
        }
        if (details.lockExclusivity !== undefined) {
          log(`    锁排他性: ${details.lockExclusivity ? '正常' : '异常'}`, 
              details.lockExclusivity ? 'green' : 'red');
        }
        if (details.timeoutMechanismWorking !== undefined) {
          log(`    超时机制: ${details.timeoutMechanismWorking ? '正常' : '异常'}`, 
              details.timeoutMechanismWorking ? 'green' : 'red');
        }
      }
    });

    if (this.testResults.errors.length > 0) {
      log('\n❌ 失败的测试详情:', 'red');
      this.testResults.errors.forEach(error => {
        log(`  - ${error.test}: ${error.error}`, 'red');
      });
    }

    // 总结
    if (this.testResults.failed === 0) {
      log('\n🎉 所有Redis分布式锁测试通过！', 'green');
      log('   RedisLockService工作完美！', 'green');
    } else if (successRate >= 75) {
      log('\n✅ Redis分布式锁基本功能正常！', 'green');
      log('   大部分锁机制工作正常', 'yellow');
    } else {
      log('\n⚠️  Redis分布式锁存在问题，需要进一步检查', 'yellow');
    }

    log('\n📝 测试说明:', 'cyan');
    log('  ✅ 通过WebSocket模拟分布式锁场景', 'blue');
    log('  ✅ 验证锁的排他性和并发控制', 'blue');
    log('  ✅ 测试锁的超时和自动释放机制', 'blue');
    log('  ✅ 检查可重入锁支持（可选功能）', 'blue');
  }
}

// 主执行函数
async function main() {
  const tester = new RedisLockServiceTester();
  
  try {
    const initialized = await tester.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await tester.runAllTests();
    
    // 根据测试结果设置退出码
    const successRate = tester.testResults.passed / tester.testResults.total;
    process.exit(successRate >= 0.75 ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 测试执行失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = RedisLockServiceTester;
