# 项目文件传输到CentOS虚拟机脚本

param(
    [Parameter(Mandatory=$true)]
    [string]$VMHost = "***************",
    
    [Parameter(Mandatory=$true)]
    [string]$VMUser,
    
    [string]$ProjectPath = "E:\football manager\server-new",
    [string]$VMProjectPath = "~/football-manager-test",
    [string]$SSHKeyPath = "",
    [switch]$UsePassword
)

# 颜色输出函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查传输依赖..."
    
    # 检查OpenSSH客户端
    if (!(Get-Command ssh -ErrorAction SilentlyContinue)) {
        Write-Error "未找到SSH客户端，请安装OpenSSH客户端"
        Write-Info "可以通过以下方式安装："
        Write-Info "1. Windows设置 -> 应用 -> 可选功能 -> 添加功能 -> OpenSSH客户端"
        Write-Info "2. 或使用PowerShell: Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******"
        return $false
    }
    
    # 检查SCP
    if (!(Get-Command scp -ErrorAction SilentlyContinue)) {
        Write-Error "未找到SCP命令"
        return $false
    }
    
    # 检查项目目录
    if (!(Test-Path $ProjectPath)) {
        Write-Error "项目目录不存在: $ProjectPath"
        return $false
    }
    
    Write-Success "依赖检查通过"
    return $true
}

# 测试虚拟机连接
function Test-VMConnection {
    Write-Info "测试虚拟机连接..."
    
    try {
        $pingResult = Test-Connection -ComputerName $VMHost -Count 2 -Quiet
        if ($pingResult) {
            Write-Success "虚拟机网络连通"
        } else {
            Write-Error "无法ping通虚拟机"
            return $false
        }
    }
    catch {
        Write-Error "网络连接测试失败: $($_.Exception.Message)"
        return $false
    }
    
    # 测试SSH端口
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $connect = $tcpClient.BeginConnect($VMHost, 22, $null, $null)
        $wait = $connect.AsyncWaitHandle.WaitOne(5000, $false)
        $tcpClient.Close()
        
        if ($wait) {
            Write-Success "SSH端口(22)可访问"
            return $true
        } else {
            Write-Error "SSH端口(22)不可访问"
            return $false
        }
    }
    catch {
        Write-Error "SSH端口测试失败: $($_.Exception.Message)"
        return $false
    }
}

# 准备项目文件
function Prepare-ProjectFiles {
    Write-Info "准备项目文件..."
    
    $tempDir = Join-Path $env:TEMP "football-manager-transfer"
    $zipPath = Join-Path $tempDir "project.zip"
    
    # 创建临时目录
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    # 排除不需要的文件和目录
    $excludePatterns = @(
        "node_modules",
        ".git",
        "dist",
        "coverage",
        "*.log",
        "test-results",
        "test-logs",
        ".env*",
        "*.tmp"
    )
    
    Write-Info "压缩项目文件（排除不必要的文件）..."
    
    # 创建排除文件列表
    $excludeFile = Join-Path $tempDir "exclude.txt"
    $excludePatterns | Out-File -FilePath $excludeFile -Encoding UTF8
    
    # 使用7-Zip压缩（如果可用）
    if (Get-Command 7z -ErrorAction SilentlyContinue) {
        $excludeArgs = $excludePatterns | ForEach-Object { "-x!$_" }
        & 7z a $zipPath "$ProjectPath\*" $excludeArgs
    }
    # 使用PowerShell内置压缩
    else {
        # 复制文件到临时目录，排除不需要的文件
        $sourceFiles = Get-ChildItem -Path $ProjectPath -Recurse | Where-Object {
            $relativePath = $_.FullName.Substring($ProjectPath.Length + 1)
            $shouldExclude = $false
            
            foreach ($pattern in $excludePatterns) {
                if ($relativePath -like $pattern -or $relativePath -like "*\$pattern\*" -or $relativePath -like "*/$pattern/*") {
                    $shouldExclude = $true
                    break
                }
            }
            
            return -not $shouldExclude
        }
        
        $tempProjectDir = Join-Path $tempDir "project"
        New-Item -ItemType Directory -Path $tempProjectDir | Out-Null
        
        foreach ($file in $sourceFiles) {
            if ($file.PSIsContainer) {
                $destDir = Join-Path $tempProjectDir $file.FullName.Substring($ProjectPath.Length + 1)
                if (!(Test-Path $destDir)) {
                    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
                }
            } else {
                $destFile = Join-Path $tempProjectDir $file.FullName.Substring($ProjectPath.Length + 1)
                $destDir = Split-Path $destFile -Parent
                if (!(Test-Path $destDir)) {
                    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
                }
                Copy-Item $file.FullName $destFile
            }
        }
        
        Compress-Archive -Path "$tempProjectDir\*" -DestinationPath $zipPath -Force
    }
    
    if (Test-Path $zipPath) {
        $zipSize = (Get-Item $zipPath).Length / 1MB
        Write-Success "项目文件压缩完成: $([math]::Round($zipSize, 2))MB"
        return $zipPath
    } else {
        Write-Error "项目文件压缩失败"
        return $null
    }
}

# 传输文件到虚拟机
function Transfer-Files {
    param([string]$ZipPath)
    
    Write-Info "传输文件到虚拟机..."
    
    # 构建SCP命令
    $scpArgs = @()
    
    if ($SSHKeyPath -and (Test-Path $SSHKeyPath)) {
        $scpArgs += "-i", $SSHKeyPath
    }
    
    $scpArgs += "-o", "StrictHostKeyChecking=no"
    $scpArgs += $ZipPath
    $scpArgs += "${VMUser}@${VMHost}:${VMProjectPath}/"
    
    try {
        Write-Info "执行SCP传输..."
        Write-Info "命令: scp $($scpArgs -join ' ')"
        
        $process = Start-Process -FilePath "scp" -ArgumentList $scpArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Success "文件传输完成"
            return $true
        } else {
            Write-Error "文件传输失败，退出码: $($process.ExitCode)"
            return $false
        }
    }
    catch {
        Write-Error "文件传输异常: $($_.Exception.Message)"
        return $false
    }
}

# 在虚拟机上解压文件
function Extract-FilesOnVM {
    Write-Info "在虚拟机上解压文件..."
    
    # 构建SSH命令
    $sshArgs = @()
    
    if ($SSHKeyPath -and (Test-Path $SSHKeyPath)) {
        $sshArgs += "-i", $SSHKeyPath
    }
    
    $sshArgs += "-o", "StrictHostKeyChecking=no"
    $sshArgs += "${VMUser}@${VMHost}"
    
    $commands = @(
        "cd $VMProjectPath",
        "unzip -o project.zip",
        "rm project.zip",
        "ls -la"
    )
    
    $commandString = $commands -join " && "
    $sshArgs += $commandString
    
    try {
        Write-Info "执行SSH命令解压文件..."
        
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Success "文件解压完成"
            return $true
        } else {
            Write-Error "文件解压失败，退出码: $($process.ExitCode)"
            return $false
        }
    }
    catch {
        Write-Error "SSH命令执行异常: $($_.Exception.Message)"
        return $false
    }
}

# 在虚拟机上安装依赖和构建项目
function Build-ProjectOnVM {
    Write-Info "在虚拟机上安装依赖和构建项目..."
    
    # 构建SSH命令
    $sshArgs = @()
    
    if ($SSHKeyPath -and (Test-Path $SSHKeyPath)) {
        $sshArgs += "-i", $SSHKeyPath
    }
    
    $sshArgs += "-o", "StrictHostKeyChecking=no"
    $sshArgs += "${VMUser}@${VMHost}"
    
    $commands = @(
        "cd $VMProjectPath",
        "npm install",
        "npm run build"
    )
    
    $commandString = $commands -join " && "
    $sshArgs += $commandString
    
    try {
        Write-Info "执行npm install和build..."
        Write-Warning "这可能需要几分钟时间..."
        
        $process = Start-Process -FilePath "ssh" -ArgumentList $sshArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Success "项目构建完成"
            return $true
        } else {
            Write-Error "项目构建失败，退出码: $($process.ExitCode)"
            return $false
        }
    }
    catch {
        Write-Error "项目构建异常: $($_.Exception.Message)"
        return $false
    }
}

# 清理临时文件
function Clear-TempFiles {
    param([string]$ZipPath)
    
    if ($ZipPath -and (Test-Path $ZipPath)) {
        $tempDir = Split-Path $ZipPath -Parent
        Remove-Item $tempDir -Recurse -Force
        Write-Info "临时文件已清理"
    }
}

# 主函数
function Main {
    Write-Info "开始传输项目到CentOS虚拟机"
    Write-Info "虚拟机: $VMUser@$VMHost"
    Write-Info "项目路径: $ProjectPath"
    Write-Info "目标路径: $VMProjectPath"
    
    # 检查依赖
    if (!(Test-Dependencies)) {
        exit 1
    }
    
    # 测试虚拟机连接
    if (!(Test-VMConnection)) {
        exit 1
    }
    
    # 准备项目文件
    $zipPath = Prepare-ProjectFiles
    if (!$zipPath) {
        exit 1
    }
    
    try {
        # 传输文件
        if (!(Transfer-Files -ZipPath $zipPath)) {
            exit 1
        }
        
        # 解压文件
        if (!(Extract-FilesOnVM)) {
            exit 1
        }
        
        # 构建项目
        if (!(Build-ProjectOnVM)) {
            Write-Warning "项目构建失败，但文件传输成功"
            Write-Info "您可以手动登录虚拟机执行: cd $VMProjectPath && npm install && npm run build"
        }
        
        Write-Success "项目传输完成！"
        Write-Info "下一步："
        Write-Info "1. 在虚拟机上启动测试服务: docker-compose -f docker-compose.test.yml up -d"
        Write-Info "2. 在Windows上执行远程测试: .\scripts\vm-remote-test.ps1 -VMHost '$VMHost'"
        
    }
    finally {
        # 清理临时文件
        Clear-TempFiles -ZipPath $zipPath
    }
}

# 显示使用说明
if (!$VMUser) {
    Write-Host "项目文件传输到CentOS虚拟机脚本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\scripts\transfer-to-vm.ps1 -VMUser <用户名> [-VMHost <IP地址>] [-SSHKeyPath <密钥路径>]" -ForegroundColor White
    Write-Host ""
    Write-Host "参数:" -ForegroundColor Yellow
    Write-Host "  -VMUser        虚拟机用户名 (必需)" -ForegroundColor White
    Write-Host "  -VMHost        虚拟机IP地址 (默认: ***************)" -ForegroundColor White
    Write-Host "  -ProjectPath   项目路径 (默认: E:\football manager\server-new)" -ForegroundColor White
    Write-Host "  -SSHKeyPath    SSH私钥路径 (可选)" -ForegroundColor White
    Write-Host "  -UsePassword   使用密码认证 (可选)" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\scripts\transfer-to-vm.ps1 -VMUser centos" -ForegroundColor Green
    Write-Host "  .\scripts\transfer-to-vm.ps1 -VMUser root -VMHost *************" -ForegroundColor Green
    Write-Host "  .\scripts\transfer-to-vm.ps1 -VMUser centos -SSHKeyPath C:\Users\<USER>\.ssh\id_rsa" -ForegroundColor Green
    exit 0
}

# 执行主函数
Main
