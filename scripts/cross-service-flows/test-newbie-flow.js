/**
 * 新手流程测试脚本
 * 测试场景：注册→创建角色→新手引导→首次抽奖
 * 
 * 涉及服务：
 * - Auth服务：用户注册和登录
 * - Character服务：角色创建和基础信息
 * - Activity服务：新手引导任务
 * - Economy/Hero服务：首次抽奖体验
 */

const axios = require('axios');
const chalk = require('chalk');
const path = require('path');
const MicroserviceWebSocketClient = require('../common/websocket-client');

// 加载根目录的.env配置
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// 配置
const CONFIG = {
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  AUTH_URL: 'http://127.0.0.1:3001',
  TEST_USER_ID: 'newbie-test-user-' + Date.now(),
  TEST_SERVER_ID: 'server_001',
  TIMEOUT: 30000
};

// 测试数据存储
const TEST_DATA = {
  token: null,
  characterId: null,
  userId: null,
  serverId: CONFIG.TEST_SERVER_ID
};

class NewbieFlowTester extends MicroserviceWebSocketClient {
  constructor() {
    super(CONFIG);  // 传递完整的CONFIG对象
    this.testResults = [];
    this.currentTestIndex = 1;  // 添加测试索引
    this.token = null;  // 添加token属性
    this.isConnected = false;  // 添加连接状态
  }

  /**
   * 主测试流程
   */
  async runTests() {
    console.log(chalk.blue('🚀 开始新手流程测试'));
    console.log(chalk.gray(`测试用户ID: ${CONFIG.TEST_USER_ID}`));

    try {
      // 第1步：认证和连接（直接照搬成功的social脚本模式）
      console.log(chalk.blue('\n=== 第1步：认证和连接 ==='));
      TEST_DATA.token = await this.getAuthToken('newbietest');
      await this.connectWebSocket();

      // 第2步：创建角色
      console.log(chalk.blue('\n=== 第2步：创建角色 ==='));
      await this.testCharacterCreation();

      // 第3步：新手引导
      console.log(chalk.blue('\n=== 第3步：新手引导 ==='));
      await this.testNewbieGuide();

      // 第4步：首次抽奖
      console.log(chalk.blue('\n=== 第4步：首次抽奖 ==='));
      await this.testFirstLottery();

      // 第7步：测试结果汇总
      this.printTestSummary();

    } catch (error) {
      console.error(chalk.red('❌ 新手流程测试失败:'), error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }

  /**
   * 获取认证令牌（直接照搬成功的social脚本代码）
   */
  async getAuthToken(userPrefix = 'newbietest') {
    try {
      console.log(chalk.yellow('🔑 获取认证令牌...'));

      // 先注册用户（通过网关）
      const username = `${userPrefix}_${Date.now()}`;
      const email = `${userPrefix}_${Date.now()}@example.com`;
      const password = 'SecureP@ssw0rd!';

      const registerResponse = await axios.post(`${CONFIG.GATEWAY_WS_URL}/api/auth/auth/register`, {
        username: username,
        email: email,
        password: password,
        confirmPassword: password,
        acceptTerms: true,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      }, { timeout: 10000 });

      if (registerResponse.data.success) {
        console.log(chalk.green('✅ 用户注册成功'));

        // 然后登录获取token（通过网关）
        const loginResponse = await axios.post(`${CONFIG.GATEWAY_WS_URL}/api/auth/auth/login`, {
          username: username,  // 使用username而不是username
          password: password
        }, { timeout: 10000 });

        // 正确解析token位置（参考character测试脚本的成功模式）
        const accessToken = loginResponse.data.data?.tokens?.accessToken;
        if (loginResponse.status === 200 && accessToken) {
          this.token = accessToken;
          TEST_DATA.token = accessToken;
          console.log(chalk.green('✅ 认证令牌获取成功'));
          console.log(chalk.gray(`Token长度: ${accessToken.length}`));
          return this.token;
        } else {
          console.log(chalk.red('❌ 登录失败，无法获取令牌'));
          console.log(chalk.red('登录响应:', JSON.stringify(loginResponse.data, null, 2)));
          throw new Error('登录失败或未获取到token');
        }
      } else {
        throw new Error('用户注册失败');
      }
    } catch (error) {
      console.log(chalk.red(`❌ 认证失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 建立WebSocket连接（直接照搬成功的social脚本代码）
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      console.log(chalk.yellow('🔌 建立 WebSocket 连接...'));

      // 使用正确的认证方式：在连接时传递token
      this.socket = require('socket.io-client')(CONFIG.GATEWAY_WS_URL, {
        auth: { token: this.token },  // 在连接时传递token
        timeout: CONFIG.TIMEOUT,
        transports: ['websocket', 'polling'],  // 支持多种传输方式
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log(chalk.green('✅ WebSocket 连接和认证成功'));
        this.isConnected = true;
        resolve(true);
      });

      this.socket.on('connect_error', (error) => {
        console.log(chalk.red(`❌ WebSocket 连接失败: ${error.message}`));
        this.isConnected = false;
        reject(error);
      });

      this.socket.on('disconnect', (reason, details) => {
        console.log(chalk.yellow(`⚠️ WebSocket 连接断开: ${reason}`));
        if (details) {
          console.log(chalk.gray(`断开详情: ${JSON.stringify(details)}`));
        }
        this.isConnected = false;
      });
    });
  }

  /**
   * 建立WebSocket连接
   */
  async setupWebSocketConnection() {
    console.log(chalk.yellow('🔌 建立WebSocket连接...'));

    try {
      await this.connect();
      await this.authenticate(TEST_DATA.token);
      console.log(chalk.green('✅ WebSocket连接和认证成功'));
      
      this.testResults.push({
        step: 'WebSocket连接',
        success: true
      });
    } catch (error) {
      console.log(chalk.red(`❌ WebSocket连接失败: ${error.message}`));
      this.testResults.push({
        step: 'WebSocket连接',
        success: false,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 测试角色创建
   */
  async testCharacterCreation() {
    console.log(chalk.yellow('👤 测试角色创建...'));

    try {
      const characterName = `新手${Date.now()}`;
      const response = await this.sendMessage('character.character.create', {
        name: characterName,
        serverId: TEST_DATA.serverId,
        openId: `newbie-${Date.now()}`, // 添加必需的openId字段
        token: TEST_DATA.token
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        TEST_DATA.characterId = data.characterId;
        console.log(chalk.green('✅ 角色创建成功'));
        console.log(chalk.gray(`   角色名称: ${characterName}`));
        console.log(chalk.gray(`   角色ID: ${TEST_DATA.characterId}`));
        
        this.testResults.push({
          step: '角色创建',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        throw new Error(`角色创建失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 角色创建失败: ${error.message}`));
      this.testResults.push({
        step: '角色创建',
        success: false,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 测试新手引导
   */
  async testNewbieGuide() {
    console.log(chalk.yellow('🎯 测试新手引导...'));

    try {
      // 获取新手任务列表
      const taskResponse = await this.sendMessage('activity.task.getNewbieTasks', {
        playerId: TEST_DATA.characterId,
        serverId: TEST_DATA.serverId
      });

      const taskSuccess = taskResponse?.payload?.data?.code === 0;
      const taskData = taskResponse?.payload?.data?.data;

      if (taskSuccess && taskData) {
        console.log(chalk.green('✅ 获取新手任务成功'));
        console.log(chalk.gray(`   任务数量: ${taskData.tasks ? taskData.tasks.length : 0}`));

        // 完成第一个新手任务
        if (taskData.tasks && taskData.tasks.length > 0) {
          const firstTask = taskData.tasks[0];
          const completeResponse = await this.sendMessage('activity.task.claimReward', {
            playerId: TEST_DATA.characterId,
            serverId: TEST_DATA.serverId,
            taskId: firstTask.taskId
          });

          const completeSuccess = completeResponse?.payload?.data?.code === 0;
          if (completeSuccess) {
            console.log(chalk.green('✅ 完成新手任务成功'));
          }
        }
        
        this.testResults.push({
          step: '新手引导',
          success: true,
          data: taskData
        });
      } else {
        const errorMsg = taskResponse?.payload?.data?.message || '未知错误';
        throw new Error(`新手引导失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 新手引导失败: ${error.message}`));
      this.testResults.push({
        step: '新手引导',
        success: false,
        error: error.message
      });
      // 新手引导失败不阻断后续流程
    }
  }

  /**
   * 测试首次抽奖
   */
  async testFirstLottery() {
    console.log(chalk.yellow('🎰 测试首次抽奖...'));

    try {
      // 尝试免费抽奖（新手福利）
      const lotteryResponse = await this.sendMessage('activity.event.buyBestFootball', {
        characterId: TEST_DATA.characterId,
        serverId: TEST_DATA.serverId,
        token: TEST_DATA.token,
        index: 1 // 单抽
      });

      const success = lotteryResponse?.payload?.data?.code === 0;
      const data = lotteryResponse?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 首次抽奖成功'));
        console.log(chalk.gray(`   抽奖类型: 最佳11人单抽`));
        console.log(chalk.gray(`   获得奖励: ${data.rewards ? data.rewards.length : 0}个`));
        
        this.testResults.push({
          step: '首次抽奖',
          success: true,
          data: data
        });
      } else {
        const errorMsg = lotteryResponse?.payload?.data?.message || '未知错误';
        throw new Error(`首次抽奖失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 首次抽奖失败: ${error.message}`));
      this.testResults.push({
        step: '首次抽奖',
        success: false,
        error: error.message
      });
      // 抽奖失败不阻断流程，可能是资源不足等正常情况
    }
  }

  /**
   * 打印测试结果汇总
   */
  printTestSummary() {
    console.log(chalk.blue('\n=== 新手流程测试结果汇总 ==='));
    
    let totalSteps = this.testResults.length;
    let successSteps = this.testResults.filter(r => r.success).length;

    console.log(chalk.white(`总测试步骤: ${totalSteps}`));
    console.log(chalk.green(`成功步骤: ${successSteps}`));
    console.log(chalk.red(`失败步骤: ${totalSteps - successSteps}`));

    this.testResults.forEach((result, index) => {
      const status = result.success ? chalk.green('✅') : chalk.red('❌');
      console.log(`${status} ${index + 1}. ${result.step}`);
      if (!result.success && result.error) {
        console.log(chalk.gray(`   错误: ${result.error}`));
      }
    });

    if (successSteps === totalSteps) {
      console.log(chalk.green('\n🎉 新手流程测试完全成功！'));
    } else if (successSteps >= totalSteps * 0.7) {
      console.log(chalk.yellow('\n⚠️ 新手流程基本正常，部分功能需要优化'));
    } else {
      console.log(chalk.red('\n❌ 新手流程存在重大问题，需要修复'));
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new NewbieFlowTester();
  tester.runTests().catch(error => {
    console.error(chalk.red('新手流程测试执行失败:'), error);
    process.exit(1);
  });
}

module.exports = NewbieFlowTester;
