const { io } = require('socket.io-client');

// Socket.IO WebSocket 连接测试
async function testWebSocketConnection() {
  console.log('🔌 开始 Socket.IO WebSocket 连接测试...');

  try {
    const socket = io('http://localhost:3000', {
      transports: ['websocket', 'polling'],
      timeout: 5000,
    });

    socket.on('connect', function() {
      console.log('✅ Socket.IO 连接成功建立');
      console.log('🆔 连接 ID:', socket.id);

      // 测试 ping 消息
      console.log('📤 发送 ping 消息...');
      socket.emit('ping', { timestamp: Date.now() });

      // 测试心跳消息
      setTimeout(() => {
        console.log('💓 发送心跳消息...');
        socket.emit('heartbeat', { clientId: 'test-client-001' });
      }, 1000);

      // 测试聊天消息
      setTimeout(() => {
        console.log('💬 发送聊天消息...');
        socket.emit('send_chat_message', {
          message: 'Hello from test client!',
          room: 'test-room',
          userId: 'test-user-001'
        });
      }, 2000);

      // 测试加入房间
      setTimeout(() => {
        console.log('🏠 测试加入房间...');
        socket.emit('join_room', {
          room: 'test-room',
          userId: 'test-user-001'
        });
      }, 3000);

      // 测试离开房间
      setTimeout(() => {
        console.log('🚪 测试离开房间...');
        socket.emit('leave_room', {
          room: 'test-room',
          userId: 'test-user-001'
        });
      }, 4000);

      // 5秒后关闭连接
      setTimeout(() => {
        console.log('🔚 关闭 Socket.IO 连接');
        socket.disconnect();
      }, 5000);
    });

    // 监听各种事件响应
    socket.on('pong', function(data) {
      console.log('🏓 收到 pong 响应:', data);
    });

    socket.on('heartbeat_response', function(data) {
      console.log('💓 收到心跳响应:', data);
    });

    socket.on('message_sent', function(data) {
      console.log('💬 消息发送确认:', data);
    });

    socket.on('room_joined', function(data) {
      console.log('🏠 房间加入确认:', data);
    });

    socket.on('room_left', function(data) {
      console.log('🚪 房间离开确认:', data);
    });

    socket.on('error', function(error) {
      console.error('❌ Socket.IO 错误:', error);
    });

    socket.on('disconnect', function(reason) {
      console.log('🔌 Socket.IO 连接断开 - 原因:', reason);
    });

  } catch (error) {
    console.error('❌ Socket.IO 连接测试失败:', error.message);
  }
}

// 运行测试
testWebSocketConnection();
