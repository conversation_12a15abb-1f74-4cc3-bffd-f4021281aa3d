# Redis 独立服务部署配置
version: '3.8'

services:
  # Redis 主实例
  redis-master:
    image: redis:7.2-alpine
    container_name: fm-redis-master
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-redispassword}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --tcp-keepalive 300
      --timeout 0
      --loglevel notice
      --databases 16
      --slowlog-log-slower-than 10000
      --slowlog-max-len 128
    volumes:
      - redis_master_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./logs/redis:/var/log/redis
    networks:
      - redis-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redispassword}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.0'
        reservations:
          memory: 2G
          cpus: '0.5'

  # Redis 从实例（可选，用于读写分离）
  redis-slave:
    image: redis:7.2-alpine
    container_name: fm-redis-slave
    restart: unless-stopped
    ports:
      - "6380:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
      - REDIS_MASTER_HOST=redis-master
      - REDIS_MASTER_PORT=6379
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-redispassword}
      --replicaof redis-master 6379
      --masterauth ${REDIS_PASSWORD:-redispassword}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --replica-read-only yes
      --tcp-keepalive 300
      --timeout 0
      --loglevel notice
    volumes:
      - redis_slave_data:/data
      - ./logs/redis:/var/log/redis
    networks:
      - redis-network
    depends_on:
      redis-master:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redispassword}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.0'
        reservations:
          memory: 2G
          cpus: '0.5'

  # Redis Sentinel（高可用监控）
  redis-sentinel-1:
    image: redis:7.2-alpine
    container_name: fm-redis-sentinel-1
    restart: unless-stopped
    ports:
      - "26379:26379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    command: >
      redis-sentinel /usr/local/etc/redis/sentinel.conf
    volumes:
      - ./config/redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
      - ./logs/redis:/var/log/redis
    networks:
      - redis-network
    depends_on:
      - redis-master
      - redis-slave
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  redis-sentinel-2:
    image: redis:7.2-alpine
    container_name: fm-redis-sentinel-2
    restart: unless-stopped
    ports:
      - "26380:26379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    command: >
      redis-sentinel /usr/local/etc/redis/sentinel.conf
    volumes:
      - ./config/redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
      - ./logs/redis:/var/log/redis
    networks:
      - redis-network
    depends_on:
      - redis-master
      - redis-slave
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  redis-sentinel-3:
    image: redis:7.2-alpine
    container_name: fm-redis-sentinel-3
    restart: unless-stopped
    ports:
      - "26381:26379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    command: >
      redis-sentinel /usr/local/etc/redis/sentinel.conf
    volumes:
      - ./config/redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
      - ./logs/redis:/var/log/redis
    networks:
      - redis-network
    depends_on:
      - redis-master
      - redis-slave
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Redis 管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: fm-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=master:redis-master:6379:0:${REDIS_PASSWORD:-redispassword},slave:redis-slave:6379:0:${REDIS_PASSWORD:-redispassword}
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
    networks:
      - redis-network
    depends_on:
      - redis-master
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

  # Redis 监控
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: fm-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis-master:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    networks:
      - redis-network
    depends_on:
      - redis-master
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

volumes:
  redis_master_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis/master
  redis_slave_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis/slave

networks:
  redis-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
