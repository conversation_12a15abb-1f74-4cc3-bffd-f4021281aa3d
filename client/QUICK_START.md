# 🚀 快速启动指南

## 📋 项目概述

这是一个基于 **Vue 3 + Ant Design Vue** 的文字风格足球游戏客户端，采用现代Web技术栈构建，提供沉浸式的文字游戏体验。

## ⚡ 快速开始

### 1. 环境要求
- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 2. 安装依赖
```bash
cd client
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 4. 构建生产版本
```bash
npm run build
```

## 🎯 核心特性

### ✨ 技术特性
- **🎨 文字风格UI**: 纯文字界面，经典MUD游戏风格
- **⚡ 实时通信**: WebSocket实时数据同步
- **📱 响应式设计**: 支持桌面和移动设备
- **🔐 安全认证**: JWT + 双层Token机制
- **🌐 多区服支持**: 完整的分区分服架构
- **📊 数据可视化**: 表格、图表展示游戏数据

### 🎮 游戏功能
- **👤 角色管理**: 创建、选择、管理游戏角色
- **⚽ 球员系统**: 球员招募、培养、交易
- **🎯 战术配置**: 阵型设置、战术调整
- **🏆 比赛系统**: 实时比赛、赛程管理
- **🎒 背包系统**: 道具管理、装备系统
- **🏋️ 训练系统**: 球员训练、技能提升
- **🏪 转会市场**: 球员交易、市场分析
- **👥 公会系统**: 社交互动、团队合作

## 🏗️ 架构设计

### 技术栈
```typescript
const techStack = {
  framework: "Vue 3.4+ (Composition API)",
  language: "TypeScript 5+",
  ui: "Ant Design Vue 4.x",
  state: "Pinia",
  router: "Vue Router 4",
  http: "Axios",
  websocket: "Socket.IO Client 4.x",
  bundler: "Vite 5+",
  styling: "Less + CSS Variables"
}
```

### 目录结构
```
client/
├── src/
│   ├── components/        # 组件库
│   │   ├── common/        # 通用组件
│   │   ├── game/          # 游戏组件
│   │   └── ui/            # UI组件
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── game/          # 游戏页面
│   │   └── system/        # 系统页面
│   ├── stores/            # 状态管理
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── types/             # 类型定义
│   ├── router/            # 路由配置
│   └── styles/            # 样式文件
├── docs/                  # 文档
└── scripts/               # 脚本
```

## 🎨 设计系统

### 颜色规范
- **主色调**: `#52c41a` (绿色，经典终端风格)
- **背景色**: `#001529` (深蓝色)
- **卡片背景**: `#002140` (稍浅的深蓝)
- **文字色**: `#ffffff` (白色)
- **次要文字**: `#a0a0a0` (灰色)
- **边框色**: `#434343` (灰色)

### 字体规范
- **主字体**: 系统默认字体栈
- **等宽字体**: `'Courier New', 'Monaco', 'Menlo'` (游戏文本)

### 组件风格
- **按钮**: 边框样式，悬停效果
- **卡片**: 深色背景，绿色边框高亮
- **表格**: 等宽字体，行悬停效果
- **表单**: 深色输入框，绿色焦点

## 🔧 开发指南

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 规则
- 组件使用 Composition API
- 状态管理使用 Pinia

### 组件开发
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
// 使用 Composition API
import { ref, computed } from 'vue'

// 类型定义
interface Props {
  // props 定义
}

// 组件逻辑
</script>

<style lang="less" scoped>
// 组件样式
</style>
```

### API 调用
```typescript
// 使用封装的 HTTP 服务
import { httpService } from '@/services/http'

const fetchData = async () => {
  try {
    const data = await httpService.get('/api/endpoint')
    return data
  } catch (error) {
    console.error('API调用失败:', error)
  }
}
```

### WebSocket 通信
```typescript
// 使用 WebSocket 服务
import { wsService } from '@/services/websocket'

// 发送消息
const sendMessage = async () => {
  try {
    const response = await wsService.sendMessage('service.action', payload)
    return response
  } catch (error) {
    console.error('WebSocket消息发送失败:', error)
  }
}

// 订阅事件
wsService.subscribe('game.update', (data) => {
  // 处理游戏更新
})
```

## 🚀 部署说明

### 开发环境
```bash
npm run dev
```
- 热重载开发服务器
- 自动代理API请求到后端
- 支持WebSocket连接

### 生产环境
```bash
npm run build
npm run preview
```
- 构建优化的生产版本
- 代码分割和压缩
- 静态资源优化

### Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
```

## 🐛 常见问题

### Q: 开发服务器启动失败？
A: 检查 Node.js 版本，清除 node_modules 重新安装

### Q: WebSocket 连接失败？
A: 确保后端服务器运行，检查防火墙设置

### Q: 样式不生效？
A: 检查 Less 语法，确保变量文件正确导入

### Q: 类型错误？
A: 运行 `npm run type-check` 检查类型定义

## 📚 相关资源

- [Vue 3 文档](https://vuejs.org/)
- [Ant Design Vue 文档](https://antdv.com/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [Vite 文档](https://vitejs.dev/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送分支
5. 创建 Pull Request

---

**🎉 现在您可以开始开发足球游戏客户端了！**
