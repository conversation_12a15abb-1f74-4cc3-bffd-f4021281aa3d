<template>
  <div class="settings-view">
    <div class="page-header">
      <h2 class="page-title">游戏设置</h2>
    </div>

    <div class="settings-content">
      <a-tabs v-model:activeKey="activeTab" tab-position="left">
        <!-- 游戏设置 -->
        <a-tab-pane key="game" tab="游戏设置">
          <div class="settings-section">
            <h3 class="section-title">显示设置</h3>
            <div class="setting-item">
              <div class="setting-label">
                <span>界面主题</span>
                <span class="setting-desc">选择游戏界面主题</span>
              </div>
              <a-select v-model:value="gameSettings.theme" style="width: 150px">
                <a-select-option value="dark">深色主题</a-select-option>
                <a-select-option value="light">浅色主题</a-select-option>
                <a-select-option value="auto">跟随系统</a-select-option>
              </a-select>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>语言设置</span>
                <span class="setting-desc">选择游戏语言</span>
              </div>
              <a-select v-model:value="gameSettings.language" style="width: 150px">
                <a-select-option value="zh-CN">简体中文</a-select-option>
                <a-select-option value="en-US">English</a-select-option>
              </a-select>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>动画效果</span>
                <span class="setting-desc">启用界面动画效果</span>
              </div>
              <a-switch v-model:checked="gameSettings.animations" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>自动保存</span>
                <span class="setting-desc">自动保存游戏进度</span>
              </div>
              <a-switch v-model:checked="gameSettings.autoSave" />
            </div>
          </div>

          <div class="settings-section">
            <h3 class="section-title">游戏体验</h3>
            <div class="setting-item">
              <div class="setting-label">
                <span>快速比赛</span>
                <span class="setting-desc">跳过比赛动画直接显示结果</span>
              </div>
              <a-switch v-model:checked="gameSettings.fastMatch" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>自动续约</span>
                <span class="setting-desc">球员合同到期时自动续约</span>
              </div>
              <a-switch v-model:checked="gameSettings.autoRenew" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>训练提醒</span>
                <span class="setting-desc">球员训练冷却完成时提醒</span>
              </div>
              <a-switch v-model:checked="gameSettings.trainingReminder" />
            </div>
          </div>
        </a-tab-pane>

        <!-- 通知设置 -->
        <a-tab-pane key="notifications" tab="通知设置">
          <div class="settings-section">
            <h3 class="section-title">系统通知</h3>
            <div class="setting-item">
              <div class="setting-label">
                <span>桌面通知</span>
                <span class="setting-desc">允许显示桌面通知</span>
              </div>
              <a-switch v-model:checked="notificationSettings.desktop" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>声音提醒</span>
                <span class="setting-desc">通知时播放提示音</span>
              </div>
              <a-switch v-model:checked="notificationSettings.sound" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>比赛通知</span>
                <span class="setting-desc">比赛开始和结束时通知</span>
              </div>
              <a-switch v-model:checked="notificationSettings.match" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>转会通知</span>
                <span class="setting-desc">转会市场相关通知</span>
              </div>
              <a-switch v-model:checked="notificationSettings.transfer" />
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>公会通知</span>
                <span class="setting-desc">公会活动和消息通知</span>
              </div>
              <a-switch v-model:checked="notificationSettings.guild" />
            </div>
          </div>
        </a-tab-pane>

        <!-- 账号设置 -->
        <a-tab-pane key="account" tab="账号设置">
          <div class="settings-section">
            <h3 class="section-title">账号信息</h3>
            <div class="account-info">
              <div class="info-item">
                <span class="label">用户名:</span>
                <span class="value">{{ userInfo.username }}</span>
              </div>
              <div class="info-item">
                <span class="label">邮箱:</span>
                <span class="value">{{ userInfo.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">注册时间:</span>
                <span class="value">{{ formatDate(userInfo.registerTime) }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后登录:</span>
                <span class="value">{{ formatDate(userInfo.lastLoginTime) }}</span>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h3 class="section-title">安全设置</h3>
            <div class="setting-item">
              <div class="setting-label">
                <span>修改密码</span>
                <span class="setting-desc">更改账号登录密码</span>
              </div>
              <a-button @click="showChangePassword = true">修改密码</a-button>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>绑定邮箱</span>
                <span class="setting-desc">绑定或更换邮箱地址</span>
              </div>
              <a-button @click="showBindEmail = true">
                {{ userInfo.email ? '更换邮箱' : '绑定邮箱' }}
              </a-button>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>两步验证</span>
                <span class="setting-desc">启用两步验证提高账号安全</span>
              </div>
              <a-switch v-model:checked="accountSettings.twoFactor" />
            </div>
          </div>
        </a-tab-pane>

        <!-- 关于 -->
        <a-tab-pane key="about" tab="关于">
          <div class="settings-section">
            <h3 class="section-title">游戏信息</h3>
            <div class="game-info">
              <div class="info-item">
                <span class="label">游戏版本:</span>
                <span class="value">v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="label">客户端版本:</span>
                <span class="value">{{ clientVersion }}</span>
              </div>
              <div class="info-item">
                <span class="label">服务器:</span>
                <span class="value">{{ serverInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">在线人数:</span>
                <span class="value">{{ serverInfo.playerCount }}</span>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h3 class="section-title">帮助与支持</h3>
            <div class="help-links">
              <a-button type="link" @click="openHelp">游戏帮助</a-button>
              <a-button type="link" @click="openFeedback">意见反馈</a-button>
              <a-button type="link" @click="openContact">联系客服</a-button>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 保存按钮 -->
    <div class="settings-actions">
      <a-space>
        <a-button @click="resetSettings">重置设置</a-button>
        <a-button type="primary" @click="saveSettings" :loading="isSaving">
          保存设置
        </a-button>
      </a-space>
    </div>

    <!-- 修改密码模态框 -->
    <a-modal
      v-model:open="showChangePassword"
      title="修改密码"
      @ok="changePassword"
      @cancel="resetPasswordForm"
      :confirm-loading="isChangingPassword"
    >
      <a-form :model="passwordForm" layout="vertical">
        <a-form-item label="当前密码" required>
          <a-input-password v-model:value="passwordForm.currentPassword" />
        </a-form-item>
        <a-form-item label="新密码" required>
          <a-input-password v-model:value="passwordForm.newPassword" />
        </a-form-item>
        <a-form-item label="确认新密码" required>
          <a-input-password v-model:value="passwordForm.confirmPassword" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 绑定邮箱模态框 -->
    <a-modal
      v-model:open="showBindEmail"
      title="绑定邮箱"
      @ok="bindEmail"
      @cancel="resetEmailForm"
      :confirm-loading="isBindingEmail"
    >
      <a-form :model="emailForm" layout="vertical">
        <a-form-item label="邮箱地址" required>
          <a-input v-model:value="emailForm.email" type="email" />
        </a-form-item>
        <a-form-item label="验证码" required>
          <a-input-group compact>
            <a-input
              v-model:value="emailForm.code"
              style="width: calc(100% - 100px)"
              placeholder="请输入验证码"
            />
            <a-button
              @click="sendVerificationCode"
              :disabled="!emailForm.email || countdown > 0"
              style="width: 100px"
            >
              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
            </a-button>
          </a-input-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'

// 状态
const authStore = useAuthStore()
const globalStore = useGlobalStore()

const activeTab = ref('game')
const isSaving = ref(false)
const isChangingPassword = ref(false)
const isBindingEmail = ref(false)
const showChangePassword = ref(false)
const showBindEmail = ref(false)
const countdown = ref(0)

// 设置数据
const gameSettings = reactive({
  theme: 'dark',
  language: 'zh-CN',
  animations: true,
  autoSave: true,
  fastMatch: false,
  autoRenew: true,
  trainingReminder: true
})

const notificationSettings = reactive({
  desktop: true,
  sound: true,
  match: true,
  transfer: true,
  guild: true
})

const accountSettings = reactive({
  twoFactor: false
})

// 用户信息
const userInfo = reactive({
  username: 'Player123',
  email: '<EMAIL>',
  registerTime: '2024-01-01',
  lastLoginTime: '2024-01-15'
})

// 服务器信息
const serverInfo = reactive({
  name: '华夏服务器',
  playerCount: 1250
})

const clientVersion = ref('1.0.0-beta')

// 表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const emailForm = reactive({
  email: '',
  code: ''
})

// 方法
const saveSettings = async () => {
  try {
    isSaving.value = true
    
    // TODO: 保存设置到服务器
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '保存成功',
      message: '设置已保存'
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '保存失败',
      message: error.message
    })
  } finally {
    isSaving.value = false
  }
}

const resetSettings = () => {
  // 重置为默认设置
  Object.assign(gameSettings, {
    theme: 'dark',
    language: 'zh-CN',
    animations: true,
    autoSave: true,
    fastMatch: false,
    autoRenew: true,
    trainingReminder: true
  })
  
  Object.assign(notificationSettings, {
    desktop: true,
    sound: true,
    match: true,
    transfer: true,
    guild: true
  })
  
  globalStore.addNotification({
    type: 'info',
    title: '设置已重置',
    message: '所有设置已恢复为默认值'
  })
}

const changePassword = async () => {
  if (!passwordForm.currentPassword || !passwordForm.newPassword) {
    globalStore.addNotification({
      type: 'warning',
      title: '信息不完整',
      message: '请填写完整的密码信息'
    })
    return
  }
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    globalStore.addNotification({
      type: 'warning',
      title: '密码不匹配',
      message: '新密码和确认密码不一致'
    })
    return
  }
  
  try {
    isChangingPassword.value = true
    
    // TODO: 调用修改密码API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '密码修改成功',
      message: '请使用新密码重新登录'
    })
    
    showChangePassword.value = false
    resetPasswordForm()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '密码修改失败',
      message: error.message
    })
  } finally {
    isChangingPassword.value = false
  }
}

const resetPasswordForm = () => {
  passwordForm.currentPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
}

const sendVerificationCode = async () => {
  if (!emailForm.email) {
    globalStore.addNotification({
      type: 'warning',
      title: '请输入邮箱',
      message: '请先输入邮箱地址'
    })
    return
  }
  
  try {
    // TODO: 发送验证码
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
    globalStore.addNotification({
      type: 'success',
      title: '验证码已发送',
      message: '请查收邮箱中的验证码'
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '发送失败',
      message: error.message
    })
  }
}

const bindEmail = async () => {
  if (!emailForm.email || !emailForm.code) {
    globalStore.addNotification({
      type: 'warning',
      title: '信息不完整',
      message: '请填写邮箱和验证码'
    })
    return
  }
  
  try {
    isBindingEmail.value = true
    
    // TODO: 绑定邮箱
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    userInfo.email = emailForm.email
    
    globalStore.addNotification({
      type: 'success',
      title: '绑定成功',
      message: '邮箱绑定成功'
    })
    
    showBindEmail.value = false
    resetEmailForm()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '绑定失败',
      message: error.message
    })
  } finally {
    isBindingEmail.value = false
  }
}

const resetEmailForm = () => {
  emailForm.email = ''
  emailForm.code = ''
}

const openHelp = () => {
  globalStore.addNotification({
    type: 'info',
    title: '游戏帮助',
    message: '打开游戏帮助页面'
  })
}

const openFeedback = () => {
  globalStore.addNotification({
    type: 'info',
    title: '意见反馈',
    message: '打开意见反馈页面'
  })
}

const openContact = () => {
  globalStore.addNotification({
    type: 'info',
    title: '联系客服',
    message: '打开客服联系页面'
  })
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  // 加载用户设置
  // TODO: 从服务器加载设置
})
</script>

<style lang="less" scoped>
.settings-view {
  padding: @padding-lg;
  
  .page-header {
    margin-bottom: @margin-lg;
    
    .page-title {
      font-size: @font-size-xl;
      color: @text-color;
      margin: 0;
    }
  }
  
  .settings-content {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    min-height: 600px;
    
    :deep(.ant-tabs) {
      .ant-tabs-tab {
        color: @text-color-secondary;
        
        &.ant-tabs-tab-active {
          color: @primary-color;
        }
      }
      
      .ant-tabs-content-holder {
        padding: @padding-lg;
      }
    }
    
    .settings-section {
      margin-bottom: @margin-xl;
      
      .section-title {
        color: @text-color;
        margin-bottom: @margin-base;
        padding-bottom: @padding-xs;
        border-bottom: 1px solid @border-color;
      }
      
      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: @padding-base 0;
        border-bottom: 1px solid rgba(67, 67, 67, 0.3);
        
        &:last-child {
          border-bottom: none;
        }
        
        .setting-label {
          flex: 1;
          
          span:first-child {
            color: @text-color;
            font-weight: 500;
            display: block;
            margin-bottom: @margin-xs;
          }
          
          .setting-desc {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
    }
    
    .account-info,
    .game-info {
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: @padding-sm 0;
        border-bottom: 1px solid rgba(67, 67, 67, 0.3);
        
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          color: @text-color-secondary;
        }
        
        .value {
          color: @text-color;
          font-weight: 500;
        }
      }
    }
    
    .help-links {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-sm;
    }
  }
  
  .settings-actions {
    margin-top: @margin-lg;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-view {
    padding: @padding-base;
    
    .settings-content {
      :deep(.ant-tabs) {
        .ant-tabs-tab-pane {
          padding: @padding-base;
        }
      }
      
      .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: @margin-base;
      }
    }
  }
}
</style>
