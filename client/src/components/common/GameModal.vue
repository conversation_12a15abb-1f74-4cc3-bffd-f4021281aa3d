<template>
  <a-modal
    v-model:open="visible"
    :title="title"
    :width="width"
    :centered="centered"
    :closable="closable"
    :mask-closable="maskClosable"
    :destroy-on-close="destroyOnClose"
    :class="['game-modal', customClass]"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      <div class="modal-title">
        <component v-if="icon" :is="icon" class="title-icon" />
        <span class="title-text">{{ title }}</span>
      </div>
    </template>

    <div class="modal-content">
      <slot />
    </div>

    <template #footer>
      <div class="modal-footer">
        <slot name="footer">
          <a-space>
            <a-button v-if="showCancel" @click="handleCancel">
              {{ cancelText }}
            </a-button>
            <a-button 
              v-if="showOk" 
              type="primary" 
              :loading="confirmLoading"
              @click="handleOk"
            >
              {{ okText }}
            </a-button>
          </a-space>
        </slot>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  open?: boolean
  title?: string
  width?: number | string
  centered?: boolean
  closable?: boolean
  maskClosable?: boolean
  destroyOnClose?: boolean
  customClass?: string
  icon?: any
  showOk?: boolean
  showCancel?: boolean
  okText?: string
  cancelText?: string
  confirmLoading?: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'ok'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  title: '提示',
  width: 520,
  centered: true,
  closable: true,
  maskClosable: true,
  destroyOnClose: false,
  customClass: '',
  showOk: true,
  showCancel: true,
  okText: '确定',
  cancelText: '取消',
  confirmLoading: false
})

const emit = defineEmits<Emits>()

const visible = ref(props.open)

watch(() => props.open, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

const handleOk = () => {
  emit('ok')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="less" scoped>
.game-modal {
  :deep(.ant-modal-content) {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-elevated;
  }

  :deep(.ant-modal-header) {
    background-color: @card-bg;
    border-bottom: 1px solid @border-color;
    padding: @padding-base @padding-lg;

    .ant-modal-title {
      color: @text-color;
      font-family: @font-family-mono;
      font-weight: bold;
    }
  }

  :deep(.ant-modal-body) {
    padding: @padding-lg;
    color: @text-color-secondary;
    background-color: @card-bg;
  }

  :deep(.ant-modal-footer) {
    background-color: @card-bg;
    border-top: 1px solid @border-color;
    padding: @padding-base @padding-lg;
  }

  :deep(.ant-modal-close) {
    color: @text-color-secondary;

    &:hover {
      color: @text-color;
    }
  }
}

.modal-title {
  display: flex;
  align-items: center;
  gap: @margin-sm;

  .title-icon {
    font-size: @font-size-lg;
    color: @primary-color;
  }

  .title-text {
    font-size: @font-size-lg;
    font-weight: bold;
  }
}

.modal-content {
  min-height: 60px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
}

// 特殊样式变体
.game-modal.success {
  :deep(.ant-modal-header) {
    border-bottom-color: @success-color;
  }

  .title-icon {
    color: @success-color;
  }
}

.game-modal.warning {
  :deep(.ant-modal-header) {
    border-bottom-color: @warning-color;
  }

  .title-icon {
    color: @warning-color;
  }
}

.game-modal.error {
  :deep(.ant-modal-header) {
    border-bottom-color: @error-color;
  }

  .title-icon {
    color: @error-color;
  }
}

.game-modal.large {
  :deep(.ant-modal) {
    max-width: 90vw;
  }
}

.game-modal.fullscreen {
  :deep(.ant-modal) {
    max-width: 100vw;
    margin: 0;
    padding: 0;
    height: 100vh;
  }

  :deep(.ant-modal-content) {
    height: 100vh;
    border-radius: 0;
  }

  :deep(.ant-modal-body) {
    height: calc(100vh - 110px);
    overflow-y: auto;
  }
}
</style>
