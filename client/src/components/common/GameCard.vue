<template>
  <div 
    :class="[
      'game-card',
      {
        'card-hoverable': hoverable,
        'card-selected': selected,
        'card-disabled': disabled,
        'card-loading': loading,
        [`card-${size}`]: size,
        [`card-${type}`]: type
      },
      customClass
    ]"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="card-loading-overlay">
      <a-spin size="small" />
    </div>

    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" class="card-header">
      <slot name="header">
        <div class="header-content">
          <div class="header-left">
            <component v-if="icon" :is="icon" class="header-icon" />
            <span v-if="title" class="header-title">{{ title }}</span>
          </div>
          <div v-if="$slots.extra" class="header-extra">
            <slot name="extra" />
          </div>
        </div>
      </slot>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer" />
    </div>

    <!-- 角标 -->
    <div v-if="badge" class="card-badge" :class="`badge-${badgeType}`">
      {{ badge }}
    </div>

    <!-- 选中指示器 -->
    <div v-if="selected" class="card-selected-indicator">
      <CheckOutlined />
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue'

interface Props {
  title?: string
  icon?: any
  hoverable?: boolean
  selected?: boolean
  disabled?: boolean
  loading?: boolean
  size?: 'small' | 'default' | 'large'
  type?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  customClass?: string
  badge?: string | number
  badgeType?: 'default' | 'primary' | 'success' | 'warning' | 'error'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  hoverable: true,
  selected: false,
  disabled: false,
  loading: false,
  size: 'default',
  type: 'default',
  customClass: '',
  badgeType: 'primary'
})

const emit = defineEmits<Emits>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="less" scoped>
.game-card {
  position: relative;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  transition: all @transition-duration;
  overflow: hidden;

  // 尺寸变体
  &.card-small {
    .card-header {
      padding: @padding-xs @padding-sm;
    }
    .card-body {
      padding: @padding-sm;
    }
    .card-footer {
      padding: @padding-xs @padding-sm;
    }
  }

  &.card-default {
    .card-header {
      padding: @padding-sm @padding-base;
    }
    .card-body {
      padding: @padding-base;
    }
    .card-footer {
      padding: @padding-sm @padding-base;
    }
  }

  &.card-large {
    .card-header {
      padding: @padding-base @padding-lg;
    }
    .card-body {
      padding: @padding-lg;
    }
    .card-footer {
      padding: @padding-base @padding-lg;
    }
  }

  // 类型变体
  &.card-primary {
    border-color: @primary-color;
    
    .card-header {
      background-color: fade(@primary-color, 10%);
      border-bottom-color: @primary-color;
    }
  }

  &.card-success {
    border-color: @success-color;
    
    .card-header {
      background-color: fade(@success-color, 10%);
      border-bottom-color: @success-color;
    }
  }

  &.card-warning {
    border-color: @warning-color;
    
    .card-header {
      background-color: fade(@warning-color, 10%);
      border-bottom-color: @warning-color;
    }
  }

  &.card-error {
    border-color: @error-color;
    
    .card-header {
      background-color: fade(@error-color, 10%);
      border-bottom-color: @error-color;
    }
  }

  // 状态变体
  &.card-hoverable {
    cursor: pointer;

    &:hover {
      border-color: @primary-color;
      transform: translateY(-2px);
      box-shadow: @box-shadow-elevated;
    }
  }

  &.card-selected {
    border-color: @primary-color;
    background-color: fade(@primary-color, 5%);
    box-shadow: @box-shadow-elevated;
  }

  &.card-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      border-color: @border-color;
      box-shadow: none;
    }
  }

  &.card-loading {
    pointer-events: none;
  }
}

.card-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.card-header {
  border-bottom: 1px solid @border-color;
  background-color: rgba(0, 0, 0, 0.1);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: @margin-xs;

      .header-icon {
        font-size: @font-size-lg;
        color: @primary-color;
      }

      .header-title {
        font-weight: bold;
        color: @text-color;
        font-size: @font-size-base;
      }
    }

    .header-extra {
      color: @text-color-secondary;
    }
  }
}

.card-body {
  color: @text-color-secondary;
  line-height: 1.6;
}

.card-footer {
  border-top: 1px solid @border-color;
  background-color: rgba(0, 0, 0, 0.05);
}

.card-badge {
  position: absolute;
  top: -1px;
  right: -1px;
  padding: 2px 6px;
  border-radius: 0 @border-radius-base 0 @border-radius-base;
  font-size: @font-size-xs;
  font-weight: bold;
  color: white;
  z-index: 5;

  &.badge-default {
    background-color: @text-color-secondary;
  }

  &.badge-primary {
    background-color: @primary-color;
  }

  &.badge-success {
    background-color: @success-color;
  }

  &.badge-warning {
    background-color: @warning-color;
  }

  &.badge-error {
    background-color: @error-color;
  }
}

.card-selected-indicator {
  position: absolute;
  top: @margin-xs;
  right: @margin-xs;
  width: 20px;
  height: 20px;
  background-color: @primary-color;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: @font-size-xs;
  z-index: 5;
}
</style>
