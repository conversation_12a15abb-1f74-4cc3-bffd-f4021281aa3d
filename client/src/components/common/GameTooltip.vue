<template>
  <a-tooltip
    :title="title"
    :placement="placement"
    :trigger="trigger"
    :overlay-class-name="`game-tooltip ${customClass}`"
    :get-popup-container="getPopupContainer"
  >
    <template #title>
      <div class="tooltip-content">
        <!-- 标题 -->
        <div v-if="title" class="tooltip-title">
          <component v-if="icon" :is="icon" class="tooltip-icon" />
          <span>{{ title }}</span>
        </div>

        <!-- 描述 -->
        <div v-if="description" class="tooltip-description">
          {{ description }}
        </div>

        <!-- 自定义内容 -->
        <div v-if="$slots.content" class="tooltip-custom">
          <slot name="content" />
        </div>

        <!-- 属性列表 -->
        <div v-if="attributes && attributes.length > 0" class="tooltip-attributes">
          <div
            v-for="(attr, index) in attributes"
            :key="index"
            class="attribute-item"
          >
            <span class="attr-label">{{ attr.label }}:</span>
            <span class="attr-value" :class="attr.type">{{ attr.value }}</span>
          </div>
        </div>

        <!-- 快捷键提示 -->
        <div v-if="shortcut" class="tooltip-shortcut">
          <span class="shortcut-label">快捷键:</span>
          <kbd class="shortcut-key">{{ shortcut }}</kbd>
        </div>
      </div>
    </template>

    <slot />
  </a-tooltip>
</template>

<script setup lang="ts">
interface AttributeItem {
  label: string
  value: string | number
  type?: 'normal' | 'success' | 'warning' | 'error' | 'primary'
}

interface Props {
  title?: string
  description?: string
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom'
  trigger?: 'hover' | 'focus' | 'click' | 'contextmenu'
  customClass?: string
  icon?: any
  attributes?: AttributeItem[]
  shortcut?: string
  getPopupContainer?: () => HTMLElement
}

withDefaults(defineProps<Props>(), {
  placement: 'top',
  trigger: 'hover',
  customClass: '',
  getPopupContainer: () => document.body
})
</script>

<style lang="less">
.game-tooltip {
  .ant-tooltip-inner {
    background-color: rgba(0, 0, 0, 0.9);
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    color: @text-color;
    font-family: @font-family-base;
    max-width: 300px;
    padding: @padding-sm @padding-base;
  }

  .ant-tooltip-arrow {
    &::before {
      background-color: rgba(0, 0, 0, 0.9);
      border: 1px solid @border-color;
    }
  }
}
</style>

<style lang="less" scoped>
.tooltip-content {
  .tooltip-title {
    display: flex;
    align-items: center;
    gap: @margin-xs;
    font-weight: bold;
    color: @text-color;
    margin-bottom: @margin-xs;
    font-size: @font-size-base;

    .tooltip-icon {
      font-size: @font-size-sm;
      color: @primary-color;
    }
  }

  .tooltip-description {
    color: @text-color-secondary;
    font-size: @font-size-sm;
    line-height: 1.4;
    margin-bottom: @margin-xs;
  }

  .tooltip-custom {
    margin-bottom: @margin-xs;
  }

  .tooltip-attributes {
    margin-bottom: @margin-xs;

    .attribute-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
      font-size: @font-size-sm;

      &:last-child {
        margin-bottom: 0;
      }

      .attr-label {
        color: @text-color-secondary;
        margin-right: @margin-xs;
      }

      .attr-value {
        font-weight: bold;
        color: @text-color;

        &.success {
          color: @success-color;
        }

        &.warning {
          color: @warning-color;
        }

        &.error {
          color: @error-color;
        }

        &.primary {
          color: @primary-color;
        }
      }
    }
  }

  .tooltip-shortcut {
    display: flex;
    align-items: center;
    gap: @margin-xs;
    margin-top: @margin-xs;
    padding-top: @margin-xs;
    border-top: 1px solid @border-color;
    font-size: @font-size-xs;

    .shortcut-label {
      color: @text-color-secondary;
    }

    .shortcut-key {
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid @border-color;
      border-radius: 3px;
      padding: 2px 6px;
      font-family: @font-family-mono;
      font-size: @font-size-xs;
      color: @text-color;
    }
  }
}

// 特殊类型的提示框样式
.tooltip-content.hero-tooltip {
  .tooltip-title {
    color: @primary-color;
  }
}

.tooltip-content.item-tooltip {
  .tooltip-title {
    color: @warning-color;
  }
}

.tooltip-content.skill-tooltip {
  .tooltip-title {
    color: @info-color;
  }
}

.tooltip-content.error-tooltip {
  .tooltip-title {
    color: @error-color;
  }
}
</style>
