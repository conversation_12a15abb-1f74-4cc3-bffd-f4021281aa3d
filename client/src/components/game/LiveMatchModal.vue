<template>
  <a-modal
    v-model:open="visible"
    title="实时比赛"
    width="900px"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    class="live-match-modal"
  >
    <div v-if="matchData" class="live-match-container">
      <!-- 比赛头部信息 -->
      <div class="match-header">
        <div class="team-info home-team">
          <div class="team-name">{{ matchData.homeTeam }}</div>
          <div class="team-formation">{{ homeFormation }}</div>
        </div>
        
        <div class="match-center">
          <div class="match-score">
            <span class="home-score">{{ matchData.homeScore || 0 }}</span>
            <span class="score-separator">-</span>
            <span class="away-score">{{ matchData.awayScore || 0 }}</span>
          </div>
          <div class="match-time">
            <span class="current-time">{{ matchData.currentTime || 0 }}'</span>
            <span class="match-status">{{ getMatchStatusText() }}</span>
          </div>
        </div>
        
        <div class="team-info away-team">
          <div class="team-name">{{ matchData.awayTeam }}</div>
          <div class="team-formation">{{ awayFormation }}</div>
        </div>
      </div>

      <!-- 比赛进度条 -->
      <div class="match-progress">
        <a-progress
          :percent="matchProgress"
          :stroke-color="getProgressColor()"
          :show-info="false"
          size="small"
        />
        <div class="progress-labels">
          <span>0'</span>
          <span>45'</span>
          <span>90'</span>
        </div>
      </div>

      <!-- 比赛事件流 -->
      <div class="match-events">
        <div class="events-header">
          <h4>比赛事件</h4>
          <div class="event-controls">
            <a-switch
              v-model:checked="autoScroll"
              size="small"
            />
            <span class="control-label">自动滚动</span>
          </div>
        </div>
        
        <div 
          ref="eventsContainer"
          class="events-container"
          :class="{ 'auto-scroll': autoScroll }"
        >
          <div
            v-for="(event, index) in matchEvents"
            :key="index"
            class="event-item"
            :class="`event-${event.type}`"
          >
            <div class="event-time">{{ event.minute }}'</div>
            <div class="event-content">
              <div class="event-icon">{{ getEventIcon(event.type) }}</div>
              <div class="event-description">{{ event.description }}</div>
              <div v-if="event.player" class="event-player">{{ event.player }}</div>
            </div>
            <div class="event-team" :class="event.team">
              {{ event.team === 'home' ? matchData.homeTeam : matchData.awayTeam }}
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="matchEvents.length === 0" class="empty-events">
            <span>比赛即将开始...</span>
          </div>
        </div>
      </div>

      <!-- 比赛统计 -->
      <div class="match-stats">
        <div class="stats-header">
          <h4>比赛统计</h4>
        </div>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">控球率</div>
            <div class="stat-bar">
              <div class="home-stat">{{ matchStats.homePossession || 50 }}%</div>
              <div class="stat-progress">
                <a-progress
                  :percent="matchStats.homePossession || 50"
                  :show-info="false"
                  size="small"
                />
              </div>
              <div class="away-stat">{{ matchStats.awayPossession || 50 }}%</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">射门次数</div>
            <div class="stat-bar">
              <div class="home-stat">{{ matchStats.homeShots || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getShotPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getShotPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-stat">{{ matchStats.awayShots || 0 }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">射正次数</div>
            <div class="stat-bar">
              <div class="home-stat">{{ matchStats.homeShotsOnTarget || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getShotOnTargetPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getShotOnTargetPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-stat">{{ matchStats.awayShotsOnTarget || 0 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 比赛控制 -->
      <div class="match-controls">
        <a-space>
          <a-button 
            v-if="matchData.status === 'playing'"
            @click="pauseMatch"
            :disabled="!canControl"
          >
            <PauseCircleOutlined />
            暂停
          </a-button>
          
          <a-button 
            v-if="matchData.status === 'paused'"
            @click="resumeMatch"
            :disabled="!canControl"
          >
            <PlayCircleOutlined />
            继续
          </a-button>
          
          <a-button 
            @click="changeSpeed"
            :disabled="!canControl"
          >
            <ThunderboltOutlined />
            {{ getSpeedText() }}
          </a-button>
          
          <a-button 
            v-if="matchData.status === 'finished'"
            type="primary"
            @click="handleMatchFinished"
          >
            查看结果
          </a-button>
          
          <a-button 
            v-if="matchData.status === 'finished'"
            @click="closeMatch"
          >
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import {
  PauseCircleOutlined,
  PlayCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'
import type { Match, MatchEvent, MatchStats } from '@/types'
import { wsService } from '@/services/websocket'

// Props
interface Props {
  visible: boolean
  matchData: Match | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'match-finished': [result: any]
}>()

// 状态
const autoScroll = ref(true)
const matchSpeed = ref(1) // 1x, 2x, 4x
const canControl = ref(true)
const eventsContainer = ref<HTMLElement>()

// 比赛数据
const matchEvents = ref<MatchEvent[]>([])
const matchStats = ref<MatchStats>({
  homePossession: 50,
  awayPossession: 50,
  homeShots: 0,
  awayShots: 0,
  homeShotsOnTarget: 0,
  awayShotsOnTarget: 0,
  homeCorners: 0,
  awayCorners: 0,
  homeFouls: 0,
  awayFouls: 0
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const matchProgress = computed(() => {
  if (!props.matchData) return 0
  const currentTime = props.matchData.currentTime || 0
  return Math.min((currentTime / 90) * 100, 100)
})

const homeFormation = computed(() => {
  return props.matchData?.homeTeam?.formation?.teamFormations?.[0]?.name || '4-4-2'
})

const awayFormation = computed(() => {
  return props.matchData?.awayTeam?.formation?.teamFormations?.[0]?.name || '4-4-2'
})

// 方法
const getMatchStatusText = (): string => {
  if (!props.matchData) return ''
  
  switch (props.matchData.status) {
    case 'playing':
      return '进行中'
    case 'paused':
      return '暂停'
    case 'finished':
      return '已结束'
    case 'halftime':
      return '中场休息'
    default:
      return '准备中'
  }
}

const getProgressColor = (): string => {
  if (!props.matchData) return '#52c41a'
  
  const time = props.matchData.currentTime || 0
  if (time >= 90) return '#ff4d4f'
  if (time >= 45) return '#faad14'
  return '#52c41a'
}

const getEventIcon = (type: string): string => {
  const icons: Record<string, string> = {
    goal: '⚽',
    card: '🟨',
    redcard: '🟥',
    substitution: '🔄',
    penalty: '🥅',
    corner: '📐',
    offside: '🚩',
    foul: '⚠️'
  }
  return icons[type] || '📝'
}

const getShotPercentage = (team: 'home' | 'away'): number => {
  const homeShots = matchStats.value.homeShots || 0
  const awayShots = matchStats.value.awayShots || 0
  const total = homeShots + awayShots

  if (total === 0) return 50

  const teamShots = team === 'home' ? homeShots : awayShots
  return (teamShots / total) * 100
}

const getShotOnTargetPercentage = (team: 'home' | 'away'): number => {
  const homeShots = matchStats.value.homeShotsOnTarget || 0
  const awayShots = matchStats.value.awayShotsOnTarget || 0
  const total = homeShots + awayShots

  if (total === 0) return 50

  const teamShots = team === 'home' ? homeShots : awayShots
  return (teamShots / total) * 100
}

const getSpeedText = (): string => {
  return `${matchSpeed.value}x 速度`
}

const pauseMatch = async () => {
  try {
    await wsService.sendMessage('match.pause', {
      matchId: props.matchData?.id
    })
  } catch (error) {
    console.error('暂停比赛失败:', error)
  }
}

const resumeMatch = async () => {
  try {
    await wsService.sendMessage('match.resume', {
      matchId: props.matchData?.id
    })
  } catch (error) {
    console.error('恢复比赛失败:', error)
  }
}

const changeSpeed = async () => {
  const speeds = [1, 2, 4]
  const currentIndex = speeds.indexOf(matchSpeed.value)
  const nextIndex = (currentIndex + 1) % speeds.length
  matchSpeed.value = speeds[nextIndex]
  
  try {
    await wsService.sendMessage('match.changeSpeed', {
      matchId: props.matchData?.id,
      speed: matchSpeed.value
    })
  } catch (error) {
    console.error('改变比赛速度失败:', error)
  }
}

const handleMatchFinished = () => {
  if (props.matchData?.result) {
    emit('match-finished', props.matchData.result)
  }
}

const closeMatch = () => {
  visible.value = false
}

const scrollToBottom = () => {
  if (autoScroll.value && eventsContainer.value) {
    nextTick(() => {
      eventsContainer.value!.scrollTop = eventsContainer.value!.scrollHeight
    })
  }
}

// WebSocket事件监听
const setupMatchListeners = () => {
  if (!props.matchData) return
  
  // 监听比赛事件
  wsService.subscribe('match.event', (event: MatchEvent) => {
    if (event.matchId === props.matchData?.id) {
      matchEvents.value.push(event)
      scrollToBottom()
      
      // 更新统计数据
      updateMatchStats(event)
    }
  })
  
  // 监听比赛状态变化
  wsService.subscribe('match.statusChanged', (data) => {
    if (data.matchId === props.matchData?.id && props.matchData) {
      props.matchData.status = data.status
      props.matchData.currentTime = data.currentTime
    }
  })
  
  // 监听比赛结束
  wsService.subscribe('match.finished', (data) => {
    if (data.matchId === props.matchData?.id && props.matchData) {
      props.matchData.status = 'finished'
      props.matchData.result = data.result
      canControl.value = false
    }
  })
}

const cleanupMatchListeners = () => {
  wsService.unsubscribe('match.event')
  wsService.unsubscribe('match.statusChanged')
  wsService.unsubscribe('match.finished')
}

const updateMatchStats = (event: MatchEvent) => {
  switch (event.type) {
    case 'shot':
      if (event.team === 'home') {
        matchStats.value.homeShots += 1
      } else {
        matchStats.value.awayShots += 1
      }
      break
    case 'shotOnTarget':
      if (event.team === 'home') {
        matchStats.value.homeShotsOnTarget += 1
      } else {
        matchStats.value.awayShotsOnTarget += 1
      }
      break
    case 'corner':
      if (event.team === 'home') {
        matchStats.value.homeCorners += 1
      } else {
        matchStats.value.awayCorners += 1
      }
      break
    case 'foul':
      if (event.team === 'home') {
        matchStats.value.homeFouls += 1
      } else {
        matchStats.value.awayFouls += 1
      }
      break
  }
}

// 监听比赛数据变化
watch(() => props.matchData, (newMatch, oldMatch) => {
  if (newMatch && newMatch.id !== oldMatch?.id) {
    // 重置数据
    matchEvents.value = newMatch.events || []
    matchStats.value = newMatch.stats || {
      possession: { home: 50, away: 50 },
      shots: { home: 0, away: 0 },
      shotsOnTarget: { home: 0, away: 0 },
      corners: { home: 0, away: 0 },
      fouls: { home: 0, away: 0 }
    }
    
    // 设置监听器
    cleanupMatchListeners()
    setupMatchListeners()
  }
}, { immediate: true })

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible) {
    setupMatchListeners()
  } else {
    cleanupMatchListeners()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    setupMatchListeners()
  }
})

onUnmounted(() => {
  cleanupMatchListeners()
})
</script>

<style lang="less" scoped>
.live-match-modal {
  :deep(.ant-modal-content) {
    background-color: @card-bg;
    border: 1px solid @border-color;
  }
  
  :deep(.ant-modal-header) {
    background-color: @card-bg;
    border-bottom: 1px solid @border-color;
    
    .ant-modal-title {
      color: @text-color;
    }
  }
  
  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.live-match-container {
  font-family: @font-family-mono;
  color: @text-color;
  
  .match-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: @padding-lg;
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid @border-color;
    
    .team-info {
      flex: 1;
      text-align: center;
      
      .team-name {
        font-size: @font-size-lg;
        font-weight: bold;
        color: @text-color;
        margin-bottom: @margin-xs;
      }
      
      .team-formation {
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
      
      &.away-team {
        text-align: center;
      }
    }
    
    .match-center {
      flex: 1;
      text-align: center;
      
      .match-score {
        font-size: 2rem;
        font-weight: bold;
        color: @primary-color;
        margin-bottom: @margin-xs;
        
        .score-separator {
          margin: 0 @margin-sm;
          color: @text-color-secondary;
        }
      }
      
      .match-time {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: @margin-xs;
        
        .current-time {
          font-size: @font-size-lg;
          font-weight: bold;
          color: @highlight-color;
        }
        
        .match-status {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
  
  .match-progress {
    padding: @padding-base @padding-lg;
    border-bottom: 1px solid @border-color;
    
    .progress-labels {
      display: flex;
      justify-content: space-between;
      margin-top: @margin-xs;
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
  }
  
  .match-events {
    padding: @padding-base @padding-lg;
    border-bottom: 1px solid @border-color;
    
    .events-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @margin-base;
      
      h4 {
        color: @text-color;
        margin: 0;
      }
      
      .event-controls {
        display: flex;
        align-items: center;
        gap: @margin-xs;
        
        .control-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
    
    .events-container {
      height: 200px;
      overflow-y: auto;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-sm;
      
      &.auto-scroll {
        scroll-behavior: smooth;
      }
      
      .event-item {
        display: flex;
        align-items: center;
        gap: @margin-sm;
        padding: @padding-xs 0;
        border-bottom: 1px solid rgba(67, 67, 67, 0.3);
        
        &:last-child {
          border-bottom: none;
        }
        
        .event-time {
          width: 40px;
          font-weight: bold;
          color: @highlight-color;
          font-size: @font-size-sm;
        }
        
        .event-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: @margin-xs;
          
          .event-icon {
            font-size: @font-size-base;
          }
          
          .event-description {
            color: @text-color;
            font-size: @font-size-sm;
          }
          
          .event-player {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
        
        .event-team {
          width: 80px;
          text-align: right;
          font-size: @font-size-sm;
          color: @text-color-secondary;
          
          &.home {
            color: @info-color;
          }
          
          &.away {
            color: @warning-color;
          }
        }
        
        &.event-goal {
          background-color: rgba(82, 196, 26, 0.1);
        }
        
        &.event-card {
          background-color: rgba(250, 173, 20, 0.1);
        }
        
        &.event-redcard {
          background-color: rgba(255, 77, 79, 0.1);
        }
      }
      
      .empty-events {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: @text-color-secondary;
        font-style: italic;
      }
    }
  }
  
  .match-stats {
    padding: @padding-base @padding-lg;
    border-bottom: 1px solid @border-color;
    
    .stats-header {
      margin-bottom: @margin-base;
      
      h4 {
        color: @text-color;
        margin: 0;
      }
    }
    
    .stats-grid {
      display: flex;
      flex-direction: column;
      gap: @margin-base;
      
      .stat-item {
        .stat-label {
          text-align: center;
          color: @text-color-secondary;
          font-size: @font-size-sm;
          margin-bottom: @margin-xs;
        }
        
        .stat-bar {
          display: flex;
          align-items: center;
          gap: @margin-sm;
          
          .home-stat,
          .away-stat {
            width: 60px;
            text-align: center;
            font-weight: bold;
            color: @text-color;
          }
          
          .stat-progress {
            flex: 1;
            
            .stat-comparison {
              display: flex;
              height: 8px;
              background-color: @border-color;
              border-radius: 4px;
              overflow: hidden;
              
              .home-bar {
                background-color: @info-color;
                transition: width 0.3s;
              }
              
              .away-bar {
                background-color: @warning-color;
                transition: width 0.3s;
              }
            }
          }
        }
      }
    }
  }
  
  .match-controls {
    padding: @padding-lg;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .live-match-modal {
    :deep(.ant-modal) {
      width: 95% !important;
      margin: 10px;
    }
  }
  
  .live-match-container {
    .match-header {
      flex-direction: column;
      gap: @margin-base;
      
      .team-info,
      .match-center {
        flex: none;
      }
    }
    
    .match-events .events-container {
      height: 150px;
    }
    
    .match-stats .stats-grid {
      .stat-item .stat-bar {
        flex-direction: column;
        gap: @margin-xs;
        
        .stat-progress {
          width: 100%;
        }
      }
    }
  }
}
</style>
