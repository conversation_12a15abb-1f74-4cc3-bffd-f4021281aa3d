<template>
  <div id="app" class="football-manager-app">
    <!-- 全局加载状态 -->
    <a-spin 
      :spinning="globalStore.isLoading" 
      size="large" 
      tip="加载中..."
      class="global-loading"
    >
      <!-- 路由视图 -->
      <router-view />
    </a-spin>

    <!-- 全局通知容器 -->
    <div id="notification-container"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useGlobalStore } from '@/stores/global'
import { useAuthStore } from '@/stores/auth'

const globalStore = useGlobalStore()
const authStore = useAuthStore()

// 应用初始化
onMounted(async () => {
  try {
    // 初始化应用配置
    await globalStore.initializeApp()
    
    // 尝试自动登录
    await authStore.tryAutoLogin()
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})
</script>

<style lang="less">
// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f0f2f5;
}

#app {
  height: 100vh;
  overflow: hidden;
}

// 全局加载样式
.global-loading {
  min-height: 100vh;
  
  .ant-spin-container {
    height: 100vh;
  }
}

// 文字游戏风格
.football-manager-app {
  // 主色调：深色背景 + 绿色文字（经典终端风格）
  --primary-color: #52c41a;
  --bg-color: #001529;
  --text-color: #ffffff;
  --border-color: #434343;
  --hover-color: #1890ff;
  
  // 字体设置
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  
  // 全局文字样式
  .game-text {
    color: var(--text-color);
    background-color: var(--bg-color);
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Courier New', monospace;
  }
  
  // 按钮样式
  .game-button {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      background-color: var(--primary-color);
      color: var(--bg-color);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .football-manager-app {
    font-size: 14px;
  }
}
</style>
